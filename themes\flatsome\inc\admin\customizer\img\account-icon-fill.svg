<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="100px" height="72px" viewBox="0 0 100 72" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 3.8.1 (29687) - http://www.bohemiancoding.com/sketch -->
    <title>account-icon-fill</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0" y="0" width="100" height="71.4285714" rx="4"></rect>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="100" height="71.4285714" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
        <mask id="mask-4" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="100" height="71.4285714" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="account-icon-fill">
            <mask id="mask-3" fill="white">
                <use xlink:href="#path-1"></use>
            </mask>
            <g id="Mask" stroke="#3498DB" mask="url(#mask-2)" stroke-width="2" fill-opacity="0.01" fill="#00A0D2">
                <use mask="url(#mask-4)" xlink:href="#path-1"></use>
            </g>
            <rect id="Rectangle-166-Copy-4" fill="#3498DB" mask="url(#mask-3)" x="24.3075435" y="8.28125" width="52.9293346" height="53.4462226" rx="26.4646673"></rect>
            <path d="M53.95375,38.00625 C53.95375,38.00625 56.12875,35.87125 56.6025,33 C57.8775,33 58.665,29.9475 57.39,28.87375 C57.44375,27.74375 59.02875,20 51,20 C42.97125,20 44.55625,27.74375 44.61,28.87375 C43.335,29.9475 44.1225,33 45.3975,33 C45.87125,35.87125 48.0475,38.00625 48.0475,38.00625 C48.0475,38.00625 48.03,40.025 47.29,40.14125 C44.905,40.5175 36,44.41125 36,48.68125 L66,48.68125 C66,44.41125 57.095,40.5175 54.71125,40.14125 C53.97125,40.025 53.95375,38.00625 53.95375,38.00625 Z" id="Path" fill="#FFFFFF" mask="url(#mask-3)"></path>
        </g>
    </g>
</svg>