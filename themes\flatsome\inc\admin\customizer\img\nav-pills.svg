<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="40px" height="40px" viewBox="0 0 40 40" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 3.8.1 (29687) - http://www.bohemiancoding.com/sketch -->
    <title>nav-pills</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0.617058501" y="0.185392157" width="39.2156863" height="39.2156863" rx="4"></rect>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="39.2156863" height="39.2156863" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
        <mask id="mask-4" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="39.2156863" height="39.2156863" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="nav-pills">
            <mask id="mask-3" fill="white">
                <use xlink:href="#path-1"></use>
            </mask>
            <g id="Mask" stroke="#3498DB" mask="url(#mask-2)" stroke-width="2" fill-opacity="0.01" fill="#00A0D2">
                <use mask="url(#mask-4)" xlink:href="#path-1"></use>
            </g>
            <rect id="Rectangle-371" fill="#3498DB" mask="url(#mask-3)" x="3" y="10" width="34" height="19" rx="4"></rect>
            <path d="M9.644,15.402 C9.74800052,15.402 9.82499975,15.4149999 9.875,15.441 C9.92500025,15.4670001 9.98199968,15.5199996 10.046,15.6 L15.026,22.08 C15.0139999,21.9759995 15.006,21.8750005 15.002,21.777 C14.998,21.6789995 14.996,21.5840005 14.996,21.492 L14.996,15.402 L16.016,15.402 L16.016,24 L15.428,24 C15.3359995,24 15.2590003,23.9840002 15.197,23.952 C15.1349997,23.9199998 15.0740003,23.8660004 15.014,23.79 L10.04,17.316 C10.048,17.4160005 10.054,17.5139995 10.058,17.61 C10.062,17.7060005 10.064,17.7939996 10.064,17.874 L10.064,24 L9.044,24 L9.044,15.402 L9.644,15.402 Z M25.19,24 L24.29,24 C24.1859995,24 24.1020003,23.9740003 24.038,23.922 C23.9739997,23.8699997 23.9260002,23.8040004 23.894,23.724 L23.09,21.648 L19.232,21.648 L18.428,23.724 C18.3999999,23.7960004 18.3520003,23.8599997 18.284,23.916 C18.2159997,23.9720003 18.1320005,24 18.032,24 L17.132,24 L20.57,15.402 L21.752,15.402 L25.19,24 Z M19.556,20.808 L22.766,20.808 L21.416,17.31 C21.3279996,17.0939989 21.2420004,16.8240016 21.158,16.5 C21.1139998,16.6640008 21.0710002,16.8149993 21.029,16.953 C20.9869998,17.0910007 20.9460002,17.2119995 20.906,17.316 L19.556,20.808 Z M24.464,15.402 L25.394,15.402 C25.4980005,15.402 25.5819997,15.4279997 25.646,15.48 C25.7100003,15.5320003 25.7579998,15.5979996 25.79,15.678 L28.22,21.744 C28.2760003,21.8800007 28.3269998,22.0279992 28.373,22.188 C28.4190002,22.3480008 28.4639998,22.5139991 28.508,22.686 C28.5440002,22.5139991 28.5829998,22.3480008 28.625,22.188 C28.6670002,22.0279992 28.7159997,21.8800007 28.772,21.744 L31.19,15.678 C31.2180001,15.6099997 31.2659997,15.5470003 31.334,15.489 C31.4020003,15.4309997 31.4859995,15.402 31.586,15.402 L32.522,15.402 L29.018,24 L27.968,24 L24.464,15.402 Z" id="NAV" fill="#FFFFFF" mask="url(#mask-3)"></path>
        </g>
    </g>
</svg>