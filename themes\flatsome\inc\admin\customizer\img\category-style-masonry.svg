<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="70px" height="60px" viewBox="0 0 70 60" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 3.7.2 (28276) - http://www.bohemiancoding.com/sketch -->
    <title>category-style-masonry</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0" y="0" width="70" height="60" rx="4"></rect>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="70" height="60" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
        <mask id="mask-4" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="70" height="60" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
        <rect id="path-5" x="9" y="8.36364465" width="15.5341022" height="12"></rect>
        <mask id="mask-6" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="15.5341022" height="12" fill="white">
            <use xlink:href="#path-5"></use>
        </mask>
        <rect id="path-7" x="26.9868552" y="8" width="15.5341022" height="18.7272893"></rect>
        <mask id="mask-8" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="15.5341022" height="18.7272893" fill="white">
            <use xlink:href="#path-7"></use>
        </mask>
        <rect id="path-9" x="44.9737103" y="8.86364465" width="15.5341022" height="13"></rect>
        <mask id="mask-10" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="15.5341022" height="13" fill="white">
            <use xlink:href="#path-9"></use>
        </mask>
        <rect id="path-11" x="9" y="23.3636446" width="15.5341022" height="18"></rect>
        <mask id="mask-12" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="15.5341022" height="18" fill="white">
            <use xlink:href="#path-11"></use>
        </mask>
        <rect id="path-13" x="26.9868552" y="30" width="15.5341022" height="18.7272893"></rect>
        <mask id="mask-14" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="15.5341022" height="18.7272893" fill="white">
            <use xlink:href="#path-13"></use>
        </mask>
        <rect id="path-15" x="44.9737103" y="25" width="15.5341022" height="18.7272893"></rect>
        <mask id="mask-16" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="15.5341022" height="18.7272893" fill="white">
            <use xlink:href="#path-15"></use>
        </mask>
        <rect id="path-17" x="9" y="44.3636446" width="15.5341022" height="18"></rect>
        <mask id="mask-18" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="15.5341022" height="18" fill="white">
            <use xlink:href="#path-17"></use>
        </mask>
        <rect id="path-19" x="26.9868552" y="51" width="15.5341022" height="18.7272893"></rect>
        <mask id="mask-20" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="15.5341022" height="18.7272893" fill="white">
            <use xlink:href="#path-19"></use>
        </mask>
        <rect id="path-21" x="44.9737103" y="46" width="15.5341022" height="18.7272893"></rect>
        <mask id="mask-22" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="15.5341022" height="18.7272893" fill="white">
            <use xlink:href="#path-21"></use>
        </mask>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="category-style-masonry">
            <mask id="mask-3" fill="white">
                <use xlink:href="#path-1"></use>
            </mask>
            <g id="Mask" stroke="#3498DB" mask="url(#mask-2)" stroke-width="2" fill-opacity="0.01" fill="#00A0D2">
                <use mask="url(#mask-4)" xlink:href="#path-1"></use>
            </g>
            <g id="Rectangle-5" mask="url(#mask-3)" stroke="#3498DB" stroke-width="2" fill="#3498DB" fill-opacity="0.127858922">
                <use mask="url(#mask-6)" xlink:href="#path-5"></use>
            </g>
            <g id="Rectangle-5" mask="url(#mask-3)" stroke="#3498DB" stroke-width="2" fill="#3498DB" fill-opacity="0.127858922">
                <use mask="url(#mask-8)" xlink:href="#path-7"></use>
            </g>
            <g id="Rectangle-5" mask="url(#mask-3)" stroke="#3498DB" stroke-width="2" fill="#3498DB" fill-opacity="0.127858922">
                <use mask="url(#mask-10)" xlink:href="#path-9"></use>
            </g>
            <g id="Rectangle-5" mask="url(#mask-3)" stroke="#3498DB" stroke-width="2" fill="#3498DB" fill-opacity="0.127858922">
                <use mask="url(#mask-12)" xlink:href="#path-11"></use>
            </g>
            <g id="Rectangle-5" mask="url(#mask-3)" stroke="#3498DB" stroke-width="2" fill="#3498DB" fill-opacity="0.127858922">
                <use mask="url(#mask-14)" xlink:href="#path-13"></use>
            </g>
            <g id="Rectangle-5" mask="url(#mask-3)" stroke="#3498DB" stroke-width="2" fill="#3498DB" fill-opacity="0.127858922">
                <use mask="url(#mask-16)" xlink:href="#path-15"></use>
            </g>
            <g id="Rectangle-5" mask="url(#mask-3)" stroke="#3498DB" stroke-width="2" fill="#3498DB" fill-opacity="0.127858922">
                <use mask="url(#mask-18)" xlink:href="#path-17"></use>
            </g>
            <g id="Rectangle-5" mask="url(#mask-3)" stroke="#3498DB" stroke-width="2" fill="#3498DB" fill-opacity="0.127858922">
                <use mask="url(#mask-20)" xlink:href="#path-19"></use>
            </g>
            <g id="Rectangle-5" mask="url(#mask-3)" stroke="#3498DB" stroke-width="2" fill="#3498DB" fill-opacity="0.127858922">
                <use mask="url(#mask-22)" xlink:href="#path-21"></use>
            </g>
        </g>
    </g>
</svg>