<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="42px" height="42px" viewBox="0 0 42 42" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 3.7.1 (28215) - http://www.bohemiancoding.com/sketch -->
    <title>nav-line</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0.617058501" y="0.185392157" width="39.2156863" height="39.2156863" rx="4"></rect>
        <mask id="mask-3" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="-1" y="-1" width="41.2156863" height="41.2156863">
            <rect x="-0.382941499" y="-0.814607843" width="41.2156863" height="41.2156863" fill="white"></rect>
            <use xlink:href="#path-1" fill="black"></use>
        </mask>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="nav-line" transform="translate(1.000000, 1.000000)">
            <mask id="mask-2" fill="white">
                <use xlink:href="#path-1"></use>
            </mask>
            <g id="Mask">
                <use fill-opacity="0.01" fill="#00A0D2" fill-rule="evenodd" xlink:href="#path-1"></use>
                <use stroke="#3498DB" mask="url(#mask-3)" stroke-width="2" xlink:href="#path-1"></use>
            </g>
            <path d="M9.722,16.324 C9.79400036,16.324 9.85399976,16.327 9.902,16.333 C9.95000024,16.339 9.99299981,16.3509999 10.031,16.369 C10.0690002,16.3870001 10.1059998,16.4129998 10.142,16.447 C10.1780002,16.4810002 10.2179998,16.5259997 10.262,16.582 L14.816,22.384 C14.7999999,22.2439993 14.789,22.1070007 14.783,21.973 C14.777,21.8389993 14.774,21.7140006 14.774,21.598 L14.774,16.324 L16.196,16.324 L16.196,25 L15.362,25 C15.2339994,25 15.1280004,24.9800002 15.044,24.94 C14.9599996,24.8999998 14.8780004,24.8280005 14.798,24.724 L10.262,18.946 C10.2740001,19.0740006 10.283,19.2009994 10.289,19.327 C10.295,19.4530006 10.298,19.5679995 10.298,19.672 L10.298,25 L8.876,25 L8.876,16.324 L9.722,16.324 Z M25.574,25 L24.326,25 C24.1859993,25 24.0710005,24.9650003 23.981,24.895 C23.8909996,24.8249996 23.8260002,24.7380005 23.786,24.634 L23.138,22.864 L19.544,22.864 L18.896,24.634 C18.8639998,24.7260005 18.8010005,24.8099996 18.707,24.886 C18.6129995,24.9620004 18.4980007,25 18.362,25 L17.108,25 L20.516,16.324 L22.166,16.324 L25.574,25 Z M19.958,21.724 L22.724,21.724 L21.668,18.838 C21.6199998,18.7099994 21.5670003,18.5590009 21.509,18.385 C21.4509997,18.2109991 21.3940003,18.022001 21.338,17.818 C21.2819997,18.022001 21.2270003,18.2119991 21.173,18.388 C21.1189997,18.5640009 21.0660003,18.7179993 21.014,18.85 L19.958,21.724 Z M24.602,16.324 L25.904,16.324 C26.0440007,16.324 26.1579996,16.3579997 26.246,16.426 C26.3340004,16.4940003 26.3999998,16.5819995 26.444,16.69 L28.484,21.982 C28.5520003,22.1540009 28.6169997,22.342999 28.679,22.549 C28.7410003,22.755001 28.7999997,22.9719989 28.856,23.2 C28.9480005,22.7399977 29.0619993,22.3340018 29.198,21.982 L31.232,16.69 C31.2680002,16.5979995 31.3319995,16.5140004 31.424,16.438 C31.5160005,16.3619996 31.6299993,16.324 31.766,16.324 L33.068,16.324 L29.564,25 L28.106,25 L24.602,16.324 Z" id="NAV-Copy-3" fill="#3498DB" mask="url(#mask-2)"></path>
        </g>
    </g>
</svg>