!function(){"use strict";const t=window.matchMedia("(prefers-reduced-motion: reduce)");let e=!1;function o(){e="undefined"==typeof UxBuilder&&t.matches}o(),t.addEventListener?.("change",o),document.documentElement.style,window.getComputedStyle(document.documentElement)["scroll-behavior"],jQuery(document).ready((function(t){t("body").on("submit","form.cart",(function(e){const o=t(this).parents(".type-product");if(!o||!o.is(".product-type-simple, .product-type-variable"))return;if(void 0!==e.originalEvent&&t(e.originalEvent.submitter).is(".ux-buy-now-button"))return;e.preventDefault();const r=t(this),a=r.find(".single_add_to_cart_button");let n=r.serialize();n+="&action=flatsome_ajax_add_to_cart",a.val()&&(n+="&add-to-cart="+a.val()),t(document.body).trigger("adding_to_cart",[a,n]),t.ajax({url:window.flatsomeVars.ajaxurl,data:n,method:"POST",success:function(e){if(!e)return;const{product_url:o,notices:r,fragments:n,cart_hash:d,error:i=""}=e;if("undefined"==typeof wc_add_to_cart_params||"yes"!==wc_add_to_cart_params.cart_redirect_after_add)if(i&&o)window.location=o;else{if(a.removeClass("loading"),r.indexOf("error")>0){jQuery.fn.magnificPopup&&jQuery.magnificPopup.close();const e=t(".woocommerce-notices-wrapper");return e.append(r),void t.scrollTo(e,{offset:-window.flatsomeVars.scrollPaddingTop-15})}t(document.body).trigger("added_to_cart",[n,d,a])}else window.location=wc_add_to_cart_params.cart_url}})}))}))}();