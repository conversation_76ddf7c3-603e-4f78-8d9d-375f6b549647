<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="100px" height="72px" viewBox="0 0 100 72" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 3.8.1 (29687) - http://www.bohemiancoding.com/sketch -->
    <title>search-icon-plain</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0" y="0" width="100" height="71.4285714" rx="4"></rect>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="100" height="71.4285714" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
        <mask id="mask-4" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="100" height="71.4285714" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="search-icon-plain">
            <mask id="mask-3" fill="white">
                <use xlink:href="#path-1"></use>
            </mask>
            <g id="Mask" stroke="#3498DB" mask="url(#mask-2)" stroke-width="2" fill-opacity="0.01" fill="#00A0D2">
                <use mask="url(#mask-4)" xlink:href="#path-1"></use>
            </g>
            <path d="M65.258,47.776 L59.48,41.998 C61.064,39.73 62,36.976 62,34 C62,26.268 55.73,20 48,20 C40.27,20 34,26.268 34,34 C34,41.732 40.268,48 48,48 C50.976,48 53.732,47.064 56,45.48 L61.778,51.258 C62.738,52.218 64.298,52.218 65.258,51.258 C66.218,50.298 66.218,48.74 65.258,47.776 L65.258,47.776 Z M47.998,44.002 C42.476,44.002 37.998,39.526 37.998,34.002 C37.998,28.478 42.476,24.002 47.998,24.002 C53.522,24.002 57.998,28.478 57.998,34.002 C58,39.526 53.524,44.002 47.998,44.002 L47.998,44.002 Z" id="Shape" fill="#3498DB" mask="url(#mask-3)"></path>
        </g>
    </g>
</svg>