<?php
/**
 * Plugin Name: UX Date Element
 * Plugin URI: https://example.com/ux-date-element
 * Description: A custom date display element for UX Builder (Flatsome theme) that shows the current date with various formatting options.
 * Version: 1.0.0
 * Author: Your Name
 * Author URI: https://example.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: ux-date-element
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('UX_DATE_ELEMENT_VERSION', '1.0.0');
define('UX_DATE_ELEMENT_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('UX_DATE_ELEMENT_PLUGIN_URL', plugin_dir_url(__FILE__));
define('UX_DATE_ELEMENT_PLUGIN_FILE', __FILE__);

/**
 * Main UX Date Element Plugin Class
 */
class UX_Date_Element {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Get single instance of the class
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Plugin activation and deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
        
        // Initialize plugin after WordPress loads
        add_action('init', array($this, 'init'));
        
        // Check if UX Builder is available
        add_action('admin_notices', array($this, 'check_dependencies'));
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Check if UX Builder (Flatsome theme) is active
        if (!$this->is_ux_builder_available()) {
            deactivate_plugins(plugin_basename(__FILE__));
            wp_die(
                __('UX Date Element requires the Flatsome theme with UX Builder to be active.', 'ux-date-element'),
                __('Plugin Activation Error', 'ux-date-element'),
                array('back_link' => true)
            );
        }
        
        // Set plugin version
        update_option('ux_date_element_version', UX_DATE_ELEMENT_VERSION);
        
        // Log activation
        error_log('UX Date Element plugin activated successfully');
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clean up if needed
        error_log('UX Date Element plugin deactivated');
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Load text domain for translations
        load_plugin_textdomain('ux-date-element', false, dirname(plugin_basename(__FILE__)) . '/languages');
        
        // Initialize UX Builder integration if available
        if ($this->is_ux_builder_available()) {
            $this->init_ux_builder_integration();
        }
    }
    
    /**
     * Check if UX Builder is available
     */
    private function is_ux_builder_available() {
        // Check if add_ux_builder_shortcode function exists (indicates UX Builder is loaded)
        return function_exists('add_ux_builder_shortcode');
    }
    
    /**
     * Check plugin dependencies and show admin notices
     */
    public function check_dependencies() {
        if (!$this->is_ux_builder_available()) {
            echo '<div class="notice notice-error"><p>';
            echo __('UX Date Element requires the Flatsome theme with UX Builder to be active.', 'ux-date-element');
            echo '</p></div>';
        }
    }
    
    /**
     * Initialize UX Builder integration
     */
    private function init_ux_builder_integration() {
        // Hook into UX Builder setup
        add_action('ux_builder_setup', array($this, 'register_ux_builder_element'));
        
        // Register shortcode
        add_shortcode('ux_current_date', array($this, 'render_date_shortcode'));
        
        // Enqueue frontend assets
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_assets'));
    }
    
    /**
     * Register the date element with UX Builder
     */
    public function register_ux_builder_element() {
        add_ux_builder_shortcode('ux_current_date', array(
            'name'      => __('Current Date', 'ux-date-element'),
            'category'  => __('Content', 'ux-date-element'),
            'priority'  => 1,
            'options'   => array(
                'format' => array(
                    'type'    => 'select',
                    'heading' => __('Date Format', 'ux-date-element'),
                    'default' => 'F j, Y',
                    'options' => array(
                        'F j, Y'    => __('December 27, 2025', 'ux-date-element'),
                        'm/d/Y'     => __('12/27/2025', 'ux-date-element'),
                        'd/m/Y'     => __('27/12/2025', 'ux-date-element'),
                        'Y-m-d'     => __('2025-12-27', 'ux-date-element'),
                        'l, F j, Y' => __('Friday, December 27, 2025', 'ux-date-element'),
                    ),
                ),
            ),
        ));
    }
    
    /**
     * Render the date shortcode
     */
    public function render_date_shortcode($atts) {
        // Parse shortcode attributes
        $atts = shortcode_atts(array(
            'format' => 'F j, Y',
        ), $atts, 'ux_current_date');
        
        // Sanitize the format
        $format = sanitize_text_field($atts['format']);
        
        // Generate the current date
        $current_date = date($format);
        
        // Return formatted HTML
        return sprintf(
            '<div class="ux-date-element">%s</div>',
            esc_html($current_date)
        );
    }
    
    /**
     * Enqueue frontend assets
     */
    public function enqueue_frontend_assets() {
        // Enqueue CSS if file exists
        $css_file = UX_DATE_ELEMENT_PLUGIN_URL . 'assets/css/ux-date-element.css';
        if (file_exists(UX_DATE_ELEMENT_PLUGIN_DIR . 'assets/css/ux-date-element.css')) {
            wp_enqueue_style(
                'ux-date-element',
                $css_file,
                array(),
                UX_DATE_ELEMENT_VERSION
            );
        }
    }
}

// Initialize the plugin
UX_Date_Element::get_instance();
