<?php
/**
 * Text Element Class for UX User Elements
 * 
 * Displays custom text with various styling options
 * 
 * @package UX_User_Elements
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Text Element Class
 */
class UX_User_Elements_Text_Element extends UX_User_Elements_Base_Element {
    
    /**
     * Setup element configuration
     */
    protected function setup_config() {
        $this->tag = 'ux_custom_text';
        
        $this->config = array_merge($this->config, array(
            'name'     => __('Custom Text', 'ux-user-elements'),
            'category' => __('Content', 'ux-user-elements'),
            'priority' => 2,
            'options'  => array_merge(
                $this->get_text_options(),
                $this->get_styling_options(),
                $this->get_common_styling_options()
            ),
        ));
    }
    
    /**
     * Get text-specific options
     */
    private function get_text_options() {
        return array(
            'text' => array(
                'type'        => 'textfield',
                'heading'     => __('Text Content', 'ux-user-elements'),
                'default'     => __('Your custom text here', 'ux-user-elements'),
                'placeholder' => __('Enter your text...', 'ux-user-elements'),
                'auto_focus'  => true,
            ),
            'html_tag' => array(
                'type'    => 'select',
                'heading' => __('HTML Tag', 'ux-user-elements'),
                'default' => 'div',
                'options' => array(
                    'div'    => __('Div', 'ux-user-elements'),
                    'p'      => __('Paragraph', 'ux-user-elements'),
                    'span'   => __('Span', 'ux-user-elements'),
                    'h1'     => __('Heading 1', 'ux-user-elements'),
                    'h2'     => __('Heading 2', 'ux-user-elements'),
                    'h3'     => __('Heading 3', 'ux-user-elements'),
                    'h4'     => __('Heading 4', 'ux-user-elements'),
                    'h5'     => __('Heading 5', 'ux-user-elements'),
                    'h6'     => __('Heading 6', 'ux-user-elements'),
                ),
            ),
            'link_url' => array(
                'type'        => 'textfield',
                'heading'     => __('Link URL', 'ux-user-elements'),
                'default'     => '',
                'placeholder' => __('https://example.com', 'ux-user-elements'),
                'description' => __('Optional: Make the text clickable', 'ux-user-elements'),
            ),
            'link_target' => array(
                'type'       => 'checkbox',
                'heading'    => __('Open in New Tab', 'ux-user-elements'),
                'default'    => '',
                'conditions' => 'link_url !== ""',
            ),
        );
    }
    
    /**
     * Get additional styling options specific to text
     */
    private function get_styling_options() {
        return array(
            'font_weight' => array(
                'type'    => 'select',
                'heading' => __('Font Weight', 'ux-user-elements'),
                'default' => 'normal',
                'options' => array(
                    'normal' => __('Normal', 'ux-user-elements'),
                    'bold'   => __('Bold', 'ux-user-elements'),
                    'light'  => __('Light', 'ux-user-elements'),
                ),
            ),
            'text_transform' => array(
                'type'    => 'select',
                'heading' => __('Text Transform', 'ux-user-elements'),
                'default' => 'none',
                'options' => array(
                    'none'       => __('None', 'ux-user-elements'),
                    'uppercase'  => __('UPPERCASE', 'ux-user-elements'),
                    'lowercase'  => __('lowercase', 'ux-user-elements'),
                    'capitalize' => __('Capitalize', 'ux-user-elements'),
                ),
            ),
            'text_decoration' => array(
                'type'    => 'select',
                'heading' => __('Text Decoration', 'ux-user-elements'),
                'default' => 'none',
                'options' => array(
                    'none'         => __('None', 'ux-user-elements'),
                    'underline'    => __('Underline', 'ux-user-elements'),
                    'line-through' => __('Strike Through', 'ux-user-elements'),
                ),
            ),
            'background_color' => array(
                'type'    => 'colorpicker',
                'heading' => __('Background Color', 'ux-user-elements'),
                'default' => '',
            ),
            'padding' => array(
                'type'        => 'textfield',
                'heading'     => __('Padding', 'ux-user-elements'),
                'default'     => '',
                'placeholder' => __('e.g., 10px, 1em, 10px 15px', 'ux-user-elements'),
                'description' => __('CSS padding value', 'ux-user-elements'),
            ),
        );
    }
    
    /**
     * Render the text shortcode
     */
    public function render_shortcode($atts, $content = '') {
        // Default attributes
        $defaults = array(
            'text'             => __('Your custom text here', 'ux-user-elements'),
            'html_tag'         => 'div',
            'link_url'         => '',
            'link_target'      => '',
            'font_weight'      => 'normal',
            'text_transform'   => 'none',
            'text_decoration'  => 'none',
            'background_color' => '',
            'padding'          => '',
            'text_align'       => 'left',
            'font_size'        => 'medium',
            'custom_font_size' => '',
            'text_color'       => '',
            'css_class'        => '',
        );
        
        // Get sanitized attributes
        $atts = $this->get_attributes($atts, $defaults);
        
        // Build CSS classes and styles
        $css_classes = $this->build_css_classes('ux-text-element', $atts);
        $inline_styles = $this->build_text_styles($atts);
        
        // Prepare the text content
        $text_content = !empty($atts['text']) ? esc_html($atts['text']) : '';
        
        // Wrap in link if URL is provided
        if (!empty($atts['link_url'])) {
            $target = !empty($atts['link_target']) ? ' target="_blank" rel="noopener"' : '';
            $text_content = sprintf(
                '<a href="%s"%s>%s</a>',
                esc_url($atts['link_url']),
                $target,
                $text_content
            );
        }
        
        // Render the element with specified HTML tag
        $html_tag = in_array($atts['html_tag'], array('div', 'p', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6')) 
                   ? $atts['html_tag'] 
                   : 'div';
        
        return $this->render_element(
            $html_tag,
            $text_content,
            $css_classes,
            $inline_styles
        );
    }
    
    /**
     * Build text-specific inline styles
     */
    private function build_text_styles($atts) {
        $styles = $this->build_inline_styles($atts);
        
        // Font weight
        if (!empty($atts['font_weight']) && $atts['font_weight'] !== 'normal') {
            $styles[] = 'font-weight: ' . esc_attr($atts['font_weight']);
        }
        
        // Text transform
        if (!empty($atts['text_transform']) && $atts['text_transform'] !== 'none') {
            $styles[] = 'text-transform: ' . esc_attr($atts['text_transform']);
        }
        
        // Text decoration
        if (!empty($atts['text_decoration']) && $atts['text_decoration'] !== 'none') {
            $styles[] = 'text-decoration: ' . esc_attr($atts['text_decoration']);
        }
        
        // Padding
        if (!empty($atts['padding'])) {
            $styles[] = 'padding: ' . esc_attr($atts['padding']);
        }
        
        return $styles;
    }
}
