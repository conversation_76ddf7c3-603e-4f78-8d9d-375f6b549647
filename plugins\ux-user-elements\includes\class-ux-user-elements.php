<?php
/**
 * Main UX User Elements Class
 * 
 * Manages all custom UX Builder elements
 * 
 * @package UX_User_Elements
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Main UX User Elements Class
 */
class UX_User_Elements {
    
    /**
     * Single instance of the class
     */
    private static $instance = null;
    
    /**
     * Array of registered elements
     */
    private $elements = array();
    
    /**
     * Get single instance of the class
     */
    public static function get_instance() {
        if (null === self::$instance) {
            self::$instance = new self();
        }
        return self::$instance;
    }
    
    /**
     * Constructor
     */
    private function __construct() {
        $this->init_hooks();
    }
    
    /**
     * Initialize WordPress hooks
     */
    private function init_hooks() {
        // Plugin activation and deactivation hooks
        register_activation_hook(UX_USER_ELEMENTS_PLUGIN_FILE, array($this, 'activate'));
        register_deactivation_hook(UX_USER_ELEMENTS_PLUGIN_FILE, array($this, 'deactivate'));
        
        // Initialize plugin after WordPress loads
        add_action('init', array($this, 'init'));
        
        // Check if UX Builder is available
        add_action('admin_notices', array($this, 'check_dependencies'));
        
        // Load plugin files
        add_action('plugins_loaded', array($this, 'load_plugin_files'));
    }
    
    /**
     * Plugin activation
     */
    public function activate() {
        // Check if Flatsome theme is active (more reliable than checking UX Builder functions)
        $current_theme = get_template();
        if ($current_theme !== 'flatsome') {
            // Show warning but don't prevent activation - UX Builder might load later
            set_transient('ux_user_elements_activation_notice', 'warning', 30);
        }
        
        // Set plugin version
        update_option('ux_user_elements_version', UX_USER_ELEMENTS_VERSION);
        
        // Log activation
        error_log('UX User Elements plugin activated successfully');
    }
    
    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clean up if needed
        error_log('UX User Elements plugin deactivated');
    }
    
    /**
     * Initialize plugin
     */
    public function init() {
        // Load text domain for translations
        load_plugin_textdomain('ux-user-elements', false, dirname(plugin_basename(UX_USER_ELEMENTS_PLUGIN_FILE)) . '/languages');
        
        // Initialize elements
        $this->init_elements();
        
        // Enqueue frontend assets
        add_action('wp_enqueue_scripts', array($this, 'enqueue_frontend_assets'));
    }
    
    /**
     * Load plugin files
     */
    public function load_plugin_files() {
        // Load base element class
        require_once UX_USER_ELEMENTS_PLUGIN_DIR . 'includes/class-base-element.php';
        
        // Load element classes
        $this->load_elements();
    }
    
    /**
     * Load all element classes
     */
    private function load_elements() {
        $elements_dir = UX_USER_ELEMENTS_PLUGIN_DIR . 'includes/elements/';
        
        // Define available elements
        $element_files = array(
            'class-date-element.php',
            'class-text-element.php',
        );
        
        // Load each element file
        foreach ($element_files as $file) {
            $file_path = $elements_dir . $file;
            if (file_exists($file_path)) {
                require_once $file_path;
            }
        }
    }
    
    /**
     * Initialize all elements
     */
    private function init_elements() {
        // Define element classes
        $element_classes = array(
            'UX_User_Elements_Date_Element',
            'UX_User_Elements_Text_Element',
        );
        
        // Initialize each element
        foreach ($element_classes as $class_name) {
            if (class_exists($class_name)) {
                $this->elements[] = new $class_name();
            }
        }
    }
    
    /**
     * Check if UX Builder is available
     */
    private function is_ux_builder_available() {
        // Check if add_ux_builder_shortcode function exists (indicates UX Builder is loaded)
        return function_exists('add_ux_builder_shortcode');
    }
    
    /**
     * Check plugin dependencies and show admin notices
     */
    public function check_dependencies() {
        // Check for activation notice
        if (get_transient('ux_user_elements_activation_notice')) {
            delete_transient('ux_user_elements_activation_notice');
            $current_theme = get_template();
            if ($current_theme !== 'flatsome') {
                echo '<div class="notice notice-warning is-dismissible"><p>';
                echo __('UX User Elements works best with the Flatsome theme. Current theme: ', 'ux-user-elements') . $current_theme;
                echo '</p></div>';
            }
        }
        
        // Show info notice if UX Builder is not available but don't block functionality
        if (!$this->is_ux_builder_available()) {
            $current_theme = get_template();
            if ($current_theme === 'flatsome') {
                echo '<div class="notice notice-info is-dismissible"><p>';
                echo __('UX User Elements: UX Builder integration will be available once UX Builder is fully loaded. Shortcodes are ready to use.', 'ux-user-elements');
                echo '</p></div>';
            } else {
                echo '<div class="notice notice-warning is-dismissible"><p>';
                echo __('UX User Elements: For full UX Builder integration, please activate the Flatsome theme. Shortcodes work with any theme.', 'ux-user-elements');
                echo '</p></div>';
            }
        }
    }
    
    /**
     * Enqueue frontend assets
     */
    public function enqueue_frontend_assets() {
        // Enqueue CSS if file exists
        $css_file = UX_USER_ELEMENTS_PLUGIN_URL . 'assets/css/ux-user-elements.css';
        if (file_exists(UX_USER_ELEMENTS_PLUGIN_DIR . 'assets/css/ux-user-elements.css')) {
            wp_enqueue_style(
                'ux-user-elements',
                $css_file,
                array(),
                UX_USER_ELEMENTS_VERSION
            );
        }
    }
    
    /**
     * Get all registered elements
     */
    public function get_elements() {
        return $this->elements;
    }
    
    /**
     * Get element count
     */
    public function get_element_count() {
        return count($this->elements);
    }
}
