!function(){var t={269:function(t,e,g){g.g.UX_EMPTY_VALUE="<none>"},8484:function(t,e,g){!function(){"use strict";g.g.isIframe=function(){return!!window.frameElement},g.g.camelCase=function(t,e){return t=e?t.charAt(0).toUpperCase()+t.slice(1):t,jQuery.camelCase(t.replace(/\_|\:/g,"-",!0))},g.g.snakeCase=function(t,e="_"){return t.replace(/[A-Z]/g,((t,g)=>(g?e:"")+t.toLowerCase()))},g.g.capitalize=function(t){return t.charAt(0).toUpperCase()+t.slice(1)},g.g.round=function(t,e){return Math.round(t/e)*e}}()},6488:function(t,e,g){g.g.jQuery.fn.cover=function(t,e={}){if(!t)return this;let n=g.g.jQuery(t),A=n.get(0).ownerDocument,o=A.defaultView,C=n.outerOffset(e),I=!!this.get(0).ownerDocument.defaultView.frameElement,s=!!n.get(0).ownerDocument.defaultView.frameElement;if(!I&&s){let t=o.frameElement.getBoundingClientRect();C.left+=t.left,C.top+=t.top}return I&&s&&(C.top+=A.documentElement.scrollTop||A.body.scrollTop),this.css({"--top":`${C.top.toFixed()}px`,width:C.width.toFixed(2),height:C.height.toFixed(2),transform:`translateX(${C.left.toFixed()}px) translateY(${C.top.toFixed()}px)`})}},2496:function(t,e,g){g.g.jQuery.fn.isVisible=function(){return t=[this.get(0)],e=!0,t.map((function(t){var g=window.getComputedStyle(t);"none"===g.display&&(e=!1),"hidden"===g.visibility&&(e=!1),"0.0"===g.opacity&&(e=!1)})),e;var t,e}},8774:function(t,e,g){g.g.jQuery.fn.outerOffset=function(t={}){var e={width:0,height:0};return this.each(((n,A)=>{var o=g.g.jQuery(A),C=A.getBoundingClientRect(),I=C.right-(e.left?e.left:C.left),s=C.bottom-(e.top?e.top:C.top),i=o.css("display").search("inline")>-1;t.includeMargins&&(i?I=o.outerWidth(!0):s=o.outerHeight(!0)),e.top=(C.top>e.top?e.top:C.top)+0,e.left=(C.left>e.left?e.left:C.left)+0,e.width=I>e.width?I:e.width,e.height=s>e.height?s:e.height})),e.right=e.left+e.width,e.bottom=e.top+e.height,e}},628:function(t,e,g){g.g.jQuery.fn.shortcode=function(){var t=this.parents(),e=null;return this.data("shortcode")?this.data("shortcode"):(t.each((function(t,n){g.g.jQuery(n).data("shortcode")&&!e&&(e=g.g.jQuery(n).data("shortcode"))})),e)}},5889:function(){window.MutationObserver||(window.MutationObserver=function(t){function e(t){this.i=[],this.m=t}function g(e){var g,n={type:null,target:null,addedNodes:[],removedNodes:[],previousSibling:null,nextSibling:null,attributeName:null,attributeNamespace:null,oldValue:null};for(g in e)n[g]!==t&&e[g]!==t&&(n[g]=e[g]);return n}function n(e,n){var I=o(e,n);return function(i){var r=i.length;if(n.a&&3===e.nodeType&&e.nodeValue!==I.a&&i.push(new g({type:"characterData",target:e,oldValue:I.a})),n.b&&I.b&&A(i,e,I.b,n.f),n.c||n.g)var a=function(e,n,o,I){function i(t,n,o,C,s){var i,a,l,c=t.length-1;for(s=-~((c-s)/2);l=t.pop();)i=o[l.j],a=C[l.l],I.c&&s&&Math.abs(l.j-l.l)>=c&&(e.push(g({type:"childList",target:n,addedNodes:[i],removedNodes:[i],nextSibling:i.nextSibling,previousSibling:i.previousSibling})),s--),I.b&&a.b&&A(e,i,a.b,I.f),I.a&&3===i.nodeType&&i.nodeValue!==a.a&&e.push(g({type:"characterData",target:i,oldValue:a.a})),I.g&&r(i,a)}function r(n,o){for(var l,c,d,p,u,h=n.childNodes,m=o.c,f=h.length,v=m?m.length:0,$=0,b=0,y=0;b<f||y<v;)(p=h[b])===(u=(d=m[y])&&d.node)?(I.b&&d.b&&A(e,p,d.b,I.f),I.a&&d.a!==t&&p.nodeValue!==d.a&&e.push(g({type:"characterData",target:p,oldValue:d.a})),c&&i(c,n,h,m,$),I.g&&(p.childNodes.length||d.c&&d.c.length)&&r(p,d),b++,y++):(a=!0,l||(l={},c=[]),p&&(l[d=C(p)]||(l[d]=!0,-1===(d=s(m,p,y,"node"))?I.c&&(e.push(g({type:"childList",target:n,addedNodes:[p],nextSibling:p.nextSibling,previousSibling:p.previousSibling})),$++):c.push({j:b,l:d})),b++),u&&u!==h[b]&&(l[d=C(u)]||(l[d]=!0,-1===(d=s(h,u,b))?I.c&&(e.push(g({type:"childList",target:o.node,removedNodes:[u],nextSibling:m[y+1],previousSibling:m[y-1]})),$--):c.push({j:d,l:y})),y++));c&&i(c,n,h,m,$)}var a;return r(n,o),a}(i,e,I,n);(a||i.length!==r)&&(I=o(e,n))}}function A(e,n,A,o){for(var C,I,s={},i=n.attributes,a=i.length;a--;)I=(C=i[a]).name,o&&o[I]===t||(r(n,C)!==A[I]&&e.push(g({type:"attributes",target:n,attributeName:I,oldValue:A[I],attributeNamespace:C.namespaceURI})),s[I]=!0);for(I in A)s[I]||e.push(g({target:n,type:"attributes",attributeName:I,oldValue:A[I]}))}function o(t,e){var g=!0;return function t(n){var A={node:n};return!e.a||3!==n.nodeType&&8!==n.nodeType?(e.b&&g&&1===n.nodeType&&(A.b=I(n.attributes,(function(t,g){return e.f&&!e.f[g.name]||(t[g.name]=r(n,g)),t}),{})),g&&(e.c||e.a||e.b&&e.g)&&(A.c=function(t,e){for(var g=[],n=0;n<t.length;n++)g[n]=e(t[n],n,t);return g}(n.childNodes,t)),g=e.g):A.a=n.nodeValue,A}(t)}function C(t){try{return t.id||(t.mo_id=t.mo_id||a++)}catch(e){try{return t.nodeValue}catch(t){return a++}}}function I(t,e,g){for(var n=0;n<t.length;n++)g=e(g,t[n],n,t);return g}function s(t,e,g,n){for(;g<t.length;g++)if((n?t[g][n]:t[g])===e)return g;return-1}e._period=30,e.prototype={observe:function(t,g){for(var A={b:!!(g.attributes||g.attributeFilter||g.attributeOldValue),c:!!g.childList,g:!!g.subtree,a:!(!g.characterData&&!g.characterDataOldValue)},o=this.i,C=0;C<o.length;C++)o[C].s===t&&o.splice(C,1);g.attributeFilter&&(A.f=I(g.attributeFilter,(function(t,e){return t[e]=!0,t}),{})),o.push({s:t,o:n(t,A)}),this.h||function(t){!function g(){var n=t.takeRecords();n.length&&t.m(n,t),t.h=setTimeout(g,e._period)}()}(this)},takeRecords:function(){for(var t=[],e=this.i,g=0;g<e.length;g++)e[g].o(t);return t},disconnect:function(){this.i=[],clearTimeout(this.h),this.h=null}};var i=document.createElement("i");i.style.top=0;var r=(i="null"!=i.attributes.style.value)?function(t,e){return e.value}:function(t,e){return"style"!==e.name?e.value:t.style.cssText},a=1;return e}(void 0))},1741:function(t,e){"use strict";var g=Object.prototype.hasOwnProperty;function n(t){try{return decodeURIComponent(t.replace(/\+/g," "))}catch(t){return null}}function A(t){try{return encodeURIComponent(t)}catch(t){return null}}e.stringify=function(t,e){e=e||"";var n,o,C=[];for(o in"string"!=typeof e&&(e="?"),t)if(g.call(t,o)){if((n=t[o])||null!=n&&!isNaN(n)||(n=""),o=A(o),n=A(n),null===o||null===n)continue;C.push(o+"="+n)}return C.length?e+C.join("&"):""},e.parse=function(t){for(var e,g=/([^=?#&]+)=?([^&]*)/g,A={};e=g.exec(t);){var o=n(e[1]),C=n(e[2]);null===o||null===C||o in A||(A[o]=C)}return A}},2980:function(t){"use strict";t.exports=function(t,e){if(e=e.split(":")[0],!(t=+t))return!1;switch(e){case"http":case"ws":return 80!==t;case"https":case"wss":return 443!==t;case"ftp":return 21!==t;case"gopher":return 70!==t;case"file":return!1}return 0!==t}},1760:function(t,e,g){"use strict";var n=g(2980),A=g(1741),o=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,C=/[\n\r\t]/g,I=/^[A-Za-z][A-Za-z0-9+-.]*:\/\//,s=/:\d+$/,i=/^([a-z][a-z0-9.+-]*:)?(\/\/)?([\\/]+)?([\S\s]*)/i,r=/^[a-zA-Z]:/;function a(t){return(t||"").toString().replace(o,"")}var l=[["#","hash"],["?","query"],function(t,e){return p(e.protocol)?t.replace(/\\/g,"/"):t},["/","pathname"],["@","auth",1],[NaN,"host",void 0,1,1],[/:(\d*)$/,"port",void 0,1],[NaN,"hostname",void 0,1,1]],c={hash:1,query:1};function d(t){var e,n=("undefined"!=typeof window?window:void 0!==g.g?g.g:"undefined"!=typeof self?self:{}).location||{},A={},o=typeof(t=t||n);if("blob:"===t.protocol)A=new h(unescape(t.pathname),{});else if("string"===o)for(e in A=new h(t,{}),c)delete A[e];else if("object"===o){for(e in t)e in c||(A[e]=t[e]);void 0===A.slashes&&(A.slashes=I.test(t.href))}return A}function p(t){return"file:"===t||"ftp:"===t||"http:"===t||"https:"===t||"ws:"===t||"wss:"===t}function u(t,e){t=(t=a(t)).replace(C,""),e=e||{};var g,n=i.exec(t),A=n[1]?n[1].toLowerCase():"",o=!!n[2],I=!!n[3],s=0;return o?I?(g=n[2]+n[3]+n[4],s=n[2].length+n[3].length):(g=n[2]+n[4],s=n[2].length):I?(g=n[3]+n[4],s=n[3].length):g=n[4],"file:"===A?s>=2&&(g=g.slice(2)):p(A)?g=n[4]:A?o&&(g=g.slice(2)):s>=2&&p(e.protocol)&&(g=n[4]),{protocol:A,slashes:o||p(A),slashesCount:s,rest:g}}function h(t,e,g){if(t=(t=a(t)).replace(C,""),!(this instanceof h))return new h(t,e,g);var o,I,s,i,c,m,f=l.slice(),v=typeof e,$=this,b=0;for("object"!==v&&"string"!==v&&(g=e,e=null),g&&"function"!=typeof g&&(g=A.parse),o=!(I=u(t||"",e=d(e))).protocol&&!I.slashes,$.slashes=I.slashes||o&&e.slashes,$.protocol=I.protocol||e.protocol||"",t=I.rest,("file:"===I.protocol&&(2!==I.slashesCount||r.test(t))||!I.slashes&&(I.protocol||I.slashesCount<2||!p($.protocol)))&&(f[3]=[/(.*)/,"pathname"]);b<f.length;b++)"function"!=typeof(i=f[b])?(s=i[0],m=i[1],s!=s?$[m]=t:"string"==typeof s?~(c="@"===s?t.lastIndexOf(s):t.indexOf(s))&&("number"==typeof i[2]?($[m]=t.slice(0,c),t=t.slice(c+i[2])):($[m]=t.slice(c),t=t.slice(0,c))):(c=s.exec(t))&&($[m]=c[1],t=t.slice(0,c.index)),$[m]=$[m]||o&&i[3]&&e[m]||"",i[4]&&($[m]=$[m].toLowerCase())):t=i(t,$);g&&($.query=g($.query)),o&&e.slashes&&"/"!==$.pathname.charAt(0)&&(""!==$.pathname||""!==e.pathname)&&($.pathname=function(t,e){if(""===t)return e;for(var g=(e||"/").split("/").slice(0,-1).concat(t.split("/")),n=g.length,A=g[n-1],o=!1,C=0;n--;)"."===g[n]?g.splice(n,1):".."===g[n]?(g.splice(n,1),C++):C&&(0===n&&(o=!0),g.splice(n,1),C--);return o&&g.unshift(""),"."!==A&&".."!==A||g.push(""),g.join("/")}($.pathname,e.pathname)),"/"!==$.pathname.charAt(0)&&p($.protocol)&&($.pathname="/"+$.pathname),n($.port,$.protocol)||($.host=$.hostname,$.port=""),$.username=$.password="",$.auth&&(~(c=$.auth.indexOf(":"))?($.username=$.auth.slice(0,c),$.username=encodeURIComponent(decodeURIComponent($.username)),$.password=$.auth.slice(c+1),$.password=encodeURIComponent(decodeURIComponent($.password))):$.username=encodeURIComponent(decodeURIComponent($.auth)),$.auth=$.password?$.username+":"+$.password:$.username),$.origin="file:"!==$.protocol&&p($.protocol)&&$.host?$.protocol+"//"+$.host:"null",$.href=$.toString()}h.prototype={set:function(t,e,g){var o=this;switch(t){case"query":"string"==typeof e&&e.length&&(e=(g||A.parse)(e)),o[t]=e;break;case"port":o[t]=e,n(e,o.protocol)?e&&(o.host=o.hostname+":"+e):(o.host=o.hostname,o[t]="");break;case"hostname":o[t]=e,o.port&&(e+=":"+o.port),o.host=e;break;case"host":o[t]=e,s.test(e)?(e=e.split(":"),o.port=e.pop(),o.hostname=e.join(":")):(o.hostname=e,o.port="");break;case"protocol":o.protocol=e.toLowerCase(),o.slashes=!g;break;case"pathname":case"hash":if(e){var C="pathname"===t?"/":"#";o[t]=e.charAt(0)!==C?C+e:e}else o[t]=e;break;case"username":case"password":o[t]=encodeURIComponent(e);break;case"auth":var I=e.indexOf(":");~I?(o.username=e.slice(0,I),o.username=encodeURIComponent(decodeURIComponent(o.username)),o.password=e.slice(I+1),o.password=encodeURIComponent(decodeURIComponent(o.password))):o.username=encodeURIComponent(decodeURIComponent(e))}for(var i=0;i<l.length;i++){var r=l[i];r[4]&&(o[r[1]]=o[r[1]].toLowerCase())}return o.auth=o.password?o.username+":"+o.password:o.username,o.origin="file:"!==o.protocol&&p(o.protocol)&&o.host?o.protocol+"//"+o.host:"null",o.href=o.toString(),o},toString:function(t){t&&"function"==typeof t||(t=A.stringify);var e,g=this,n=g.host,o=g.protocol;o&&":"!==o.charAt(o.length-1)&&(o+=":");var C=o+(g.protocol&&g.slashes||p(g.protocol)?"//":"");return g.username?(C+=g.username,g.password&&(C+=":"+g.password),C+="@"):g.password?(C+=":"+g.password,C+="@"):"file:"!==g.protocol&&p(g.protocol)&&!n&&"/"!==g.pathname&&(C+="@"),(":"===n[n.length-1]||s.test(g.hostname)&&!g.port)&&(n+=":"),C+=n+g.pathname,(e="object"==typeof g.query?t(g.query):g.query)&&(C+="?"!==e.charAt(0)?"?"+e:e),g.hash&&(C+=g.hash),C}},h.extractProtocol=u,h.location=d,h.trimLeft=a,h.qs=A,t.exports=h},2391:function(t,e,g){var n={"./components/add-shortcode/add-shortcode.html":6191,"./components/app-actions/app-actions.html":1859,"./components/app-sidebar/app-sidebar.html":847,"./components/app-tools/app-move-tool/app-move-tool.template.html":3397,"./components/app-tools/app-resize-tool/app-resize-tool.template.html":5861,"./components/app-tools/app-tools.html":9335,"./components/context-menu/context-menu.template.html":2103,"./components/shortcode-hierarchy-list-item/shortcode-hierarchy-list-item.html":679,"./components/ux-option/types/checkbox.html":435,"./components/ux-option/types/col-slider.html":642,"./components/ux-option/types/colorpicker.html":6549,"./components/ux-option/types/file.html":5044,"./components/ux-option/types/gallery.html":5402,"./components/ux-option/types/group.html":7995,"./components/ux-option/types/image.html":2381,"./components/ux-option/types/margins.html":6499,"./components/ux-option/types/radio-buttons.html":6147,"./components/ux-option/types/radio-images.html":6714,"./components/ux-option/types/scrubfield.html":3631,"./components/ux-option/types/select.html":7486,"./components/ux-option/types/slider.html":1619,"./components/ux-option/types/text-editor.html":3055,"./components/ux-option/types/textarea.html":8908,"./components/ux-option/types/textfield.html":2013,"./components/ux-option/types/title.html":1054,"./components/ux-option/types/urlfield.html":1517,"./routes/home/<USER>":7009,"./routes/settings/settings.html":9369,"./routes/shortcode/shortcode.html":705,"./shortcodes/_loading.html":3833};function A(t){var e=o(t);return g(e)}function o(t){if(!g.o(n,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return n[t]}A.keys=function(){return Object.keys(n)},A.resolve=o,t.exports=A,A.id=2391},6191:function(t){"use strict";t.exports='<p ng-if="$ctrl.errorMessage" class="error error-message">\n  {{ $ctrl.errorMessage }}\n</p>\n\n<div class="add-shortcode-selector">\n  <div class="add-shortcode-header">\n    <h2 class="title">Add Content</h2>\n    <nav class="add-shortcode-types">\n      <button type="button"\n        ng-click="$ctrl.showType(\'shortcodes\')"\n        ng-class="{ active: $ctrl.type === \'shortcodes\' }">\n        Elements\n      </button>\n      <button type="button"\n        ng-if="$ctrl.shortcode.isRoot"\n        ng-click="$ctrl.showType(\'import\')"\n        ng-class="{ active: $ctrl.type === \'import\' }">\n        Import\n      </button>\n    </nav>\n  </div>\n\n  <div class="add-shortcode-items" ng-if="$ctrl.type === \'shortcodes\'">\n\n    <div class="flatsome-studio-button" ng-if="$ctrl.flatsomeStudioIsActive">\n      <button type="button" class="wp-style alt button-large button-block"\n        ng-click="$ctrl.showFlatsomeStudio()">\n        <span class="dashicons dashicons-screenoptions"></span> Flatsome Studio\n      </button>\n      <hr />\n    </div>\n\n    <input class="filter-elements" type="text" placeholder="Search&hellip;" ng-model="$ctrl.filter.name">\n\n    <div class="add-shortcode-category"\n      ng-repeat="category in $ctrl.items"\n      ng-show="items.length">\n      <h3>{{:: category.name }}</h3>\n      <ul>\n        <li class="add-shortcode-box" ng-repeat="item in items = (category.items | filter: $ctrl.filter)">\n          <button class="add-shortcode-box-button" type="button" ng-class="{ \'is-loading\': $ctrl.isLoading === item.tag }" ng-click="$ctrl.add(item, 0)">\n            <img ng-if="item.thumbnail" ng-src="{{:: item.thumbnail }}" alt="{{:: item.name }}"/>\n            <div ng-if="$ctrl.isLoading === item.tag" class="add-shortcode-loading-spinner loading-spinner is-visible"></div>\n            <span class="title">{{:: item.name }}</span>\n          </button>\n        </li>\n      </ul>\n    </div>\n  </div>\n</div>\n\n<template-importer ng-if="$ctrl.type === \'import\'"></template-importer>\n\n<div class="add-shortcode-presets">\n  <h3>Presets</h3>\n  <ul ng-if="$ctrl.presets">\n    <li class="add-shortcode-box" ng-repeat="preset in $ctrl.presets">\n      <button type="button" class="add-shortcode-box-button"\n        title="{{:: preset.name }}"\n        ng-class="{ \'with-thumbnail\' : !!preset.thumbnail, \'active\' : $ctrl.currentPreset === $index }"\n        ng-click="$ctrl.usePreset(preset.content); $ctrl.currentPreset = $index">\n        <div ng-if="preset.custom" class="add-shortcode-icon">\n          <svg width="36" height="36" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M7.73009 2.41274L8.95709 3.63719L3.40181 9.18095L2.17482 7.95652L7.73009 2.41274ZM7.73009 0.242432L0 7.95652L3.40181 11.3513L11.1319 3.63719L7.73009 0.242432Z" fill="#007CBA"/> <path d="M7.8196 11.3114L8.95987 12.4493L7.8196 13.5873L6.67928 12.4493L7.8196 11.3114ZM7.8196 9.14111L4.50439 12.4492L7.8196 15.7575L11.1348 12.4492L7.8196 9.14087V9.14111Z" fill="#007CBA"/> <path d="M12.2322 6.90786L13.3725 8.0458L12.2322 9.18369L11.0921 8.04584L12.2323 6.90795L12.2322 6.90786ZM12.2323 4.73763L8.91699 8.04584L12.2322 11.3542L15.5474 8.04584L12.2322 4.73755L12.2323 4.73763Z" fill="#007CBA" fill-opacity="0.6"/> </svg>\n        </div>\n        <img ng-if="!preset.custom && preset.thumbnail" ng-src="{{:: preset.thumbnail }}" alt="{{:: preset.name }}">\n        <span class="title">{{:: preset.name }}</span>\n      </button>\n      <div ng-if="preset.custom === true" class="add-shortcode-actions">\n        <button class="blank" ng-click="$ctrl.templates.updatePreset(preset)">\n          <span class="dashicons dashicons-edit"></span>\n        </button>\n        <button class="blank" ng-click="$ctrl.removePreset(preset)">\n          <span class="dashicons dashicons-trash"></span>\n        </button>\n      </div>\n    </li>\n  </ul>\n  <button\n    type="button"\n    class="wp-style alt button-large button-block"\n    ng-click="$ctrl.stack.close()"\n    ng-if="$ctrl.presets">\n    Apply\n  </button>\n</div>\n'},1859:function(t){"use strict";t.exports='<button\n  title="Undo"\n  type="button"\n  class="blank has-tooltip"\n  ng-click="$ctrl.undo()"\n  ng-disabled="$ctrl.canUndo() === false">\n  <span class="dashicons dashicons-undo"></span>\n  <div class="uxb-tooltip">Undo</div>\n</button>\n\n<button\n  title="Redo"\n  type="button"\n  class="blank has-tooltip"\n  ng-click="$ctrl.redo()"\n  ng-disabled="$ctrl.canRedo() === false">\n  <span class="dashicons dashicons-redo"></span>\n  <div class="uxb-tooltip">Redo</div>\n</button>\n\n<hr/>\n\n<button type="button"\n    class="blank has-tooltip"\n    title="{{:: breakpoint.title }}"\n    ng-click="$ctrl.setBreakpoint($index)"\n    ng-class="{ \'active\' : $ctrl.isActiveBreakpont($index) }"\n    ng-repeat="(name, breakpoint) in $ctrl.breakpoints.all track by breakpoint.width">\n    <span class="{{:: breakpoint.icon }}"></span>\n    <div class="uxb-tooltip">{{:: breakpoint.title }}</div>\n    <div class="has-breakpoint-values" ng-if="$ctrl.hasBreakpointValues($index)"></div>\n</button>\n\n<hr/>\n\n<button type="button"\n    class="blank has-tooltip"\n    title="{{:: action.tooltip }}"\n    ng-click="$ctrl.doAction(action)"\n    ng-repeat="action in $ctrl.actions">\n    <span class="{{:: action.icon }}"></span>\n    <div class="uxb-tooltip">{{:: action.tooltip }}</div>\n</button>\n'},847:function(t){"use strict";t.exports='<app-sidebar-main class="animate-{{ $ctrl.routeAnimation }}">\n\n  <div class="app-sidebar-top title-row">\n  \t<div class="title-row-icon">\n\t  \t<button type="button"\n        title="Exit Builder"\n        class="blank" ng-click="$ctrl.app.exit()"\n        ng-disabled="$ctrl.permissions.exit === false">\n\t   \t \t<span class="dashicons dashicons-no-alt"></span>\n\t\t  </button>\n  \t</div>\n  \t<div class="title-row-title">\n      {{ $ctrl.store.post.attributes.values.post_title }}\n    </div>\n  \t<div class="title-row-actions">\n  \t\t<button type="button" class="blank" ng-click="$ctrl.app.goto(\'/settings\')">\n\t      <span class="dashicons dashicons-admin-generic"></span>\n\t    </button>\n  \t</div>\n  </div>\n\n  <div class="app-sidebar-view">\n    <home-view ng-if="$ctrl.viewName === \'home\'"></home-view>\n    <settings-view ng-if="$ctrl.viewName === \'settings\'"></settings-view>\n    <shortcode-view ng-if="$ctrl.viewName === \'shortcode\'" shortcode="$ctrl.viewProps.shortcode"></shortcode-view>\n  </div>\n\n  <div class="app-sidebar-footer">\n    <button type="button" class="blank app-sidebar-toggle" ng-click="$ctrl.toggle()">\n      <span class="dashicons dashicons-arrow-left-alt2"\n        ng-if="$ctrl.store.showSidebar === true"\n      ></span>\n      <span class="dashicons dashicons-arrow-right-alt2"\n        ng-if="$ctrl.store.showSidebar === false"\n      ></span>\n    </button>\n  </div>\n\n</app-sidebar-main>\n'},3397:function(t){"use strict";t.exports='<div class="uxb-move">\n  <div class="uxb-move-handle"\n    ng-if="$ctrl.shortcode"\n    ng-class="$ctrl.classNames($ctrl.shortcode)"\n    draggable-shortcode="$ctrl.shortcode">\n    <span class="uxb-move-icon dashicons dashicons-move"></span>\n  </div>\n</div>\n'},5861:function(t){"use strict";t.exports='<div class="uxb-resize uxb-resize-top"></div>\n<div class="uxb-resize uxb-resize-right"></div>\n<div class="uxb-resize uxb-resize-bottom"></div>\n<div class="uxb-resize uxb-resize-left"></div>\n'},9335:function(t){"use strict";t.exports='<app-outline-tool class="auto-size" shortcode="$ctrl.outlined"></app-outline-tool>\n<app-resize-tool class="auto-size" shortcode="$ctrl.outlined"></app-resize-tool>\n<app-move-tool class="auto-size" shortcode="$ctrl.outlined"></app-move-tool>\n<app-select-tool class="auto-size" shortcode="$ctrl.selected"></app-select-tool>\n<add-buttons></add-buttons>\n\n<div class="tools-addable">\n  <div class="line"></div>\n  <add-button\n    class="button"\n    index="$ctrl.index"\n    shortcode="$ctrl.addable">\n  </add-button>\n</div>\n'},2103:function(t){"use strict";t.exports='<div class="context-menu-menu" ng-click="$ctrl.hide()">\n  <shortcode-actions shortcode="$ctrl.shortcode"></shortcode-actions>\n</div>\n'},679:function(t){"use strict";t.exports='<div class="hierarchy-title" ng-class="{ \'active\' : $ctrl.isActive(), [\'open\'] : $ctrl.shortcode.states.open, [\'visibility-\'+$ctrl.shortcode.options.visibility] : $ctrl.shortcode.options.visibility }">\n\n    <button type="button" class="hierarchy-toggle"\n        ng-if="$ctrl.shortcode.children"\n        ng-click="$ctrl.toggleChildren()">\n    </button>\n\n    <div class="hierarchy-content"\n        ng-click="$ctrl.selectShortcode()"\n        ng-mouseover="$ctrl.outlineShortcode()"\n        ng-dblclick="$ctrl.configureShortcode()">\n\n        <span class="hierarchy-name">{{:: $ctrl.shortcode.data.name }}</span>\n\n        <span class="hierarchy-info" ng-bind="$ctrl.getShortcodeInfo()"></span>\n    </div>\n\n    <div class="hierarchy-tools">\n      <button type="button blank" ng-click="$ctrl.showContextMenu($event)">\n        <span class="dashicons dashicons-admin-generic"></span>\n      </button>\n    </div>\n</div>\n\n<shortcode-hierarchy-list\n  ng-if="$ctrl.shortcode.states.open"\n  ng-class="{ \'open\': $ctrl.shortcode.states.open }"\n  shortcode="$ctrl.shortcode">\n</shortcode-hierarchy-list>\n'},435:function(t){"use strict";t.exports='<label>\n  <input type="checkbox"\n    ng-model="$ctrl.model"\n    ng-true-value="\'true\'"\n    ng-false-value="\'0\'">\n  <span></span>\n</label>\n'},642:function(t){"use strict";t.exports='<div class="col-slider-wrap col-slider-cols-{{ $ctrl.model }}">\n\n\t<table class="col-slider-table">\n\t\t<tbody>\n\t\t\t<tr>\n\t\t\t\t<td ng-repeat="n in _.range($ctrl.option.min, $ctrl.option.max + 1) track by $index">{{:: n }}</td>\n\t\t\t</tr>\n\t\t</tbody>\n\t</table>\n\n\t<input type="range"\n\t\tclass="col-slider-input"\n    min="{{:: $ctrl.option.min }}"\n    max="{{:: $ctrl.option.max }}"\n    ng-model="$ctrl.model">\n</div>\n'},6549:function(t){"use strict";t.exports='<ux-option-colorpicker\n  option="$ctrl.option"\n  model="$ctrl.model"\n></ux-option-colorpicker>\n\n<div ng-if="$ctrl.option.helpers" class="option-helpers option-helpers-colors">\n  <a href="javascript:"\n    title="Remove"\n    ng-click="$ctrl.model = null">\n    <span class="dashicons dashicons-no-alt"></span>\n  </a>\n  <a href="javascript:"\n    title="{{:: value.title }}"\n    style="background-color: {{:: value.value }}"\n    ng-repeat="(key, value) in $ctrl.option.helpers"\n    ng-click="$ctrl.model = value.value">\n  </a>\n</div>\n'},5044:function(t){"use strict";t.exports='<ux-option-file\n  option="$ctrl.option"\n  model="$ctrl.model"\n></ux-option-file>\n'},5402:function(t){"use strict";t.exports='<ux-option-gallery\n  option="$ctrl.option"\n  model="$ctrl.model"\n></ux-option-gallery>\n'},7995:function(t){"use strict";t.exports='<ux-option-group\n  option="$ctrl.option"\n  shortcode="$ctrl.shortcode"\n  responsive="$ctrl.$optionsCtrl.responsive"\n  model="$ctrl.$optionsCtrl.model"\n></ux-option-group>\n'},2381:function(t){"use strict";t.exports='<ux-option-image\n  option="$ctrl.option"\n  model="$ctrl.model"\n></ux-option-image>\n'},6499:function(t){"use strict";t.exports='<ux-option-margins\n  option="$ctrl.option"\n  model="$ctrl.model"\n></ux-option-margins>\n'},6147:function(t){"use strict";t.exports='<div class="option-button-group">\n\t<label ng-repeat="(value, data) in $ctrl.option.options"\n\t    ng-class="{ \'active\' : $ctrl.model === value }">\n\t    <input type="radio" value="{{:: value }}" ng-model="$ctrl.model"/>\n\t    <button type="button blank" title="{{:: data.title }}">\n\t    \t<span ng-if="data.icon" class="dashicons {{:: data.icon }}"></span>\n\t    \t<span ng-if="!data.icon"> {{:: data.title }}</span>\n\t    </button>\n\t</label>\n</div>\n'},6714:function(t){"use strict";t.exports='<label ng-repeat="(key, value) in $ctrl.option.options"\n    ng-class="{ \'active\' : $ctrl.model === key }">\n    <input type="radio" value="{{:: key }}" ng-model="$ctrl.model">\n    <img src="{{:: value.image }}" alt="{{:: value.title }}" title="{{:: value.title }}">\n</label>\n'},3631:function(t){"use strict";t.exports='<input type="text"\n  class="scrubfield"\n\tscrubfield="$ctrl.option"\n\tng-model="$ctrl.model"\n\tplaceholder="{{:: $ctrl.option.default }}"\n\tng-model-options="{\n        \'updateOn\': \'blur default\'\n    }">\n<div ng-if="$ctrl.option.helpers" class="option-helpers">\n\t<a \tng-repeat="(key, value) in $ctrl.option.helpers" href="javascript:"\n\t\tng-click="$ctrl.model = value.value">\n\t\t{{:: value.title }}\n\t</a>\n</div>\n'},7486:function(t){"use strict";t.exports='<ux-option-select\n  option="$ctrl.option"\n  value="$ctrl.model"\n></ux-option-select>\n'},1619:function(t){"use strict";t.exports='<div class="slider-wrap">\n\n  <input type="range"\n    ng-attr-min="{{:: $ctrl.option.min }}"\n    ng-attr-max="{{:: $ctrl.option.max }}"\n    ng-attr-step="{{:: $ctrl.option.step }}"\n    ng-attr-value="{{:: $ctrl.model }}"\n    ng-model="$ctrl.model"\n    ng-model-options="{ updateOn: \'input\' }"\n  >\n\n  <input type="number" to-number\n    ng-attr-min="{{:: $ctrl.option.min }}"\n    ng-attr-max="{{:: $ctrl.option.max }}"\n    ng-model="$ctrl.model">\n\n  <span class="slider-unit">{{:: $ctrl.option.unit }}</span>\n\n</div>\n'},3055:function(t){"use strict";t.exports='<ux-option-editor\n  option="$ctrl.option"\n  model="$ctrl.model"\n></ux-option-editor>\n'},8908:function(t){"use strict";t.exports='  <textarea\n    placeholder="{{::$ctrl.option.placeholder }}"\n    ng-model="$ctrl.model"\n    ng-model-options="{\n        updateOn: \'blur default\',\n        debounce: {\n            blur : 0,\n            default: 10\n        }\n    }"></textarea>\n'},2013:function(t){"use strict";t.exports='  <input type="text"\n    placeholder="{{::$ctrl.option.placeholder }}"\n    ng-model="$ctrl.model"\n    ng-model-options="{\n        updateOn: \'blur default\',\n        debounce: {\n            blur : 0,\n            default: 10\n        }\n    }">\n'},1054:function(t){"use strict";t.exports="{{:: $ctrl.option.heading }}\n"},1517:function(t){"use strict";t.exports='<ux-option-urlfield option="$ctrl.option"></ux-option-urlfield>\n'},7009:function(t){"use strict";t.exports='<app-sidebar-view class="home-view">\n\n  <view-header>\n\n  </view-header>\n\n  <view-body ng-if="$ctrl.store.postContent">\n    <shortcode-hierarchy-list shortcode="$ctrl.store.postContent"></shortcode-hierarchy-list>\n  </view-body>\n\n  <view-footer>\n    <button id="app-draft-button" type="button"\n      class="wp-style button-large button-block"\n      ng-if="$ctrl.store.post.status === \'draft\' || $ctrl.store.post.status === \'auto-draft\'"\n      ng-class="{ \'loading\': $ctrl.store.isSaving === \'draft\' }"\n      ng-disabled="$ctrl.permissions.save === false"\n      ng-click="$ctrl.save(\'draft\')">\n      Save Draft\n    </button>\n    <button id="app-private-button" type="button"\n      class="wp-style button-large button-block"\n      ng-if="$ctrl.store.post.status === \'private\'"\n      ng-class="{ \'loading\': $ctrl.store.isSaving === \'private\' }"\n      ng-disabled="$ctrl.permissions.save === false"\n      ng-click="$ctrl.save(\'private\')">\n      Save Private\n    </button>\n    <button id="app-save-button" type="button"\n      class="wp-style alt button-large button-block"\n      ng-class="{ \'loading\': $ctrl.store.isSaving && $ctrl.store.isSaving !== \'draft\' && $ctrl.store.isSaving !== \'private\'}"\n      ng-disabled="$ctrl.permissions.save === false"\n      ng-click="$ctrl.save()">\n      {{ $ctrl.saveButtonText }}\n    </button>\n    <button id="app-save-button" type="button"\n      class="wp-style button-large button-block button-exit animate-fade-in-right"\n      ng-if="$ctrl.store.isSaved"\n      ng-click="$ctrl.app.exit()">\n      &times;\n    </button>\n  </view-footer>\n\n</app-sidebar-view>\n'},9369:function(t){"use strict";t.exports='<app-sidebar-view class="settings-view">\n\n  <view-header>\n    <div class="title-row">\n      <div class="title-row-icon">\n          <button class="button-reset view-header-title" type="button" ng-click="$ctrl.   exit()">\n               <span class="dashicons dashicons-arrow-left-alt2"></span>\n          </button>\n      </div>\n      <div class="title-row-title"> Post settings </div>\n    </div>\n  </view-header>\n\n  <view-body>\n    <ux-options\n      options="$ctrl.post.attributes.options.tree"\n      model="$ctrl.post.attributes.values">\n    </ux-options>\n    <ux-options\n      options="$ctrl.post.meta.options.tree"\n      model="$ctrl.post.meta.values">\n    </ux-options>\n    <div class="box">\n      <h3 class="box-title">Actions</h3>\n      <div class="box-content">\n        <button class="wp-style button-block" ng-click="$ctrl.saveAsTemplate()">Save as template&hellip;</button>\n        <div style="padding: 5px 0;" />\n        <button class="wp-style danger button-block" ng-click="$ctrl.clearContent()">Clear content&hellip;</button>\n      </div>\n    </div>\n  </view-body>\n\n  <view-footer>\n    <button type="button" class="wp-style blank" ng-click="$ctrl.discard()">\n      <span class="dashicons dashicons-no-alt"></span>Discard\n    </button>\n    <button type="button" class="wp-style" ng-click="$ctrl.exit()">\n      <span class="dashicons dashicons-yes"></span>Apply\n    </button>\n  </view-footer>\n\n</app-sidebar-view>\n'},705:function(t){"use strict";t.exports='<app-sidebar-view class="shortcode-view">\n\n  <view-header>\n    <div class="title-row">\n      <div class="title-row-icon">\n         <button class="button-reset view-header-title" type="button" ng-click="$ctrl.exit(\'/\')">\n             <span class="dashicons dashicons-arrow-left-alt2"></span>\n         </button>\n      </div>\n      <div class="title-row-title"> {{ $ctrl.shortcode.data.name }} </div>\n      <div class="title-row-actions"></div>\n    </div>\n  </view-header>\n\n  <view-body>\n    <ux-options\n      ng-repeat="_shortcode in $ctrl.shortcodes track by _shortcode.$id"\n      options="$ctrl.options"\n      shortcode="$ctrl.shortcode"\n      responsive="$ctrl.responsiveValues"\n      model="$ctrl.shortcode.options">\n    </ux-options>\n  </view-body>\n\n  <view-footer>\n    <button type="button" class="wp-style outline" ng-click="$ctrl.discard()">\n      Discard\n    </button>\n    <button type="button" class="wp-style" ng-click="$ctrl.exit()">\n      Apply\n    </button>\n  </view-footer>\n\n</app-sidebar-view>\n'},3833:function(t){"use strict";t.exports='<div class="uxb-template-loading">Loading&hellip;</div>\n'}},e={};function g(n){var A=e[n];if(void 0!==A)return A.exports;var o=e[n]={exports:{}};return t[n](o,o.exports,g),o.exports}g.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return g.d(e,{a:e}),e},g.d=function(t,e){for(var n in e)g.o(e,n)&&!g.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})},g.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),g.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},function(){"use strict";function t(t,e,g,n,A){t.digestTtl(20),g.debugEnabled("dev"===window.location.hostname.split(".").pop()),A.debugInfoEnabled(!1),e.defaults.headers.common["X-Requested-With"]="XMLHttpRequest",n.decorator("$rootScope",["$delegate",function(t){return Object.defineProperty(t.constructor.prototype,"_",{value:_,enumerable:!1}),t}])}g(5889),g(269),g(8484),t.$inject=["$rootScopeProvider","$httpProvider","$logProvider","$provide","$compileProvider"],n.$inject=["$rootScope","$window","AppEvent"];var e=null;function n(t,e,g){var n=!0;t.$watch((()=>{n&&e.postCustomMessage(g.APPLY)})),e.addEventListener(g.APPLY,(()=>{n=!1,null===t.$$phase&&t.$apply(),n=!0}),!1),e.addEventListener(g.BROADCAST,(e=>{t.$broadcast(e.data.type,e.data.data)}),!1),e.addEventListener(g.EMIT,(e=>{t.$emit(e.data.type,e.data.data)}),!1)}function A(t,e,g,n){t.on("keydown",(t=>{const A=t.metaKey||t.ctrlKey,o=90===t.keyCode,C=27===t.keyCode,I=t.shiftKey;C&&g.stack?(t.preventDefault(),g.stack.close(),e.apply()):A&&o&&(I?n.redo():n.undo(),e.apply(),t.preventDefault())}))}g.g.postCustomMessage=function(t,g,n){e=e||(self===top?document.querySelectorAll(".iframe-frame")[0].contentWindow:window.parent),(n=new CustomEvent(t)).data=g,e.dispatchEvent(n)},A.$inject=["$document","app","store","history"],I.$inject=["$parse"];const o=(window.parent||window).uxBuilderData,C=50;function I(t){return o.$set=function(e,g){return t(s(e)).assign(o,g)},o.$get=function(e,g){var n=t(s(e))(o);return!angular.isDefined(n)&&g?o.$set(e,g):n},o.$unset=function(e){var g;return e.indexOf("*")?(g=e.split("*")[0],t(s(g)).assign(o,null)):t(s(e)).assign(o,null)},o.$disable=function(){o.enabled=!1},o.$enable=function(){o.enabled=!0},o.$addAction=function(t,e){if(!o.enabled)return;o.history.splice(0,o.history.length-(C-1)),o.currentAction<o.history.length-1&&o.history.splice(o.currentAction+1,o.history.length);const g=o.history.slice().pop();g&&g.payload.key===e.key?!1!==e.override&&(g.payload=e):o.currentAction=o.history.push({type:t,payload:e})-1},o.$resetToAction=function(t){o.history.splice(t+1,o.history.length),o.currentAction=Math.min(t,o.currentAction)},o}function s(t){var e=[],g=t.split(".");return _.each(g,(function(t){e.push(jQuery.camelCase(t.replace(/:/g,"-")))})),e.join(".")}new class{controller(t,e){o.shortcodes.hasOwnProperty(t)&&(o.shortcodes[t].controller=e)}on(t,e){o.$$events[t]=o.$$events[t]||[],o.$$events[t].push(e)}addfilter(t,e){o.$$filters[t]=o.$$filters[t]||[],o.$$filters[t].push(e)}addAction(t){o.actions.push(t)}};const i=angular.module("uxBuilder",[]);var r=i;function a(t){return{restrict:"A",controllerAs:"draggableShortcode",bindToController:{shortcode:"=draggableShortcode",options:"=draggableOptions",element:"=draggableElement"},controller:["$scope","$element","targets",function(e,g,n){var A=this,o=null;e.$watch("draggableShortcode.shortcode",(function(C){o&&o.destroy(),o=t(A.shortcode,A.element||g.get(0),A.options),A.options&&A.options.targets&&A.options.targets.map((function(t){angular.isObject(t)?(t.shortcode=A.shortcode,t.element=g,n.add(t.name,t)):n.add(t,{shortcode:A.shortcode,element:g})})),e.$on("$destroy",(function(){o.destroy(),n.removeElement(g.get(0))}))}))}]}}function l(t){return{restrict:"A",controllerAs:"attachment",bindToController:!0,scope:{id:"=wpAttachment",size:"=wpAttachmentSize",model:"=wpAttachmentModel",width:"@wpAttachmentWidth",height:"@wpAttachmentHeight"},controller:["app","store","$scope","$element",function(t,e,g,n){var A=!1;function o(){var t=g.attachment.id,n=g.attachment.size||"full",A=g.attachment.width||0,o=g.attachment.height||0,I=`cache.attachment.image${A||o?`w${A}.h${o}`:n}.id${t}`,s=e.$get(I);if(angular.isDefined(s))return C(s);jQuery.getJSON(e.ajaxUrl,{action:"ux_builder_get_attachment",attachment_id:t,attachment_size:n,attachment_width:A,attachment_height:o}).done((function(t){t.success?C(e.$set(I,t.data)):window.self===window.top&&g.attachment.model&&(s=["data:image/png;base64,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","480px","270px"]),s&&C(s)})).fail((function(e){console.error("Failed to load attachment",t)}))}function C(t){"IMG"===n[0].tagName?(n.addClass("processing"),n.on("load",(function(){n.off("load").removeClass("processing")})),n.attr("src",t[0]),n.attr("width",t[1]),n.attr("height",t[2])):n.css("background-image","url("+t[0]+")"),A=!0}n.on("load.wpAttachment",(()=>t("tools").fixPositions())),g.$watch((()=>this.id),(function(t){return t&&""!==t?"string"==typeof t&&t.indexOf("/")>-1?C([t]):void o():("IMG"===n[0].tagName?(n.removeAttr("src"),n.removeAttr("width"),n.removeAttr("height")):n.css("background-image",""),void(A=!1))})),g.$watch((()=>this.size),(function(t,e){A&&t!==e&&o()})),g.$on("$destroy",(function(){n.off("load.wpAttachment")}))}]}}function c(t){return e=>t.trustAsHtml(e)}function d(t){return(e,g="html")=>t.trustAs(g,e)}i.config(t),i.run(n),i.run(A),i.factory("presetCache",["$cacheFactory",t=>t()]),r.constant("Event",{READY:"ready",CHANGE:"change",COMPLETE:"complete",ERROR:"error",SCROLL:"scroll",RESIZE:"resize"}),r.constant("AppEvent",{READY:"app-ready",APPLY:"app-apply",EMIT:"app-emit",BROADCAST:"app-broadcast"}),r.constant("IframeEvent",{READY:"iframe-ready",RELOAD:"iframe-reload",RESIZE:"iframe-resize",CHANGED:"iframe-changed",SCROLL:"iframe-scroll"}),r.constant("MouseEvent",{}),r.constant("TouchEvent",{}),r.constant("ShortcodeEvent",{CREATE:"shortcode-create",CREATED:"shortcode-created",ATTACHED:"shortcode-attached",CONFIGURE:"shortcode-configure",RECOMPILED:"shortcode-recompiled",ACTIVE:"shortcode-active",INACTIVE:"shortcode-inactive",CHANGED:"shortcode-changed",ADDED:"shortcode-added",MOVED:"shortcode-moved",DETACHED:"shortcode-detached",REMOVED:"shortcode-removed",MOUSEOVER:"shortocde-mouseover",MOUSEOUT:"shortocde-mouseout",CLICK:"shortcode-click",OUTLINED:"shortcode-outlined",SELECTED:"shortcode-selected",DUPLICATED:"shortcode-duplicated",DELETED:"shortcode-deleted"}),r.constant("ChildEvent",{ADDED:"child-added",REMOVED:"child-removed"}),r.constant("OptionsEvent",{SHOW:"options-show",CLEAR:"options-clear",HIDE:"options-hide"}),r.constant("MediaEvent",{CHANGED:"media-changed"}),r.constant("DragEvent",{PAN_START:"draggable-pan-start",PAN_MOVE:"draggable-pan-move",PAN_END:"draggable-pan-end",START:"draggable-start",MOVE:"draggable-move",END:"draggable-end"}),a.$inject=["draggable"],l.$inject=["app"],r.directive("draggableShortcode",a),r.directive("toNumber",(function(){return{require:"ngModel",link:function(t,e,g,n){n.$formatters.push((t=>{const e=parseFloat(t);return isNaN(e)?null:e}))}}})),r.directive("wpAttachment",l),c.$inject=["$sce"],d.$inject=["$sce"];var p=window.wp.autop;angular.module("app.filters",[]).filter("noDefault",(function(){return(t,e)=>t!==e.default&&t})).filter("heightCheck",(function(){return t=>"100%"===t?"100vh":t})).filter("html",c).filter("rgba",(function(){return function(t){let e=t;if(e.indexOf("#")>-1){let t=/^#?([a-f\d])([a-f\d])([a-f\d])$/i;e=e.replace(t,((t,e,g,n)=>e+e+g+g+n+n)),e=e.replace("#","");let g=parseInt(e.substring(0,2),16),n=parseInt(e.substring(2,4),16),A=parseInt(e.substring(4,6),16);e=`rgba(${g},${n},${A},0.3)`}return e}})).filter("trusted",d).filter("autop",(function(){return t=>(t=t.replace(/<p>\s*<\/p>/g,"<p>&nbsp;</p>"),(t=(0,p.autop)(t)).replace(/<p>&nbsp;<\/p>/g,"<p> </p>"))})),g(6488),g(2496),g(8774);function u(t=document){let e=t.documentElement;return{top:(t.defaultView.pageYOffset||e.scrollTop)-(e.clientTop||0),left:(t.defaultView.pageXOffset||e.scrollLeft)-(e.clientLeft||0)}}function h(t,e=document){e.documentElement.scrollTop=t,e.body.parentNode.scrollTop=t,e.body.scrollTop=t}g.g.jQuery.fn.scrollToElement=function(t,e=450,n=0,A=null){const o=g.g.jQuery(t),C=o.get(0).ownerDocument;let I=C.defaultView.innerHeight,s=C.body.getBoundingClientRect(),i=o.get(0).getBoundingClientRect(),r=i.top-s.top+i.height/2+n;i.height<I?r-=I/2:i.height>I&&(r=i.top-s.top),function(t,e=500,g=null,n=0,A=document){let o=u(A).top,C=o+(t-o)*n,I=t-C,s=0,i=0,r=function(){var t;s+=20,i=s/e,h(C+I*((t=i)<.5?4*t*t*t:(t-1)*(2*t-2)*(2*t-2)+1),A),s<e?window.requestAnimationFrame(r):g&&"function"==typeof g&&g()};r()}(r,e,A,n,C)},g(628);class m{constructor(t){return t.frameElement?t.parent.angular:angular}}m.$inject=["$window"];class f{constructor(t){this.store=t,this.store.states=this.store.states||{}}outlineShortcode(t){this.store.states.outlinedShortcode=t}selectShortcode(t){this.store.states.selectedShortcode=t}configureShortcode(t){this.store.states.configuringShortcode=t}}f.$inject=["store"];class v{constructor(t,e,g,n,A,o,C,I){const s=angular.copy(t.permissions);function i(e){return t.components[e]}return i.goto=function(e){t.currentPath=e},i.exit=function(){if(!s.exit)return;let e="publish"===t.post.status?t.backUrl:t.editUrl;(t.isSaved||confirm("Exit? All changes since last save will be lost."))&&((window.parent||window).location.href=e)},i.register=function(e,g){t.components=t.components||{},t.components[e]=g},i.remove=function(e){delete t.components[e]},i.apply=function(t){e.$apply(t)},i.broadcast=function(t,n){e.$broadcast(t,n),g.postCustomMessage(o.BROADCAST,{type:t,data:n})},i.emit=function(t,n){e.$emit(t,n),g.postCustomMessage(o.EMIT,{type:t,data:n})},i.trigger=function(e,...g){if(this.broadcast(e,...g),t.$$events.hasOwnProperty(e))for(var n=0;n<t.$$events[e].length;n++)t.$$events[e][n].call(this,...g)},i.filter=function(e,g,...n){if(t.$$filters.hasOwnProperty(e))for(var A=0;A<t.$$filters[e].length;A++)g=t.$$filters[e][A].call(this,g,...n);return g},i.states=t.states,i.resetAll=function(){t.$$events={},t.$$filters={},this.resetState()},i.resetState=function(){for(let e in t.states)t.states[e]=null},i.freeze=function(e){t.states.freezed=e},i.outlineShortcode=function(e=null){e&&this.broadcast(C.OUTLINED,e),t.states.outlinedShortcode=!0!==t.states.freezed?e:null},i.selectShortcode=function(e=null){e&&this.broadcast(C.SELECTED,e),t.states.selectedShortcode=!0!==t.states.freezed?e:null},i.configureShortcode=function(t=null){t?(this.selectShortcode(t),this.broadcast(C.CONFIGURE,t),this.goto(`/shortcode/${t.$id}`)):this.goto("/")},i.setBreakpoint=function(e){t.breakpoints.current=e,i.broadcast(I.CHANGED,e)},i}}v.$inject=["store","$rootScope","$window","$timeout","$log","AppEvent","ShortcodeEvent","MediaEvent"];class ${constructor(t,e){this.app=t,this.utils=e}attach(t){t.addEventListener&&t.addEventListener("contextmenu",this.onRightClick.bind(this),!1)}onRightClick(t){let e=this.utils.getGlobalCoordinates(t.view,t.clientX,t.clientY),g=this.utils.shortcodeFromPoint(e.x,e.y);g.isRoot||(this.menu.open(g,t.target,t.clientX,t.clientY),this.app.apply(),t?t.preventDefault():window.event.returnValue=!1)}get menu(){return this.app("contextMenu")}}$.$inject=["app","utils"];class b{constructor(t,e,n,A,o,C,I,s,i,r){var a={},l=(window.parent||window).angular.element("draggable-helper");return function(c,d,p){var u=this;p=angular.extend({cssProps:{},droppable:!0,broadcast:!0,start:angular.noop,move:angular.noop,end:angular.noop},p);var h=d||c.$element.get(0),m=new g.g.HammerJS(h,p);return m.get("pan").set({direction:g.g.HammerJS.DIRECTION_ALL,threshold:1}),angular.element(h).data("shortcode",c),angular.element(h).addClass("uxb-draggable"),m.on("hammer.input",(function(t){t.srcEvent.stopPropagation(),t.srcEvent.stopImmediatePropagation()})),m.on("panstart",f),m.on("pan",(function(e){if(a.shortcode||f(e),$(e),a.defaultPrevented||(l.addClass("active"),l.css({transform:`translate3d(${a.global.x}px, ${a.global.y}px, 0px)`}),l.find("h3 span").css({transform:`rotate(${-25*a.originalEvent.velocityY}deg)`})),a.target=null,a.addToShortcode=null,c.$recompile)return m.stop(!0),v(e);if(n.find(a.global.x,a.global.y).map((function(t){!a.target&&t.target&&t.target.allows(c)&&(a.target=t)})),a.target){let t=a.target.target===c.parent,g=t&&a.target.index===c.index,n=t&&a.target.index===c.index+1,A=e.srcEvent.altKey;!g&&!n||A||(a.target=null)}p.move(e),t("tools").showAddableSpot(a.target,e.srcEvent.altKey),t.outlineShortcode(a.target?a.target.target.parent:null),t.broadcast(r.PAN_MOVE,a),p.broadcast&&t.broadcast(r.MOVE,a),t("tools").apply(),c.$scope.$digest()})),m.on("panend",v),m;function f(g){n.updateOffsets(!0,0),l.find("h3 span").text(c.data.name),o.onkeydown=e=>t("tools").toggleAddableButton(e.altKey),o.onkeyup=e=>t("tools").toggleAddableButton(e.altKey),a.shortcode=c,a.iframeRect=s().get(0).getBoundingClientRect(),a.container=c.parent.$element,a.isSelected=t.states.selectedShortcode===c,a.defaultPrevented=!1,a.showHelper=!0,a.constrains=!1,a.initial={},$(g),a.initial={},a.initial.innerX=a.innerX,a.initial.innerY=a.innerY,a.initial.elementX=a.elementX,a.initial.elementY=a.elementY,c.$element.addClass("uxb-shortcode-dragging"),angular.element(C.parent.document.body).addClass("dragging"),angular.element("body").addClass("dragging"),p.start(g),c.states.dragging=!0,e.isDragging=!0,t.broadcast(r.PAN_START,a),p.broadcast&&t.broadcast(r.START,a),t("tools").apply(),c.$scope.$digest()}function v(g){if($(g),l.find("h3 span").removeAttr("style"),l.removeClass("active"),c.states.dragging=!1,a.target&&!a.defaultPrevented){let t=g.srcEvent.altKey,e=a.target.target,n=a.target.index;a.addedShortode=t?A.duplicate(c,n,!1,e):A.move(c,e,n)}c.$element.removeClass("uxb-shortcode-dragging"),angular.element(C.parent.document.body).removeClass("dragging"),angular.element("body").removeClass("dragging"),p.end(g),t.broadcast(r.PAN_END,a),p.broadcast&&t.broadcast(r.END,a),o.onkeydown=null,o.onkeyup=null,a={},I((()=>e.isDragging=!1),0)}function $(t){a.originalEvent=t,a.global=i.getGlobalCoordinates(t.target.ownerDocument.defaultView,t.center.x,t.center.y),a.element=angular.element(h),a.draggable=u,a.main={},a.main.x=a.global.x,a.main.y=a.global.y,a.iframe={},a.iframe.x=a.global.x-a.iframeRect.left,a.iframe.y=a.global.y-a.iframeRect.top,a.virtual={},a.virtual.width=a.shortcode.$element.width(),a.virtual.height=a.shortcode.$element.height(),a.virtual.top=a.iframe.y-a.initial.elementY,a.virtual.right=a.iframe.x+a.virtual.width-a.initial.elementX,a.virtual.bottom=a.iframe.y+a.virtual.height-a.initial.elementY,a.virtual.left=a.iframe.x-a.initial.elementX,a.constrains&&(a.constrains=a.container.outerOffset(),a.innerX=a.iframe.x-a.constrains.left-a.initial.elementX,a.innerY=a.iframe.y-a.constrains.top-a.initial.elementY,a.virtual.top<a.constrains.top&&(a.innerY=0),a.virtual.right>a.constrains.right&&(a.innerX=a.constrains.width-a.virtual.width),a.virtual.bottom>a.constrains.bottom&&(a.innerY=a.constrains.height-a.virtual.height),a.virtual.left<a.constrains.left&&(a.innerX=0)),a.elementX=a.iframe.x-c.$element.offset().left,a.elementY=a.iframe.y-c.$element.offset().top+s().contents().scrollTop(),a.preventDefault=function(){a.defaultPrevented=!0},a.setContainment=function(t){a.constrains=t.outerOffset(),a.container=t}}}}}b.$inject=["app","store","targets","Shortcode","$document","$window","$timeout","$iframe","utils","DragEvent"];class y{constructor(t,e){return{injectStyles:function(t){return g("styles",t,(function(t,e,g){return(g=document.createElement("link")).id="ux-builder-style-"+e,g.rel="stylesheet",g.type="text/css",g.media="all",g.href=t,g}))},injectScripts:function(t){return g("scripts",t,(function(t,e,g){return(g=document.createElement("script")).id="ux-builder-script-"+e,g.type="text/javascript",g.src=t,g}))}};function g(g,n,A){var o=e.defer(),C=0,I=0;return _.each(n,(function(e,A){!0===t.$get(g+"."+A+".loaded")?delete n[A]:C++})),0===_.size(n)?(o.resolve(),o.promise):(_.each(n,(function(e,n){var s=t.$get(g+"."+n)||A(e,n);if(angular.isDefined(s.loaded)&&!0!==s.loaded)return s.addEventListener("load",i);function i(){s.loaded=!0,++I===C&&o.resolve()}s.loaded=!1,s.addEventListener("load",i),t.$set(g+"."+n,s),document.getElementsByTagName("head")[0].appendChild(s)})),o.promise)}}}y.$inject=["store","$q"];class w{constructor(t){this.enabled=!0,this.store=t,this.store.editor=this.store.editor||{state:{}}}get state(){return this.store.editor.state}disable(){this.enabled=!1,this.state={}}enable(){this.enabled=!0}update(){}outline(t){this.state.outlined=this.enabled?t:null}select(t){this.state.selected=this.enabled?t:null}configure(t){this.state.configuring=this.enabled?t:null}target(t){this.state.target=this.enabled?t:null}}w.$inject=["store"];class x{constructor(){return function(t,e,n){return n?g.g.propagatingHammer(new g.g.HammerJS(t,e)):new g.g.HammerJS(t,e)}}}x.$inject=[];class E{constructor(t,e,g,n,A){this.app=t,this.store=e,this.manager=g,this.ShortcodeEvent=n,this.$timeout=A}undo(){const t=this.store.history[this.store.currentAction];return this.doAction(t,-1)}redo(){const t=this.store.history[this.store.currentAction+1];return this.doAction(t,0)}doAction(t,e){if(!t)return;const{type:g,payload:n}=t,{shortcode:A}=this.store;switch(this.store.$disable(),this.store.currentAction=this.store.history.indexOf(t)+e,g){case"reorderChildren":{const{id:t,parentId:e,toIndex:g,fromIndex:o}=n,C=o>g?o+1:o;this.manager.move(A[t],A[e],C,!1),n.fromIndex=g,n.toIndex=o;break}case"moveChild":{const{id:t,parentId:e,index:g,fromParentId:o,fromIndex:C}=n;this.manager.move(A[t],A[o],C,!1),n.fromParentId=e,n.fromIndex=g,n.parentId=o,n.index=C;break}case"updateOption":{const{name:t,optionValue:e,responsiveValue:g}=n,o=A[n.id];n.optionValue=angular.copy(o.optionValues[t]),n.responsiveValue=angular.copy(o.responsiveValues[t]),o.optionValues[t]=e,g&&(o.responsiveValues[t]=g);break}case"updateMultipleOptions":{const{mutations:t}=n;for(const e in t){const g=A[e];for(const n in t[e]){const A=t[e][n],{optionValue:o,responsiveValue:C}=A;A.optionValue=angular.copy(g.optionValues[n]),A.responsiveValue=angular.copy(g.responsiveValues[n]),g.optionValues[n]=o,C&&(g.responsiveValues[n]=C)}}break}case"clearResponsiveValue":{const{id:t,optionName:e,breakpointIndex:g,value:o}=n,C=A[t].responsiveValues[e];C[g]?C[g]=null:C[g]=o;break}case"updateContent":{const{id:t,content:e}=n,g=A[t];n.content=g.content,g.content=e;break}case"addChild":case"removeChild":if(A[n.id])this.manager.remove(A[n.id],!1);else{const t=A[n.parentId].addChild(n.data,n.index,!1);this.$timeout((()=>{this.app.trigger(this.ShortcodeEvent.ATTACHED,t)}))}break;case"removeContent":Object.keys(A).length>1?this.manager.remove(this.store.postContent,!1):(n.content.forEach(((t,e)=>{this.store.postContent.addChild(t,e,!1)})),this.$timeout((()=>{this.app.trigger(this.ShortcodeEvent.ATTACHED,this.store.postContent)})))}return this.store.$enable(),t}}E.$inject=["app","store","Shortcode","ShortcodeEvent","$timeout"];var D=g(1760),S=g.n(D);class N{constructor(t,e,g,n,A,o){this.app=t,this.store=e,this.targets=g,this.manager=n,this.$timeout=A,this.metaOptions=e.post.meta.options.flat,this.postMeta=e.post.meta.values,this.IframeEvent=o}reload(t){if(this.store.isReloading)return;this.store.loading=!0;let e=S()(this.store.iframeUrl);for(let t in this.metaOptions){let g=this.metaOptions[t];e.query+=`&${g.$orgName}=${this.postMeta[g.$name]}`}this.store.post.content=t||this.store.postContent.copy(((t,e)=>{t.$id=e.$id})),this.targets.remove(this.store.postContent),this.manager.remove(this.store.postContent,!1),this.app.resetAll(),this.store.isReloading=!!this.$timeout((()=>{this.store.iframeUrl=e.toString(),delete this.store.isReloading}),0)}}N.$inject=["app","store","targets","Shortcode","$timeout","IframeEvent"];class k{constructor(t,e,g){return{defaultBreakpoint:n,currentBreakpoint:A,getMediaValue:function(t,e){return(e=e||A())>n()?o(t,e):I(t,e)},getMediaIndex:function(t,e){return(e=e||A())>n()?C(t,e):s(t,e)},getLowerMediaValue:o,getLowerMediaIndex:C,getHigherMediaValue:I,getHigherMediaIndex:s,hasValueBetween:function(t,e,g){for(var n=e+1;n<g;n++)if(t[n])return!0;return!1}};function n(){return e.breakpoints.default}function A(){return e.breakpoints.current}function o(t,e){for(var g=e||A();g>=0;g--)if(t[g])return t[g];return null}function C(t,e){for(var g=e||A();g>=0;g--)if(t[g])return g;return 0}function I(t,e){for(var g=e||A();g<t.length;g++)if(t[g])return t[g];return null}function s(t,e){for(var g=e||A();g<t.length;g++)if(t[g])return g;return 0}}}k.$inject=["app","store","utils"];let O,T={};class M{constructor(t,e,g){angular.merge(this,t),this.responsiveValues={},this.optionValues={},this.$isReady=!1;const n=this;this.$id=g||t.$id||function(t,e){let g=Math.floor(65536*(1+Math.random())).toString(16).substring(1);return e?`${t.tag}-${g}`:"root"}(t,e),this.$parentId=e?e.$id:t.$parentId||null,this.$textContent=t.content||"",this.data=angular.copy(o.shortcodes[this.tag]),e&&angular.extend(this.data,e.data.children),this.states={active:!1,dragging:!1,open:void 0},this.options={get $responsive(){return n.responsiveValues},set $responsive(t){n.responsiveValues=t}};for(let e in t.options.$responsive){this.responsiveValues[e]=[];for(let g=0;g<t.options.$responsive[e].length;g++)this.responsiveValues[e][g]=R(t.options.$responsive[e][g],this.data.options.named[e])}for(let e in t.options)"$"!==e.charAt(0)&&(Object.defineProperty(this.options,e,{enumerable:!0,get:()=>this.data.options.named[e].responsive?G(this.responsiveValues[e]):this.optionValues[e],set:t=>{let g=this.data.options.named[e],n=R(t,g),A=o.breakpoints.current;this.$isReady&&o.enabled&&(clearTimeout(O),T[this.$id]||(T[this.$id]={}),T[this.$id][e]||(T[this.$id][e]={optionValue:angular.copy(this.optionValues[e]),responsiveValue:angular.copy(this.responsiveValues[e])}),O=setTimeout((()=>{const t=Object.keys(T);1===t.length&&1===Object.keys(T[t[0]]).length?o.$addAction("updateOption",{id:this.$id,name:e,override:!1,optionValue:T[t[0]][e].optionValue,responsiveValue:T[t[0]][e].responsiveValue,key:`updateOption-${t[0]}-${e}-${A}`}):t.length&&o.$addAction("updateMultipleOptions",{mutations:T,override:!1,key:`updateMultipleOptions-${t.join("-")}-${A}`}),T={},this.apply()}),250)),this.optionValues[e]=n,g.responsive&&n!==G(this.responsiveValues[e])&&(this.responsiveValues[e][A]=n)}}),this.options[e]=t.options[e]);this.data.options.flat.forEach((t=>{null===this.options[t.$name]&&(this.options[t.$name]=t.default),t.$isValidFor(e)||(this.options[t.$name]=null,t.responsive&&(this.options.$responsive[t.$name]=[null,null,null]))})),o.shortcode[this.$id]=this,t.hasOwnProperty("children")&&(this.children=t.children.map((t=>new M(t,this)))),this.$isReady=!0}get content(){return this.$textContent||""}set content(t){t!==this.content&&this.$isReady&&o.enabled&&o.$addAction("updateContent",{id:this.$id,override:!1,content:this.content,key:`updateContent-${this.$id}`}),this.$textContent=t}apply(){this.$scope&&!this.$scope.$$phase&&this.$scope.$apply()}addChild(t,e,g=!0){let n;if(t instanceof M)if(t.$parentId!==this.$id){const A=t.$parentId,C=t.index;n=new M(L(t.detatch(),(function(t,e){t.$id=e.$id})),this,t.$id),g&&o.$addAction("moveChild",{key:`moveChild-${n.$id}-${A}-${C}`,id:n.$id,parentId:this.$id,index:e,fromIndex:C,fromParentId:A})}else{const A=t.index;e-=e>t.index?1:0,n=t.detatch(),g&&o.$addAction("reorderChildren",{key:`reorderChildren-${t.$id}-${A}-${e}`,parentId:this.$id,id:t.$id,toIndex:e,fromIndex:A})}else n=new M(Y(t),this),g&&o.$addAction("addChild",{key:`addChild-${this.$id}-${n.$id}`,id:n.$id,parentId:this.$id,index:e,data:n.copy(((t,e)=>{t.$id=e.$id}))});if(!this.allows(n))throw Error(`${n.data.name} is not allowed in ${this.data.name}`);const A=e>=0?e:this.children.length;return this.children.splice(A,0,n),n}childAt(t){return this.isParent?this.children[t]:null}removeChild(t){return this.isParent?this.children[t].remove():null}replaceChild(t,e){return this.isParent?(this.children[e]=t instanceof M?t:new M(t,this),this.children[e]):null}replaceWith(t){return this.isRoot?null:this.parent.replaceChild(t,this.index)}is(t){return this.data.tag===t}isChildOf(t){return this.parent===t}isDescendantOf(t){return t.descendants.indexOf(this)>-1}isSelfOrDescendantOf(t){return t.descendantsAndSelf.indexOf(this)>-1}isAncestorOf(t){return this.descendants.indexOf(t)>-1}isSelfOrAncestorOf(t){return this.descendantsAndSelf.indexOf(t)>-1}copy(t=null){let e={};for(let t in this)_.isFunction(this[t])||"$"!==t.charAt(0)&&"optionValues"!==t&&"responsiveValues"!==t&&"children"!==t&&"data"!==t&&this.hasOwnProperty(t)&&(e[t]=angular.copy(this[t]));if(this.$textContent&&(e.content=this.$textContent),this.isParent){e.children=[];for(let g=0;g<this.children.length;g++)e.children.push(this.children[g].copy(t))}return t&&t(e,this),e}duplicate(t=null,e=!1){const g=this.copy(((t,g)=>{e&&(t.$id=g.$id)}));return this.parent.addChild(g,t||this.index+1)}detatch(){return this.parent?this.parent.children.splice(this.index,1)[0]:null}remove(t=!0){this.descendants.forEach((t=>{delete o.shortcode[t.$id]})),this.parent&&(delete o.shortcode[this.$id],t&&o.$addAction("removeChild",{id:this.$id,index:this.index,parentId:this.parent.$id,key:`removeChild-${this.$id}`,data:this.copy(((t,e)=>{t.$id=e.$id}))})),this.detatch()}allows(t){return function(t,e){return!e.isSelfOrDescendantOf(t)&&!t.descendants.filter((t=>!1===t.data.nested&&t.tag===e.tag)).length&&e.allowed.hasOwnProperty(t.tag)}(t,this)}get allowed(){return function(t){let e=t.ancestorsAndSelf,g={};if(t.data.allow.length)return t.data.allow.reduce((function(t,e){return o.shortcodes[e]&&(t[e]=o.shortcodes[e]),t}),{});for(let n in o.shortcodes){let A=o.shortcodes[n];A.hidden||A.require.length&&-1===A.require.indexOf(t.tag)||!1===A.nested&&t.tag===A.tag||!1===A.nested&&e.filter((function(t){return t.tag===A.tag})).length||(g[A.tag]=A)}return g}(this)}get presets(){let t=this.parent.allowed,e=angular.copy(this.data.presets);return e.forEach((function(g,n){let A=B(g.content);if(A.unshift(g.content),!t.hasOwnProperty(g.content.tag))return e.splice(n,1);A.forEach((function(t){t.tag===parent.tag&&!0!==o.shortcodes[t.tag].nested&&e.splice(n,1)}))})),e}get isParent(){return!!this.children}get isChild(){return!!this.parent}get isEmpty(){return this.isParent&&0===this.children.length}get isRoot(){return"_root"===this.tag}get parent(){return this.$parentId?o.shortcode[this.$parentId]:null}get index(){return this.parent?this.parent.children.indexOf(this):0}get depth(){return this.ancestors.length}get ancestors(){let t=[];return this.parent&&(t.push(this.parent),t=t.concat(this.parent.ancestors)),t}get ancestorsAndSelf(){let t=this.ancestors;return t.unshift(this),t}get descendants(){let t=[];if(this.isParent)for(let e=0;e<this.children.length;e++)t.push(this.children[e]),t=t.concat(this.children[e].descendants);return t}get descendantsAndSelf(){let t=this.descendants;return t.unshift(this),t}get siblings(){return this.parent?this.parent.children.filter((t=>t!==this)):[]}get nextSibling(){return this.parent&&this.parent.children[this.index+1]?this.parent.children[this.index+1]:null}get previousSibling(){return this.parent&&this.parent.children[this.index-1]?this.parent.children[this.index-1]:null}get siblingsAndSelf(){return this.parent?this.parent.children:[this]}}function R(t,e){return null===t||"string"==typeof t&&""===t?t:Array.isArray(t)&&0===t.length?e.default||"":isNaN(t)?angular.isUndefined(t)?e.default:(Array.isArray(t)&&(t=t.join(e.config?e.config.delimiter:",")),String(t)):"string"!=typeof t||!/^0\d/.test(t)&&"+"!==t.charAt(0)?Number(t):String(t)}function L(t,e){let g={};for(let e in t)"$"!==e.charAt(0)&&"children"!==e&&t.hasOwnProperty(e)&&(g[e]=angular.copy(t[e]));if(t.isParent){g.children=[];for(var n=0;n<t.children.length;n++)g.children.push(L(t.children[n],e))}return t.$textContent&&(g.content=t.$textContent),e&&e(g,t),g}function Y(t){let e=null,g=angular.copy(t);if(angular.isDefined(g.children))for(let t in g.children)e=Y(o.shortcodes[g.children[t].tag].presets[0].content),g.children[t].isparent&&!g.children[t].children.length&&(g.children[t]=angular.merge({},e,g.children[t])),g.children[t].content&&""===g.children[t].content&&(g.children[t].content=e.content);return g}function B(t){let e=[];return t.children&&t.children.forEach((function(t){e.push(t),e.concat(B(t))})),e}function G(t,e=o.breakpoints.current){return e>o.breakpoints.default?function(t,e=o.breakpoints.current){for(let g=e;g>=0;g--)if(null!==t[g])return t[g];return null}(t,e):function(t,e=o.breakpoints.current){for(let g=e;g<t.length;g++)if(null!==t[g])return t[g];return null}(t,e)}class P{constructor(t,e,g,n,A){this.app=t,this.store=e,this.$timeout=g,this.shortcodeTemplateCache=n,this.ShortcodeEvent=A}instantiate(t,e,g){return new M(t,e,g)}create(t,e,g){let n=this.instantiate(t,e,g);return n.$$new=!0,n}move(t,e,g,n=!0){let A=e.addChild(t,g,n);return A.$$moved=!0,t.$parentId!==A.$parentId&&(this.app.trigger(this.ShortcodeEvent.DETACHED,t),A.$$new=!!A.data.template||!!this.shortcodeTemplateCache.get(A.$id)),this.app.trigger(this.ShortcodeEvent.MOVED,A),A}duplicate(t,e=t.index+1,g=!1,n=t.parent){const A=t.copy(((t,e)=>{g&&(t.$id=e.$id)})),o=n.addChild(A,e);return o.$$new=!0,o.data.template||this.shortcodeTemplateCache.put(o.$id,this.shortcodeTemplateCache.get(t.$id)),o}remove(t,e=!0){if(this.app.trigger(this.ShortcodeEvent.DETACHED,t),this.app.states.selectedShortcode&&this.app.states.selectedShortcode.isSelfOrDescendantOf(t)&&(this.app.outlineShortcode(null),this.app.selectShortcode(null),this.app.configureShortcode(null)),t.isRoot)for(let g=t.children.length-1;g>=0;g--)t.children[g].remove(e);else t.remove(e)}copy(t){return t.copy(((t,e)=>{let g=this.store.breakpoints.default,n=e.options.$responsive;for(let e in n)t.options[e]=n[e][g];for(let g in t.options)"$responsive"!==g&&null===t.options[g]&&(t.options[g]=e.data.options.get(g).default);Object.keys(e.options).forEach((g=>{const n=e.data.options.named[g];!n||n.$isValidFor(e.parent)&&n.$satisfiesConditions(e.options)||(delete t.options[g],delete t.options.$responsive[g])}))}))}}P.$inject=["app","store","$timeout","shortcodeTemplateCache","ShortcodeEvent"];class V{constructor(t,e,g,n,A){this.store=t,this.$iframe=e,this.$timeout=A,g.addEventListener("resize",(()=>this.updateOffsets()),!1),g.addEventListener("scroll",(()=>this.updateOffsets()),!1),this.updateOffsets(!0,0)}add(t,e){let g=angular.extend({name:t,element:e.shortcode.$element,shortcode:e.shortcode,target:e.target||"center"===t?e.shortcode:e.shortcode.parent,droppable:!0,addable:!0,active:!0},e);g.offsets=g.element.outerOffset(),this.store.targets.push(g)}enable(t){if(angular.isArray(t))return t.forEach((t=>this.enable(t)));for(let e=0;e<this.store.targets.length;e++)this.store.targets[e].target===t&&(this.store.targets[e].active=!0)}disable(t){if(angular.isArray(t))return t.forEach((t=>this.disable(t)));for(let e=0;e<this.store.targets.length;e++)this.store.targets[e].target===t&&(this.store.targets[e].active=!1)}remove(t){let e=t.descendantsAndSelf;this.store.targets=this.store.targets.reduce((function(t,g){return e.indexOf(g.shortcode)<0&&t.push(g),t}),[])}removeElement(t){this.store.targets=this.store.targets.reduce((function(e,g){return g.element.get(0)!==t&&e.push(g),e}),[])}enableElement(t){for(let e=0;e<this.store.targets.length;e++)this.store.targets[e].element.get(0)===t&&(this.store.targets[e].active=!0,this.store.targets[e].shortcode.descendants.forEach((t=>{t.$element&&this.enableElement(t.$element.get(0))})))}disableElement(t){for(let e=0;e<this.store.targets.length;e++)this.store.targets[e].element.get(0)===t&&(this.store.targets[e].active=!1,this.store.targets[e].shortcode.descendants.forEach((t=>{t.$element&&this.disableElement(t.$element.get(0))})))}find(t,e,g=!1,n=50){let A=[],o=!!this.store.isDragging;for(let C=0;C<this.store.targets.length;C++){let I,s,i,r,a,l=this.store.targets[C],c=l.element[0].ownerDocument.defaultView,{parent:d}=l.shortcode,p=t,u=e;if(!l.active)continue;if("center"===l.name&&!l.shortcode.isEmpty)continue;if(o&&!l.droppable)continue;if(!o&&!l.addable)continue;if(!o&&!c.frameElement)continue;if(!c){this.removeElement(l.element.get(0));continue}switch(c.frameElement&&angular.isDefined(this.frameOffsets)&&(p-=this.frameOffsets.left,u-=this.frameOffsets.top),d&&"ux_stack"===d.tag&&"col"===d.options.direction&&(l={...l,name:"left"===l.name?"top":"bottom"}),l.name){case"top":a=l.shortcode.index,i=l.offsets.left+l.offsets.width/2,r=l.offsets.top,I=g?i:p,s=r;break;case"right":a=l.shortcode.index+1,i=l.offsets.right,r=l.offsets.top+l.offsets.height/2,I=l.offsets.right,s=g?r:u;break;case"bottom":a=l.shortcode.index+1,i=l.offsets.left+l.offsets.width/2,r=l.offsets.bottom,I=g?i:p,s=r;break;case"left":a=l.shortcode.index,i=l.offsets.left,r=l.offsets.top+l.offsets.height/2,I=i,s=g?r:u;break;case"center":a=0,i=i=l.offsets.left+l.offsets.width/2,r=r=l.offsets.top+l.offsets.height/2}let h={fromSide:Math.sqrt(Math.pow(Math.abs(I-p),2)+Math.pow(Math.abs(s-u),2)),fromCenter:Math.sqrt(Math.pow(Math.abs(i-p),2)+Math.pow(Math.abs(r-u),2))},m=l.shortcode.isEmpty,f=m?0:n,v=this.isInside(l,p,u,f),$=this.isInside(l,p,u),b=m?v:h.fromSide<f;v&&b&&A.push({target:l.target||("center"!==l.name?l.shortcode.parent:l.shortcode),insideLimit:v,insideElement:$,element:l.element,shortcode:l.shortcode,name:l.name,distance:h,index:a})}return _.chain(A).sortBy((t=>-t.target.ancestors.length)).sortBy((t=>t.distance.fromCenter)).value()}isInside(t,e,g,n=0){var A=n,o=n,C=Math.min(t.offsets.left-A,t.offsets.right+A),I=Math.max(t.offsets.left-A,t.offsets.right+A),s=Math.min(t.offsets.top-o,t.offsets.bottom+o),i=Math.max(t.offsets.top-o,t.offsets.bottom+o);return C<=e&&e<=I&&s<=g&&g<=i}updateOffsets(t=!1,e=250){this.$timeout.cancel(this.store.__updateTargets),this.store.__updateTargets=this.$timeout((()=>{this.frameOffsets=this.$iframe().get(0).getBoundingClientRect();for(let e=0;e<this.store.targets.length;e++){let g=this.store.targets[e];(t||g.active&&g.element[0].ownerDocument.defaultView===window)&&("center"===g.name&&g.shortcode.isEmpty&&g.shortcode.$content&&g.element[0].ownerDocument.defaultView&&g.element[0].ownerDocument.defaultView.frameElement?g.offsets=g.shortcode.$content.outerOffset({includeMargins:!0}):g.element?g.offsets=g.element.outerOffset({includeMargins:!0}):this.store.remove(g.shortcode))}delete this.store.__updateTargets}),e,!1)}}V.$inject=["store","$iframe","$window","$document","$timeout"];class j{constructor(t,e,g,n,A,o,C){this.app=t,this.store=e,this.modal=g,this.presetCache=n,this.Shortcode=A,this.isSaving=!1,this.shortcode=!1,this.store.currentModal=null,this.store.templateName="",this.content=!1,this.$timeout=o,this.$q=C}showModal(t){const{title:e,name:g,...n}=t;this.store.templateName=g||"",this.store.templateError="",this.store.templateData=n,this.store.currentModal=this.modal.show("custom-template-modal",{title:e}),this.store.currentModal.onClose((()=>{this.store.templateName="",this.store.templateError="",this.store.templateData=null,this.store.currentModal=null})),this.$timeout((()=>{this.store.currentModal&&this.store.currentModal.$el.find(".custom-template-modal__input").focus()}),75)}savePreset(t){this.showModal({title:`Save ${t.data.name} as preset`,tag:t.tag,content:this.Shortcode.copy(t)})}updatePreset(t){this.showModal({title:`Edit ${t.name} preset`,...t})}saveTemplate(t){this.showModal({title:"Save as template",tag:"_root",content:this.Shortcode.copy(t),template:this.store.post.meta.values._wp_page_template})}editTemplate(t){this.showModal({title:`Edit ${t.name} template`,...t})}save(){if(this.isSaving)return;const t={...this.store.templateData,post_id:this.store.post.id,title:this.store.templateName,content:angular.toJson(this.store.templateData.content)};this.isSaving=!0,jQuery.post(this.store.ajaxUrl,{action:"ux_builder_save_custom_template",security:this.store.nonce,data:t}).done((({data:t,success:e})=>{e?t&&(this.presetCache.remove(t.tag),this.app.broadcast("template-saved",t),this.store.currentModal&&this.store.currentModal.hide()):this.store.currentModal&&this.store.currentModal.setError(t?t.message:"Failed to save template."),this.isSaving=!1,this.app.apply()})).fail((t=>{this.store.currentModal&&this.store.currentModal.setError(t.statusText),this.isSaving=!1,this.app.apply()}))}remove(t){return this.$q(((e,g)=>{if(confirm(`Do you want to delete ${t.name}?`))return jQuery.post(this.store.ajaxUrl,{action:"ux_builder_delete_custom_template",post_id:this.store.post.id,security:this.store.nonce,id:t.id}).done((({data:t,success:n})=>{n?t&&(this.app.broadcast("template-removed",t),e(!0)):g(new Error(t?t.message:"Failed to delete template.")),this.app.apply()})).fail((t=>{g(new Error(t.statusText))}));e()}))}}j.$inject=["app","store","modal","presetCache","Shortcode","$timeout","$q"];const Q=(window.parent||window).uxBuilderData;class z{constructor(t,e,g,n){this.arrayPrefix=function(t,e){return t.reduce(((t,g)=>(t.push(`${e}${g}`),t)),[])},this.isIframe=function(){return!!g.frameElement},this.getGlobalCoordinates=function(t,e,g){if(t.frameElement){let n=t.frameElement.getBoundingClientRect();e+=n.left,g+=n.top}return{x:e,y:g}},this.camelCase=function(t,e){return t=e?t.charAt(0).toUpperCase()+t.slice(1):t,jQuery.camelCase(t.replace(/\_|\:/g,"-",!0))},this.kebabCase=function(t){return t.replace(/[A-Z\u00C0-\u00D6\u00D8-\u00DE]/g,(function(t){return t.toLowerCase()}))},this.elementFromPoint=function(t,e){var A=g.parent||g,o=A.document,C=n().get(0).contentWindow,I=n().get(0).contentDocument,s=t,i=e,r=n().get(0).getBoundingClientRect(),a=t-r.left,l=e-r.top,c=A.angular.element(o.elementFromPoint(s,i)),d=C.angular.element(I.elementFromPoint(a,l));return d.length?d:c},this.shortcodeFromPoint=function(e,g){return this.elementFromPoint(e,g).shortcode()||t.postContent}}}z.$inject=["store","$document","$window","$iframe"];class Z{constructor(t,e,g){this.app=t,this.store=e,this.$editor=(window.parent||window).angular.element("wp-editor"),this.$iframe=this.$editor.find("iframe").get(0).contentWindow,g.addEventListener("message",(t=>this.onMessage(t)),!1)}onMessage(t){if("uxBuilderWpEditor"===t.data.source)switch(t.data.type){case"change":this.updateContent(t.data.content);break;case"discard":this.discard();break;case"hide":this.close()}}get editor(){return this.$iframe.wp.editor}open(){this.store.$set("stack",this),this.$editor.addClass("is-visible"),this.originalContent=this.app.states.selectedShortcode.content,this.$iframe.postMessage({source:"uxbuilder",type:"setContent",content:this.originalContent},"*")}updateContent(t){this.app.states.selectedShortcode.content=t,this.app.states.selectedShortcode.apply()}discard(){this.updateContent(this.originalContent),this.originalContent="",this.close()}close(){this.store.$set("stack",!1),this.$editor.removeClass("is-visible")}}Z.$inject=["app","store","$window"];class U{constructor(t){this.$media=(window.parent||window).angular.element("wp-media"),this.$iframe=this.$media.find("iframe").get(0).contentWindow,t.addEventListener("message",(t=>this.onMessage(t)),!1)}onMessage(t){if("uxBuilderWpMedia"===t.data.source)switch(t.data.type){case"close":this.close();break;case"select":"function"==typeof this.cb&&this.cb(t.data.attachment)}}get media(){return this.$iframe.wp.media}open(t){this.$media.addClass("is-active")}close(){this.$media.removeClass("is-active")}}U.$inject=["$window"];class H{constructor(t,e){return function(){return t.element((e.parent.document||document).getElementsByTagName("iframe")[0])}}}H.$inject=["$angular","$window"],angular.module("app.services",[]).service("$angular",m).service("actions",f).service("app",v).service("contextmenu",$).service("draggable",b).service("dependencies",y).service("editor",w).service("history",E).service("iframe",N).service("modal",class{show(t,e={}){const g=jQuery(`#${t}-modal`,parent.document),n=g.find(".app-modal-title"),A=g.find(".app-modal-error");return e.title&&n.html(e.title),g.addClass("is-visible"),{$el:g,hide:()=>this.hide(t),setError(t){A.html(t)},clearError(){A.empty()},onClose(t){g.one("modal:close",t)}}}hide(t){const e=jQuery(`#${t}-modal`,parent.document),g=e.find(".app-modal-error");e.trigger("modal:close"),e.removeClass("is-visible"),g.empty(),e.off()}}).service("hammer",x).service("ResponsiveHelper",k).service("Shortcode",P).service("shortcodeTemplateCache",(function(){return Q.templateCache=Q.templateCache||new Map,{put(t,e){Q.templateCache.set(t,e)},get(t){return Q.templateCache.get(t)},remove(t){Q.templateCache.delete(t)},removeAll(){Q.templateCache.clear()},destroy(){Q.templateCache.clear()},info(){return{id:"shortcodeTemplateCache",size:Q.templateCache.size}}}})).service("utils",z).service("store",I).service("targets",V).service("templates",j).service("$iframe",H).service("wpEditor",Z).service("wpMedia",U);class W{constructor(t,e,g,n,A,o,C,I,s,i){this.app=t,this.store=e,this.shortcode=g,this.targets=n,this.$scope=o;let r=!!g.data.draggable&&A(g);if(g.data.addableSpots.map((t=>{n.add(t,{shortcode:g})})),C.find("a, img").attr("draggable","false"),C.on("click",(function(n){n.stopPropagation(),n.preventDefault(),e.isDragging||(t.configureShortcode(g),t.apply())})),this.onOptionsChanged(g.options,null,!0),o.$watchCollection((()=>g.options),((e,n)=>{e!==n&&(this.store.isSaved=!1,this.app.trigger(i.CHANGED,{shortcode:g,options:e,oldOptions:n}),this.onOptionsChanged(e,n)),t.states.selectedShortcode===g&&this.app("tools").fixPositions()})),angular.isDefined(g.content)){let e=!1;o.$watch((()=>g.content),((n,A)=>{n!==A&&(s.cancel(e),e=s((()=>{t.trigger("shortcode-content-change",g)}),0,!1))}))}o.$watch((()=>t.states.selectedShortcode),(t=>{C.toggleClass("uxb-selected",t===g)})),t.states.selectedShortcode&&t.states.selectedShortcode.$id===g.$id&&t.selectShortcode(g),g.$$new&&s((()=>t.trigger(i.ATTACHED,g)),0,!1),g.$$new=!1,g.$$moved=!1,o.$on("$destroy",(function(){n.remove(g),C.off("click"),r&&r.element&&r.destroy()}))}onOptionsChanged(t,e,g){let n=!1;for(let A in t){if("$"===A.charAt(0))continue;let o=this.shortcode.data.options.named[A],C=e?e[A]:"",I=null===t[A]&&C===o.default;if(g||!I&&t[A]!==C)if(o.onChange){let e=o.apply(this.shortcode.$element,t[A],C);n=n||!e}else n=!0}!this.shortcode.$noRecompile&&!this.shortcode.data.template&&n&&e&&e!==t&&this.$scope.$$recompile()}}function K(t){const e=g(2391);angular.forEach(e.keys(),(function(g){const n=e(g);t.put(g.replace("./",""),n.default||n)}))}W.$inject=["app","store","shortcode","targets","draggable","$scope","$element","$iframe","$timeout","ShortcodeEvent"],r.controller("ShortcodeController",W),K.$inject=["$templateCache"],r.run(K);class F{constructor(t,e,g){this.app=t,this.$scope=e,this.$element=g}$onInit(){this.$element.toggleClass("with-label",!!this.label)}addShortcode(){this.app.outlineShortcode(null),this.app.selectShortcode(null),this.app.configureShortcode(null),this.app("stack").open('\n      <add-shortcode\n        shortcode="$ctrl.shortcode"\n        index="$ctrl.index"\n      ></add-shortcode>\n    ',this.$scope)}}function X(t,e,g){angular.isDefined(t.$scope.$customCtrl)&&angular.isFunction(t.$scope.$customCtrl[e])&&t.$scope.$customCtrl[e](g)}function J(t){let e=g.g.HammerJS.DIRECTION_ALL;switch(t){case"top":case"bottom":e=g.g.HammerJS.DIRECTION_VERTICAL;break;case"right":case"left":e=g.g.HammerJS.DIRECTION_HORIZONTAL}return e}function q(t,e){for(let g in t)if(t[g].$element.get(0)===e)return g;return null}F.$inject=["app","$scope","$element"],r.component("addButton",{controller:F,bindings:{shortcode:"<",label:"@",index:"<"},template:'\n    <button type="button" ng-click="$ctrl.addShortcode()">\n      <div class="wrapper">\n        <span class="icon">+</span>\n        <span class="label">{{:: $ctrl.label }}</span>\n      </div>\n    </button>\n  '});const tt={top:{},right:{},bottom:{},left:{}};class et{constructor(t,e,n,A,o,C){this.app=t,this.store=e,this.utils=n,this.$timeout=A,this.$element=C,this.shortcode=null;for(let t in tt)tt[t].$element=C.find(`.uxb-resize-${t}`),tt[t].draggable=new g.g.HammerJS(tt[t].$element.get(0),{edge:t}),tt[t].draggable.get("pan").set({direction:J(t)}),tt[t].draggable.on("hammer.input",this.onHammerInput.bind(this)),tt[t].draggable.on("panstart",this.onPanStart.bind(this)),tt[t].draggable.on("pan",this.onPanMove.bind(this)),tt[t].draggable.on("panend",this.onPanEnd.bind(this));o.$watch((()=>t.states.outlinedShortcode),(t=>{if(this.app.states.resizingShortcode)return;if(!t)return C.removeClass("uxb-is-active");let e=function(t){let e=t.ancestorsAndSelf;for(let t=0;t<e.length;t++)if(e[t].data.resize)return e[t];return null}(t),g=!!e&&!1!==e.data.resize;for(let t in tt){let n=g&&angular.isArray(e.data.resize)&&e.data.resize.indexOf(t)>-1,A=g&&!0===e.data.resize;tt[t].$element.toggleClass("uxb-is-active",n||A)}C.toggleClass("uxb-is-active",g),C.cover(g?e.$element:null),this.shortcode=e})),o.$on(Event.CHANGE,(()=>{this.shortcode&&C.cover(this.shortcode.$element)}))}onHammerInput(t){t.srcEvent.stopPropagation(),t.srcEvent.stopImmediatePropagation(),this.app.states.resizingShortcode=this.shortcode,this.store.isDragging=!0}onPanStart(t){t.edge=q(tt,t.target),angular.element("body").addClass(`uxb-is-resizing-${t.edge}`),X(this.app.states.resizingShortcode,`onResize${g.g.capitalize(t.edge)}Start`,t),X(this.app.states.resizingShortcode,"onResizeStart",t),this.app.outlineShortcode(null),this.app("tools").hideAddableSpot(),this.app("tools").apply(),this.app.states.resizingShortcode.$scope.$digest()}onPanMove(t){t.edge=q(tt,t.target),X(this.app.states.resizingShortcode,`onResize${g.g.capitalize(t.edge)}Move`,t),X(this.app.states.resizingShortcode,"onResizeMove",t),this.app("tools").apply(),this.app.states.resizingShortcode.$scope.$digest(),this.$element.cover(this.app.states.resizingShortcode.$element)}onPanEnd(t){t.edge=q(tt,t.target),angular.element("body").removeClass(`uxb-is-resizing-${t.edge}`),X(this.app.states.resizingShortcode,`onResize${g.g.capitalize(t.edge)}End`,t),X(this.app.states.resizingShortcode,"onResizeEnd",t),this.$timeout((()=>{this.app.states.resizingShortcode=null,this.store.isDragging=!1}),0)}}et.$inject=["app","store","utils","$timeout","$scope","$element"];var gt=g(5861);r.component("appResizeTool",{controller:et,template:gt,bindings:{shortcode:"<"}});class nt{constructor(t,e,g,n,A,o){this.app=t,this.store=e,this.utils=g,this.$timeout=n,this.$element=o,this.shortcode=null,A.$watch((()=>t.states.outlinedShortcode),(t=>{if(!t)return o.removeClass("uxb-is-active");let e=function(t){let e=t.ancestorsAndSelf;for(let t=0;t<e.length;t++)if(e[t].data.move)return e[t];return null}(t),g=!!e&&!1!==e.data.move;o.toggleClass("uxb-is-active",g),o.cover(g?e.$element:null),this.shortcode=e})),A.$on(Event.CHANGE,(()=>{this.shortcode&&o.cover(this.shortcode.$element)}))}classNames(t){return this.utils.arrayPrefix(angular.isArray(t.data.move)?t.data.move:[t.data.move],"uxb-is-")}}nt.$inject=["app","store","utils","$timeout","$scope","$element"];var At=g(3397);r.component("appMoveTool",{controller:nt,template:At,bindings:{shortcode:"<"}});class ot{constructor(t,e,g,n){this.buttons=[],this.shortcode=null,e.$watch((()=>t.states.outlinedShortcode),(t=>{if(!t)return g.removeClass("active");this.shortcode=this.getShortcodeWithButtons(t),g.toggleClass("active",!!this.shortcode),g.cover(this.shortcode?this.shortcode.$element:null)})),e.$on(n.CHANGE,(()=>{this.shortcode&&g.cover(this.shortcode.$element)}))}getShortcodeWithButtons(t){var e=t.ancestorsAndSelf;for(let t=0;t<e.length;t++)if(e[t].data.addButtons)return e[t];return null}getIndex(t){var e=0;return"right-center"!==t&&"bottom-right"!==t&&"bottom-center"!==t&&"bottom-left"!==t||(e=this.shortcode.children.length),e}}ot.$inject=["app","$scope","$element","Event"],r.component("addButtons",{controller:ot,template:'\n    <div class="add-buttons">\n      <add-button\n        class="{{:: button }}"\n        shortcode="$ctrl.shortcode"\n        index="$ctrl.getIndex(button)"\n        data-tooltip="{{:: $ctrl.shortcode.data.message }}"\n        ng-repeat="button in $ctrl.shortcode.data.addButtons">\n      </add-button>\n    </div>\n  '});class Ct{constructor(t,e,g,n,A,o,C,I,s,i,r,a,l,c,d){var p=this,u={},h=C.element("app").find(".tools-addable"),m=n.find(".tools-addable");p.target=null,p.$targetElement=null,p.addable=null,t.register("tools",p),_.each(e.tools,(function(t,e){var A=snakeCase(e,"-");o(`<${A} shortcode="shortcode"></${A}>`)(g,((e,g)=>{I((()=>{t.$element=e,t.$ctrl=g.$$childHead.$ctrl,n.append(e)}),0,!1)}))})),s().on(c.CHANGED,(function(){A.requestAnimationFrame(p.fixPositions)})),s().on(c.RESIZE,(function(){A.requestAnimationFrame(p.fixPositions)})),s().on(c.SCROLL,(function(){})),g.$on(d.CHANGED,(()=>p.fixPositions())),g.$on(d.DELETED,(()=>p.hide())),g.$on(c.RELOAD,(()=>p.hide())),g.$on(l.PAN_MOVE,((e,g)=>{g.defaultPrevented?(t.outlineShortcode(null),p.hideAddableSpot()):t.outlineShortcode(g.isWithin?g.target.parent():null)})),g.$on(l.PAN_END,(()=>{p.hideAddableSpot(),p.fixPositions()})),p.apply=function(){g.$digest()};let f=!1;p.fixPositions=function(){I.cancel(f),f=I((function(){for(let t in u)u[t].$tool.hasClass("active")&&u[t].$tool.cover(u[t].$element);p.$targetElement&&p.getAddableElement().cover(p.$targetElement),g.$broadcast(r.CHANGE)}),0)},p.addTool=function(t,e,A){u.hasOwnProperty(t)&&p.removeTool(t),u[t]={},u[t].$element=A,u[t].$scope=g.$new(),u[t].$tool=o(e)(u[t].$scope),n.append(u[t].$tool)},p.getTool=function(t){var e=u[t];return{element:function(){return e.$element},scope:function(){return e.$scope.$$childHead},tool:function(){return e.$tool}}},p.showTool=function(t){u[t].$tool.addClass("active"),u[t].$tool.cover(u[t].$element)},p.hideTool=function(t){u[t].$tool.removeClass("active")},p.removeTool=function(t){u[t].$element=null,u[t].$scope.$destroy(),u[t].$tool.remove(),u[t].$tool=null,delete u[t]},p.hide=function(){p.hideAddableSpot()},p.getAddableElement=function(){return p.$targetElement&&p.$targetElement.get(0).ownerDocument.defaultView.frameElement?(h.removeClass("active"),m):(m.removeClass("active"),h)},p.showAddableSpot=function(e,g=!0){if(!e||t.states.freezed)return p.hideAddableSpot();p.toggleAddableButton(g),p.target=e,p.addable=e.target,p.index=e.index,p.$targetElement=p.target.shortcode.isEmpty&&p.target.element[0].ownerDocument.defaultView.frameElement&&"center"===p.target.name?p.target.shortcode.$content:p.target.element,p.getAddableElement().toggleClass("is-empty","center"===e.name),p.getAddableElement().toggleClass("is-left","left"===e.name),p.getAddableElement().toggleClass("is-right","right"===e.name),p.getAddableElement().toggleClass("is-top","top"===e.name),p.getAddableElement().toggleClass("is-bottom","bottom"===e.name),p.getAddableElement().toggleClass("is-deputy",p.target.element!==p.target.shortcode.$element),p.getAddableElement().cover(p.$targetElement,{includeMargins:!0}).addClass("active")},p.toggleAddableButton=function(t){p.getAddableElement().toggleClass("no-button",!1===t)},p.hideAddableSpot=function(){return p.getAddableElement().removeClass("active"),p.$targetElement=null,p.target=null,!1},p.addShortcode=function(){t("addables").open(p.addable,p.index),p.target=null,p.addable=null,p.index=null}}}Ct.$inject=["app","store","$scope","$element","$window","$compile","$angular","$timeout","$iframe","utils","Event","AppEvent","DragEvent","IframeEvent","ShortcodeEvent"],r.component("appTools",{controller:Ct,template:g(9335)});class It{constructor(t,e,g,n){var A=g.find(".name");e.$watch((()=>t.states.outlinedShortcode),(t=>{if(!t)return g.removeClass("active");A.text(t.data.name),g.toggleClass("active",t.$element.isVisible()),g.cover(t.$element)})),e.$on(n.CHANGE,(()=>{t.states.outlinedShortcode&&g.cover(t.states.outlinedShortcode.$element)}))}}It.$inject=["app","$scope","$element","Event"],r.component("appOutlineTool",{controller:It,template:'\n    <div class="wrapper">\n      <h3 class="name"></h3>\n    </div>\n  '});class st{constructor(t,e,g,n,A){this.app=g,this.shortcode=null,this.resizeObserver=new ResizeObserver((()=>{g.states.selectedShortcode&&e.cover(g.states.selectedShortcode.$element)})),t.$watch((()=>g.states.selectedShortcode),(t=>{if(this.resizeObserver.disconnect(),!t)return e.removeClass("active");this.shortcode=g.states.selectedShortcode,this.ancestors=g.states.selectedShortcode.ancestors.reverse(),this.ancestors.splice(0,1),e.cover(t.$element).addClass("active"),t.$element&&this.resizeObserver.observe(t.$element.get(0))})),t.$on(A.CHANGE,(()=>{g.states.selectedShortcode&&e.cover(g.states.selectedShortcode.$element)}))}$onDestroy(){this.resizeObserver.disconnect()}configureShortcode(t){this.app.configureShortcode(t)}outlineShortcode(t){this.app.outlineShortcode(t)}hideOutline(){this.app.outlineShortcode(null)}showContextMenu(t){this.app("contextMenu").open(this.shortcode,t.currentTarget)}}st.$inject=["$scope","$element","app","store","Event"],r.component("appSelectTool",{controller:st,bindings:{shortcode:"<"},template:'\n    <div class="wrapper">\n      <div ng-if="$ctrl.ancestors.length > 0" class="ancestors">\n        <span class="dashicons dashicons-arrow-up-alt2"></span>\n        <ul>\n          <li ng-repeat="ancestor in $ctrl.ancestors">\n            <button type="button" class="name"\n              draggable-shortcode="ancestor"\n              ng-click="$ctrl.configureShortcode(ancestor); $event.stopPropagation();"\n              ng-mouseover="$ctrl.outlineShortcode(ancestor)"\n              ng-mouseout="$ctrl.hideOutline()">\n              {{:: ancestor.data.name }}\n            </button>\n          </li>\n        </ul>\n      </div>\n      <h3 class="name" draggable-shortcode="$ctrl.shortcode">\n        {{ $ctrl.shortcode.data.name }}\n      </h3>\n      <button class="options" ng-click="$ctrl.showContextMenu($event); $event.stopPropagation();">\n        <span class="dashicons dashicons-admin-generic"></span>\n      </button>\n    </div>\n  '});class it{constructor(t,e,g,n,A,o,C,I,s,i,r,a,l,c,d,p,m,f,v){n.attach(document);var $=null,b=performance.now();let y,w;function x(){const{iframeRect:t,iframe:{x:e,y:g}}=w,{top:n}=u(),A=100;let o,C=g,I=t.bottom-t.top-g;C<A?o=(t.top+C-(t.top+A))/5:I<A&&(o=(t.bottom+A-(t.bottom+I))/5),o&&e>=0&&e<=t.right-t.left&&h(n+o)}e.postContent=o.create(e.post.content),c.debug(`Element instances created in ${performance.now()-b}ms`),angular.element("body").on("click",(function(g){g.preventDefault(),e.isDragging||e.isScrolling||(t.configureShortcode(null),t.apply())})),angular.element("body").on("mousemove",(function(n){e.isDragging||e.isScrolling||e.isReloading||a.requestAnimationFrame((function(){let e=A.getGlobalCoordinates(n.view,n.clientX,n.clientY),o=g.find(e.x,e.y),C=A.shortcodeFromPoint(e.x,e.y);t("tools").showAddableSpot(o.length?o[0]:null),C.isRoot||t.outlineShortcode(C),t("tools").apply()}))})),angular.element("body").on("mouseleave",(function(g){e.isDragging||e.isScrolling||e.isReloading||a.requestAnimationFrame((function(){t("tools").hideAddableSpot(),t.outlineShortcode(null),t("tools").apply()}))})),angular.element("body").on("dragstart",(t=>{t.preventDefault()})),angular.element(a).on("scroll",(function(){i().trigger(v.SCROLL),e.isScrolling=!0,l.cancel($),$=l((()=>e.isScrolling=!1),200,!1)})),I.$on(f.CHANGED,(function(){t.states.selectedShortcode?l((()=>lt(t.states.selectedShortcode.$element)),0,!1):rt(s)&&l((()=>lt(s)),0,!1)})),C.$on(p.PAN_START,((t,e)=>{w=e,y=setInterval(x,1e3/60)})),C.$on(p.PAN_MOVE,((t,e)=>{w=e})),C.$on(p.PAN_END,(()=>{clearInterval(y),y=w=null})),angular.element(a).on(m.RESIZE,(function(){i().trigger(v.RESIZE)})),new MutationObserver((t=>{e.initialized&&g.updateOffsets(!0),i().trigger(v.CHANGED,t)})).observe(document.querySelector("post-content"),{attributes:!0,childList:!0,subtree:!0,characterData:!1}),l((()=>{rt(s)&&lt(s,0),e.initialized=!0,e.loading=!1,t.broadcast(d.READY),ct(document,"uxb_app_ready")}),0,!0)}}function rt(t){let e=t.offset(),g=t.height();return e.top+g<at()||e.top>at()+window.innerHeight}function at(){return document.documentElement.scrollTop||document.body.scrollTop}function lt(t,e=void 0){angular.element("html, body").scrollToElement(t,e)}function ct(t,e){t.dispatchEvent(new Event(e))}it.$inject=["app","store","targets","contextmenu","utils","Shortcode","$rootScope","$scope","$element","$iframe","$compile","$window","$timeout","$log","AppEvent","DragEvent","Event","MediaEvent","IframeEvent"],r.component("postWrapper",{controller:it,template:"\n    <post-content></post-content>\n  "});const dt=angular.element("body");class pt{constructor(t,e,g,n,A,o,C,I,s,i){this.app=t,this.store=e,this.utils=n,this.responsiveHelper=s;var r=performance.now();this.store.postContent.$element=o,this.store.postContent.$scope=A,this.store.postContent.data.addableSpots.map((t=>{g.add(t,{shortcode:this.store.postContent,target:this.store.postContent})})),this.onPostOptionsChanged(e.post.attributes.values,{},e.post.attributes.options),A.$watchCollection((()=>e.post.attributes.values),((t,g)=>{this.onPostOptionsChanged(t,g,e.post.attributes.options)})),this.onPostOptionsChanged(e.post.meta.values,{},e.post.meta.options),A.$watchCollection((()=>e.post.meta.values),((t,g)=>{this.onPostOptionsChanged(t,g,e.post.meta.options)})),A.$on(i.CHANGED,(()=>g.updateOffsets())),C((()=>{I.debug(`Elements compiled in ${performance.now()-r}ms`)}),0,!1)}onPostOptionsChanged(t,e,g){for(let n in t)if(t[n]!==e[n]){let A=g.get(n);A.onChange&&A.apply(dt,t[n],e[n])}}showTemplates(){return this.store.postContent.isEmpty&&Object.keys(this.store.templates).length}}pt.$inject=["app","store","targets","utils","$scope","$element","$timeout","$log","ResponsiveHelper","ShortcodeEvent"],r.component("postContent",{controller:pt,template:'\n    <content content="_root [root]" shortcode="$ctrl.store.postContent"></content>\n    <template-selector ng-if="$ctrl.showTemplates()"></template-selector>\n  '}),r.component("templateSelector",{controller:["app","store","iframe","$scope","templates",function(t,e,g,n,A){this.store=e,this.templates=A,this.activeTab="flatsome",this.presets=[],this.isLoading=!0,this.errorMessage="",this.setTemplate=function(n){e.loading=!0,jQuery.post(e.ajaxUrl,{action:"ux_builder_to_array",id:n}).done((({data:A,success:o})=>{if(!o)return console.error(`Failed to compile template ${n}`);e.post.meta.values.hasOwnProperty("_wp_page_template")&&(e.post.meta.values._wp_page_template=e.templates[n].template||"default"),g.reload(A.content),t.apply()}))},this.useContentTemplate=function(n){e.loading=!0,jQuery.post(e.ajaxUrl,{action:"ux_builder_to_array",content:n.raw}).done((({data:A,success:o})=>{if(!o)return console.error(`Failed to compile template ${n.id}`);e.post.meta.values.hasOwnProperty("_wp_page_template")&&(e.post.meta.values._wp_page_template=n.template||"default"),g.reload(A.content),t.apply()}))},this.removeTemplate=async t=>{this.templates.remove(t).catch((t=>{this.errorMessage=t.message}))},this.$onInit=()=>{jQuery.get(e.ajaxUrl,{action:"ux_builder_parse_presets",tag:"_root"}).done((({data:t,success:e})=>{e&&t&&Array.isArray(t.presets)?this.presets=t.presets.filter((t=>t.custom)):this.errorMessage=t?t.message:"Failed to load templates.",this.isLoading=!1,n.$apply()})).fail((t=>{this.errorMessage=t.statusText,this.isLoading=!1,n.$apply()}))};const o=(t,e)=>{"_root"===e.tag&&(this.presets=e.presets.filter((t=>t.custom)),n.$apply())};n.$on("template-saved",o),n.$on("template-removed",o)}],template:'\n    <h2 class="uxb-templates-title">Insert a template</h2>\n    <div class="uxb-tabs">\n      <button class="uxb-tab" ng-class="{ \'uxb-active\': $ctrl.activeTab === \'flatsome\' }" ng-click="$ctrl.activeTab = \'flatsome\'">Flatsome</button>\n      <button class="uxb-tab" ng-class="{ \'uxb-active\': $ctrl.activeTab === \'custom\' }" ng-click="$ctrl.activeTab = \'custom\'">Custom</button>\n    </div>\n    <div class="uxb-templates-custom" ng-if="$ctrl.activeTab === \'custom\'">\n      <div ng-if="$ctrl.isLoading" class="uxb-loading-spinner"></div>\n      <div ng-if="!$ctrl.isLoading && !$ctrl.errorMessage && $ctrl.presets.length === 0">No custom templates yet&hellip;</div>\n      <p ng-if="$ctrl.errorMessage" class="uxb-error">{{ $ctrl.errorMessage }}</p>\n      <div class="uxb-templates-list">\n        <div class="uxb-template" ng-repeat="template in $ctrl.presets">\n          <button class="uxb-template-button" ng-click="$ctrl.useContentTemplate(template)">\n            <div class="uxb-template-icon">\n              <svg width="42" height="42" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M7.73009 2.41274L8.95709 3.63719L3.40181 9.18095L2.17482 7.95652L7.73009 2.41274ZM7.73009 0.242432L0 7.95652L3.40181 11.3513L11.1319 3.63719L7.73009 0.242432Z" fill="#007CBA"/> <path d="M7.8196 11.3114L8.95987 12.4493L7.8196 13.5873L6.67928 12.4493L7.8196 11.3114ZM7.8196 9.14111L4.50439 12.4492L7.8196 15.7575L11.1348 12.4492L7.8196 9.14087V9.14111Z" fill="#007CBA"/> <path d="M12.2322 6.90786L13.3725 8.0458L12.2322 9.18369L11.0921 8.04584L12.2323 6.90795L12.2322 6.90786ZM12.2323 4.73763L8.91699 8.04584L12.2322 11.3542L15.5474 8.04584L12.2322 4.73755L12.2323 4.73763Z" fill="#007CBA" fill-opacity="0.6"/> </svg>\n            </div>\n            <div class="uxb-template-label">{{:: template.name }}</div>\n          </button>\n          <div class="uxb-template-actions">\n            <button ng-click="$ctrl.templates.editTemplate(template)">\n              <span class="dashicons dashicons-edit"></span>\n            </button>\n            <button ng-click="$ctrl.removeTemplate(template)">\n              <span class="dashicons dashicons-trash"></span>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n    <div class="uxb-templates-flatsome" ng-if="$ctrl.activeTab === \'flatsome\'">\n      <h4>*Images are not included.</h4>\n      <div class="uxb-templates-list">\n        <div class="uxb-template" ng-repeat="(id, template) in $ctrl.store.templates track by id">\n          <button type="button" class="uxb-template-button" ng-click="$ctrl.setTemplate(id)">\n            <img ng-attr-src="{{:: template.thumbnail }}">\n            <span class="">{{:: template.name }}</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  '});const ut=[];let ht=0;class mt{constructor(t,e,g,n,A,o,C,I,s,i,r,a,l,c,d,p,u,h){this.app=t,this.store=e,this.dependencies=n,this.$scope=A,this.$element=o,this.$compile=C,this.$q=I,this.$transclude=s,this.$injector=i,this.$templateCache=r,this.shortcodeTemplateCache=a,this.$animate=l,this.$timeout=c,this.$controller=d,this.$log=p,this.ShortcodeEvent=u,this.ChildEvent=h}$onInit(){const{app:t,store:e,dependencies:g,$scope:n,$element:A,$compile:o,$q:C,$transclude:I,$templateCache:s,shortcodeTemplateCache:i,$animate:r,$timeout:a,$controller:l,$log:c,ShortcodeEvent:d,ChildEvent:p}=this;var u,h=this;function m(g){let n,A=C.defer(),I=!0;if(n=g.data.template?g.data.template:i.get(g.data.templateUrl||g.$id),angular.isDefined(n))return o(v(g,n))(g.$scope,(function(t){t.data("shortcode",g),g.$element=t,A.resolve(g)})),A.promise;void 0===g.$element&&(n=v(g,s.get("shortcodes/_loading.html"),!0),g.$element=o(n)(g.$scope),g.$element.data("shortcode",g),I=!1),g.$element&&g.$element.addClass("processing");const r={url:e.postUrl,type:"POST",data:{security:e.nonce,post_id:e.post.id,ux_builder_action:"do_shortcode",ux_builder_shortcode:g.copy((function(t,e){t.$id=e.$id,t.depth=e.ancestors.length,t.nested=_.filter(e.ancestors,(function(t){return t.tag===e.tag})).length}))},beforeSend(t){g.$$xhr=t,ht++},success(e){g.$scope&&(g.$$new=!0,i.put(g.$id,e),n=v(g,e),o(n)(g.$scope,(function(e){I&&t.trigger(d.DETACHED,g),g.children&&g.children.forEach((function(t){t.$element&&(t.$element.data("shortcode",null),t.$element.remove(),delete t.$element)})),e.data("shortcode",g),g.$element.removeClass("processing"),g.$element.replaceWith(e),g.$element=e,A.resolve(g)})))},complete(t,e){g.$$xhr=null,ht--,"success"!==e&&A.reject(),ut.length&&jQuery.ajax(ut.shift())}};return ht<5?jQuery.ajax(r):ut.push(r),A.promise}function f(t){var n={$scope:t.$scope,$element:t.$element,tools:e.tools?e.tools[`custom${capitalize(t.tag)}Tools`]:null,shortcode:t};C.all([g.injectStyles(t.data.styles),g.injectScripts(t.data.scripts)]).finally((()=>{l("ShortcodeController as $ctrl",n),t.data.controller&&l(`${t.tag}__controller as $customCtrl`,n)}))}function v(t,e,g){if(e=(e=(e=e.replace("<content",'<content content="'+t.tag+" ["+t.$id+']" shortcode="shortcode"')).replace(/<script\b[^<]*(?:(?!<\/script>)<[^<]*)*<\/script>/gi,"")).replace(/<!--[\s\S]*?-->/g,""),t.data.wrap||g){let g=["uxb-wrapper",`uxb-wrapper--${t.tag}`];t.data.inline&&g.push("uxb-wrapper-inline"),t.data.overlay&&(e+='<div class="uxb-overlay"></div>'),e=`\n                  <div class="${g.join(" ")}">\n                    ${e}\n                  </div>\n                `}return e}n.$watchCollection((()=>this.shortcode.children),(function(e,g){var o=[],C=[];_.each(e,(function(g,C){g.$element||(o.push(g),g.$scope=n.$new(),function(e){var g=e.index,n=e.siblings;e.$scope.shortcode=e,e.$scope.$index=g,e.$scope.$first=0===g,e.$scope.$last=g===n.length-1,e.$scope.$middle=!(e.$scope.$first||e.$scope.$last),e.$scope.$odd=!(e.$scope.$even=!(1&g)),e.$scope.$$recompile=function(){return(g=e).$$xhr&&g.$$xhr.abort(),a.cancel(g.$recompile),void(g.$recompile=a((()=>{i.remove(g.$id),m(g).then((function(e){const g=e.copy(((t,e)=>{t.$id=e.$id})),n=e.replaceWith(g);n.$$new=!0,t.broadcast(d.RECOMPILED,n)})).catch((()=>{})),c.debug("x recompiling",g.tag),delete g.$recompile}),250,!1));var g},e.$scope.$on("$destroy",(()=>{delete e.$scope.shortcode,delete e.$scope.$$recompile}))}(g),m(g).then(f),g.$scope.$customCtrl?.$onBeforeMove?.(),r.move(g.$element,null,0===C?A:e[C-1].$element),g.$scope.$customCtrl?.$onAfterMove?.())})),_.each(g,(function(t,g){e.indexOf(t)>-1||(t.originalIndex=g,C.push(t))})),g&&g.length===e.length&&_.each(e,(function(t,e){t.$scope.$customCtrl?.$onBeforeMove?.(),r.move(t.$element,null,0===e?A:h.shortcode.children[e-1].$element),t.$scope.$customCtrl?.$onAfterMove?.()})),_.each(C,(t=>n.$parent.$broadcast(p.REMOVED,t))),_.each(o,(t=>n.$parent.$broadcast(p.ADDED,t))),_.each(C,(function(t){r.leave(t.$element),t.$scope.$destroy(),t.$element.data("shortcode",null),t.$element.remove(),t.$element=null,t.$content=null,t.$scope=null})),I(((t,g)=>{u||0!==e.length?h.shortcode.$content&&(u.$destroy(),r.leave(h.shortcode.$content),h.shortcode.$content.data("shortcode",null),h.shortcode.$content.remove(),h.shortcode.$content=null,u=null):(u=g,t.html(`\n                        <div class="uxb-empty-message">\n                            ${h.shortcode.data.message}\n                        </div>\n                    `),t.data("shortcode",h.shortcode),h.shortcode.$content=t,r.enter(h.shortcode.$content,A.parent(),A))}))}))}}mt.$inject=["app","store","utils","dependencies","$scope","$element","$compile","$q","$transclude","$injector","$templateCache","shortcodeTemplateCache","$animate","$timeout","$controller","$log","ShortcodeEvent","ChildEvent"],r.component("content",{controller:mt,transclude:"element",bindings:{shortcode:"<"}});const ft=(window.frameElement?window.parent:window).uxBuilderData;g.g.UxBuilder=window.parent.UxBuilder,r.requires.push("app.filters"),r.requires.push("app.services");for(let t in ft.modules.iframe)r.requires.push(ft.modules.iframe[t]);r.config(["$controllerProvider",function(t){angular.forEach(ft.shortcodes,(function(e,g){t.register(`${g}__controller`,e.controller||angular.noop)}))}])}()}();