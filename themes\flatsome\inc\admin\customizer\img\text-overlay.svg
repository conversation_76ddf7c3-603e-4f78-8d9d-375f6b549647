<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="70px" height="71px" viewBox="0 0 70 71" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 3.8.1 (29687) - http://www.bohemiancoding.com/sketch -->
    <title>text-overlay</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0" y="0" width="70" height="50" rx="4"></rect>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="70" height="50" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
        <mask id="mask-4" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="70" height="50" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="text-overlay" transform="translate(0.000000, 0.569000)">
            <text id="Overlay" font-family="Lato-Regular, Lato" font-size="13" font-weight="normal" fill="#9A8F9A">
                <tspan x="0" y="67">Overlay</tspan>
            </text>
            <mask id="mask-3" fill="white">
                <use xlink:href="#path-1"></use>
            </mask>
            <g id="Mask" stroke="#3498DB" mask="url(#mask-2)" stroke-width="2" fill-opacity="0.01" fill="#00A0D2">
                <use mask="url(#mask-4)" xlink:href="#path-1"></use>
            </g>
            <rect id="Rectangle-5-Copy-7" fill="#3498DB" mask="url(#mask-3)" x="10.9226985" y="-6.32647839" width="47" height="50"></rect>
            <rect id="Path-Copy-14" fill-opacity="0.7" fill="#FFFFFF" mask="url(#mask-3)" x="17.9226985" y="33.1069471" width="30.2959155" height="3.18012422"></rect>
            <rect id="Path-Copy-13" fill-opacity="0.7" fill="#FFFFFF" mask="url(#mask-3)" x="16.9226985" y="26.9268229" width="33.0773015" height="3.18012422"></rect>
        </g>
    </g>
</svg>