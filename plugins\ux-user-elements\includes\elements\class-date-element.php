<?php
/**
 * Date Element Class for UX User Elements
 * 
 * Displays current date with various formatting and styling options
 * 
 * @package UX_User_Elements
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Date Element Class
 */
class UX_User_Elements_Date_Element extends UX_User_Elements_Base_Element {
    
    /**
     * Setup element configuration
     */
    protected function setup_config() {
        $this->tag = 'ux_current_date';
        
        $this->config = array_merge($this->config, array(
            'name'     => __('Current Date', 'ux-user-elements'),
            'category' => __('Content', 'ux-user-elements'),
            'priority' => 1,
            'options'  => array_merge(
                $this->get_date_options(),
                $this->get_common_styling_options()
            ),
        ));
    }
    
    /**
     * Get date-specific options
     */
    private function get_date_options() {
        return array(
            'format' => array(
                'type'    => 'select',
                'heading' => __('Date Format', 'ux-user-elements'),
                'default' => 'F j, Y',
                'options' => array(
                    'F j, Y'       => __('December 30, 2025', 'ux-user-elements'),
                    'm/d/Y'        => __('12/30/2025', 'ux-user-elements'),
                    'd/m/Y'        => __('30/12/2025', 'ux-user-elements'),
                    'Y-m-d'        => __('2025-12-30', 'ux-user-elements'),
                    'l, F j, Y'    => __('Monday, December 30, 2025', 'ux-user-elements'),
                    'F j, Y g:i A' => __('December 30, 2025 3:45 PM', 'ux-user-elements'),
                    'custom'       => __('Custom Format', 'ux-user-elements'),
                ),
            ),
            'custom_format' => array(
                'type'        => 'textfield',
                'heading'     => __('Custom Date Format', 'ux-user-elements'),
                'default'     => '',
                'placeholder' => __('e.g., Y-m-d H:i:s', 'ux-user-elements'),
                'description' => __('Use PHP date format. See <a href="https://www.php.net/manual/en/datetime.format.php" target="_blank">PHP date formats</a>', 'ux-user-elements'),
                'conditions'  => 'format === "custom"',
            ),
            'show_time' => array(
                'type'       => 'checkbox',
                'heading'    => __('Show Time', 'ux-user-elements'),
                'default'    => '',
                'conditions' => 'format !== "custom"',
            ),
        );
    }
    
    /**
     * Render the date shortcode
     */
    public function render_shortcode($atts, $content = '') {
        // Default attributes
        $defaults = array(
            'format'           => 'F j, Y',
            'custom_format'    => '',
            'show_time'        => '',
            'text_align'       => 'left',
            'font_size'        => 'medium',
            'custom_font_size' => '',
            'text_color'       => '',
            'css_class'        => '',
        );
        
        // Get sanitized attributes
        $atts = $this->get_attributes($atts, $defaults);
        
        // Determine the date format to use
        $date_format = $this->get_date_format($atts);
        
        // Generate the current date
        $current_date = date($date_format);
        
        // Build CSS classes and styles
        $css_classes = $this->build_css_classes('ux-date-element', $atts);
        $inline_styles = $this->build_inline_styles($atts);
        
        // Render the element
        return $this->render_element(
            'div',
            esc_html($current_date),
            $css_classes,
            $inline_styles
        );
    }
    
    /**
     * Get the date format based on shortcode attributes
     */
    private function get_date_format($atts) {
        // If custom format is specified, use it
        if ($atts['format'] === 'custom' && !empty($atts['custom_format'])) {
            return $atts['custom_format'];
        }
        
        // Get the base format
        $format = $atts['format'];
        
        // Add time if requested (and not using custom format)
        if (!empty($atts['show_time']) && $atts['format'] !== 'custom') {
            // Add time format if not already present
            if (strpos($format, 'g:i A') === false && strpos($format, 'H:i') === false) {
                $format .= ' g:i A';
            }
        }
        
        return $format;
    }
}
