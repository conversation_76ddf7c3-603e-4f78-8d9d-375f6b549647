<?php
/**
 * Test Plugin Activation
 * 
 * This file contains tests to verify the plugin activates correctly
 * and all necessary components are loaded.
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test Plugin Activation Class
 */
class UX_Date_Element_Activation_Test {
    
    /**
     * Run all activation tests
     */
    public static function run_tests() {
        $results = array();
        
        $results['plugin_file_exists'] = self::test_plugin_file_exists();
        $results['plugin_constants_defined'] = self::test_plugin_constants_defined();
        $results['plugin_class_exists'] = self::test_plugin_class_exists();
        $results['ux_builder_dependency_check'] = self::test_ux_builder_dependency_check();
        $results['shortcode_registered'] = self::test_shortcode_registered();
        
        return $results;
    }
    
    /**
     * Test if main plugin file exists
     */
    public static function test_plugin_file_exists() {
        $plugin_file = UX_DATE_ELEMENT_PLUGIN_DIR . 'ux-date-element.php';
        return array(
            'test' => 'Plugin File Exists',
            'passed' => file_exists($plugin_file),
            'message' => file_exists($plugin_file) ? 'Plugin file found' : 'Plugin file not found'
        );
    }
    
    /**
     * Test if plugin constants are defined
     */
    public static function test_plugin_constants_defined() {
        $constants = array(
            'UX_DATE_ELEMENT_VERSION',
            'UX_DATE_ELEMENT_PLUGIN_DIR',
            'UX_DATE_ELEMENT_PLUGIN_URL',
            'UX_DATE_ELEMENT_PLUGIN_FILE'
        );
        
        $all_defined = true;
        $missing = array();
        
        foreach ($constants as $constant) {
            if (!defined($constant)) {
                $all_defined = false;
                $missing[] = $constant;
            }
        }
        
        return array(
            'test' => 'Plugin Constants Defined',
            'passed' => $all_defined,
            'message' => $all_defined ? 'All constants defined' : 'Missing constants: ' . implode(', ', $missing)
        );
    }
    
    /**
     * Test if main plugin class exists
     */
    public static function test_plugin_class_exists() {
        return array(
            'test' => 'Plugin Class Exists',
            'passed' => class_exists('UX_Date_Element'),
            'message' => class_exists('UX_Date_Element') ? 'UX_Date_Element class found' : 'UX_Date_Element class not found'
        );
    }
    
    /**
     * Test UX Builder dependency check
     */
    public static function test_ux_builder_dependency_check() {
        $ux_builder_available = function_exists('add_ux_builder_shortcode');
        return array(
            'test' => 'UX Builder Dependency Check',
            'passed' => true, // This test always passes, we just check the status
            'message' => $ux_builder_available ? 'UX Builder is available' : 'UX Builder is not available (expected in test environment)'
        );
    }
    
    /**
     * Test if shortcode is registered
     */
    public static function test_shortcode_registered() {
        global $shortcode_tags;
        $shortcode_exists = isset($shortcode_tags['ux_current_date']);
        
        return array(
            'test' => 'Shortcode Registered',
            'passed' => $shortcode_exists,
            'message' => $shortcode_exists ? 'ux_current_date shortcode is registered' : 'ux_current_date shortcode is not registered'
        );
    }
    
    /**
     * Display test results
     */
    public static function display_results($results) {
        echo "<h3>Plugin Activation Test Results</h3>\n";
        echo "<table border='1' cellpadding='5' cellspacing='0'>\n";
        echo "<tr><th>Test</th><th>Status</th><th>Message</th></tr>\n";
        
        foreach ($results as $result) {
            $status = $result['passed'] ? '✅ PASS' : '❌ FAIL';
            $row_class = $result['passed'] ? 'pass' : 'fail';
            echo "<tr class='{$row_class}'>";
            echo "<td>{$result['test']}</td>";
            echo "<td>{$status}</td>";
            echo "<td>{$result['message']}</td>";
            echo "</tr>\n";
        }
        
        echo "</table>\n";
        
        // Summary
        $total_tests = count($results);
        $passed_tests = count(array_filter($results, function($r) { return $r['passed']; }));
        echo "<p><strong>Summary: {$passed_tests}/{$total_tests} tests passed</strong></p>\n";
    }
}

// Auto-run tests if this file is accessed directly via web browser
if (isset($_GET['run_activation_tests']) && $_GET['run_activation_tests'] === '1') {
    if (current_user_can('manage_options')) {
        $results = UX_Date_Element_Activation_Test::run_tests();
        ?>
        <!DOCTYPE html>
        <html>
        <head>
            <title>UX Date Element - Activation Tests</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                table { border-collapse: collapse; width: 100%; }
                th, td { text-align: left; padding: 8px; }
                .pass { background-color: #d4edda; }
                .fail { background-color: #f8d7da; }
            </style>
        </head>
        <body>
            <?php UX_Date_Element_Activation_Test::display_results($results); ?>
            <p><a href="<?php echo admin_url('plugins.php'); ?>">← Back to Plugins</a></p>
        </body>
        </html>
        <?php
        exit;
    }
}
