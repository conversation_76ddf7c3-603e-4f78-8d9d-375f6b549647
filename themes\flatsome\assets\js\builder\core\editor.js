!function(){var t={269:function(t,e,n){n.g.UX_EMPTY_VALUE="<none>"},8484:function(t,e,n){!function(){"use strict";n.g.isIframe=function(){return!!window.frameElement},n.g.camelCase=function(t,e){return t=e?t.charAt(0).toUpperCase()+t.slice(1):t,jQuery.camelCase(t.replace(/\_|\:/g,"-",!0))},n.g.snakeCase=function(t,e="_"){return t.replace(/[A-Z]/g,((t,n)=>(n?e:"")+t.toLowerCase()))},n.g.capitalize=function(t){return t.charAt(0).toUpperCase()+t.slice(1)},n.g.round=function(t,e){return Math.round(t/e)*e}}()},3042:function(t,e,n){"use strict";n.d(e,{A:function(){return o}});class o{constructor(t,e,n,o,s,i,g,r,a,A,l){this.app=t,this.store=e,this.templates=n,this.presetCache=o,this.shortcodeTemplateCache=s,this.manager=i,this.type="shortcodes",this.query="",this.orderBy="name",this.category=null,this.categories=[],this.items=[],this.items_=[],this.filter={name:""},this.isLoading=!1,this.errorMessage="",this.$scope=g,this.$element=r,this.$timeout=a,this.$iframe=A,this.$placeholder=angular.element('<div class="uxb-placeholder"></div>'),this.ShortcodeEvent=l,g.$on("flatsome-studio-imported",((t,e)=>{e.content.children.forEach((t=>{this.addShortcode(t,!1),this.index++})),a((()=>this.stack.close()))}));const C=(t,e)=>{this.presetCache.put(e.tag,e.presets),this.addedShortcode&&this.addedShortcode.tag===e.tag&&(this.addedShortcode.data.hasPresets=e.presets.length>0,this.presets=e.presets)};g.$on("template-saved",C),g.$on("template-removed",C)}$onInit(){if(this.showType("shortcodes"),_.each(this.items,(t=>{_.each(t.items,((e,n)=>{var o=s(e.presets[0].content);o.unshift(e.presets[0].content),_.each(o,(e=>{this.shortcode.tag===e.tag&&!1===this.store.shortcodes[e.tag].nested&&t.items.splice(n,1)}))}))})),1===this.items.length&&1===this.items[0].items.length)return this.add(this.items[0].items[0],0);this.$timeout((()=>{this.$element.find(".filter-elements").focus()}),100),this.$timeout((()=>{this.shortcode.childAt(this.index)?(this.shortcode.childAt(this.index).$element.before(this.$placeholder),this.$timeout((()=>this.scrollToElement(this.$placeholder)),100)):this.shortcode.childAt(this.index-1)?(this.shortcode.childAt(this.index-1).$element.after(this.$placeholder),this.$timeout((()=>this.scrollToElement(this.$placeholder)),100)):this.scrollToElement(this.shortcode.$element)}),200)}$onDestroy(){if(this.addedShortcode){const t=this.addedShortcode;this.$timeout((()=>{this.app.selectShortcode(t),this.app.configureShortcode(t)}),150)}this.categories=[],this.items=[],this.type="shortcodes",this.category=null,this.addedShortcode=null,this.index=null,this.presets=null,this.currentPreset=null,this.filter.name="",this.errorMessage="",this.isLoading=!1,this.app.freeze(!1),this.$placeholder.remove(),this.$placeholder=null,this.$element.removeClass("visible is-showing-presets")}add(t,e){this.addedShortcode=this.addShortcode(angular.copy(t.presets[e].content)),this.app.broadcast(this.ShortcodeEvent.ADDED,this.addedShortcode),this.$placeholder.detach(),this.loadPresets(this.addedShortcode)}loadPresets(){const t=this.addedShortcode.tag,e=this.addedShortcode.index,n=this.presetCache.get(t),o=n=>{if(this.presetCache.put(t,n),n.length<=1)this.stack.close();else{for(const t of n)if(!0!==t.custom){this.currentPreset=n.indexOf(t);break}this.$element.scrollTop(0),this.$element.addClass("is-showing-presets"),this.presets=n,this.index=e}};Array.isArray(n)?n.length?o(n):this.stack.close():(this.isLoading=t,jQuery.get(this.store.ajaxUrl,{action:"ux_builder_parse_presets",tag:t}).done((({data:t})=>{o(t.presets||[]),this.isLoading=!1,this.$scope.$apply()})).fail((t=>{o([]),this.errorMessage=t.message,this.isLoading=!1,this.$scope.$apply()})))}showType(t){this.type=t,this.category=null,this.items=this.getItems(this.type),this.categories=_.keys(this.items)}get flatsomeStudioIsActive(){return this.shortcode.isRoot&&this.store.flatsomeStudioUrl}showFlatsomeStudio(){this.shortcode.isRoot&&(this.store.showFlatsomeStudio=!this.store.showFlatsomeStudio)}getItems(t,e){var n=[],o=_.chain(this.shortcode.allowed).sortBy((t=>t.name)).sortBy((t=>this.shortcode.data.allow.length?this.shortcode.data.allow.indexOf(t.tag)-this.shortcode.data.allow.length:t.priority)).value();return o=_.filter(o,(t=>!e||t.category===e)),o=_.filter(o,(t=>"ux_gutenberg"!==t.tag)),o=_.groupBy(o,(t=>t.category)),_.each(o,((t,e)=>{n.push({name:e,items:t})})),n}showCategory(t=null){this.category=t,this.items=this.getItems(this.type,t)}usePreset(t){const e={...t,$id:this.addedShortcode.$id};this.shortcodeTemplateCache.remove(e.$id),this.manager.remove(this.addedShortcode,!1),this.addedShortcode=this.addShortcode(e,!0)}removePreset(t){this.templates.remove(t).catch((t=>{this.errorMessage=t.message}))}scrollToElement(t){this.$iframe().contents().find("body").scrollToElement(t)}addShortcode(t,e=!0){let n=this.shortcode.addChild(t,this.index);return n.$$new=!0,e&&n.data.scrollTo&&this.$timeout((()=>{this.$iframe().contents().find("body").scrollToElement(n.$element)}),0,!1),n}}function s(t){var e=[];return _.each(t.children,(function(t){e.push(t),e=_.union(e,s(t))})),e}o.$inject=["app","store","templates","presetCache","shortcodeTemplateCache","Shortcode","$scope","$element","$timeout","$iframe","ShortcodeEvent"]},6488:function(t,e,n){n.g.jQuery.fn.cover=function(t,e={}){if(!t)return this;let o=n.g.jQuery(t),s=o.get(0).ownerDocument,i=s.defaultView,g=o.outerOffset(e),r=!!this.get(0).ownerDocument.defaultView.frameElement,a=!!o.get(0).ownerDocument.defaultView.frameElement;if(!r&&a){let t=i.frameElement.getBoundingClientRect();g.left+=t.left,g.top+=t.top}return r&&a&&(g.top+=s.documentElement.scrollTop||s.body.scrollTop),this.css({"--top":`${g.top.toFixed()}px`,width:g.width.toFixed(2),height:g.height.toFixed(2),transform:`translateX(${g.left.toFixed()}px) translateY(${g.top.toFixed()}px)`})}},2496:function(t,e,n){n.g.jQuery.fn.isVisible=function(){return t=[this.get(0)],e=!0,t.map((function(t){var n=window.getComputedStyle(t);"none"===n.display&&(e=!1),"hidden"===n.visibility&&(e=!1),"0.0"===n.opacity&&(e=!1)})),e;var t,e}},8774:function(t,e,n){n.g.jQuery.fn.outerOffset=function(t={}){var e={width:0,height:0};return this.each(((o,s)=>{var i=n.g.jQuery(s),g=s.getBoundingClientRect(),r=g.right-(e.left?e.left:g.left),a=g.bottom-(e.top?e.top:g.top),A=i.css("display").search("inline")>-1;t.includeMargins&&(A?r=i.outerWidth(!0):a=i.outerHeight(!0)),e.top=(g.top>e.top?e.top:g.top)+0,e.left=(g.left>e.left?e.left:g.left)+0,e.width=r>e.width?r:e.width,e.height=a>e.height?a:e.height})),e.right=e.left+e.width,e.bottom=e.top+e.height,e}},628:function(t,e,n){n.g.jQuery.fn.shortcode=function(){var t=this.parents(),e=null;return this.data("shortcode")?this.data("shortcode"):(t.each((function(t,o){n.g.jQuery(o).data("shortcode")&&!e&&(e=n.g.jQuery(o).data("shortcode"))})),e)}},4729:function(t,e,n){var o;!function(s,i,g,r){"use strict";var a,A=["","webkit","Moz","MS","ms","o"],l=i.createElement("div"),C="function",I=Math.round,c=Math.abs,p=Date.now;function h(t,e,n){return setTimeout(b(t,n),e)}function d(t,e,n){return!!Array.isArray(t)&&(u(t,n[e],n),!0)}function u(t,e,n){var o;if(t)if(t.forEach)t.forEach(e,n);else if(t.length!==r)for(o=0;o<t.length;)e.call(n,t[o],o,t),o++;else for(o in t)t.hasOwnProperty(o)&&e.call(n,t[o],o,t)}function m(t,e,n){var o="DEPRECATED METHOD: "+e+"\n"+n+" AT \n";return function(){var e=new Error("get-stack-trace"),n=e&&e.stack?e.stack.replace(/^[^\(]+?[\n$]/gm,"").replace(/^\s+at\s+/gm,"").replace(/^Object.<anonymous>\s*\(/gm,"{anonymous}()@"):"Unknown Stack Trace",i=s.console&&(s.console.warn||s.console.log);return i&&i.call(s.console,o,n),t.apply(this,arguments)}}a="function"!=typeof Object.assign?function(t){if(t===r||null===t)throw new TypeError("Cannot convert undefined or null to object");for(var e=Object(t),n=1;n<arguments.length;n++){var o=arguments[n];if(o!==r&&null!==o)for(var s in o)o.hasOwnProperty(s)&&(e[s]=o[s])}return e}:Object.assign;var f=m((function(t,e,n){for(var o=Object.keys(e),s=0;s<o.length;)(!n||n&&t[o[s]]===r)&&(t[o[s]]=e[o[s]]),s++;return t}),"extend","Use `assign`."),v=m((function(t,e){return f(t,e,!0)}),"merge","Use `assign`.");function $(t,e,n){var o,s=e.prototype;(o=t.prototype=Object.create(s)).constructor=t,o._super=s,n&&a(o,n)}function b(t,e){return function(){return t.apply(e,arguments)}}function y(t,e){return typeof t==C?t.apply(e&&e[0]||r,e):t}function w(t,e){return t===r?e:t}function x(t,e,n){u(k(e),(function(e){t.addEventListener(e,n,!1)}))}function E(t,e,n){u(k(e),(function(e){t.removeEventListener(e,n,!1)}))}function S(t,e){for(;t;){if(t==e)return!0;t=t.parentNode}return!1}function T(t,e){return t.indexOf(e)>-1}function k(t){return t.trim().split(/\s+/g)}function D(t,e,n){if(t.indexOf&&!n)return t.indexOf(e);for(var o=0;o<t.length;){if(n&&t[o][n]==e||!n&&t[o]===e)return o;o++}return-1}function O(t){return Array.prototype.slice.call(t,0)}function N(t,e,n){for(var o=[],s=[],i=0;i<t.length;){var g=e?t[i][e]:t[i];D(s,g)<0&&o.push(t[i]),s[i]=g,i++}return n&&(o=e?o.sort((function(t,n){return t[e]>n[e]})):o.sort()),o}function M(t,e){for(var n,o,s=e[0].toUpperCase()+e.slice(1),i=0;i<A.length;){if((o=(n=A[i])?n+s:e)in t)return o;i++}return r}var P=1;function R(t){var e=t.ownerDocument||t;return e.defaultView||e.parentWindow||s}var Y="ontouchstart"in s,B=M(s,"PointerEvent")!==r,j=Y&&/mobile|tablet|ip(ad|hone|od)|android/i.test(navigator.userAgent),L="touch",_="mouse",V=25,G=1,Q=4,z=8,U=1,F=2,Z=4,W=8,H=16,X=F|Z,q=W|H,K=X|q,J=["x","y"],tt=["clientX","clientY"];function et(t,e){var n=this;this.manager=t,this.callback=e,this.element=t.element,this.target=t.options.inputTarget,this.domHandler=function(e){y(t.options.enable,[t])&&n.handler(e)},this.init()}function nt(t,e,n){var o=n.pointers.length,s=n.changedPointers.length,i=e&G&&o-s==0,g=e&(Q|z)&&o-s==0;n.isFirst=!!i,n.isFinal=!!g,i&&(t.session={}),n.eventType=e,function(t,e){var n=t.session,o=e.pointers,s=o.length;n.firstInput||(n.firstInput=ot(e)),s>1&&!n.firstMultiple?n.firstMultiple=ot(e):1===s&&(n.firstMultiple=!1);var i=n.firstInput,g=n.firstMultiple,a=g?g.center:i.center,A=e.center=st(o);e.timeStamp=p(),e.deltaTime=e.timeStamp-i.timeStamp,e.angle=at(a,A),e.distance=rt(a,A),function(t,e){var n=e.center,o=t.offsetDelta||{},s=t.prevDelta||{},i=t.prevInput||{};e.eventType!==G&&i.eventType!==Q||(s=t.prevDelta={x:i.deltaX||0,y:i.deltaY||0},o=t.offsetDelta={x:n.x,y:n.y}),e.deltaX=s.x+(n.x-o.x),e.deltaY=s.y+(n.y-o.y)}(n,e),e.offsetDirection=gt(e.deltaX,e.deltaY);var l,C,I=it(e.deltaTime,e.deltaX,e.deltaY);e.overallVelocityX=I.x,e.overallVelocityY=I.y,e.overallVelocity=c(I.x)>c(I.y)?I.x:I.y,e.scale=g?(l=g.pointers,rt((C=o)[0],C[1],tt)/rt(l[0],l[1],tt)):1,e.rotation=g?function(t,e){return at(e[1],e[0],tt)+at(t[1],t[0],tt)}(g.pointers,o):0,e.maxPointers=n.prevInput?e.pointers.length>n.prevInput.maxPointers?e.pointers.length:n.prevInput.maxPointers:e.pointers.length,function(t,e){var n,o,s,i,g=t.lastInterval||e,a=e.timeStamp-g.timeStamp;if(e.eventType!=z&&(a>V||g.velocity===r)){var A=e.deltaX-g.deltaX,l=e.deltaY-g.deltaY,C=it(a,A,l);o=C.x,s=C.y,n=c(C.x)>c(C.y)?C.x:C.y,i=gt(A,l),t.lastInterval=e}else n=g.velocity,o=g.velocityX,s=g.velocityY,i=g.direction;e.velocity=n,e.velocityX=o,e.velocityY=s,e.direction=i}(n,e);var h=t.element;S(e.srcEvent.target,h)&&(h=e.srcEvent.target),e.target=h}(t,n),t.emit("hammer.input",n),t.recognize(n),t.session.prevInput=n}function ot(t){for(var e=[],n=0;n<t.pointers.length;)e[n]={clientX:I(t.pointers[n].clientX),clientY:I(t.pointers[n].clientY)},n++;return{timeStamp:p(),pointers:e,center:st(e),deltaX:t.deltaX,deltaY:t.deltaY}}function st(t){var e=t.length;if(1===e)return{x:I(t[0].clientX),y:I(t[0].clientY)};for(var n=0,o=0,s=0;s<e;)n+=t[s].clientX,o+=t[s].clientY,s++;return{x:I(n/e),y:I(o/e)}}function it(t,e,n){return{x:e/t||0,y:n/t||0}}function gt(t,e){return t===e?U:c(t)>=c(e)?t<0?F:Z:e<0?W:H}function rt(t,e,n){n||(n=J);var o=e[n[0]]-t[n[0]],s=e[n[1]]-t[n[1]];return Math.sqrt(o*o+s*s)}function at(t,e,n){n||(n=J);var o=e[n[0]]-t[n[0]],s=e[n[1]]-t[n[1]];return 180*Math.atan2(s,o)/Math.PI}et.prototype={handler:function(){},init:function(){this.evEl&&x(this.element,this.evEl,this.domHandler),this.evTarget&&x(this.target,this.evTarget,this.domHandler),this.evWin&&x(R(this.element),this.evWin,this.domHandler)},destroy:function(){this.evEl&&E(this.element,this.evEl,this.domHandler),this.evTarget&&E(this.target,this.evTarget,this.domHandler),this.evWin&&E(R(this.element),this.evWin,this.domHandler)}};var At={mousedown:G,mousemove:2,mouseup:Q},lt="mousedown",Ct="mousemove mouseup";function It(){this.evEl=lt,this.evWin=Ct,this.pressed=!1,et.apply(this,arguments)}$(It,et,{handler:function(t){var e=At[t.type];e&G&&0===t.button&&(this.pressed=!0),2&e&&1!==t.which&&(e=Q),this.pressed&&(e&Q&&(this.pressed=!1),this.callback(this.manager,e,{pointers:[t],changedPointers:[t],pointerType:_,srcEvent:t}))}});var ct={pointerdown:G,pointermove:2,pointerup:Q,pointercancel:z,pointerout:z},pt={2:L,3:"pen",4:_,5:"kinect"},ht="pointerdown",dt="pointermove pointerup pointercancel";function ut(){this.evEl=ht,this.evWin=dt,et.apply(this,arguments),this.store=this.manager.session.pointerEvents=[]}s.MSPointerEvent&&!s.PointerEvent&&(ht="MSPointerDown",dt="MSPointerMove MSPointerUp MSPointerCancel"),$(ut,et,{handler:function(t){var e=this.store,n=!1,o=t.type.toLowerCase().replace("ms",""),s=ct[o],i=pt[t.pointerType]||t.pointerType,g=i==L,r=D(e,t.pointerId,"pointerId");s&G&&(0===t.button||g)?r<0&&(e.push(t),r=e.length-1):s&(Q|z)&&(n=!0),r<0||(e[r]=t,this.callback(this.manager,s,{pointers:e,changedPointers:[t],pointerType:i,srcEvent:t}),n&&e.splice(r,1))}});var mt={touchstart:G,touchmove:2,touchend:Q,touchcancel:z};function ft(){this.evTarget="touchstart",this.evWin="touchstart touchmove touchend touchcancel",this.started=!1,et.apply(this,arguments)}function vt(t,e){var n=O(t.touches),o=O(t.changedTouches);return e&(Q|z)&&(n=N(n.concat(o),"identifier",!0)),[n,o]}$(ft,et,{handler:function(t){var e=mt[t.type];if(e===G&&(this.started=!0),this.started){var n=vt.call(this,t,e);e&(Q|z)&&n[0].length-n[1].length==0&&(this.started=!1),this.callback(this.manager,e,{pointers:n[0],changedPointers:n[1],pointerType:L,srcEvent:t})}}});var $t={touchstart:G,touchmove:2,touchend:Q,touchcancel:z},bt="touchstart touchmove touchend touchcancel";function yt(){this.evTarget=bt,this.targetIds={},et.apply(this,arguments)}function wt(t,e){var n=O(t.touches),o=this.targetIds;if(e&(2|G)&&1===n.length)return o[n[0].identifier]=!0,[n,n];var s,i,g=O(t.changedTouches),r=[],a=this.target;if(i=n.filter((function(t){return S(t.target,a)})),e===G)for(s=0;s<i.length;)o[i[s].identifier]=!0,s++;for(s=0;s<g.length;)o[g[s].identifier]&&r.push(g[s]),e&(Q|z)&&delete o[g[s].identifier],s++;return r.length?[N(i.concat(r),"identifier",!0),r]:void 0}$(yt,et,{handler:function(t){var e=$t[t.type],n=wt.call(this,t,e);n&&this.callback(this.manager,e,{pointers:n[0],changedPointers:n[1],pointerType:L,srcEvent:t})}});var xt=2500;function Et(){et.apply(this,arguments);var t=b(this.handler,this);this.touch=new yt(this.manager,t),this.mouse=new It(this.manager,t),this.primaryTouch=null,this.lastTouches=[]}function St(t,e){t&G?(this.primaryTouch=e.changedPointers[0].identifier,Tt.call(this,e)):t&(Q|z)&&Tt.call(this,e)}function Tt(t){var e=t.changedPointers[0];if(e.identifier===this.primaryTouch){var n={x:e.clientX,y:e.clientY};this.lastTouches.push(n);var o=this.lastTouches;setTimeout((function(){var t=o.indexOf(n);t>-1&&o.splice(t,1)}),xt)}}function kt(t){for(var e=t.srcEvent.clientX,n=t.srcEvent.clientY,o=0;o<this.lastTouches.length;o++){var s=this.lastTouches[o],i=Math.abs(e-s.x),g=Math.abs(n-s.y);if(i<=25&&g<=25)return!0}return!1}$(Et,et,{handler:function(t,e,n){var o=n.pointerType==L,s=n.pointerType==_;if(!(s&&n.sourceCapabilities&&n.sourceCapabilities.firesTouchEvents)){if(o)St.call(this,e,n);else if(s&&kt.call(this,n))return;this.callback(t,e,n)}},destroy:function(){this.touch.destroy(),this.mouse.destroy()}});var Dt=M(l.style,"touchAction"),Ot=Dt!==r,Nt="compute",Mt="auto",Pt="manipulation",Rt="none",Yt="pan-x",Bt="pan-y",jt=function(){if(!Ot)return!1;var t={},e=s.CSS&&s.CSS.supports;return["auto","manipulation","pan-y","pan-x","pan-x pan-y","none"].forEach((function(n){t[n]=!e||s.CSS.supports("touch-action",n)})),t}();function Lt(t,e){this.manager=t,this.set(e)}Lt.prototype={set:function(t){t==Nt&&(t=this.compute()),Ot&&this.manager.element.style&&jt[t]&&(this.manager.element.style[Dt]=t),this.actions=t.toLowerCase().trim()},update:function(){this.set(this.manager.options.touchAction)},compute:function(){var t=[];return u(this.manager.recognizers,(function(e){y(e.options.enable,[e])&&(t=t.concat(e.getTouchAction()))})),function(t){if(T(t,Rt))return Rt;var e=T(t,Yt),n=T(t,Bt);return e&&n?Rt:e||n?e?Yt:Bt:T(t,Pt)?Pt:Mt}(t.join(" "))},preventDefaults:function(t){var e=t.srcEvent,n=t.offsetDirection;if(this.manager.session.prevented)e.preventDefault();else{var o=this.actions,s=T(o,Rt)&&!jt[Rt],i=T(o,Bt)&&!jt[Bt],g=T(o,Yt)&&!jt[Yt];if(s){var r=1===t.pointers.length,a=t.distance<2,A=t.deltaTime<250;if(r&&a&&A)return}if(!g||!i)return s||i&&n&X||g&&n&q?this.preventSrc(e):void 0}},preventSrc:function(t){this.manager.session.prevented=!0,t.preventDefault()}};var _t=1,Vt=32;function Gt(t){this.options=a({},this.defaults,t||{}),this.id=P++,this.manager=null,this.options.enable=w(this.options.enable,!0),this.state=_t,this.simultaneous={},this.requireFail=[]}function Qt(t){return 16&t?"cancel":8&t?"end":4&t?"move":2&t?"start":""}function zt(t){return t==H?"down":t==W?"up":t==F?"left":t==Z?"right":""}function Ut(t,e){var n=e.manager;return n?n.get(t):t}function Ft(){Gt.apply(this,arguments)}function Zt(){Ft.apply(this,arguments),this.pX=null,this.pY=null}function Wt(){Ft.apply(this,arguments)}function Ht(){Gt.apply(this,arguments),this._timer=null,this._input=null}function Xt(){Ft.apply(this,arguments)}function qt(){Ft.apply(this,arguments)}function Kt(){Gt.apply(this,arguments),this.pTime=!1,this.pCenter=!1,this._timer=null,this._input=null,this.count=0}function Jt(t,e){return(e=e||{}).recognizers=w(e.recognizers,Jt.defaults.preset),new te(t,e)}function te(t,e){this.options=a({},Jt.defaults,e||{}),this.options.inputTarget=this.options.inputTarget||t,this.handlers={},this.session={},this.recognizers=[],this.oldCssProps={},this.element=t,this.input=new(this.options.inputClass||(B?ut:j?yt:Y?Et:It))(this,nt),this.touchAction=new Lt(this,this.options.touchAction),ee(this,!0),u(this.options.recognizers,(function(t){var e=this.add(new t[0](t[1]));t[2]&&e.recognizeWith(t[2]),t[3]&&e.requireFailure(t[3])}),this)}function ee(t,e){var n,o=t.element;o.style&&(u(t.options.cssProps,(function(s,i){n=M(o.style,i),e?(t.oldCssProps[n]=o.style[n],o.style[n]=s):o.style[n]=t.oldCssProps[n]||""})),e||(t.oldCssProps={}))}Gt.prototype={defaults:{},set:function(t){return a(this.options,t),this.manager&&this.manager.touchAction.update(),this},recognizeWith:function(t){if(d(t,"recognizeWith",this))return this;var e=this.simultaneous;return e[(t=Ut(t,this)).id]||(e[t.id]=t,t.recognizeWith(this)),this},dropRecognizeWith:function(t){return d(t,"dropRecognizeWith",this)||(t=Ut(t,this),delete this.simultaneous[t.id]),this},requireFailure:function(t){if(d(t,"requireFailure",this))return this;var e=this.requireFail;return-1===D(e,t=Ut(t,this))&&(e.push(t),t.requireFailure(this)),this},dropRequireFailure:function(t){if(d(t,"dropRequireFailure",this))return this;t=Ut(t,this);var e=D(this.requireFail,t);return e>-1&&this.requireFail.splice(e,1),this},hasRequireFailures:function(){return this.requireFail.length>0},canRecognizeWith:function(t){return!!this.simultaneous[t.id]},emit:function(t){var e=this,n=this.state;function o(n){e.manager.emit(n,t)}n<8&&o(e.options.event+Qt(n)),o(e.options.event),t.additionalEvent&&o(t.additionalEvent),n>=8&&o(e.options.event+Qt(n))},tryEmit:function(t){if(this.canEmit())return this.emit(t);this.state=Vt},canEmit:function(){for(var t=0;t<this.requireFail.length;){if(!(this.requireFail[t].state&(Vt|_t)))return!1;t++}return!0},recognize:function(t){var e=a({},t);if(!y(this.options.enable,[this,e]))return this.reset(),void(this.state=Vt);56&this.state&&(this.state=_t),this.state=this.process(e),30&this.state&&this.tryEmit(e)},process:function(t){},getTouchAction:function(){},reset:function(){}},$(Ft,Gt,{defaults:{pointers:1},attrTest:function(t){var e=this.options.pointers;return 0===e||t.pointers.length===e},process:function(t){var e=this.state,n=t.eventType,o=6&e,s=this.attrTest(t);return o&&(n&z||!s)?16|e:o||s?n&Q?8|e:2&e?4|e:2:Vt}}),$(Zt,Ft,{defaults:{event:"pan",threshold:10,pointers:1,direction:K},getTouchAction:function(){var t=this.options.direction,e=[];return t&X&&e.push(Bt),t&q&&e.push(Yt),e},directionTest:function(t){var e=this.options,n=!0,o=t.distance,s=t.direction,i=t.deltaX,g=t.deltaY;return s&e.direction||(e.direction&X?(s=0===i?U:i<0?F:Z,n=i!=this.pX,o=Math.abs(t.deltaX)):(s=0===g?U:g<0?W:H,n=g!=this.pY,o=Math.abs(t.deltaY))),t.direction=s,n&&o>e.threshold&&s&e.direction},attrTest:function(t){return Ft.prototype.attrTest.call(this,t)&&(2&this.state||!(2&this.state)&&this.directionTest(t))},emit:function(t){this.pX=t.deltaX,this.pY=t.deltaY;var e=zt(t.direction);e&&(t.additionalEvent=this.options.event+e),this._super.emit.call(this,t)}}),$(Wt,Ft,{defaults:{event:"pinch",threshold:0,pointers:2},getTouchAction:function(){return[Rt]},attrTest:function(t){return this._super.attrTest.call(this,t)&&(Math.abs(t.scale-1)>this.options.threshold||2&this.state)},emit:function(t){if(1!==t.scale){var e=t.scale<1?"in":"out";t.additionalEvent=this.options.event+e}this._super.emit.call(this,t)}}),$(Ht,Gt,{defaults:{event:"press",pointers:1,time:251,threshold:9},getTouchAction:function(){return[Mt]},process:function(t){var e=this.options,n=t.pointers.length===e.pointers,o=t.distance<e.threshold,s=t.deltaTime>e.time;if(this._input=t,!o||!n||t.eventType&(Q|z)&&!s)this.reset();else if(t.eventType&G)this.reset(),this._timer=h((function(){this.state=8,this.tryEmit()}),e.time,this);else if(t.eventType&Q)return 8;return Vt},reset:function(){clearTimeout(this._timer)},emit:function(t){8===this.state&&(t&&t.eventType&Q?this.manager.emit(this.options.event+"up",t):(this._input.timeStamp=p(),this.manager.emit(this.options.event,this._input)))}}),$(Xt,Ft,{defaults:{event:"rotate",threshold:0,pointers:2},getTouchAction:function(){return[Rt]},attrTest:function(t){return this._super.attrTest.call(this,t)&&(Math.abs(t.rotation)>this.options.threshold||2&this.state)}}),$(qt,Ft,{defaults:{event:"swipe",threshold:10,velocity:.3,direction:X|q,pointers:1},getTouchAction:function(){return Zt.prototype.getTouchAction.call(this)},attrTest:function(t){var e,n=this.options.direction;return n&(X|q)?e=t.overallVelocity:n&X?e=t.overallVelocityX:n&q&&(e=t.overallVelocityY),this._super.attrTest.call(this,t)&&n&t.offsetDirection&&t.distance>this.options.threshold&&t.maxPointers==this.options.pointers&&c(e)>this.options.velocity&&t.eventType&Q},emit:function(t){var e=zt(t.offsetDirection);e&&this.manager.emit(this.options.event+e,t),this.manager.emit(this.options.event,t)}}),$(Kt,Gt,{defaults:{event:"tap",pointers:1,taps:1,interval:300,time:250,threshold:9,posThreshold:10},getTouchAction:function(){return[Pt]},process:function(t){var e=this.options,n=t.pointers.length===e.pointers,o=t.distance<e.threshold,s=t.deltaTime<e.time;if(this.reset(),t.eventType&G&&0===this.count)return this.failTimeout();if(o&&s&&n){if(t.eventType!=Q)return this.failTimeout();var i=!this.pTime||t.timeStamp-this.pTime<e.interval,g=!this.pCenter||rt(this.pCenter,t.center)<e.posThreshold;if(this.pTime=t.timeStamp,this.pCenter=t.center,g&&i?this.count+=1:this.count=1,this._input=t,0==this.count%e.taps)return this.hasRequireFailures()?(this._timer=h((function(){this.state=8,this.tryEmit()}),e.interval,this),2):8}return Vt},failTimeout:function(){return this._timer=h((function(){this.state=Vt}),this.options.interval,this),Vt},reset:function(){clearTimeout(this._timer)},emit:function(){8==this.state&&(this._input.tapCount=this.count,this.manager.emit(this.options.event,this._input))}}),Jt.VERSION="2.0.7",Jt.defaults={domEvents:!1,touchAction:Nt,enable:!0,inputTarget:null,inputClass:null,preset:[[Xt,{enable:!1}],[Wt,{enable:!1},["rotate"]],[qt,{direction:X}],[Zt,{direction:X},["swipe"]],[Kt],[Kt,{event:"doubletap",taps:2},["tap"]],[Ht]],cssProps:{userSelect:"none",touchSelect:"none",touchCallout:"none",contentZooming:"none",userDrag:"none",tapHighlightColor:"rgba(0,0,0,0)"}},te.prototype={set:function(t){return a(this.options,t),t.touchAction&&this.touchAction.update(),t.inputTarget&&(this.input.destroy(),this.input.target=t.inputTarget,this.input.init()),this},stop:function(t){this.session.stopped=t?2:1},recognize:function(t){var e=this.session;if(!e.stopped){var n;this.touchAction.preventDefaults(t);var o=this.recognizers,s=e.curRecognizer;(!s||s&&8&s.state)&&(s=e.curRecognizer=null);for(var i=0;i<o.length;)n=o[i],2===e.stopped||s&&n!=s&&!n.canRecognizeWith(s)?n.reset():n.recognize(t),!s&&14&n.state&&(s=e.curRecognizer=n),i++}},get:function(t){if(t instanceof Gt)return t;for(var e=this.recognizers,n=0;n<e.length;n++)if(e[n].options.event==t)return e[n];return null},add:function(t){if(d(t,"add",this))return this;var e=this.get(t.options.event);return e&&this.remove(e),this.recognizers.push(t),t.manager=this,this.touchAction.update(),t},remove:function(t){if(d(t,"remove",this))return this;if(t=this.get(t)){var e=this.recognizers,n=D(e,t);-1!==n&&(e.splice(n,1),this.touchAction.update())}return this},on:function(t,e){if(t!==r&&e!==r){var n=this.handlers;return u(k(t),(function(t){n[t]=n[t]||[],n[t].push(e)})),this}},off:function(t,e){if(t!==r){var n=this.handlers;return u(k(t),(function(t){e?n[t]&&n[t].splice(D(n[t],e),1):delete n[t]})),this}},emit:function(t,e){this.options.domEvents&&function(t,e){var n=i.createEvent("Event");n.initEvent(t,!0,!0),n.gesture=e,e.target.dispatchEvent(n)}(t,e);var n=this.handlers[t]&&this.handlers[t].slice();if(n&&n.length){e.type=t,e.preventDefault=function(){e.srcEvent.preventDefault()};for(var o=0;o<n.length;)n[o](e),o++}},destroy:function(){this.element&&ee(this,!1),this.handlers={},this.session={},this.input.destroy(),this.element=null}},a(Jt,{INPUT_START:G,INPUT_MOVE:2,INPUT_END:Q,INPUT_CANCEL:z,STATE_POSSIBLE:_t,STATE_BEGAN:2,STATE_CHANGED:4,STATE_ENDED:8,STATE_RECOGNIZED:8,STATE_CANCELLED:16,STATE_FAILED:Vt,DIRECTION_NONE:U,DIRECTION_LEFT:F,DIRECTION_RIGHT:Z,DIRECTION_UP:W,DIRECTION_DOWN:H,DIRECTION_HORIZONTAL:X,DIRECTION_VERTICAL:q,DIRECTION_ALL:K,Manager:te,Input:et,TouchAction:Lt,TouchInput:yt,MouseInput:It,PointerEventInput:ut,TouchMouseInput:Et,SingleTouchInput:ft,Recognizer:Gt,AttrRecognizer:Ft,Tap:Kt,Pan:Zt,Swipe:qt,Pinch:Wt,Rotate:Xt,Press:Ht,on:x,off:E,each:u,merge:v,extend:f,assign:a,inherit:$,bindFn:b,prefixed:M}),(void 0!==s?s:"undefined"!=typeof self?self:{}).Hammer=Jt,(o=function(){return Jt}.call(e,n,e,t))===r||(t.exports=o)}(window,document)},1741:function(t,e){"use strict";var n=Object.prototype.hasOwnProperty;function o(t){try{return decodeURIComponent(t.replace(/\+/g," "))}catch(t){return null}}function s(t){try{return encodeURIComponent(t)}catch(t){return null}}e.stringify=function(t,e){e=e||"";var o,i,g=[];for(i in"string"!=typeof e&&(e="?"),t)if(n.call(t,i)){if((o=t[i])||null!=o&&!isNaN(o)||(o=""),i=s(i),o=s(o),null===i||null===o)continue;g.push(i+"="+o)}return g.length?e+g.join("&"):""},e.parse=function(t){for(var e,n=/([^=?#&]+)=?([^&]*)/g,s={};e=n.exec(t);){var i=o(e[1]),g=o(e[2]);null===i||null===g||i in s||(s[i]=g)}return s}},2980:function(t){"use strict";t.exports=function(t,e){if(e=e.split(":")[0],!(t=+t))return!1;switch(e){case"http":case"ws":return 80!==t;case"https":case"wss":return 443!==t;case"ftp":return 21!==t;case"gopher":return 70!==t;case"file":return!1}return 0!==t}},1760:function(t,e,n){"use strict";var o=n(2980),s=n(1741),i=/^[\x00-\x20\u00a0\u1680\u2000-\u200a\u2028\u2029\u202f\u205f\u3000\ufeff]+/,g=/[\n\r\t]/g,r=/^[A-Za-z][A-Za-z0-9+-.]*:\/\//,a=/:\d+$/,A=/^([a-z][a-z0-9.+-]*:)?(\/\/)?([\\/]+)?([\S\s]*)/i,l=/^[a-zA-Z]:/;function C(t){return(t||"").toString().replace(i,"")}var I=[["#","hash"],["?","query"],function(t,e){return h(e.protocol)?t.replace(/\\/g,"/"):t},["/","pathname"],["@","auth",1],[NaN,"host",void 0,1,1],[/:(\d*)$/,"port",void 0,1],[NaN,"hostname",void 0,1,1]],c={hash:1,query:1};function p(t){var e,o=("undefined"!=typeof window?window:void 0!==n.g?n.g:"undefined"!=typeof self?self:{}).location||{},s={},i=typeof(t=t||o);if("blob:"===t.protocol)s=new u(unescape(t.pathname),{});else if("string"===i)for(e in s=new u(t,{}),c)delete s[e];else if("object"===i){for(e in t)e in c||(s[e]=t[e]);void 0===s.slashes&&(s.slashes=r.test(t.href))}return s}function h(t){return"file:"===t||"ftp:"===t||"http:"===t||"https:"===t||"ws:"===t||"wss:"===t}function d(t,e){t=(t=C(t)).replace(g,""),e=e||{};var n,o=A.exec(t),s=o[1]?o[1].toLowerCase():"",i=!!o[2],r=!!o[3],a=0;return i?r?(n=o[2]+o[3]+o[4],a=o[2].length+o[3].length):(n=o[2]+o[4],a=o[2].length):r?(n=o[3]+o[4],a=o[3].length):n=o[4],"file:"===s?a>=2&&(n=n.slice(2)):h(s)?n=o[4]:s?i&&(n=n.slice(2)):a>=2&&h(e.protocol)&&(n=o[4]),{protocol:s,slashes:i||h(s),slashesCount:a,rest:n}}function u(t,e,n){if(t=(t=C(t)).replace(g,""),!(this instanceof u))return new u(t,e,n);var i,r,a,A,c,m,f=I.slice(),v=typeof e,$=this,b=0;for("object"!==v&&"string"!==v&&(n=e,e=null),n&&"function"!=typeof n&&(n=s.parse),i=!(r=d(t||"",e=p(e))).protocol&&!r.slashes,$.slashes=r.slashes||i&&e.slashes,$.protocol=r.protocol||e.protocol||"",t=r.rest,("file:"===r.protocol&&(2!==r.slashesCount||l.test(t))||!r.slashes&&(r.protocol||r.slashesCount<2||!h($.protocol)))&&(f[3]=[/(.*)/,"pathname"]);b<f.length;b++)"function"!=typeof(A=f[b])?(a=A[0],m=A[1],a!=a?$[m]=t:"string"==typeof a?~(c="@"===a?t.lastIndexOf(a):t.indexOf(a))&&("number"==typeof A[2]?($[m]=t.slice(0,c),t=t.slice(c+A[2])):($[m]=t.slice(c),t=t.slice(0,c))):(c=a.exec(t))&&($[m]=c[1],t=t.slice(0,c.index)),$[m]=$[m]||i&&A[3]&&e[m]||"",A[4]&&($[m]=$[m].toLowerCase())):t=A(t,$);n&&($.query=n($.query)),i&&e.slashes&&"/"!==$.pathname.charAt(0)&&(""!==$.pathname||""!==e.pathname)&&($.pathname=function(t,e){if(""===t)return e;for(var n=(e||"/").split("/").slice(0,-1).concat(t.split("/")),o=n.length,s=n[o-1],i=!1,g=0;o--;)"."===n[o]?n.splice(o,1):".."===n[o]?(n.splice(o,1),g++):g&&(0===o&&(i=!0),n.splice(o,1),g--);return i&&n.unshift(""),"."!==s&&".."!==s||n.push(""),n.join("/")}($.pathname,e.pathname)),"/"!==$.pathname.charAt(0)&&h($.protocol)&&($.pathname="/"+$.pathname),o($.port,$.protocol)||($.host=$.hostname,$.port=""),$.username=$.password="",$.auth&&(~(c=$.auth.indexOf(":"))?($.username=$.auth.slice(0,c),$.username=encodeURIComponent(decodeURIComponent($.username)),$.password=$.auth.slice(c+1),$.password=encodeURIComponent(decodeURIComponent($.password))):$.username=encodeURIComponent(decodeURIComponent($.auth)),$.auth=$.password?$.username+":"+$.password:$.username),$.origin="file:"!==$.protocol&&h($.protocol)&&$.host?$.protocol+"//"+$.host:"null",$.href=$.toString()}u.prototype={set:function(t,e,n){var i=this;switch(t){case"query":"string"==typeof e&&e.length&&(e=(n||s.parse)(e)),i[t]=e;break;case"port":i[t]=e,o(e,i.protocol)?e&&(i.host=i.hostname+":"+e):(i.host=i.hostname,i[t]="");break;case"hostname":i[t]=e,i.port&&(e+=":"+i.port),i.host=e;break;case"host":i[t]=e,a.test(e)?(e=e.split(":"),i.port=e.pop(),i.hostname=e.join(":")):(i.hostname=e,i.port="");break;case"protocol":i.protocol=e.toLowerCase(),i.slashes=!n;break;case"pathname":case"hash":if(e){var g="pathname"===t?"/":"#";i[t]=e.charAt(0)!==g?g+e:e}else i[t]=e;break;case"username":case"password":i[t]=encodeURIComponent(e);break;case"auth":var r=e.indexOf(":");~r?(i.username=e.slice(0,r),i.username=encodeURIComponent(decodeURIComponent(i.username)),i.password=e.slice(r+1),i.password=encodeURIComponent(decodeURIComponent(i.password))):i.username=encodeURIComponent(decodeURIComponent(e))}for(var A=0;A<I.length;A++){var l=I[A];l[4]&&(i[l[1]]=i[l[1]].toLowerCase())}return i.auth=i.password?i.username+":"+i.password:i.username,i.origin="file:"!==i.protocol&&h(i.protocol)&&i.host?i.protocol+"//"+i.host:"null",i.href=i.toString(),i},toString:function(t){t&&"function"==typeof t||(t=s.stringify);var e,n=this,o=n.host,i=n.protocol;i&&":"!==i.charAt(i.length-1)&&(i+=":");var g=i+(n.protocol&&n.slashes||h(n.protocol)?"//":"");return n.username?(g+=n.username,n.password&&(g+=":"+n.password),g+="@"):n.password?(g+=":"+n.password,g+="@"):"file:"!==n.protocol&&h(n.protocol)&&!o&&"/"!==n.pathname&&(g+="@"),(":"===o[o.length-1]||a.test(n.hostname)&&!n.port)&&(o+=":"),g+=o+n.pathname,(e="object"==typeof n.query?t(n.query):n.query)&&(g+="?"!==e.charAt(0)?"?"+e:e),n.hash&&(g+=n.hash),g}},u.extractProtocol=d,u.location=p,u.trimLeft=C,u.qs=s,t.exports=u},2391:function(t,e,n){var o={"./components/add-shortcode/add-shortcode.html":6191,"./components/app-actions/app-actions.html":1859,"./components/app-sidebar/app-sidebar.html":847,"./components/app-tools/app-move-tool/app-move-tool.template.html":3397,"./components/app-tools/app-resize-tool/app-resize-tool.template.html":5861,"./components/app-tools/app-tools.html":9335,"./components/context-menu/context-menu.template.html":2103,"./components/shortcode-hierarchy-list-item/shortcode-hierarchy-list-item.html":679,"./components/ux-option/types/checkbox.html":435,"./components/ux-option/types/col-slider.html":642,"./components/ux-option/types/colorpicker.html":6549,"./components/ux-option/types/file.html":5044,"./components/ux-option/types/gallery.html":5402,"./components/ux-option/types/group.html":7995,"./components/ux-option/types/image.html":2381,"./components/ux-option/types/margins.html":6499,"./components/ux-option/types/radio-buttons.html":6147,"./components/ux-option/types/radio-images.html":6714,"./components/ux-option/types/scrubfield.html":3631,"./components/ux-option/types/select.html":7486,"./components/ux-option/types/slider.html":1619,"./components/ux-option/types/text-editor.html":3055,"./components/ux-option/types/textarea.html":8908,"./components/ux-option/types/textfield.html":2013,"./components/ux-option/types/title.html":1054,"./components/ux-option/types/urlfield.html":1517,"./routes/home/<USER>":7009,"./routes/settings/settings.html":9369,"./routes/shortcode/shortcode.html":705,"./shortcodes/_loading.html":3833};function s(t){var e=i(t);return n(e)}function i(t){if(!n.o(o,t)){var e=new Error("Cannot find module '"+t+"'");throw e.code="MODULE_NOT_FOUND",e}return o[t]}s.keys=function(){return Object.keys(o)},s.resolve=i,t.exports=s,s.id=2391},6191:function(t){"use strict";t.exports='<p ng-if="$ctrl.errorMessage" class="error error-message">\n  {{ $ctrl.errorMessage }}\n</p>\n\n<div class="add-shortcode-selector">\n  <div class="add-shortcode-header">\n    <h2 class="title">Add Content</h2>\n    <nav class="add-shortcode-types">\n      <button type="button"\n        ng-click="$ctrl.showType(\'shortcodes\')"\n        ng-class="{ active: $ctrl.type === \'shortcodes\' }">\n        Elements\n      </button>\n      <button type="button"\n        ng-if="$ctrl.shortcode.isRoot"\n        ng-click="$ctrl.showType(\'import\')"\n        ng-class="{ active: $ctrl.type === \'import\' }">\n        Import\n      </button>\n    </nav>\n  </div>\n\n  <div class="add-shortcode-items" ng-if="$ctrl.type === \'shortcodes\'">\n\n    <div class="flatsome-studio-button" ng-if="$ctrl.flatsomeStudioIsActive">\n      <button type="button" class="wp-style alt button-large button-block"\n        ng-click="$ctrl.showFlatsomeStudio()">\n        <span class="dashicons dashicons-screenoptions"></span> Flatsome Studio\n      </button>\n      <hr />\n    </div>\n\n    <input class="filter-elements" type="text" placeholder="Search&hellip;" ng-model="$ctrl.filter.name">\n\n    <div class="add-shortcode-category"\n      ng-repeat="category in $ctrl.items"\n      ng-show="items.length">\n      <h3>{{:: category.name }}</h3>\n      <ul>\n        <li class="add-shortcode-box" ng-repeat="item in items = (category.items | filter: $ctrl.filter)">\n          <button class="add-shortcode-box-button" type="button" ng-class="{ \'is-loading\': $ctrl.isLoading === item.tag }" ng-click="$ctrl.add(item, 0)">\n            <img ng-if="item.thumbnail" ng-src="{{:: item.thumbnail }}" alt="{{:: item.name }}"/>\n            <div ng-if="$ctrl.isLoading === item.tag" class="add-shortcode-loading-spinner loading-spinner is-visible"></div>\n            <span class="title">{{:: item.name }}</span>\n          </button>\n        </li>\n      </ul>\n    </div>\n  </div>\n</div>\n\n<template-importer ng-if="$ctrl.type === \'import\'"></template-importer>\n\n<div class="add-shortcode-presets">\n  <h3>Presets</h3>\n  <ul ng-if="$ctrl.presets">\n    <li class="add-shortcode-box" ng-repeat="preset in $ctrl.presets">\n      <button type="button" class="add-shortcode-box-button"\n        title="{{:: preset.name }}"\n        ng-class="{ \'with-thumbnail\' : !!preset.thumbnail, \'active\' : $ctrl.currentPreset === $index }"\n        ng-click="$ctrl.usePreset(preset.content); $ctrl.currentPreset = $index">\n        <div ng-if="preset.custom" class="add-shortcode-icon">\n          <svg width="36" height="36" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M7.73009 2.41274L8.95709 3.63719L3.40181 9.18095L2.17482 7.95652L7.73009 2.41274ZM7.73009 0.242432L0 7.95652L3.40181 11.3513L11.1319 3.63719L7.73009 0.242432Z" fill="#007CBA"/> <path d="M7.8196 11.3114L8.95987 12.4493L7.8196 13.5873L6.67928 12.4493L7.8196 11.3114ZM7.8196 9.14111L4.50439 12.4492L7.8196 15.7575L11.1348 12.4492L7.8196 9.14087V9.14111Z" fill="#007CBA"/> <path d="M12.2322 6.90786L13.3725 8.0458L12.2322 9.18369L11.0921 8.04584L12.2323 6.90795L12.2322 6.90786ZM12.2323 4.73763L8.91699 8.04584L12.2322 11.3542L15.5474 8.04584L12.2322 4.73755L12.2323 4.73763Z" fill="#007CBA" fill-opacity="0.6"/> </svg>\n        </div>\n        <img ng-if="!preset.custom && preset.thumbnail" ng-src="{{:: preset.thumbnail }}" alt="{{:: preset.name }}">\n        <span class="title">{{:: preset.name }}</span>\n      </button>\n      <div ng-if="preset.custom === true" class="add-shortcode-actions">\n        <button class="blank" ng-click="$ctrl.templates.updatePreset(preset)">\n          <span class="dashicons dashicons-edit"></span>\n        </button>\n        <button class="blank" ng-click="$ctrl.removePreset(preset)">\n          <span class="dashicons dashicons-trash"></span>\n        </button>\n      </div>\n    </li>\n  </ul>\n  <button\n    type="button"\n    class="wp-style alt button-large button-block"\n    ng-click="$ctrl.stack.close()"\n    ng-if="$ctrl.presets">\n    Apply\n  </button>\n</div>\n'},1859:function(t){"use strict";t.exports='<button\n  title="Undo"\n  type="button"\n  class="blank has-tooltip"\n  ng-click="$ctrl.undo()"\n  ng-disabled="$ctrl.canUndo() === false">\n  <span class="dashicons dashicons-undo"></span>\n  <div class="uxb-tooltip">Undo</div>\n</button>\n\n<button\n  title="Redo"\n  type="button"\n  class="blank has-tooltip"\n  ng-click="$ctrl.redo()"\n  ng-disabled="$ctrl.canRedo() === false">\n  <span class="dashicons dashicons-redo"></span>\n  <div class="uxb-tooltip">Redo</div>\n</button>\n\n<hr/>\n\n<button type="button"\n    class="blank has-tooltip"\n    title="{{:: breakpoint.title }}"\n    ng-click="$ctrl.setBreakpoint($index)"\n    ng-class="{ \'active\' : $ctrl.isActiveBreakpont($index) }"\n    ng-repeat="(name, breakpoint) in $ctrl.breakpoints.all track by breakpoint.width">\n    <span class="{{:: breakpoint.icon }}"></span>\n    <div class="uxb-tooltip">{{:: breakpoint.title }}</div>\n    <div class="has-breakpoint-values" ng-if="$ctrl.hasBreakpointValues($index)"></div>\n</button>\n\n<hr/>\n\n<button type="button"\n    class="blank has-tooltip"\n    title="{{:: action.tooltip }}"\n    ng-click="$ctrl.doAction(action)"\n    ng-repeat="action in $ctrl.actions">\n    <span class="{{:: action.icon }}"></span>\n    <div class="uxb-tooltip">{{:: action.tooltip }}</div>\n</button>\n'},847:function(t){"use strict";t.exports='<app-sidebar-main class="animate-{{ $ctrl.routeAnimation }}">\n\n  <div class="app-sidebar-top title-row">\n  \t<div class="title-row-icon">\n\t  \t<button type="button"\n        title="Exit Builder"\n        class="blank" ng-click="$ctrl.app.exit()"\n        ng-disabled="$ctrl.permissions.exit === false">\n\t   \t \t<span class="dashicons dashicons-no-alt"></span>\n\t\t  </button>\n  \t</div>\n  \t<div class="title-row-title">\n      {{ $ctrl.store.post.attributes.values.post_title }}\n    </div>\n  \t<div class="title-row-actions">\n  \t\t<button type="button" class="blank" ng-click="$ctrl.app.goto(\'/settings\')">\n\t      <span class="dashicons dashicons-admin-generic"></span>\n\t    </button>\n  \t</div>\n  </div>\n\n  <div class="app-sidebar-view">\n    <home-view ng-if="$ctrl.viewName === \'home\'"></home-view>\n    <settings-view ng-if="$ctrl.viewName === \'settings\'"></settings-view>\n    <shortcode-view ng-if="$ctrl.viewName === \'shortcode\'" shortcode="$ctrl.viewProps.shortcode"></shortcode-view>\n  </div>\n\n  <div class="app-sidebar-footer">\n    <button type="button" class="blank app-sidebar-toggle" ng-click="$ctrl.toggle()">\n      <span class="dashicons dashicons-arrow-left-alt2"\n        ng-if="$ctrl.store.showSidebar === true"\n      ></span>\n      <span class="dashicons dashicons-arrow-right-alt2"\n        ng-if="$ctrl.store.showSidebar === false"\n      ></span>\n    </button>\n  </div>\n\n</app-sidebar-main>\n'},3397:function(t){"use strict";t.exports='<div class="uxb-move">\n  <div class="uxb-move-handle"\n    ng-if="$ctrl.shortcode"\n    ng-class="$ctrl.classNames($ctrl.shortcode)"\n    draggable-shortcode="$ctrl.shortcode">\n    <span class="uxb-move-icon dashicons dashicons-move"></span>\n  </div>\n</div>\n'},5861:function(t){"use strict";t.exports='<div class="uxb-resize uxb-resize-top"></div>\n<div class="uxb-resize uxb-resize-right"></div>\n<div class="uxb-resize uxb-resize-bottom"></div>\n<div class="uxb-resize uxb-resize-left"></div>\n'},9335:function(t){"use strict";t.exports='<app-outline-tool class="auto-size" shortcode="$ctrl.outlined"></app-outline-tool>\n<app-resize-tool class="auto-size" shortcode="$ctrl.outlined"></app-resize-tool>\n<app-move-tool class="auto-size" shortcode="$ctrl.outlined"></app-move-tool>\n<app-select-tool class="auto-size" shortcode="$ctrl.selected"></app-select-tool>\n<add-buttons></add-buttons>\n\n<div class="tools-addable">\n  <div class="line"></div>\n  <add-button\n    class="button"\n    index="$ctrl.index"\n    shortcode="$ctrl.addable">\n  </add-button>\n</div>\n'},2103:function(t){"use strict";t.exports='<div class="context-menu-menu" ng-click="$ctrl.hide()">\n  <shortcode-actions shortcode="$ctrl.shortcode"></shortcode-actions>\n</div>\n'},679:function(t){"use strict";t.exports='<div class="hierarchy-title" ng-class="{ \'active\' : $ctrl.isActive(), [\'open\'] : $ctrl.shortcode.states.open, [\'visibility-\'+$ctrl.shortcode.options.visibility] : $ctrl.shortcode.options.visibility }">\n\n    <button type="button" class="hierarchy-toggle"\n        ng-if="$ctrl.shortcode.children"\n        ng-click="$ctrl.toggleChildren()">\n    </button>\n\n    <div class="hierarchy-content"\n        ng-click="$ctrl.selectShortcode()"\n        ng-mouseover="$ctrl.outlineShortcode()"\n        ng-dblclick="$ctrl.configureShortcode()">\n\n        <span class="hierarchy-name">{{:: $ctrl.shortcode.data.name }}</span>\n\n        <span class="hierarchy-info" ng-bind="$ctrl.getShortcodeInfo()"></span>\n    </div>\n\n    <div class="hierarchy-tools">\n      <button type="button blank" ng-click="$ctrl.showContextMenu($event)">\n        <span class="dashicons dashicons-admin-generic"></span>\n      </button>\n    </div>\n</div>\n\n<shortcode-hierarchy-list\n  ng-if="$ctrl.shortcode.states.open"\n  ng-class="{ \'open\': $ctrl.shortcode.states.open }"\n  shortcode="$ctrl.shortcode">\n</shortcode-hierarchy-list>\n'},435:function(t){"use strict";t.exports='<label>\n  <input type="checkbox"\n    ng-model="$ctrl.model"\n    ng-true-value="\'true\'"\n    ng-false-value="\'0\'">\n  <span></span>\n</label>\n'},642:function(t){"use strict";t.exports='<div class="col-slider-wrap col-slider-cols-{{ $ctrl.model }}">\n\n\t<table class="col-slider-table">\n\t\t<tbody>\n\t\t\t<tr>\n\t\t\t\t<td ng-repeat="n in _.range($ctrl.option.min, $ctrl.option.max + 1) track by $index">{{:: n }}</td>\n\t\t\t</tr>\n\t\t</tbody>\n\t</table>\n\n\t<input type="range"\n\t\tclass="col-slider-input"\n    min="{{:: $ctrl.option.min }}"\n    max="{{:: $ctrl.option.max }}"\n    ng-model="$ctrl.model">\n</div>\n'},6549:function(t){"use strict";t.exports='<ux-option-colorpicker\n  option="$ctrl.option"\n  model="$ctrl.model"\n></ux-option-colorpicker>\n\n<div ng-if="$ctrl.option.helpers" class="option-helpers option-helpers-colors">\n  <a href="javascript:"\n    title="Remove"\n    ng-click="$ctrl.model = null">\n    <span class="dashicons dashicons-no-alt"></span>\n  </a>\n  <a href="javascript:"\n    title="{{:: value.title }}"\n    style="background-color: {{:: value.value }}"\n    ng-repeat="(key, value) in $ctrl.option.helpers"\n    ng-click="$ctrl.model = value.value">\n  </a>\n</div>\n'},5044:function(t){"use strict";t.exports='<ux-option-file\n  option="$ctrl.option"\n  model="$ctrl.model"\n></ux-option-file>\n'},5402:function(t){"use strict";t.exports='<ux-option-gallery\n  option="$ctrl.option"\n  model="$ctrl.model"\n></ux-option-gallery>\n'},7995:function(t){"use strict";t.exports='<ux-option-group\n  option="$ctrl.option"\n  shortcode="$ctrl.shortcode"\n  responsive="$ctrl.$optionsCtrl.responsive"\n  model="$ctrl.$optionsCtrl.model"\n></ux-option-group>\n'},2381:function(t){"use strict";t.exports='<ux-option-image\n  option="$ctrl.option"\n  model="$ctrl.model"\n></ux-option-image>\n'},6499:function(t){"use strict";t.exports='<ux-option-margins\n  option="$ctrl.option"\n  model="$ctrl.model"\n></ux-option-margins>\n'},6147:function(t){"use strict";t.exports='<div class="option-button-group">\n\t<label ng-repeat="(value, data) in $ctrl.option.options"\n\t    ng-class="{ \'active\' : $ctrl.model === value }">\n\t    <input type="radio" value="{{:: value }}" ng-model="$ctrl.model"/>\n\t    <button type="button blank" title="{{:: data.title }}">\n\t    \t<span ng-if="data.icon" class="dashicons {{:: data.icon }}"></span>\n\t    \t<span ng-if="!data.icon"> {{:: data.title }}</span>\n\t    </button>\n\t</label>\n</div>\n'},6714:function(t){"use strict";t.exports='<label ng-repeat="(key, value) in $ctrl.option.options"\n    ng-class="{ \'active\' : $ctrl.model === key }">\n    <input type="radio" value="{{:: key }}" ng-model="$ctrl.model">\n    <img src="{{:: value.image }}" alt="{{:: value.title }}" title="{{:: value.title }}">\n</label>\n'},3631:function(t){"use strict";t.exports='<input type="text"\n  class="scrubfield"\n\tscrubfield="$ctrl.option"\n\tng-model="$ctrl.model"\n\tplaceholder="{{:: $ctrl.option.default }}"\n\tng-model-options="{\n        \'updateOn\': \'blur default\'\n    }">\n<div ng-if="$ctrl.option.helpers" class="option-helpers">\n\t<a \tng-repeat="(key, value) in $ctrl.option.helpers" href="javascript:"\n\t\tng-click="$ctrl.model = value.value">\n\t\t{{:: value.title }}\n\t</a>\n</div>\n'},7486:function(t){"use strict";t.exports='<ux-option-select\n  option="$ctrl.option"\n  value="$ctrl.model"\n></ux-option-select>\n'},1619:function(t){"use strict";t.exports='<div class="slider-wrap">\n\n  <input type="range"\n    ng-attr-min="{{:: $ctrl.option.min }}"\n    ng-attr-max="{{:: $ctrl.option.max }}"\n    ng-attr-step="{{:: $ctrl.option.step }}"\n    ng-attr-value="{{:: $ctrl.model }}"\n    ng-model="$ctrl.model"\n    ng-model-options="{ updateOn: \'input\' }"\n  >\n\n  <input type="number" to-number\n    ng-attr-min="{{:: $ctrl.option.min }}"\n    ng-attr-max="{{:: $ctrl.option.max }}"\n    ng-model="$ctrl.model">\n\n  <span class="slider-unit">{{:: $ctrl.option.unit }}</span>\n\n</div>\n'},3055:function(t){"use strict";t.exports='<ux-option-editor\n  option="$ctrl.option"\n  model="$ctrl.model"\n></ux-option-editor>\n'},8908:function(t){"use strict";t.exports='  <textarea\n    placeholder="{{::$ctrl.option.placeholder }}"\n    ng-model="$ctrl.model"\n    ng-model-options="{\n        updateOn: \'blur default\',\n        debounce: {\n            blur : 0,\n            default: 10\n        }\n    }"></textarea>\n'},2013:function(t){"use strict";t.exports='  <input type="text"\n    placeholder="{{::$ctrl.option.placeholder }}"\n    ng-model="$ctrl.model"\n    ng-model-options="{\n        updateOn: \'blur default\',\n        debounce: {\n            blur : 0,\n            default: 10\n        }\n    }">\n'},1054:function(t){"use strict";t.exports="{{:: $ctrl.option.heading }}\n"},1517:function(t){"use strict";t.exports='<ux-option-urlfield option="$ctrl.option"></ux-option-urlfield>\n'},7009:function(t){"use strict";t.exports='<app-sidebar-view class="home-view">\n\n  <view-header>\n\n  </view-header>\n\n  <view-body ng-if="$ctrl.store.postContent">\n    <shortcode-hierarchy-list shortcode="$ctrl.store.postContent"></shortcode-hierarchy-list>\n  </view-body>\n\n  <view-footer>\n    <button id="app-draft-button" type="button"\n      class="wp-style button-large button-block"\n      ng-if="$ctrl.store.post.status === \'draft\' || $ctrl.store.post.status === \'auto-draft\'"\n      ng-class="{ \'loading\': $ctrl.store.isSaving === \'draft\' }"\n      ng-disabled="$ctrl.permissions.save === false"\n      ng-click="$ctrl.save(\'draft\')">\n      Save Draft\n    </button>\n    <button id="app-private-button" type="button"\n      class="wp-style button-large button-block"\n      ng-if="$ctrl.store.post.status === \'private\'"\n      ng-class="{ \'loading\': $ctrl.store.isSaving === \'private\' }"\n      ng-disabled="$ctrl.permissions.save === false"\n      ng-click="$ctrl.save(\'private\')">\n      Save Private\n    </button>\n    <button id="app-save-button" type="button"\n      class="wp-style alt button-large button-block"\n      ng-class="{ \'loading\': $ctrl.store.isSaving && $ctrl.store.isSaving !== \'draft\' && $ctrl.store.isSaving !== \'private\'}"\n      ng-disabled="$ctrl.permissions.save === false"\n      ng-click="$ctrl.save()">\n      {{ $ctrl.saveButtonText }}\n    </button>\n    <button id="app-save-button" type="button"\n      class="wp-style button-large button-block button-exit animate-fade-in-right"\n      ng-if="$ctrl.store.isSaved"\n      ng-click="$ctrl.app.exit()">\n      &times;\n    </button>\n  </view-footer>\n\n</app-sidebar-view>\n'},9369:function(t){"use strict";t.exports='<app-sidebar-view class="settings-view">\n\n  <view-header>\n    <div class="title-row">\n      <div class="title-row-icon">\n          <button class="button-reset view-header-title" type="button" ng-click="$ctrl.   exit()">\n               <span class="dashicons dashicons-arrow-left-alt2"></span>\n          </button>\n      </div>\n      <div class="title-row-title"> Post settings </div>\n    </div>\n  </view-header>\n\n  <view-body>\n    <ux-options\n      options="$ctrl.post.attributes.options.tree"\n      model="$ctrl.post.attributes.values">\n    </ux-options>\n    <ux-options\n      options="$ctrl.post.meta.options.tree"\n      model="$ctrl.post.meta.values">\n    </ux-options>\n    <div class="box">\n      <h3 class="box-title">Actions</h3>\n      <div class="box-content">\n        <button class="wp-style button-block" ng-click="$ctrl.saveAsTemplate()">Save as template&hellip;</button>\n        <div style="padding: 5px 0;" />\n        <button class="wp-style danger button-block" ng-click="$ctrl.clearContent()">Clear content&hellip;</button>\n      </div>\n    </div>\n  </view-body>\n\n  <view-footer>\n    <button type="button" class="wp-style blank" ng-click="$ctrl.discard()">\n      <span class="dashicons dashicons-no-alt"></span>Discard\n    </button>\n    <button type="button" class="wp-style" ng-click="$ctrl.exit()">\n      <span class="dashicons dashicons-yes"></span>Apply\n    </button>\n  </view-footer>\n\n</app-sidebar-view>\n'},705:function(t){"use strict";t.exports='<app-sidebar-view class="shortcode-view">\n\n  <view-header>\n    <div class="title-row">\n      <div class="title-row-icon">\n         <button class="button-reset view-header-title" type="button" ng-click="$ctrl.exit(\'/\')">\n             <span class="dashicons dashicons-arrow-left-alt2"></span>\n         </button>\n      </div>\n      <div class="title-row-title"> {{ $ctrl.shortcode.data.name }} </div>\n      <div class="title-row-actions"></div>\n    </div>\n  </view-header>\n\n  <view-body>\n    <ux-options\n      ng-repeat="_shortcode in $ctrl.shortcodes track by _shortcode.$id"\n      options="$ctrl.options"\n      shortcode="$ctrl.shortcode"\n      responsive="$ctrl.responsiveValues"\n      model="$ctrl.shortcode.options">\n    </ux-options>\n  </view-body>\n\n  <view-footer>\n    <button type="button" class="wp-style outline" ng-click="$ctrl.discard()">\n      Discard\n    </button>\n    <button type="button" class="wp-style" ng-click="$ctrl.exit()">\n      Apply\n    </button>\n  </view-footer>\n\n</app-sidebar-view>\n'},3833:function(t){"use strict";t.exports='<div class="uxb-template-loading">Loading&hellip;</div>\n'}},e={};function n(o){var s=e[o];if(void 0!==s)return s.exports;var i=e[o]={exports:{}};return t[o](i,i.exports,n),i.exports}n.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return n.d(e,{a:e}),e},n.d=function(t,e){for(var o in e)n.o(e,o)&&!n.o(t,o)&&Object.defineProperty(t,o,{enumerable:!0,get:e[o]})},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),n.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)},function(){"use strict";function t(t,e,n,o,s){t.digestTtl(20),n.debugEnabled("dev"===window.location.hostname.split(".").pop()),s.debugInfoEnabled(!1),e.defaults.headers.common["X-Requested-With"]="XMLHttpRequest",o.decorator("$rootScope",["$delegate",function(t){return Object.defineProperty(t.constructor.prototype,"_",{value:_,enumerable:!1}),t}])}n(269),n(8484),t.$inject=["$rootScopeProvider","$httpProvider","$logProvider","$provide","$compileProvider"],o.$inject=["$rootScope","$window","AppEvent"];var e=null;function o(t,e,n){var o=!0;t.$watch((()=>{o&&e.postCustomMessage(n.APPLY)})),e.addEventListener(n.APPLY,(()=>{o=!1,null===t.$$phase&&t.$apply(),o=!0}),!1),e.addEventListener(n.BROADCAST,(e=>{t.$broadcast(e.data.type,e.data.data)}),!1),e.addEventListener(n.EMIT,(e=>{t.$emit(e.data.type,e.data.data)}),!1)}function s(t,e,n,o){t.on("keydown",(t=>{const s=t.metaKey||t.ctrlKey,i=90===t.keyCode,g=27===t.keyCode,r=t.shiftKey;g&&n.stack?(t.preventDefault(),n.stack.close(),e.apply()):s&&i&&(r?o.redo():o.undo(),e.apply(),t.preventDefault())}))}n.g.postCustomMessage=function(t,n,o){e=e||(self===top?document.querySelectorAll(".iframe-frame")[0].contentWindow:window.parent),(o=new CustomEvent(t)).data=n,e.dispatchEvent(o)},s.$inject=["$document","app","store","history"],r.$inject=["$parse"];const i=(window.parent||window).uxBuilderData,g=50;function r(t){return i.$set=function(e,n){return t(a(e)).assign(i,n)},i.$get=function(e,n){var o=t(a(e))(i);return!angular.isDefined(o)&&n?i.$set(e,n):o},i.$unset=function(e){var n;return e.indexOf("*")?(n=e.split("*")[0],t(a(n)).assign(i,null)):t(a(e)).assign(i,null)},i.$disable=function(){i.enabled=!1},i.$enable=function(){i.enabled=!0},i.$addAction=function(t,e){if(!i.enabled)return;i.history.splice(0,i.history.length-(g-1)),i.currentAction<i.history.length-1&&i.history.splice(i.currentAction+1,i.history.length);const n=i.history.slice().pop();n&&n.payload.key===e.key?!1!==e.override&&(n.payload=e):i.currentAction=i.history.push({type:t,payload:e})-1},i.$resetToAction=function(t){i.history.splice(t+1,i.history.length),i.currentAction=Math.min(t,i.currentAction)},i}function a(t){var e=[],n=t.split(".");return _.each(n,(function(t){e.push(jQuery.camelCase(t.replace(/:/g,"-")))})),e.join(".")}const A=new class{controller(t,e){i.shortcodes.hasOwnProperty(t)&&(i.shortcodes[t].controller=e)}on(t,e){i.$$events[t]=i.$$events[t]||[],i.$$events[t].push(e)}addfilter(t,e){i.$$filters[t]=i.$$filters[t]||[],i.$$filters[t].push(e)}addAction(t){i.actions.push(t)}},l=angular.module("uxBuilder",[]);var C=l;function I(t){return{restrict:"A",controllerAs:"draggableShortcode",bindToController:{shortcode:"=draggableShortcode",options:"=draggableOptions",element:"=draggableElement"},controller:["$scope","$element","targets",function(e,n,o){var s=this,i=null;e.$watch("draggableShortcode.shortcode",(function(g){i&&i.destroy(),i=t(s.shortcode,s.element||n.get(0),s.options),s.options&&s.options.targets&&s.options.targets.map((function(t){angular.isObject(t)?(t.shortcode=s.shortcode,t.element=n,o.add(t.name,t)):o.add(t,{shortcode:s.shortcode,element:n})})),e.$on("$destroy",(function(){i.destroy(),o.removeElement(n.get(0))}))}))}]}}function c(t){return{restrict:"A",controllerAs:"attachment",bindToController:!0,scope:{id:"=wpAttachment",size:"=wpAttachmentSize",model:"=wpAttachmentModel",width:"@wpAttachmentWidth",height:"@wpAttachmentHeight"},controller:["app","store","$scope","$element",function(t,e,n,o){var s=!1;function i(){var t=n.attachment.id,o=n.attachment.size||"full",s=n.attachment.width||0,i=n.attachment.height||0,r=`cache.attachment.image${s||i?`w${s}.h${i}`:o}.id${t}`,a=e.$get(r);if(angular.isDefined(a))return g(a);jQuery.getJSON(e.ajaxUrl,{action:"ux_builder_get_attachment",attachment_id:t,attachment_size:o,attachment_width:s,attachment_height:i}).done((function(t){t.success?g(e.$set(r,t.data)):window.self===window.top&&n.attachment.model&&(a=["data:image/png;base64,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","480px","270px"]),a&&g(a)})).fail((function(e){console.error("Failed to load attachment",t)}))}function g(t){"IMG"===o[0].tagName?(o.addClass("processing"),o.on("load",(function(){o.off("load").removeClass("processing")})),o.attr("src",t[0]),o.attr("width",t[1]),o.attr("height",t[2])):o.css("background-image","url("+t[0]+")"),s=!0}o.on("load.wpAttachment",(()=>t("tools").fixPositions())),n.$watch((()=>this.id),(function(t){return t&&""!==t?"string"==typeof t&&t.indexOf("/")>-1?g([t]):void i():("IMG"===o[0].tagName?(o.removeAttr("src"),o.removeAttr("width"),o.removeAttr("height")):o.css("background-image",""),void(s=!1))})),n.$watch((()=>this.size),(function(t,e){s&&t!==e&&i()})),n.$on("$destroy",(function(){o.off("load.wpAttachment")}))}]}}function p(t){return e=>t.trustAsHtml(e)}function h(t){return(e,n="html")=>t.trustAs(n,e)}l.config(t),l.run(o),l.run(s),l.factory("presetCache",["$cacheFactory",t=>t()]),C.constant("Event",{READY:"ready",CHANGE:"change",COMPLETE:"complete",ERROR:"error",SCROLL:"scroll",RESIZE:"resize"}),C.constant("AppEvent",{READY:"app-ready",APPLY:"app-apply",EMIT:"app-emit",BROADCAST:"app-broadcast"}),C.constant("IframeEvent",{READY:"iframe-ready",RELOAD:"iframe-reload",RESIZE:"iframe-resize",CHANGED:"iframe-changed",SCROLL:"iframe-scroll"}),C.constant("MouseEvent",{}),C.constant("TouchEvent",{}),C.constant("ShortcodeEvent",{CREATE:"shortcode-create",CREATED:"shortcode-created",ATTACHED:"shortcode-attached",CONFIGURE:"shortcode-configure",RECOMPILED:"shortcode-recompiled",ACTIVE:"shortcode-active",INACTIVE:"shortcode-inactive",CHANGED:"shortcode-changed",ADDED:"shortcode-added",MOVED:"shortcode-moved",DETACHED:"shortcode-detached",REMOVED:"shortcode-removed",MOUSEOVER:"shortocde-mouseover",MOUSEOUT:"shortocde-mouseout",CLICK:"shortcode-click",OUTLINED:"shortcode-outlined",SELECTED:"shortcode-selected",DUPLICATED:"shortcode-duplicated",DELETED:"shortcode-deleted"}),C.constant("ChildEvent",{ADDED:"child-added",REMOVED:"child-removed"}),C.constant("OptionsEvent",{SHOW:"options-show",CLEAR:"options-clear",HIDE:"options-hide"}),C.constant("MediaEvent",{CHANGED:"media-changed"}),C.constant("DragEvent",{PAN_START:"draggable-pan-start",PAN_MOVE:"draggable-pan-move",PAN_END:"draggable-pan-end",START:"draggable-start",MOVE:"draggable-move",END:"draggable-end"}),I.$inject=["draggable"],c.$inject=["app"],C.directive("draggableShortcode",I),C.directive("toNumber",(function(){return{require:"ngModel",link:function(t,e,n,o){o.$formatters.push((t=>{const e=parseFloat(t);return isNaN(e)?null:e}))}}})),C.directive("wpAttachment",c),p.$inject=["$sce"],h.$inject=["$sce"];var d=window.wp.autop;function u(){return t=>(t=t.replace(/<p>\s*<\/p>/g,"<p>&nbsp;</p>"),(t=(0,d.autop)(t)).replace(/<p>&nbsp;<\/p>/g,"<p> </p>"))}angular.module("app.filters",[]).filter("noDefault",(function(){return(t,e)=>t!==e.default&&t})).filter("heightCheck",(function(){return t=>"100%"===t?"100vh":t})).filter("html",p).filter("rgba",(function(){return function(t){let e=t;if(e.indexOf("#")>-1){let t=/^#?([a-f\d])([a-f\d])([a-f\d])$/i;e=e.replace(t,((t,e,n,o)=>e+e+n+n+o+o)),e=e.replace("#","");let n=parseInt(e.substring(0,2),16),o=parseInt(e.substring(2,4),16),s=parseInt(e.substring(4,6),16);e=`rgba(${n},${o},${s},0.3)`}return e}})).filter("trusted",h).filter("autop",u),n(6488),n(2496),n(8774);n.g.jQuery.fn.scrollToElement=function(t,e=450,o=0,s=null){const i=n.g.jQuery(t),g=i.get(0).ownerDocument;let r=g.defaultView.innerHeight,a=g.body.getBoundingClientRect(),A=i.get(0).getBoundingClientRect(),l=A.top-a.top+A.height/2+o;A.height<r?l-=r/2:A.height>r&&(l=A.top-a.top),function(t,e=500,n=null,o=0,s=document){let i=function(t=document){let e=t.documentElement;return{top:(t.defaultView.pageYOffset||e.scrollTop)-(e.clientTop||0),left:(t.defaultView.pageXOffset||e.scrollLeft)-(e.clientLeft||0)}}(s).top,g=i+(t-i)*o,r=t-g,a=0,A=0,l=function(){var t;a+=20,A=a/e,function(t,e=document){e.documentElement.scrollTop=t,e.body.parentNode.scrollTop=t,e.body.scrollTop=t}(g+r*((t=A)<.5?4*t*t*t:(t-1)*(2*t-2)*(2*t-2)+1),s),a<e?window.requestAnimationFrame(l):n&&"function"==typeof n&&n()};l()}(l,e,s,o,g)},n(628);class m{constructor(t){return t.frameElement?t.parent.angular:angular}}m.$inject=["$window"];class f{constructor(t){this.store=t,this.store.states=this.store.states||{}}outlineShortcode(t){this.store.states.outlinedShortcode=t}selectShortcode(t){this.store.states.selectedShortcode=t}configureShortcode(t){this.store.states.configuringShortcode=t}}f.$inject=["store"];class v{constructor(t,e,n,o,s,i,g,r){const a=angular.copy(t.permissions);function A(e){return t.components[e]}return A.goto=function(e){t.currentPath=e},A.exit=function(){if(!a.exit)return;let e="publish"===t.post.status?t.backUrl:t.editUrl;(t.isSaved||confirm("Exit? All changes since last save will be lost."))&&((window.parent||window).location.href=e)},A.register=function(e,n){t.components=t.components||{},t.components[e]=n},A.remove=function(e){delete t.components[e]},A.apply=function(t){e.$apply(t)},A.broadcast=function(t,o){e.$broadcast(t,o),n.postCustomMessage(i.BROADCAST,{type:t,data:o})},A.emit=function(t,o){e.$emit(t,o),n.postCustomMessage(i.EMIT,{type:t,data:o})},A.trigger=function(e,...n){if(this.broadcast(e,...n),t.$$events.hasOwnProperty(e))for(var o=0;o<t.$$events[e].length;o++)t.$$events[e][o].call(this,...n)},A.filter=function(e,n,...o){if(t.$$filters.hasOwnProperty(e))for(var s=0;s<t.$$filters[e].length;s++)n=t.$$filters[e][s].call(this,n,...o);return n},A.states=t.states,A.resetAll=function(){t.$$events={},t.$$filters={},this.resetState()},A.resetState=function(){for(let e in t.states)t.states[e]=null},A.freeze=function(e){t.states.freezed=e},A.outlineShortcode=function(e=null){e&&this.broadcast(g.OUTLINED,e),t.states.outlinedShortcode=!0!==t.states.freezed?e:null},A.selectShortcode=function(e=null){e&&this.broadcast(g.SELECTED,e),t.states.selectedShortcode=!0!==t.states.freezed?e:null},A.configureShortcode=function(t=null){t?(this.selectShortcode(t),this.broadcast(g.CONFIGURE,t),this.goto(`/shortcode/${t.$id}`)):this.goto("/")},A.setBreakpoint=function(e){t.breakpoints.current=e,A.broadcast(r.CHANGED,e)},A}}v.$inject=["store","$rootScope","$window","$timeout","$log","AppEvent","ShortcodeEvent","MediaEvent"];class ${constructor(t,e){this.app=t,this.utils=e}attach(t){t.addEventListener&&t.addEventListener("contextmenu",this.onRightClick.bind(this),!1)}onRightClick(t){let e=this.utils.getGlobalCoordinates(t.view,t.clientX,t.clientY),n=this.utils.shortcodeFromPoint(e.x,e.y);n.isRoot||(this.menu.open(n,t.target,t.clientX,t.clientY),this.app.apply(),t?t.preventDefault():window.event.returnValue=!1)}get menu(){return this.app("contextMenu")}}$.$inject=["app","utils"];class b{constructor(t,e,o,s,i,g,r,a,A,l){var C={},I=(window.parent||window).angular.element("draggable-helper");return function(c,p,h){var d=this;h=angular.extend({cssProps:{},droppable:!0,broadcast:!0,start:angular.noop,move:angular.noop,end:angular.noop},h);var u=p||c.$element.get(0),m=new n.g.HammerJS(u,h);return m.get("pan").set({direction:n.g.HammerJS.DIRECTION_ALL,threshold:1}),angular.element(u).data("shortcode",c),angular.element(u).addClass("uxb-draggable"),m.on("hammer.input",(function(t){t.srcEvent.stopPropagation(),t.srcEvent.stopImmediatePropagation()})),m.on("panstart",f),m.on("pan",(function(e){if(C.shortcode||f(e),$(e),C.defaultPrevented||(I.addClass("active"),I.css({transform:`translate3d(${C.global.x}px, ${C.global.y}px, 0px)`}),I.find("h3 span").css({transform:`rotate(${-25*C.originalEvent.velocityY}deg)`})),C.target=null,C.addToShortcode=null,c.$recompile)return m.stop(!0),v(e);if(o.find(C.global.x,C.global.y).map((function(t){!C.target&&t.target&&t.target.allows(c)&&(C.target=t)})),C.target){let t=C.target.target===c.parent,n=t&&C.target.index===c.index,o=t&&C.target.index===c.index+1,s=e.srcEvent.altKey;!n&&!o||s||(C.target=null)}h.move(e),t("tools").showAddableSpot(C.target,e.srcEvent.altKey),t.outlineShortcode(C.target?C.target.target.parent:null),t.broadcast(l.PAN_MOVE,C),h.broadcast&&t.broadcast(l.MOVE,C),t("tools").apply(),c.$scope.$digest()})),m.on("panend",v),m;function f(n){o.updateOffsets(!0,0),I.find("h3 span").text(c.data.name),i.onkeydown=e=>t("tools").toggleAddableButton(e.altKey),i.onkeyup=e=>t("tools").toggleAddableButton(e.altKey),C.shortcode=c,C.iframeRect=a().get(0).getBoundingClientRect(),C.container=c.parent.$element,C.isSelected=t.states.selectedShortcode===c,C.defaultPrevented=!1,C.showHelper=!0,C.constrains=!1,C.initial={},$(n),C.initial={},C.initial.innerX=C.innerX,C.initial.innerY=C.innerY,C.initial.elementX=C.elementX,C.initial.elementY=C.elementY,c.$element.addClass("uxb-shortcode-dragging"),angular.element(g.parent.document.body).addClass("dragging"),angular.element("body").addClass("dragging"),h.start(n),c.states.dragging=!0,e.isDragging=!0,t.broadcast(l.PAN_START,C),h.broadcast&&t.broadcast(l.START,C),t("tools").apply(),c.$scope.$digest()}function v(n){if($(n),I.find("h3 span").removeAttr("style"),I.removeClass("active"),c.states.dragging=!1,C.target&&!C.defaultPrevented){let t=n.srcEvent.altKey,e=C.target.target,o=C.target.index;C.addedShortode=t?s.duplicate(c,o,!1,e):s.move(c,e,o)}c.$element.removeClass("uxb-shortcode-dragging"),angular.element(g.parent.document.body).removeClass("dragging"),angular.element("body").removeClass("dragging"),h.end(n),t.broadcast(l.PAN_END,C),h.broadcast&&t.broadcast(l.END,C),i.onkeydown=null,i.onkeyup=null,C={},r((()=>e.isDragging=!1),0)}function $(t){C.originalEvent=t,C.global=A.getGlobalCoordinates(t.target.ownerDocument.defaultView,t.center.x,t.center.y),C.element=angular.element(u),C.draggable=d,C.main={},C.main.x=C.global.x,C.main.y=C.global.y,C.iframe={},C.iframe.x=C.global.x-C.iframeRect.left,C.iframe.y=C.global.y-C.iframeRect.top,C.virtual={},C.virtual.width=C.shortcode.$element.width(),C.virtual.height=C.shortcode.$element.height(),C.virtual.top=C.iframe.y-C.initial.elementY,C.virtual.right=C.iframe.x+C.virtual.width-C.initial.elementX,C.virtual.bottom=C.iframe.y+C.virtual.height-C.initial.elementY,C.virtual.left=C.iframe.x-C.initial.elementX,C.constrains&&(C.constrains=C.container.outerOffset(),C.innerX=C.iframe.x-C.constrains.left-C.initial.elementX,C.innerY=C.iframe.y-C.constrains.top-C.initial.elementY,C.virtual.top<C.constrains.top&&(C.innerY=0),C.virtual.right>C.constrains.right&&(C.innerX=C.constrains.width-C.virtual.width),C.virtual.bottom>C.constrains.bottom&&(C.innerY=C.constrains.height-C.virtual.height),C.virtual.left<C.constrains.left&&(C.innerX=0)),C.elementX=C.iframe.x-c.$element.offset().left,C.elementY=C.iframe.y-c.$element.offset().top+a().contents().scrollTop(),C.preventDefault=function(){C.defaultPrevented=!0},C.setContainment=function(t){C.constrains=t.outerOffset(),C.container=t}}}}}b.$inject=["app","store","targets","Shortcode","$document","$window","$timeout","$iframe","utils","DragEvent"];class y{constructor(t,e){return{injectStyles:function(t){return n("styles",t,(function(t,e,n){return(n=document.createElement("link")).id="ux-builder-style-"+e,n.rel="stylesheet",n.type="text/css",n.media="all",n.href=t,n}))},injectScripts:function(t){return n("scripts",t,(function(t,e,n){return(n=document.createElement("script")).id="ux-builder-script-"+e,n.type="text/javascript",n.src=t,n}))}};function n(n,o,s){var i=e.defer(),g=0,r=0;return _.each(o,(function(e,s){!0===t.$get(n+"."+s+".loaded")?delete o[s]:g++})),0===_.size(o)?(i.resolve(),i.promise):(_.each(o,(function(e,o){var a=t.$get(n+"."+o)||s(e,o);if(angular.isDefined(a.loaded)&&!0!==a.loaded)return a.addEventListener("load",A);function A(){a.loaded=!0,++r===g&&i.resolve()}a.loaded=!1,a.addEventListener("load",A),t.$set(n+"."+o,a),document.getElementsByTagName("head")[0].appendChild(a)})),i.promise)}}}y.$inject=["store","$q"];class w{constructor(t){this.enabled=!0,this.store=t,this.store.editor=this.store.editor||{state:{}}}get state(){return this.store.editor.state}disable(){this.enabled=!1,this.state={}}enable(){this.enabled=!0}update(){}outline(t){this.state.outlined=this.enabled?t:null}select(t){this.state.selected=this.enabled?t:null}configure(t){this.state.configuring=this.enabled?t:null}target(t){this.state.target=this.enabled?t:null}}w.$inject=["store"];class x{constructor(){return function(t,e,o){return o?n.g.propagatingHammer(new n.g.HammerJS(t,e)):new n.g.HammerJS(t,e)}}}x.$inject=[];class E{constructor(t,e,n,o,s){this.app=t,this.store=e,this.manager=n,this.ShortcodeEvent=o,this.$timeout=s}undo(){const t=this.store.history[this.store.currentAction];return this.doAction(t,-1)}redo(){const t=this.store.history[this.store.currentAction+1];return this.doAction(t,0)}doAction(t,e){if(!t)return;const{type:n,payload:o}=t,{shortcode:s}=this.store;switch(this.store.$disable(),this.store.currentAction=this.store.history.indexOf(t)+e,n){case"reorderChildren":{const{id:t,parentId:e,toIndex:n,fromIndex:i}=o,g=i>n?i+1:i;this.manager.move(s[t],s[e],g,!1),o.fromIndex=n,o.toIndex=i;break}case"moveChild":{const{id:t,parentId:e,index:n,fromParentId:i,fromIndex:g}=o;this.manager.move(s[t],s[i],g,!1),o.fromParentId=e,o.fromIndex=n,o.parentId=i,o.index=g;break}case"updateOption":{const{name:t,optionValue:e,responsiveValue:n}=o,i=s[o.id];o.optionValue=angular.copy(i.optionValues[t]),o.responsiveValue=angular.copy(i.responsiveValues[t]),i.optionValues[t]=e,n&&(i.responsiveValues[t]=n);break}case"updateMultipleOptions":{const{mutations:t}=o;for(const e in t){const n=s[e];for(const o in t[e]){const s=t[e][o],{optionValue:i,responsiveValue:g}=s;s.optionValue=angular.copy(n.optionValues[o]),s.responsiveValue=angular.copy(n.responsiveValues[o]),n.optionValues[o]=i,g&&(n.responsiveValues[o]=g)}}break}case"clearResponsiveValue":{const{id:t,optionName:e,breakpointIndex:n,value:i}=o,g=s[t].responsiveValues[e];g[n]?g[n]=null:g[n]=i;break}case"updateContent":{const{id:t,content:e}=o,n=s[t];o.content=n.content,n.content=e;break}case"addChild":case"removeChild":if(s[o.id])this.manager.remove(s[o.id],!1);else{const t=s[o.parentId].addChild(o.data,o.index,!1);this.$timeout((()=>{this.app.trigger(this.ShortcodeEvent.ATTACHED,t)}))}break;case"removeContent":Object.keys(s).length>1?this.manager.remove(this.store.postContent,!1):(o.content.forEach(((t,e)=>{this.store.postContent.addChild(t,e,!1)})),this.$timeout((()=>{this.app.trigger(this.ShortcodeEvent.ATTACHED,this.store.postContent)})))}return this.store.$enable(),t}}E.$inject=["app","store","Shortcode","ShortcodeEvent","$timeout"];var S=n(1760),T=n.n(S);class k{constructor(t,e,n,o,s,i){this.app=t,this.store=e,this.targets=n,this.manager=o,this.$timeout=s,this.metaOptions=e.post.meta.options.flat,this.postMeta=e.post.meta.values,this.IframeEvent=i}reload(t){if(this.store.isReloading)return;this.store.loading=!0;let e=T()(this.store.iframeUrl);for(let t in this.metaOptions){let n=this.metaOptions[t];e.query+=`&${n.$orgName}=${this.postMeta[n.$name]}`}this.store.post.content=t||this.store.postContent.copy(((t,e)=>{t.$id=e.$id})),this.targets.remove(this.store.postContent),this.manager.remove(this.store.postContent,!1),this.app.resetAll(),this.store.isReloading=!!this.$timeout((()=>{this.store.iframeUrl=e.toString(),delete this.store.isReloading}),0)}}k.$inject=["app","store","targets","Shortcode","$timeout","IframeEvent"];class D{constructor(t,e,n){return{defaultBreakpoint:o,currentBreakpoint:s,getMediaValue:function(t,e){return(e=e||s())>o()?i(t,e):r(t,e)},getMediaIndex:function(t,e){return(e=e||s())>o()?g(t,e):a(t,e)},getLowerMediaValue:i,getLowerMediaIndex:g,getHigherMediaValue:r,getHigherMediaIndex:a,hasValueBetween:function(t,e,n){for(var o=e+1;o<n;o++)if(t[o])return!0;return!1}};function o(){return e.breakpoints.default}function s(){return e.breakpoints.current}function i(t,e){for(var n=e||s();n>=0;n--)if(t[n])return t[n];return null}function g(t,e){for(var n=e||s();n>=0;n--)if(t[n])return n;return 0}function r(t,e){for(var n=e||s();n<t.length;n++)if(t[n])return t[n];return null}function a(t,e){for(var n=e||s();n<t.length;n++)if(t[n])return n;return 0}}}D.$inject=["app","store","utils"];let O,N={};class M{constructor(t,e,n){angular.merge(this,t),this.responsiveValues={},this.optionValues={},this.$isReady=!1;const o=this;this.$id=n||t.$id||function(t,e){let n=Math.floor(65536*(1+Math.random())).toString(16).substring(1);return e?`${t.tag}-${n}`:"root"}(t,e),this.$parentId=e?e.$id:t.$parentId||null,this.$textContent=t.content||"",this.data=angular.copy(i.shortcodes[this.tag]),e&&angular.extend(this.data,e.data.children),this.states={active:!1,dragging:!1,open:void 0},this.options={get $responsive(){return o.responsiveValues},set $responsive(t){o.responsiveValues=t}};for(let e in t.options.$responsive){this.responsiveValues[e]=[];for(let n=0;n<t.options.$responsive[e].length;n++)this.responsiveValues[e][n]=P(t.options.$responsive[e][n],this.data.options.named[e])}for(let e in t.options)"$"!==e.charAt(0)&&(Object.defineProperty(this.options,e,{enumerable:!0,get:()=>this.data.options.named[e].responsive?j(this.responsiveValues[e]):this.optionValues[e],set:t=>{let n=this.data.options.named[e],o=P(t,n),s=i.breakpoints.current;this.$isReady&&i.enabled&&(clearTimeout(O),N[this.$id]||(N[this.$id]={}),N[this.$id][e]||(N[this.$id][e]={optionValue:angular.copy(this.optionValues[e]),responsiveValue:angular.copy(this.responsiveValues[e])}),O=setTimeout((()=>{const t=Object.keys(N);1===t.length&&1===Object.keys(N[t[0]]).length?i.$addAction("updateOption",{id:this.$id,name:e,override:!1,optionValue:N[t[0]][e].optionValue,responsiveValue:N[t[0]][e].responsiveValue,key:`updateOption-${t[0]}-${e}-${s}`}):t.length&&i.$addAction("updateMultipleOptions",{mutations:N,override:!1,key:`updateMultipleOptions-${t.join("-")}-${s}`}),N={},this.apply()}),250)),this.optionValues[e]=o,n.responsive&&o!==j(this.responsiveValues[e])&&(this.responsiveValues[e][s]=o)}}),this.options[e]=t.options[e]);this.data.options.flat.forEach((t=>{null===this.options[t.$name]&&(this.options[t.$name]=t.default),t.$isValidFor(e)||(this.options[t.$name]=null,t.responsive&&(this.options.$responsive[t.$name]=[null,null,null]))})),i.shortcode[this.$id]=this,t.hasOwnProperty("children")&&(this.children=t.children.map((t=>new M(t,this)))),this.$isReady=!0}get content(){return this.$textContent||""}set content(t){t!==this.content&&this.$isReady&&i.enabled&&i.$addAction("updateContent",{id:this.$id,override:!1,content:this.content,key:`updateContent-${this.$id}`}),this.$textContent=t}apply(){this.$scope&&!this.$scope.$$phase&&this.$scope.$apply()}addChild(t,e,n=!0){let o;if(t instanceof M)if(t.$parentId!==this.$id){const s=t.$parentId,g=t.index;o=new M(R(t.detatch(),(function(t,e){t.$id=e.$id})),this,t.$id),n&&i.$addAction("moveChild",{key:`moveChild-${o.$id}-${s}-${g}`,id:o.$id,parentId:this.$id,index:e,fromIndex:g,fromParentId:s})}else{const s=t.index;e-=e>t.index?1:0,o=t.detatch(),n&&i.$addAction("reorderChildren",{key:`reorderChildren-${t.$id}-${s}-${e}`,parentId:this.$id,id:t.$id,toIndex:e,fromIndex:s})}else o=new M(Y(t),this),n&&i.$addAction("addChild",{key:`addChild-${this.$id}-${o.$id}`,id:o.$id,parentId:this.$id,index:e,data:o.copy(((t,e)=>{t.$id=e.$id}))});if(!this.allows(o))throw Error(`${o.data.name} is not allowed in ${this.data.name}`);const s=e>=0?e:this.children.length;return this.children.splice(s,0,o),o}childAt(t){return this.isParent?this.children[t]:null}removeChild(t){return this.isParent?this.children[t].remove():null}replaceChild(t,e){return this.isParent?(this.children[e]=t instanceof M?t:new M(t,this),this.children[e]):null}replaceWith(t){return this.isRoot?null:this.parent.replaceChild(t,this.index)}is(t){return this.data.tag===t}isChildOf(t){return this.parent===t}isDescendantOf(t){return t.descendants.indexOf(this)>-1}isSelfOrDescendantOf(t){return t.descendantsAndSelf.indexOf(this)>-1}isAncestorOf(t){return this.descendants.indexOf(t)>-1}isSelfOrAncestorOf(t){return this.descendantsAndSelf.indexOf(t)>-1}copy(t=null){let e={};for(let t in this)_.isFunction(this[t])||"$"!==t.charAt(0)&&"optionValues"!==t&&"responsiveValues"!==t&&"children"!==t&&"data"!==t&&this.hasOwnProperty(t)&&(e[t]=angular.copy(this[t]));if(this.$textContent&&(e.content=this.$textContent),this.isParent){e.children=[];for(let n=0;n<this.children.length;n++)e.children.push(this.children[n].copy(t))}return t&&t(e,this),e}duplicate(t=null,e=!1){const n=this.copy(((t,n)=>{e&&(t.$id=n.$id)}));return this.parent.addChild(n,t||this.index+1)}detatch(){return this.parent?this.parent.children.splice(this.index,1)[0]:null}remove(t=!0){this.descendants.forEach((t=>{delete i.shortcode[t.$id]})),this.parent&&(delete i.shortcode[this.$id],t&&i.$addAction("removeChild",{id:this.$id,index:this.index,parentId:this.parent.$id,key:`removeChild-${this.$id}`,data:this.copy(((t,e)=>{t.$id=e.$id}))})),this.detatch()}allows(t){return function(t,e){return!e.isSelfOrDescendantOf(t)&&!t.descendants.filter((t=>!1===t.data.nested&&t.tag===e.tag)).length&&e.allowed.hasOwnProperty(t.tag)}(t,this)}get allowed(){return function(t){let e=t.ancestorsAndSelf,n={};if(t.data.allow.length)return t.data.allow.reduce((function(t,e){return i.shortcodes[e]&&(t[e]=i.shortcodes[e]),t}),{});for(let o in i.shortcodes){let s=i.shortcodes[o];s.hidden||s.require.length&&-1===s.require.indexOf(t.tag)||!1===s.nested&&t.tag===s.tag||!1===s.nested&&e.filter((function(t){return t.tag===s.tag})).length||(n[s.tag]=s)}return n}(this)}get presets(){let t=this.parent.allowed,e=angular.copy(this.data.presets);return e.forEach((function(n,o){let s=B(n.content);if(s.unshift(n.content),!t.hasOwnProperty(n.content.tag))return e.splice(o,1);s.forEach((function(t){t.tag===parent.tag&&!0!==i.shortcodes[t.tag].nested&&e.splice(o,1)}))})),e}get isParent(){return!!this.children}get isChild(){return!!this.parent}get isEmpty(){return this.isParent&&0===this.children.length}get isRoot(){return"_root"===this.tag}get parent(){return this.$parentId?i.shortcode[this.$parentId]:null}get index(){return this.parent?this.parent.children.indexOf(this):0}get depth(){return this.ancestors.length}get ancestors(){let t=[];return this.parent&&(t.push(this.parent),t=t.concat(this.parent.ancestors)),t}get ancestorsAndSelf(){let t=this.ancestors;return t.unshift(this),t}get descendants(){let t=[];if(this.isParent)for(let e=0;e<this.children.length;e++)t.push(this.children[e]),t=t.concat(this.children[e].descendants);return t}get descendantsAndSelf(){let t=this.descendants;return t.unshift(this),t}get siblings(){return this.parent?this.parent.children.filter((t=>t!==this)):[]}get nextSibling(){return this.parent&&this.parent.children[this.index+1]?this.parent.children[this.index+1]:null}get previousSibling(){return this.parent&&this.parent.children[this.index-1]?this.parent.children[this.index-1]:null}get siblingsAndSelf(){return this.parent?this.parent.children:[this]}}function P(t,e){return null===t||"string"==typeof t&&""===t?t:Array.isArray(t)&&0===t.length?e.default||"":isNaN(t)?angular.isUndefined(t)?e.default:(Array.isArray(t)&&(t=t.join(e.config?e.config.delimiter:",")),String(t)):"string"!=typeof t||!/^0\d/.test(t)&&"+"!==t.charAt(0)?Number(t):String(t)}function R(t,e){let n={};for(let e in t)"$"!==e.charAt(0)&&"children"!==e&&t.hasOwnProperty(e)&&(n[e]=angular.copy(t[e]));if(t.isParent){n.children=[];for(var o=0;o<t.children.length;o++)n.children.push(R(t.children[o],e))}return t.$textContent&&(n.content=t.$textContent),e&&e(n,t),n}function Y(t){let e=null,n=angular.copy(t);if(angular.isDefined(n.children))for(let t in n.children)e=Y(i.shortcodes[n.children[t].tag].presets[0].content),n.children[t].isparent&&!n.children[t].children.length&&(n.children[t]=angular.merge({},e,n.children[t])),n.children[t].content&&""===n.children[t].content&&(n.children[t].content=e.content);return n}function B(t){let e=[];return t.children&&t.children.forEach((function(t){e.push(t),e.concat(B(t))})),e}function j(t,e=i.breakpoints.current){return e>i.breakpoints.default?function(t,e=i.breakpoints.current){for(let n=e;n>=0;n--)if(null!==t[n])return t[n];return null}(t,e):function(t,e=i.breakpoints.current){for(let n=e;n<t.length;n++)if(null!==t[n])return t[n];return null}(t,e)}class L{constructor(t,e,n,o,s){this.app=t,this.store=e,this.$timeout=n,this.shortcodeTemplateCache=o,this.ShortcodeEvent=s}instantiate(t,e,n){return new M(t,e,n)}create(t,e,n){let o=this.instantiate(t,e,n);return o.$$new=!0,o}move(t,e,n,o=!0){let s=e.addChild(t,n,o);return s.$$moved=!0,t.$parentId!==s.$parentId&&(this.app.trigger(this.ShortcodeEvent.DETACHED,t),s.$$new=!!s.data.template||!!this.shortcodeTemplateCache.get(s.$id)),this.app.trigger(this.ShortcodeEvent.MOVED,s),s}duplicate(t,e=t.index+1,n=!1,o=t.parent){const s=t.copy(((t,e)=>{n&&(t.$id=e.$id)})),i=o.addChild(s,e);return i.$$new=!0,i.data.template||this.shortcodeTemplateCache.put(i.$id,this.shortcodeTemplateCache.get(t.$id)),i}remove(t,e=!0){if(this.app.trigger(this.ShortcodeEvent.DETACHED,t),this.app.states.selectedShortcode&&this.app.states.selectedShortcode.isSelfOrDescendantOf(t)&&(this.app.outlineShortcode(null),this.app.selectShortcode(null),this.app.configureShortcode(null)),t.isRoot)for(let n=t.children.length-1;n>=0;n--)t.children[n].remove(e);else t.remove(e)}copy(t){return t.copy(((t,e)=>{let n=this.store.breakpoints.default,o=e.options.$responsive;for(let e in o)t.options[e]=o[e][n];for(let n in t.options)"$responsive"!==n&&null===t.options[n]&&(t.options[n]=e.data.options.get(n).default);Object.keys(e.options).forEach((n=>{const o=e.data.options.named[n];!o||o.$isValidFor(e.parent)&&o.$satisfiesConditions(e.options)||(delete t.options[n],delete t.options.$responsive[n])}))}))}}L.$inject=["app","store","$timeout","shortcodeTemplateCache","ShortcodeEvent"];class V{constructor(t,e,n,o,s){this.store=t,this.$iframe=e,this.$timeout=s,n.addEventListener("resize",(()=>this.updateOffsets()),!1),n.addEventListener("scroll",(()=>this.updateOffsets()),!1),this.updateOffsets(!0,0)}add(t,e){let n=angular.extend({name:t,element:e.shortcode.$element,shortcode:e.shortcode,target:e.target||"center"===t?e.shortcode:e.shortcode.parent,droppable:!0,addable:!0,active:!0},e);n.offsets=n.element.outerOffset(),this.store.targets.push(n)}enable(t){if(angular.isArray(t))return t.forEach((t=>this.enable(t)));for(let e=0;e<this.store.targets.length;e++)this.store.targets[e].target===t&&(this.store.targets[e].active=!0)}disable(t){if(angular.isArray(t))return t.forEach((t=>this.disable(t)));for(let e=0;e<this.store.targets.length;e++)this.store.targets[e].target===t&&(this.store.targets[e].active=!1)}remove(t){let e=t.descendantsAndSelf;this.store.targets=this.store.targets.reduce((function(t,n){return e.indexOf(n.shortcode)<0&&t.push(n),t}),[])}removeElement(t){this.store.targets=this.store.targets.reduce((function(e,n){return n.element.get(0)!==t&&e.push(n),e}),[])}enableElement(t){for(let e=0;e<this.store.targets.length;e++)this.store.targets[e].element.get(0)===t&&(this.store.targets[e].active=!0,this.store.targets[e].shortcode.descendants.forEach((t=>{t.$element&&this.enableElement(t.$element.get(0))})))}disableElement(t){for(let e=0;e<this.store.targets.length;e++)this.store.targets[e].element.get(0)===t&&(this.store.targets[e].active=!1,this.store.targets[e].shortcode.descendants.forEach((t=>{t.$element&&this.disableElement(t.$element.get(0))})))}find(t,e,n=!1,o=50){let s=[],i=!!this.store.isDragging;for(let g=0;g<this.store.targets.length;g++){let r,a,A,l,C,I=this.store.targets[g],c=I.element[0].ownerDocument.defaultView,{parent:p}=I.shortcode,h=t,d=e;if(!I.active)continue;if("center"===I.name&&!I.shortcode.isEmpty)continue;if(i&&!I.droppable)continue;if(!i&&!I.addable)continue;if(!i&&!c.frameElement)continue;if(!c){this.removeElement(I.element.get(0));continue}switch(c.frameElement&&angular.isDefined(this.frameOffsets)&&(h-=this.frameOffsets.left,d-=this.frameOffsets.top),p&&"ux_stack"===p.tag&&"col"===p.options.direction&&(I={...I,name:"left"===I.name?"top":"bottom"}),I.name){case"top":C=I.shortcode.index,A=I.offsets.left+I.offsets.width/2,l=I.offsets.top,r=n?A:h,a=l;break;case"right":C=I.shortcode.index+1,A=I.offsets.right,l=I.offsets.top+I.offsets.height/2,r=I.offsets.right,a=n?l:d;break;case"bottom":C=I.shortcode.index+1,A=I.offsets.left+I.offsets.width/2,l=I.offsets.bottom,r=n?A:h,a=l;break;case"left":C=I.shortcode.index,A=I.offsets.left,l=I.offsets.top+I.offsets.height/2,r=A,a=n?l:d;break;case"center":C=0,A=A=I.offsets.left+I.offsets.width/2,l=l=I.offsets.top+I.offsets.height/2}let u={fromSide:Math.sqrt(Math.pow(Math.abs(r-h),2)+Math.pow(Math.abs(a-d),2)),fromCenter:Math.sqrt(Math.pow(Math.abs(A-h),2)+Math.pow(Math.abs(l-d),2))},m=I.shortcode.isEmpty,f=m?0:o,v=this.isInside(I,h,d,f),$=this.isInside(I,h,d),b=m?v:u.fromSide<f;v&&b&&s.push({target:I.target||("center"!==I.name?I.shortcode.parent:I.shortcode),insideLimit:v,insideElement:$,element:I.element,shortcode:I.shortcode,name:I.name,distance:u,index:C})}return _.chain(s).sortBy((t=>-t.target.ancestors.length)).sortBy((t=>t.distance.fromCenter)).value()}isInside(t,e,n,o=0){var s=o,i=o,g=Math.min(t.offsets.left-s,t.offsets.right+s),r=Math.max(t.offsets.left-s,t.offsets.right+s),a=Math.min(t.offsets.top-i,t.offsets.bottom+i),A=Math.max(t.offsets.top-i,t.offsets.bottom+i);return g<=e&&e<=r&&a<=n&&n<=A}updateOffsets(t=!1,e=250){this.$timeout.cancel(this.store.__updateTargets),this.store.__updateTargets=this.$timeout((()=>{this.frameOffsets=this.$iframe().get(0).getBoundingClientRect();for(let e=0;e<this.store.targets.length;e++){let n=this.store.targets[e];(t||n.active&&n.element[0].ownerDocument.defaultView===window)&&("center"===n.name&&n.shortcode.isEmpty&&n.shortcode.$content&&n.element[0].ownerDocument.defaultView&&n.element[0].ownerDocument.defaultView.frameElement?n.offsets=n.shortcode.$content.outerOffset({includeMargins:!0}):n.element?n.offsets=n.element.outerOffset({includeMargins:!0}):this.store.remove(n.shortcode))}delete this.store.__updateTargets}),e,!1)}}V.$inject=["store","$iframe","$window","$document","$timeout"];class G{constructor(t,e,n,o,s,i,g){this.app=t,this.store=e,this.modal=n,this.presetCache=o,this.Shortcode=s,this.isSaving=!1,this.shortcode=!1,this.store.currentModal=null,this.store.templateName="",this.content=!1,this.$timeout=i,this.$q=g}showModal(t){const{title:e,name:n,...o}=t;this.store.templateName=n||"",this.store.templateError="",this.store.templateData=o,this.store.currentModal=this.modal.show("custom-template-modal",{title:e}),this.store.currentModal.onClose((()=>{this.store.templateName="",this.store.templateError="",this.store.templateData=null,this.store.currentModal=null})),this.$timeout((()=>{this.store.currentModal&&this.store.currentModal.$el.find(".custom-template-modal__input").focus()}),75)}savePreset(t){this.showModal({title:`Save ${t.data.name} as preset`,tag:t.tag,content:this.Shortcode.copy(t)})}updatePreset(t){this.showModal({title:`Edit ${t.name} preset`,...t})}saveTemplate(t){this.showModal({title:"Save as template",tag:"_root",content:this.Shortcode.copy(t),template:this.store.post.meta.values._wp_page_template})}editTemplate(t){this.showModal({title:`Edit ${t.name} template`,...t})}save(){if(this.isSaving)return;const t={...this.store.templateData,post_id:this.store.post.id,title:this.store.templateName,content:angular.toJson(this.store.templateData.content)};this.isSaving=!0,jQuery.post(this.store.ajaxUrl,{action:"ux_builder_save_custom_template",security:this.store.nonce,data:t}).done((({data:t,success:e})=>{e?t&&(this.presetCache.remove(t.tag),this.app.broadcast("template-saved",t),this.store.currentModal&&this.store.currentModal.hide()):this.store.currentModal&&this.store.currentModal.setError(t?t.message:"Failed to save template."),this.isSaving=!1,this.app.apply()})).fail((t=>{this.store.currentModal&&this.store.currentModal.setError(t.statusText),this.isSaving=!1,this.app.apply()}))}remove(t){return this.$q(((e,n)=>{if(confirm(`Do you want to delete ${t.name}?`))return jQuery.post(this.store.ajaxUrl,{action:"ux_builder_delete_custom_template",post_id:this.store.post.id,security:this.store.nonce,id:t.id}).done((({data:t,success:o})=>{o?t&&(this.app.broadcast("template-removed",t),e(!0)):n(new Error(t?t.message:"Failed to delete template.")),this.app.apply()})).fail((t=>{n(new Error(t.statusText))}));e()}))}}G.$inject=["app","store","modal","presetCache","Shortcode","$timeout","$q"];const Q=(window.parent||window).uxBuilderData;class z{constructor(t,e,n,o){this.arrayPrefix=function(t,e){return t.reduce(((t,n)=>(t.push(`${e}${n}`),t)),[])},this.isIframe=function(){return!!n.frameElement},this.getGlobalCoordinates=function(t,e,n){if(t.frameElement){let o=t.frameElement.getBoundingClientRect();e+=o.left,n+=o.top}return{x:e,y:n}},this.camelCase=function(t,e){return t=e?t.charAt(0).toUpperCase()+t.slice(1):t,jQuery.camelCase(t.replace(/\_|\:/g,"-",!0))},this.kebabCase=function(t){return t.replace(/[A-Z\u00C0-\u00D6\u00D8-\u00DE]/g,(function(t){return t.toLowerCase()}))},this.elementFromPoint=function(t,e){var s=n.parent||n,i=s.document,g=o().get(0).contentWindow,r=o().get(0).contentDocument,a=t,A=e,l=o().get(0).getBoundingClientRect(),C=t-l.left,I=e-l.top,c=s.angular.element(i.elementFromPoint(a,A)),p=g.angular.element(r.elementFromPoint(C,I));return p.length?p:c},this.shortcodeFromPoint=function(e,n){return this.elementFromPoint(e,n).shortcode()||t.postContent}}}z.$inject=["store","$document","$window","$iframe"];class U{constructor(t,e,n){this.app=t,this.store=e,this.$editor=(window.parent||window).angular.element("wp-editor"),this.$iframe=this.$editor.find("iframe").get(0).contentWindow,n.addEventListener("message",(t=>this.onMessage(t)),!1)}onMessage(t){if("uxBuilderWpEditor"===t.data.source)switch(t.data.type){case"change":this.updateContent(t.data.content);break;case"discard":this.discard();break;case"hide":this.close()}}get editor(){return this.$iframe.wp.editor}open(){this.store.$set("stack",this),this.$editor.addClass("is-visible"),this.originalContent=this.app.states.selectedShortcode.content,this.$iframe.postMessage({source:"uxbuilder",type:"setContent",content:this.originalContent},"*")}updateContent(t){this.app.states.selectedShortcode.content=t,this.app.states.selectedShortcode.apply()}discard(){this.updateContent(this.originalContent),this.originalContent="",this.close()}close(){this.store.$set("stack",!1),this.$editor.removeClass("is-visible")}}U.$inject=["app","store","$window"];class F{constructor(t){this.$media=(window.parent||window).angular.element("wp-media"),this.$iframe=this.$media.find("iframe").get(0).contentWindow,t.addEventListener("message",(t=>this.onMessage(t)),!1)}onMessage(t){if("uxBuilderWpMedia"===t.data.source)switch(t.data.type){case"close":this.close();break;case"select":"function"==typeof this.cb&&this.cb(t.data.attachment)}}get media(){return this.$iframe.wp.media}open(t){this.$media.addClass("is-active")}close(){this.$media.removeClass("is-active")}}F.$inject=["$window"];class Z{constructor(t,e){return function(){return t.element((e.parent.document||document).getElementsByTagName("iframe")[0])}}}function W(t){const e=n(2391);angular.forEach(e.keys(),(function(n){const o=e(n);t.put(n.replace("./",""),o.default||o)}))}Z.$inject=["$angular","$window"],angular.module("app.services",[]).service("$angular",m).service("actions",f).service("app",v).service("contextmenu",$).service("draggable",b).service("dependencies",y).service("editor",w).service("history",E).service("iframe",k).service("modal",class{show(t,e={}){const n=jQuery(`#${t}-modal`,parent.document),o=n.find(".app-modal-title"),s=n.find(".app-modal-error");return e.title&&o.html(e.title),n.addClass("is-visible"),{$el:n,hide:()=>this.hide(t),setError(t){s.html(t)},clearError(){s.empty()},onClose(t){n.one("modal:close",t)}}}hide(t){const e=jQuery(`#${t}-modal`,parent.document),n=e.find(".app-modal-error");e.trigger("modal:close"),e.removeClass("is-visible"),n.empty(),e.off()}}).service("hammer",x).service("ResponsiveHelper",D).service("Shortcode",L).service("shortcodeTemplateCache",(function(){return Q.templateCache=Q.templateCache||new Map,{put(t,e){Q.templateCache.set(t,e)},get(t){return Q.templateCache.get(t)},remove(t){Q.templateCache.delete(t)},removeAll(){Q.templateCache.clear()},destroy(){Q.templateCache.clear()},info(){return{id:"shortcodeTemplateCache",size:Q.templateCache.size}}}})).service("utils",z).service("store",r).service("targets",V).service("templates",G).service("$iframe",Z).service("wpEditor",U).service("wpMedia",F),W.$inject=["$templateCache"],C.run(W);class H{constructor(t,e,n,o,s,i,g,r,a,A){let l,C,I;function c(){const{main:{x:t,y:e}}=C,n=A.find(".view-body"),s=n.scrollTop(),i=100;let g,r=e,a=I.bottom-I.top-e;r<i?g=(I.top+r-(I.top+i))/5:a<i&&(g=(I.bottom+i-(I.bottom+a))/5),g&&t>=0&&t<=I.right-I.left&&(n.scrollTop(s+g),o.updateOffsets(!1,0))}this.app=t,this.store=e,this.utils=n,this.Shortcode=s,this.$log=g,this.permissions=angular.copy(this.store.permissions),this.$onDestroy=()=>{clearInterval(l),l=C=null},a.$on(i.PAN_START,((t,e)=>{C=e,I=A.get(0).getBoundingClientRect(),l=setInterval(c,1e3/60)})),a.$on(i.PAN_MOVE,((t,e)=>{C=e})),a.$on(i.PAN_END,(()=>{clearInterval(l),l=C=null}))}save(t="publish"){if(!this.permissions.save)return;let e={status:t,id:this.store.post.id,attributes:this.store.post.attributes.values,meta:this.store.post.meta.values,content:this.Shortcode.copy(this.store.postContent)};this.$log.debug("Saving",e),this.store.isSaving=t,this.store.isSaved=!1,jQuery.post(this.store.ajaxUrl,{action:"ux_builder_save",data:angular.toJson(e),security:this.store.nonce}).done(this.onSaveSuccess.bind(this)).fail(this.onSaveFail.bind(this))}onSaveSuccess({success:t,data:e}){e?(this.$log.info("Post was saved",e),this.store.post.status=e.post.post_status,this.store.saveButton=e.save_button,this.store.isSaved=!0):this.$log.warn("An error occurred while saving post"),this.store.isSaving=!1,this.app.apply()}onSaveFail(t){this.$log.error(t),this.store.isSaving=!1,this.app.apply()}getOptionName(t,e){for(let n in e){if("group"===e[n].type)return this.getOptionName(t,e[n].options);if(e[n].$name===t)return e[n].$orgName}return t}get saveButtonText(){return this.store.saveButton}}H.$inject=["app","store","utils","targets","Shortcode","DragEvent","$log","$scope","$rootScope","$element"],C.component("homeView",{controller:H,template:n(7009)});class X{constructor(t,e,n,o,s,i){this.app=t,this.store=e,this.post=e.post,this.templates=n,this.modal=o,this.manager=s,this.customTemplateName="",i.$watchCollection((()=>this.post.attributes.values),((t,e)=>{t!==e&&(this.store.isSaved=!1)})),i.$watchCollection((()=>this.post.meta.values),((t,e)=>{t!==e&&(this.store.isSaved=!1)}))}saveAsTemplate(){this.templates.saveTemplate(this.store.postContent)}clearContent(){if(!0===confirm("Are you sure you want to clear all content?")){const t=this.store.postContent.copy(((t,e)=>{t.$id=e.$id}));this.store.$addAction("removeContent",{key:"removeContent",content:t.children}),this.manager.remove(this.store.postContent,!1)}}exit(){this.app.goto("/")}}X.$inject=["app","store","templates","modal","Shortcode","$scope"],C.component("settingsView",{controller:X,template:n(9369)});class q{constructor(t,e,n,o,s,i,g,r){this.app=t,this.store=e,this.utils=n,this.$scope=o,this.shortcode=null,this.options=null,this.previousOptions=null,this.previousContent=null,this.responsiveHelper=i,this.currentHistoryAction=e.currentAction,o.$on(g.MOVED,((t,e)=>{this.shortcode.$id===e.$id&&(this.shortcode=e)})),o.$on(g.RECOMPILED,((t,e)=>{this.shortcode.$id===e.$id&&(this.shortcode=e,this.app.selectShortcode(e))})),o.$watch("$ctrl.shortcode",(t=>{t.states.active=!0,this.previousContent=t.content||null,this.options=t.data.options.tree,this.responsiveValues=t.options.$responsive,this.previousOptions=angular.copy(t.options),this.app.selectShortcode(t)})),o.$on("$destroy",(()=>{this.shortcode=null,this.options=[],this.previousOptions=null,this.previousContent=null,this.currentHistoryAction=null}))}get shortcodes(){return this.shortcode?[this.shortcode]:[]}exit(){this.app.goto("/")}discard(){if(this.store.$disable(),this.previousContent&&(this.shortcode.content=this.previousContent),this.previousOptions)for(let t in this.shortcode.options)"$"!==t.charAt(0)&&(this.previousOptions.hasOwnProperty(t)?this.shortcode.options[t]=this.previousOptions[t]:delete this.shortcode.options[t]);this.store.$resetToAction(this.currentHistoryAction),this.store.$enable(),this.exit()}}q.$inject=["app","store","utils","$scope","$timeout","ResponsiveHelper","ShortcodeEvent","DragEvent"],C.component("shortcodeView",{controller:q,template:n(705),bindings:{shortcode:"<"}});const K=(window.frameElement?window.parent:window).uxBuilderData;class J{constructor(t,e,n){this.app=t,this.$scope=e,this.$element=n}$onInit(){this.$element.toggleClass("with-label",!!this.label)}addShortcode(){this.app.outlineShortcode(null),this.app.selectShortcode(null),this.app.configureShortcode(null),this.app("stack").open('\n      <add-shortcode\n        shortcode="$ctrl.shortcode"\n        index="$ctrl.index"\n      ></add-shortcode>\n    ',this.$scope)}}J.$inject=["app","$scope","$element"],C.component("addButton",{controller:J,bindings:{shortcode:"<",label:"@",index:"<"},template:'\n    <button type="button" ng-click="$ctrl.addShortcode()">\n      <div class="wrapper">\n        <span class="icon">+</span>\n        <span class="label">{{:: $ctrl.label }}</span>\n      </div>\n    </button>\n  '}),C.component("addShortcode",{controller:n(3042).A,template:n(6191),require:{stack:"^appStack"},bindings:{shortcode:"<",index:"<"}});const tt=angular.injector(["ng"]);class et{constructor(t){this.tree=t,this.named={};const e=tt.get("$parse","shortcode.options");this.flat=t.reduce(((t,e)=>("group"===e.type?t=t.concat(e.options):t.push(e),t)),[]),this.flat.forEach((t=>{this.named[t.$name]=t,t.onChange&&(t.apply=function({selector:t,recompile:e,handler:n},o){return function(s,i,g=i){if(t&&(s=s.find(t)),0===s.length)return!1;switch(n.type){case"class":!function(t,e,n,o){let s=angular.isString(e.class),i=s?nt(e.class,n):e.class[n],g=s?nt(e.class,o):e.class[o];ot(o)||t.removeClass(g),ot(n)||t.addClass(i)}(s,n,i,g);break;case"style":!function(t,e,n,o){for(let o in e.rules)t.css(e.rules[o].property,ot(n)?"":nt(e.rules[o].value,n))}(s,n,i);break;case"content":!function(t,e,n,o){t.text(nt(e.value,n))}(s,n,i)}return!((!ot(i)||!ot(g))&&(e&&ot(i)&&!ot(g)||e&&i===o.default))}}(t.onChange,t)),t.$isValidFor=function(t){if(this.require){if("string"==typeof this.require&&this.require!==t.tag)return!1;if(Array.isArray(this.require)&&this.require.indexOf(t.tag)<0)return!1}return!0},t.$satisfiesConditions=function(t){return!this.conditions||e(this.conditions)({$ctrl:{model:t}})}}))}get(t){for(let e in this.flat)if(this.flat[e].$name===t||this.flat[e].$orgName===t)return this.flat[e]}}function nt(t,e){return t.replace(/\{\{\s*?value\s*?\}\}/g,e)}function ot(t){return""===t||null==t}class st{constructor(t,e,n,o,s,i,g,r){this.app=t,this.store=e,this.templates=n,g.debug("Data:",e),o.attach(document);let a=this.store.post.attributes.options,A=this.store.post.meta.options;this.store.post.attributes.options=new et(a),this.store.post.meta.options=new et(A);for(let t in this.store.shortcodes){let e=new et(this.store.shortcodes[t].options);this.store.shortcodes[t].options=e}s.$on(r.CHANGED,(function(t,n){i.removeClass((function(t,e){return(e.match(/(^|\s)media-\S+/g)||[]).join(" ")})),i.addClass("media-"+_.keys(e.breakpoints.all)[n])}))}}st.$inject=["app","store","templates","contextmenu","$scope","$element","$log","MediaEvent"],C.component("app",{controller:st,template:'\n    <app-wrapper ng-class="{\n      \'is-saved\': $ctrl.store.isSaved,\n      \'sidebar-visible\': $ctrl.store.showSidebar === true,\n      \'sidebar-hidden\': $ctrl.store.showSidebar === false\n    }">\n      <app-sidebar></app-sidebar>\n      <app-content></app-content>\n      <app-actions></app-actions>\n    </app-wrapper>\n\n    <wp-editor class="app-stack"></wp-editor>\n    <wp-media></wp-media>\n\n    <div class="tools-addable">\n      <div class="line"></div>\n    </div>\n\n    <app-stack></app-stack>\n\n    <app-modal\n      id="custom-template-modal"\n      class="custom-template-modal"\n      ng-class="{ \'is-saving\': $ctrl.templates.isSaving }"\n    >\n      <div class="loading-spinner"></div>\n      <form class="custom-template-modal__form" ng-submit="$ctrl.templates.save()">\n        <input class="custom-template-modal__input" type="text" placeholder="Enter name" ng-model="$ctrl.store.templateName">\n        <button class="custom-template-modal__button wp-style alt" ng-click="$ctrl.templates.save()">Save</button>\n      </form>\n    </app-modal>\n  '});class it{constructor(t,e,n,o,s,i,g){this.breakpoints=e.breakpoints,this.actions=e.actions,this.undo=function(){n.undo()},this.redo=function(){n.redo()},this.canUndo=function(){return e.currentAction>=0},this.canRedo=function(){return e.currentAction<e.history.length-1},this.viewRevisions=function(){console.log("actions.viewRevisions")},this.setBreakpoint=function(e){t.setBreakpoint(e)},this.isActiveBreakpont=function(t){return o.currentBreakpoint()===t},this.hasBreakpointValues=function(e){if(!t.states.selectedShortcode)return!1;if(e===o.defaultBreakpoint())return!1;var n=!1;return angular.forEach(t.states.selectedShortcode.options.$responsive,(function(t){t[e]&&(n=!0)})),n},this.doAction=function(t){t.handler(i)}}}it.$inject=["app","store","history","ResponsiveHelper","ShortcodeEvent","$injector","$timeout"],C.component("appActions",{controller:it,template:n(1859)});class gt{constructor(t){this.app=t}}gt.$inject=["app"],C.component("appContent",{controller:gt,template:"\n    <app-content-iframe></app-content-iframe>\n    <app-content-toolbar></app-content-toolbar>\n  "});class rt{constructor(t,e,n,o,s,i,g,r,a,A,l,C){this.app=t,this.store=e,this.targets=n,this.iframe=o,this.$timeout=g,this.Shortcode=a,this.IframeEvent=C,this.loading=!1;var I=this.store.post.meta.options.flat,c=this.store.post.meta.values;s.$watchCollection((()=>c),((t,e)=>{if(!this.store.initialized||t===e)return;let n={},o=!1;for(let s in I){let i=I[s];t[i.$name]!==e[i.$name]&&(n[i.$name]=t[i.$name],i.reload&&(o=!0))}o&&this.iframe.reload()})),s.$on(A.READY,(()=>this.loading=!1)),s.$on(l.PAN_START,(()=>i.find(".iframe-overlay").removeClass("hidden"))),s.$on(l.PAN_END,(()=>i.find(".iframe-overlay").addClass("hidden")))}}rt.$inject=["app","store","targets","iframe","$scope","$element","$timeout","$window","Shortcode","AppEvent","DragEvent","IframeEvent"],C.component("appContentIframe",{controller:rt,template:'\n    <iframe class="iframe-frame" ng-attr-src="{{ $ctrl.store.iframeUrl }}"></iframe>\n    <div class="iframe-overlay hidden"></div>\n    <ux-loader loading="$ctrl.loading"></ux-loader>\n  '});class at{constructor(t,e,n,o,s,i){var g=null,r=null,a=n.find(".toolbar-content");t.register("toolbar",this),this.title="",e.$watch((()=>t.states.selectedShortcode),(t=>{if(!t)return;let e=null,n=t.ancestorsAndSelf;for(let o=0;o<n.length;o++)if(!e&&n[o].data.toolbar&&n[o].data.toolbar.showOnChildActive){e=angular.copy(n[o].data.toolbar),e.shortcode=n[o],e.shortcode.children.map((function(e){e.states.active=e.isAncestorOf(t)}));break}e?this.show(e):this.hide()})),e.$on(i.DETACHED,((t,e)=>{g&&g.shortcode===e&&this.hide()})),this.selectShortcode=function(){t.configureShortcode(g.shortcode)},this.show=function(t){g&&g.shortcode===t.shortcode||(t=function(t){return t.shortcode.data.toolbar.showChildrenSelector&&(t.template='<children-selector shortcode="shortcode"></children-selector>'),t}(t),this.title=t.title||t.shortcode.data.name,(g=(t.scope||t.shortcode.$scope).$new()).shortcode=t.shortcode,r=o(t.template||t.shortcode.data.toolbar.template||s.get(t.templateUrl))(g),a.html(r),n.addClass("visible"))},this.hide=function(){g&&(g.$destroy(),r.remove(),r=null,g=null),this.title="",n.removeClass("visible"),a.empty()}}}at.$inject=["app","$scope","$element","$compile","$templateCache","ShortcodeEvent"],C.component("appContentToolbar",{controller:at,template:'\n    <div class="toolbar-wrapper">\n      <h2 class="toolbar-title">\n        <button type="button" class="inline" ng-click="$ctrl.selectShortcode($ctrl.shortcode)">\n          {{ $ctrl.title }}\n        </button>\n      </h2>\n      <div class="toolbar-content"></div>\n      <div class="toolbar-hide">\n        <button type="button" ng-click="$ctrl.hide()">&times;</button>\n      </div>\n    </div>\n  '});class At{constructor(t,e){this.app=t,this.$scope=e,this.currentChild=null}$onInit(){const{app:t,$scope:e}=this;_.each(this.shortcode.children,(t=>{t.states.active&&(this.currentChild=t)})),e.$watch((()=>t.states.selectedShortcode),(t=>{if(!t)return this.currentChild=null;for(let e=0;e<this.shortcode.children.length;e++)t.isSelfOrDescendantOf(this.shortcode.children[e])&&(this.currentChild=this.shortcode.children[e])}))}selectShortcode(t){this.currentChild!==t&&(this.currentChild=t,this.app.selectShortcode(t),this.app.configureShortcode(null))}configureShortcode(t){this.app.configureShortcode(t)}getThumbnail(t){var e=t.data.toolbarThumbnail;return t.options[e]||e||null}}At.$inject=["app","$scope"],C.component("childrenSelector",{controller:At,bindings:{shortcode:"="},template:'\n  <ul class="">\n    <li ng-repeat="shortcode in $ctrl.shortcode.children"\n      draggable-shortcode="shortcode"\n      draggable-options="{\n        targets: [\n          { name: \'left\', addable: false },\n          { name: \'right\', addable: false }\n        ]\n      }">\n      <button type="button"\n        ng-click="$ctrl.selectShortcode(shortcode)"\n        ng-dblclick="$ctrl.configureShortcode(shortcode)"\n        ng-class="{ \'active\': shortcode === $ctrl.currentChild }"\n        wp-attachment="$ctrl.getThumbnail(shortcode)"\n        wp-attachment-size="\'thumbnail\'">\n        {{:: shortcode.data.name }}\n      </button>\n    </li>\n    <li>\n      <add-button\n        index="$ctrl.shortcode.children.length"\n        shortcode="$ctrl.shortcode">\n      </add-button>\n    </li>\n  </ul>\n  '}),C.component("appLoader",{controller:["$scope","store","$element",function(t,e,n){t.$watch((()=>e.loading),(t=>n.toggleClass("loading",t)))}],template:'\n    <div class="app-loader-message">\n\t    <div class="loading-spinner"></div>\n      <p>Loading UX Builder</p>\n    </div>\n  '}),C.component("appModal",{controller:["modal","$element",function(t,e){this.hide=function(){t.hide(this.id)},e.on("contextmenu",(t=>{t.stopPropagation()}),!1)}],bindings:{id:"@",title:"@"},transclude:!0,template:'\n    <div class="app-modal" id="{{ $ctrl.id }}-modal" ng-click="$event.stopPropagation()">\n      <div class="app-modal-content">\n        <button class="app-modal-close" ng-click="$ctrl.hide()">&times;</button>\n        <div class="app-modal-title">{{ $ctrl.title }}</div>\n        <div class="app-modal-body">\n          <ng-transclude></ng-transclude>\n        </div>\n        <div class="app-modal-error error"></div>\n      </div>\n    </div>\n  '});class lt{constructor(t,e,n){this.app=e,this.store=n,this.viewName="home",this.viewProps={},this.permissions=angular.copy(this.store.permissions),t.$watch((()=>n.currentPath),(t=>{const[e="home",...o]=t.split("/").filter(Boolean);"shortcode"===e?(this.viewName="shortcode",this.viewProps={shortcode:n.shortcode[o[0]]}):"settings"===e?(this.viewName="settings",this.viewProps={}):(this.viewName=e,this.viewProps={})}))}toggle(){this.store.showSidebar=!this.store.showSidebar}}lt.$inject=["$scope","app","store"],C.component("appSidebar",{controller:lt,template:n(847)}),C.component("appSidebarView",{transclude:{header:"?viewHeader",body:"?viewBody",footer:"?viewFooter"},template:'\n    <div class="view-header" ng-transclude="header"></div>\n    <div class="view-body" ng-transclude="body"></div>\n    <div class="view-footer" ng-transclude="footer"></div>\n  '});class Ct{constructor(t,e,n,o,s,i){this.app=t,this.store=e,this.$element=n,this.$compile=o,this.$timeout=s,this.$scope=i,this.$wrapper=n.find(".wrapper-inner"),this.$loader=n.find(".loading-spinner"),this.currentScope=null,this.readyTimeout=null,this.app.register("stack",this)}open(t,e,n){this.app.freeze(!0),this.store.$set("stack",this),this.app("tools").hide(),this.$wrapper.empty(),this.$element.toggleClass("stack--large","large"===n),this.$element.addClass("is-visible"),this.$loader.addClass("is-visible"),this.readyTimeout=this.$timeout((()=>{this.$loader.removeClass("is-visible"),this.$compile(t)(e?e.$new():this.$scope,((t,e)=>{this.currentScope=e,this.$wrapper.append(t),this.$wrapper.addClass("is-visible")}))}),300)}close(){this.app.freeze(!1),this.store.$set("stack",!1),this.$element.removeClass("is-visible"),this.$wrapper.removeClass("is-visible").empty(),this.$timeout.cancel(this.readyTimeout),this.currentScope&&this.currentScope.$destroy()}}Ct.$inject=["app","store","$element","$compile","$timeout","$scope"],C.component("appStack",{controller:Ct,template:'\n    <div class="backdrop" ng-click="$ctrl.close()"></div>\n    <div class="wrapper">\n      <div class="loading-spinner"></div>\n      <button type="button" class="close" ng-click="$ctrl.close()">&times;</button>\n      <div class="wrapper-inner"></div>\n    </div>\n  '});class It{constructor(t,e,n,o){this.app=t,this.$element=e,this.$window=n,this.$timeout=o,this.$menu=e.find(".context-menu-menu"),this.$element.on("click",this.close.bind(this)),this.app.register("contextMenu",this)}open(t,e,n,o){this.previousSelected=this.app.states.selectedShortcode,this.app.selectShortcode(t),this.shortcode=t,this.app("tools").hide();let s=n&&o?20:5,i=this.$menu.outerOffset(),g=e.getBoundingClientRect(),r=e.ownerDocument.defaultView.frameElement,a=(n||g.left+g.width/2)-i.width/2,A=(o||g.bottom)+s,l=A+i.height>this.$window.innerHeight;if(l&&(A=(o||g.top)-i.height-s),r){let t=r.getBoundingClientRect();a+=t.left,A+=t.top}this.$menu.css({left:a,top:A}),this.$menu.toggleClass("position-bottom",!l),this.$menu.toggleClass("position-top",l),this.$element.addClass("is-active")}clear(){this.previousSelected=null}close(){this.previousSelected&&(this.app.selectShortcode(null),this.$timeout((()=>{this.previousSelected.$element&&this.app.selectShortcode(this.previousSelected),this.previousSelected=null}),0,!1)),this.shortcode=null,this.$element.removeClass("is-active"),this.app.apply()}}It.$inject=["app","$element","$window","$timeout"];var ct={controller:It,template:n(2103),bindings:{shortcode:"<"}};C.component("contextMenu",ct),C.component("draggableHelper",{template:"\n    <h3><span></span></h3>\n  "}),C.component("flatsomeStudio",{controller:["app","store","$scope","$timeout","$q",function(t,e,n,o,s){this.store=e,this.isInitialized=!1,this.isImporting=!1,this.isActive=!1,this.processed=0,this.progress=0,this.steps=0,this.importImages=!0,this.errors=[],this.content=null,this.images=null,this.title=null,n.$watch((()=>e.showFlatsomeStudio),(t=>{t&&(this.isInitialized=!0)})),this.onMessage=e=>{const n=(e.origin||"").replace(/\/+$/,"");/^http(s)?:\/\/studio\.uxthemes\.com$/.test(n)&&"success"===e.data.status&&(this.content=e.data.data.content,this.images=e.data.data.images,this.title=e.data.data.title,this.steps=Object.keys(this.images).length+1,this.isActive=!0,1===this.steps&&this.store.isRegistered&&this.import(),t.apply())},this.import=()=>{if(!this.store.isRegistered)throw new Error("Must register site to import.");if(this.isImporting=!0,this.progress=0,this.errors=[],1===this.steps||!this.importImages)return this.content=this.content.replace(/{{{.+}}}/g,""),this.addShortcode(this.content);let n=s.when();for(let o in this.images)n=n.then((()=>jQuery.post(e.ajaxUrl,{action:"ux_builder_import_media",url:this.images[o],id:o}).then((({data:e,success:n})=>{this.processed++,this.progress=Math.round(this.processed/this.steps*100),n&&e?this.content=this.content.replaceAll(`{{{${o}}}}`,e.id):n||this.errors.unshift([this.images[o],e?e.message:""]),t.apply()})))).catch((t=>{this.processed++,this.progress=Math.round(this.processed/this.steps*100),this.errors.unshift([this.images[o],t.statusText])}));n=n.then((()=>{this.addShortcode(this.content)}))},this.hide=()=>{this.isActive=!1,this.isImporting=!1,this.content=null,this.images=null,this.title=null,this.errors=[],this.store.showFlatsomeStudio=!1},this.addShortcode=n=>jQuery.post(e.ajaxUrl,{action:"ux_builder_to_array",content:n.replace(/\{{3}\d+\}{3}/,"")}).done((({data:e,success:n})=>{this.progress=100,t.apply(),o((()=>{this.steps=0,this.processed=0,this.errors.length||this.hide(),t.broadcast("flatsome-studio-imported",e)}),250)})),window.addEventListener("message",this.onMessage,!1)}],template:'\n    <div class="flatsome-studio" ng-show="$ctrl.store.showFlatsomeStudio" ng-if="$ctrl.isInitialized">\n      <iframe class="flatsome-studio__iframe" ng-attr-src="{{ $ctrl.store.flatsomeStudioUrl | trusted: \'resourceUrl\' }}" frameborder="0"></iframe>\n      <button class="flatsome-studio__close" ng-click="$ctrl.store.showFlatsomeStudio = false">&times;</button>\n\n      <div class="flatsome-studio__overlay" ng-if="$ctrl.isActive">\n        <div class="flatsome-studio__box">\n          <h5 class="flatsome-studio__box-title">\n            Import\n          </h5>\n          <h2 class="flatsome-studio__box-title">\n            {{ $ctrl.title }}\n          </h2>\n          <div class="flatsome-studio__box-content" ng-if="!$ctrl.isImporting">\n            <div class="notice notice-info" ng-if="!$ctrl.store.isRegistered">\n              <p>Please register your site to import from Flatsome Studio</p>\n              <p><a href="{{:: $ctrl.store.registerUrl }}" target="_blank">Register now</a></p>\n            </div>\n            <label class="flatsome-studio__setting" ng-if="$ctrl.steps > 1">\n              <input type="checkbox" ng-model="$ctrl.importImages" ng-disabled="!$ctrl.store.isRegistered" />\n              Import images ({{ $ctrl.steps - 1 }})\n            </label>\n            <div class="flatsome-studio__actions">\n              <button class="wp-style" ng-click="$ctrl.isActive = false">Cancel</button>\n              <button class="wp-style alt" ng-click="$ctrl.import()" ng-disabled="!$ctrl.store.isRegistered">Start</button>\n            </div>\n          </div>\n          <div class="flatsome-studio__box-content is-importing" ng-if="$ctrl.isImporting">\n            <div class="flatsome-studio__progress">\n              <div class="flatsome-studio__progress-bar" ng-style="{ width: $ctrl.progress + \'%\' }">\n                {{ $ctrl.progress }}%\n              </div>\n            </div>\n            <div ng-if="$ctrl.errors.length" class="flatsome-studio__box-errors">\n              <h4>Some images could not be imported:</h4>\n              <ul>\n                <li ng-repeat="error in $ctrl.errors track by $index" class="error">\n                  {{ error[0] }} ({{ error[1] }})\n                </li>\n              </ul>\n              <button ng-if="$ctrl.steps === $ctrl.processed" class="wp-style alt" ng-click="$ctrl.hide()">\n                Okay\n              </button>\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  '});class pt{constructor(t,e,n,o,s){this.app=t,this.store=e,this.templates=n,this.manager=o,this.contextMenu=s.contextMenu}configureShortcode(){this.contextMenu.clear(),this.app.configureShortcode(this.shortcode)}deleteShortcode(){this.manager.remove(this.shortcode)}duplicateShortcode(){this.contextMenu.clear(),this.app.configureShortcode(this.manager.duplicate(this.shortcode))}copyOptions(){this.store.clipboard.options={tag:this.shortcode.tag,data:angular.copy(this.shortcode.options)}}copyAsShortcode(){jQuery.post(this.store.ajaxUrl,{action:"ux_builder_copy_as_shortcode",security:this.store.nonce,post_id:this.store.post.id,data:{shortcode:this.manager.copy(this.shortcode)}}).done((({data:t,success:e})=>{if(!e)return;const n=t.content.replace(/\\/g,"");prompt("Copy to clipboard: Cmd+C for Mac / Ctrl+C for Windows, then press Enter",n)})).fail((t=>{console.log(t.statusText)}))}pasteOptions(){this.store.clipboard.options&&this.store.clipboard.options.tag===this.shortcode.tag&&(ht(this.shortcode.options.$responsive,this.store.clipboard.options.data.$responsive,this.shortcode.data.options),ht(this.shortcode.options,this.store.clipboard.options.data,this.shortcode.data.options))}saveAsPreset(){this.templates.savePreset(this.shortcode)}}function ht(t,e,n){let o=["textfield","image"];for(let s in t)"$"!==s.charAt(0)&&o.indexOf(n.get(s).type)<0&&(t[s]=e[s])}pt.$inject=["app","store","templates","Shortcode","$scope"],C.component("shortcodeActions",{controller:pt,require:{contextMenu:"^contextMenu"},bindings:{shortcode:"<"},template:'\n    <button type="button" ng-click="$ctrl.configureShortcode()">\n      Options\n    </button>\n    <button type="button" ng-click="$ctrl.duplicateShortcode()">\n      Duplicate\n    </button>\n    <button type="button" ng-click="$ctrl.copyOptions()">\n      Copy options\n    </button>\n    <button type="button"\n      ng-if="$ctrl.store.clipboard.options.tag === $ctrl.shortcode.tag"\n      ng-click="$ctrl.pasteOptions()">\n      Paste options\n    </button>\n    <button type="button" ng-click="$ctrl.copyAsShortcode()">\n      Copy as shortcode\n    </button>\n    <button type="button" ng-if="!$ctrl.shortcode.data.hidden" ng-click="$ctrl.saveAsPreset()">\n      Save as preset&hellip;\n    </button>\n    <button type="button" ng-click="$ctrl.deleteShortcode()">\n      Delete&hellip;\n    </button>\n  '}),C.component("shortcodeHierarchyList",{controller:function(){this.$onInit=()=>{"_root"===this.shortcode.tag?this.label="Add elements":this.label=`Add to ${this.shortcode.data.name}`}},template:'\n    <shortcode-hierarchy-list-item\n      ng-repeat="shortcode in $ctrl.shortcode.children"\n      shortcode="shortcode">\n    </shortcode-hierarchy-list-item>\n    <div class="hierarchy-empty"\n      ng-if="$ctrl.shortcode.isParent">\n      <add-button\n        shortcode="$ctrl.shortcode"\n        index="$ctrl.shortcode.children.length"\n        label="{{:: $ctrl.label }}">\n      </add-button>\n    </div>\n  ',bindings:{shortcode:"<"}});class dt{constructor(t,e,n,o,s,i,g,r,a){this.app=t,this.store=e,this.targets=n,this.$scope=s,this.$iframe=r,this.$element=i,this.draggable=o,this.$interpolate=a,this.ShortcodeEvent=g}$onInit(){const{app:t,store:e,targets:n,$scope:o,$element:s,$interpolate:i,$iframe:g,draggable:r,ShortcodeEvent:a}=this;let A=angular.element("shortcode-hierarchy"),l=i(this.shortcode.data.info),C=r(this.shortcode,s[0],{broadcast:!1});void 0===this.shortcode.states.open&&(this.shortcode.states.open=e.postContent.children.length<=5&&1===this.shortcode.depth),this.shortcode.data.addableSpots&&(n.add("top",{element:s,shortcode:this.shortcode}),n.add("bottom",{element:s,shortcode:this.shortcode})),this.shortcode.isParent&&n.add("center",{element:s,shortcode:this.shortcode}),this.isActive=function(){return t.states.selectedShortcode===this.shortcode},this.outlineShortcode=function(){t.outlineShortcode(this.shortcode)},this.selectShortcode=function(){t.selectShortcode(this.shortcode),g().contents().find("body").scrollToElement(this.shortcode.$element)},this.configureShortcode=function(){t.configureShortcode(this.shortcode)},this.toggleChildren=function(){this.shortcode.states.open=!this.shortcode.states.open},this.getShortcodeInfo=function(){let t=this.shortcode.$scope?this.shortcode.$scope.$customCtrl:null;return t&&angular.isFunction(t.$getShortcodeInfo)?t.$getShortcodeInfo():l(this.shortcode.options)},o.$on(a.SELECTED,((t,e)=>{if(this.shortcode.isSelfOrAncestorOf(e)){e.ancestors.forEach((t=>{t.states.open=!0}));const t=s.get(0).getBoundingClientRect().top+A.scrollTop()-parseInt(A.css("padding-top"))-A.height()/2;A.animate({scrollTop:t})}})),o.$on("$destroy",(()=>{A=null,C.destroy()}))}showContextMenu(t){this.app("contextMenu").open(this.shortcode,t.currentTarget)}}dt.$inject=["app","store","targets","draggable","$scope","$element","ShortcodeEvent","$iframe","$interpolate"],C.component("shortcodeHierarchyListItem",{controller:dt,template:n(679),bindings:{shortcode:"<"}}),C.component("templateImporter",{controller:["app","store",function(t,e){this.value="",this.import=function(){jQuery.post(e.ajaxUrl,{action:"ux_builder_to_array",content:this.value}).done((({data:t,success:e})=>{if(!e)return console.error("Failed to compile template");t.content.children.forEach((t=>{this.manager.addShortcode(t,!1),this.manager.index++})),this.manager.stack.close()}))}}],require:{manager:"^addShortcode"},template:'\n    <div class="template-importer">\n      <textarea style="height: calc(100vh - 185px); margin-top: 20px; margin-bottom: 15px; width: 100%; font-size: .9rem" ng-model="$ctrl.value" placeholder="Insert exported code here or Shortcodes"></textarea>\n      <button type="button" class="wp-style alt button-large button-block" ng-click="$ctrl.import()">\n        Import\n      </button>\n    </div>\n  '}),C.component("templateSelector",{controller:["app","store","iframe","$scope","templates",function(t,e,n,o,s){this.store=e,this.templates=s,this.activeTab="flatsome",this.presets=[],this.isLoading=!0,this.errorMessage="",this.setTemplate=function(o){e.loading=!0,jQuery.post(e.ajaxUrl,{action:"ux_builder_to_array",id:o}).done((({data:s,success:i})=>{if(!i)return console.error(`Failed to compile template ${o}`);e.post.meta.values.hasOwnProperty("_wp_page_template")&&(e.post.meta.values._wp_page_template=e.templates[o].template||"default"),n.reload(s.content),t.apply()}))},this.useContentTemplate=function(o){e.loading=!0,jQuery.post(e.ajaxUrl,{action:"ux_builder_to_array",content:o.raw}).done((({data:s,success:i})=>{if(!i)return console.error(`Failed to compile template ${o.id}`);e.post.meta.values.hasOwnProperty("_wp_page_template")&&(e.post.meta.values._wp_page_template=o.template||"default"),n.reload(s.content),t.apply()}))},this.removeTemplate=async t=>{this.templates.remove(t).catch((t=>{this.errorMessage=t.message}))},this.$onInit=()=>{jQuery.get(e.ajaxUrl,{action:"ux_builder_parse_presets",tag:"_root"}).done((({data:t,success:e})=>{e&&t&&Array.isArray(t.presets)?this.presets=t.presets.filter((t=>t.custom)):this.errorMessage=t?t.message:"Failed to load templates.",this.isLoading=!1,o.$apply()})).fail((t=>{this.errorMessage=t.statusText,this.isLoading=!1,o.$apply()}))};const i=(t,e)=>{"_root"===e.tag&&(this.presets=e.presets.filter((t=>t.custom)),o.$apply())};o.$on("template-saved",i),o.$on("template-removed",i)}],template:'\n    <h2 class="uxb-templates-title">Insert a template</h2>\n    <div class="uxb-tabs">\n      <button class="uxb-tab" ng-class="{ \'uxb-active\': $ctrl.activeTab === \'flatsome\' }" ng-click="$ctrl.activeTab = \'flatsome\'">Flatsome</button>\n      <button class="uxb-tab" ng-class="{ \'uxb-active\': $ctrl.activeTab === \'custom\' }" ng-click="$ctrl.activeTab = \'custom\'">Custom</button>\n    </div>\n    <div class="uxb-templates-custom" ng-if="$ctrl.activeTab === \'custom\'">\n      <div ng-if="$ctrl.isLoading" class="uxb-loading-spinner"></div>\n      <div ng-if="!$ctrl.isLoading && !$ctrl.errorMessage && $ctrl.presets.length === 0">No custom templates yet&hellip;</div>\n      <p ng-if="$ctrl.errorMessage" class="uxb-error">{{ $ctrl.errorMessage }}</p>\n      <div class="uxb-templates-list">\n        <div class="uxb-template" ng-repeat="template in $ctrl.presets">\n          <button class="uxb-template-button" ng-click="$ctrl.useContentTemplate(template)">\n            <div class="uxb-template-icon">\n              <svg width="42" height="42" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M7.73009 2.41274L8.95709 3.63719L3.40181 9.18095L2.17482 7.95652L7.73009 2.41274ZM7.73009 0.242432L0 7.95652L3.40181 11.3513L11.1319 3.63719L7.73009 0.242432Z" fill="#007CBA"/> <path d="M7.8196 11.3114L8.95987 12.4493L7.8196 13.5873L6.67928 12.4493L7.8196 11.3114ZM7.8196 9.14111L4.50439 12.4492L7.8196 15.7575L11.1348 12.4492L7.8196 9.14087V9.14111Z" fill="#007CBA"/> <path d="M12.2322 6.90786L13.3725 8.0458L12.2322 9.18369L11.0921 8.04584L12.2323 6.90795L12.2322 6.90786ZM12.2323 4.73763L8.91699 8.04584L12.2322 11.3542L15.5474 8.04584L12.2322 4.73755L12.2323 4.73763Z" fill="#007CBA" fill-opacity="0.6"/> </svg>\n            </div>\n            <div class="uxb-template-label">{{:: template.name }}</div>\n          </button>\n          <div class="uxb-template-actions">\n            <button ng-click="$ctrl.templates.editTemplate(template)">\n              <span class="dashicons dashicons-edit"></span>\n            </button>\n            <button ng-click="$ctrl.removeTemplate(template)">\n              <span class="dashicons dashicons-trash"></span>\n            </button>\n          </div>\n        </div>\n      </div>\n    </div>\n    <div class="uxb-templates-flatsome" ng-if="$ctrl.activeTab === \'flatsome\'">\n      <h4>*Images are not included.</h4>\n      <div class="uxb-templates-list">\n        <div class="uxb-template" ng-repeat="(id, template) in $ctrl.store.templates track by id">\n          <button type="button" class="uxb-template-button" ng-click="$ctrl.setTemplate(id)">\n            <img ng-attr-src="{{:: template.thumbnail }}">\n            <span class="">{{:: template.name }}</span>\n          </button>\n        </div>\n      </div>\n    </div>\n  '});class ut{constructor(t,e){t.$watch((()=>this.loading),(t=>{e.toggleClass("loading",!!t)}))}}ut.$inject=["$scope","$element"],C.component("uxLoader",{controller:ut,bindings:{loading:"<"}});var mt=n(4729),ft=n.n(mt);function vt(t,e){return bt(t)+(yt(t)||e)}function $t(t,e,n){let o=bt(e),s=vt(e,n),i=(bt(t)||o)+(yt(t)||n);return i!==s?i:s}function bt(t){if(""===t)return t;let e=String(t).match(/[\d\.]+/g);return e&&"-"===String(t).charAt(0)&&(e*=-1),e?parseFloat(e,10).toFixed(wt(e)):""}function yt(t){let e=String(t).match(/\D+$/g);return!!(e&&e.length&&["em","ex","ch","rem","vw","vh","vmin","vmax","%","cm","mm","in","px","pt","pc"].indexOf(e[0])>-1)&&e[0]}function wt(t){let e=String(t).split(".");return e.length>1?e[1].length:0}function xt(t){return{restrict:"A",require:"ngModel",scope:{option:"=scrubfield"},link(e,n,o,s){let i=e.option,g=t(n.get(0),{cssProps:{}}),r=vt(i.default,i.unit),a=i.unit,A=null,l=0;const C=()=>!1;n.on("dragstart",C),s.$render=function(){n[0].value=s.$viewValue!==r?s.$viewValue:""},n.on("blur",(function(t){t.currentTarget.value&&(A=parseFloat(bt(t.currentTarget.value)),a=yt(t.currentTarget.value)||i.unit||"",s.$setViewValue(isNaN(A)?"":`${A}${a}`),s.$render())})),g.get("pan").set({threshold:10,direction:mt.DIRECTION_VERTICAL}),g.on("panstart",(function(t){A=parseFloat(bt(s.$viewValue||i.default))||0,a=yt(s.$viewValue)||yt(i.default)||i.unit,jQuery("body").addClass("resize-ns dragging"),l=t.deltaY>0?10:-10})),g.on("panup pandown",(function(t){var e=-1*(t.deltaY-l),n=A,o=i.step||1,g=wt(o);n=(A+e*o).toFixed(g),n=angular.isDefined(i.min)&&n<=i.min?i.min:n,n=angular.isDefined(i.max)&&n>=i.max?i.max:n,s.$setViewValue($t(n,i.default,a)),s.$render()})),g.on("panend",(function(t){jQuery("body").removeClass("resize-ns dragging"),A=null,s.$render()})),e.$on("$destroy",(function(){n.off("dragstart",C),g.destroy(),g=null}))}}}xt.$inject=["hammer"];class Et{constructor(t,e,n){this.app=t,this.$colorpicker=e.find(".option-colorpicker-input"),this.supportsEyeDropper="EyeDropper"in window,n.$watch((()=>this.model),((t,e)=>{this.$colorpicker.spectrum("set",t)}))}$onInit(){this.$colorpicker.spectrum({color:this.model,allowEmpty:!0,showInput:!0,showAlpha:"hex"!==this.option.format,preferredFormat:this.option.format||"rgb",move:t=>this.setColor(t),hide:t=>this.setColor(t)})}$onDestroy(){this.$colorpicker.spectrum("destroy"),this.$colorpicker=null}setColor(t){this.model=t?t.toString():null,this.$optionsCtrl.shortcode?this.$optionsCtrl.shortcode.apply():this.app.apply()}async eyeDropper(){try{const t=new EyeDropper,e=await t.open();e.sRGBHex&&this.setColor(e.sRGBHex)}catch(t){console.error(t.name,t.message)}}}Et.$inject=["app","$element","$scope"];var St={controller:Et,require:{$optionsCtrl:"^uxOptions"},bindings:{option:"<",model:"="},template:'\n    <input class="option-colorpicker-input" type="text" ng-model="$ctrl.model">\n    <button ng-if="$ctrl.supportsEyeDropper" class="option-colorpicker-eyedropper button blank" ng-click="$ctrl.eyeDropper()"><span class="dashicons dashicons-color-picker"></span></button>\n  '};const Tt=u();class kt{constructor(t,e,n,o,s,i){this.app=t,this.wpEditor=e,this.$scope=n,this.$element=s,n.$on("$destroy",(()=>{this.$element=null}))}autop(){if(!1!==this.option.tinymce){const{selectedShortcode:{content:t}}=this.app.states;this.app.states.selectedShortcode.content=Tt(t)}}openTextEditor(){this.wpEditor.open()}}kt.$inject=["app","wpEditor","$scope","$window","$element","$timeout"];var Dt={controller:kt,require:{$optionsCtrl:"^uxOptions"},bindings:{option:"<",model:"="},template:'\n    <div class="option-text-editor">\n      <button ng-if="$ctrl.option.tinymce !== false" type="button" class="open-editor wp-style alt"\n        ng-click="$ctrl.openTextEditor()">\n        Open Text Editor\n      </button>\n      <textarea class="raw-text"\n        ng-style="{\n          \'fontFamily\': !$ctrl.option.tinymce !== false ? \'monospace\' : null,\n          \'height\': $ctrl.option.height\n        }"\n        ng-blur="$ctrl.autop()"\n        ng-model="$ctrl.app.states.selectedShortcode.content">\n      </textarea>\n    </div>\n  '};class Ot{constructor(t,e,n){this.app=t,this.wpMedia=e,this.$scope=n}$onInit(){const{$scope:t,app:e,option:n,wpMedia:o}=this;this.modal=o.media({title:"Select or Upload file",button:{text:"Use this file"},library:{type:n.mimeTypes||"text/plain"},multiple:!1}),this.modal.on("close",(()=>{o.close()})),this.modal.on("select",(()=>{const t=this.modal.state().get("selection").first().toJSON();this.model=t?.url||null,o.close(),e.apply()})),t.$on("$destroy",(()=>{this.modal.detach(),this.modal=null}))}openMediaModal(){this.modal.open(),this.wpMedia.open()}}Ot.$inject=["app","wpMedia","$scope"];var Nt={controller:Ot,require:{$optionsCtrl:"^uxOptions"},bindings:{option:"<",model:"="},template:'\n    <div class="option-file">\n      <input\n        type="text"\n        placeholder="{{:: $ctrl.option.placeholder }}"\n        ng-model="$ctrl.model"\n        ng-model-options="{\n          updateOn: \'blur default\',\n          debounce: {\n            blur: 0,\n            default: 10\n          }\n        }"\n      >\n    </div>\n    <div class="option-actions">\n      <button\n        type="button"\n        class="wp-style outline"\n        ng-click="$ctrl.model = null"\n        ng-if="$ctrl.model"\n      >\n        Clear\n      </button>\n      <button type="button" class="wp-style outline" ng-click="$ctrl.openMediaModal()">\n        <span>Select file</span>\n      </button>\n    </div>\n  '};class Mt{constructor(t,e,n,o,s,i){this.app=t,this.wpMedia=e,this.$scope=n,this.$element=s,this.$timeout=i,this.$focusPoint=s.find(".focus-point"),this.modal=this.wpMedia.media({title:"Select or Upload image",button:{text:"Use this image"},library:{type:"image"},multiple:!1}),this.modal.on("close",(()=>{this.wpMedia.close()})),this.modal.on("select",(()=>{this.model=this.modal.state().get("selection").first().toJSON().id,this.wpMedia.close(),t.apply()}))}$onInit(){const{$timeout:t,$scope:e}=this;this.option.bgPosition&&t((()=>this.setupFocusPoint()),0,!1),e.$on("$destroy",(()=>{this.option.bgPosition&&this.draggableFocusPoint.destroy(),this.modal.detach(),this.modal=null,this.$focusPoint=null,this.$element=null}))}openMediaModal(){this.modal.open(),this.wpMedia.open()}thumbSize(){return this.option.thumbSize?this.$optionsCtrl.model[this.option.thumbSize]:"thumbnail"}setupFocusPoint(){let t=null,e=this.getBgPosition();this.draggableFocusPoint=new(ft())(this.$focusPoint.get(0)),this.draggableFocusPoint.get("pan").set({direction:ft().DIRECTION_ALL,threshold:0}),this.draggableFocusPoint.on("panstart",(e=>{t=this.$element.find("img").get(0).getClientRects()[0],angular.element("body").addClass("dragging")})),this.draggableFocusPoint.on("pan",(e=>{var n=e.center.y-t.top,o=e.center.x-t.left;n=(n=n<=0?0:n)>=t.height?t.height:n,o=(o=o<=0?0:o)>=t.width?t.width:o;var s=Math.round(o/t.width*100),i={top:`${Math.round(n/t.height*100)}%`,left:`${s}%`};this.setFocusPointPosition(i),this.setBgPositionString(i),this.$scope.$apply()})),this.draggableFocusPoint.on("panend",(t=>{angular.element("body").removeClass("dragging")})),this.setFocusPointPosition(e)}setFocusPointPosition(t){this.$focusPoint.css(t)}setBgPositionString(t){this.option.bgPosition&&(this.$optionsCtrl.model[this.option.bgPosition]=`${t.left} ${t.top}`)}getBgPosition(){var t={left:0,top:0},e=this.$optionsCtrl.model[this.option.bgPosition];return e&&(t.left=e.split(" ")[0],t.top=e.split(" ")[1]),t}getComputedBgPosition(){}}Mt.$inject=["app","wpMedia","$scope","$window","$element","$timeout"];var Pt={controller:Mt,require:{$optionsCtrl:"^uxOptions"},bindings:{option:"<",model:"="},template:'\n    <div class="option-image-wrapper">\n      <img draggable="false"\n        ng-if="$ctrl.model"\n        wp-attachment="$ctrl.model"\n        wp-attachment-model="$ctrl.model"\n        wp-attachment-size="$ctrl.thumbSize()">\n\n      <div class="focus-point"\n        ng-class="{ \'active\': $ctrl.model && $ctrl.option.bgPosition }">\n      </div>\n    </div>\n\n    <div class="option-actions">\n      <button type="button" class="wp-style outline"\n        class="wp-style"\n        ng-click="$ctrl.model = null"\n        ng-if="$ctrl.model">\n        Remove\n      </button>\n      <button type="button" class="wp-style outline"\n        ng-click="$ctrl.openMediaModal()">\n        <span ng-if="$ctrl.model">Change media</span>\n        <span ng-if="!$ctrl.model">Select media</span>\n      </button>\n    </div>\n  '};class Rt{constructor(t,e){this.$scope=t,this.$element=e,this.currentInput=null,this.keys=[],this.modelOptions={updateOn:"blur default"}}$onInit(){const{$scope:t,$element:e}=this;e.toggleClass("is-simple",this.option.simple),this.keys=["first","second"],this.option.simple||this.keys.push("third","fourth"),t.$watch("$ctrl.model",(t=>{const e=t?t.split(" "):this.option.default.split(" ");this.values=this.keys.reduce(((t,n,o)=>(n!==this.currentInput&&(t[n]=e[o]?this.normalizeValue(e[o]):""),t)),this.values||{})}))}onChange(t,e){this.values[t]=e,this.model=this.keys.map((t=>this.normalizeValue(this.values[t]||"0px"))).join(" ")}onFocus(t){this.currentInput=t}onBlur(){this.model&&this.normalizeValues(),this.currentInput=null}clear(){for(let t in this.values)this.values[t]=null;this.model=null}normalizeValues(){for(const t in this.values)this.values[t]=this.normalizeValue(this.values[t]||"0px")}normalizeValue(t){return $t(t,this.option.default||"0",this.option.unit||"px")}}Rt.$inject=["$scope","$element"];var Yt={controller:Rt,bindings:{option:"<",model:"="},template:'\n    <div class="flex-options">\n      <input\n        type="text"\n        scrubfield="$ctrl.option"\n        ng-repeat="name in $ctrl.keys"\n        ng-change="$ctrl.onChange(name, $ctrl.values[name])"\n        ng-focus="$ctrl.onFocus(name)"\n        ng-blur="$ctrl.onBlur()"\n        ng-model="$ctrl.values[name]"\n        ng-model-options="$ctrl.modelOptions"\n        placeholder="{{:: $ctrl.option.default }}"\n        class="{{:: name }}"\n      >\n      <button type="button" class="blank" ng-click="$ctrl.clear()">\n        <span class="dashicons dashicons-no-alt"></span>\n      </button>\n    </div>\n  '};class Bt{constructor(t,e,n,o,s,i){this.app=t,this.store=e,this.wpMedia=n,this.$window=i}openModal(){let t=this.wpMedia.media.gallery,e=this.app.states.selectedShortcode.data.options.flat,n=this.app.states.selectedShortcode,o=e.map((t=>{let e=t.$orgName,o=n.options[t.$name];return"ids"===e&&""===o&&(o="0"),`${e}="${o}"`})),s=t.edit(`[gallery ${o.join(" ")}]`),i=s.content.get();this.wpMedia.open(),s.on("close",(()=>{this.wpMedia.close()})),s.on("uploader:ready",(function(){""===n.options.ids&&(i.toolbar.get("spinner").$el.removeClass("is-active"),i.uploader.$el.removeClass("hidden"))})),s.state("gallery-edit").on("update",(o=>{let i=t.shortcode(o).attrs.named;e.forEach((function(t){if(i.hasOwnProperty(t.$orgName)){let e=angular.isArray(i[t.$orgName])?i[t.$orgName].join(","):i[t.$orgName];n.options[t.$name]=e}})),s.detach(),this.wpMedia.close(),this.app.apply()}))}}Bt.$inject=["app","store","wpMedia","$scope","$element","$window"];var jt={controller:Bt,require:{$optionsCtrl:"^uxOptions"},bindings:{option:"<",model:"="},template:'\n    <button type="button" class="wp-style" ng-click="$ctrl.openModal()">\n      Edit gallery\n    </button>\n  '},Lt=window.jQuery,_t=n.n(Lt);class Vt{constructor(t,e,n,o,s,i,g){this.app=t,this.store=e,this.$scope=o,this.$element=s,this.$timeout=i,this.$log=g}$onInit(){const{app:t,store:e,$scope:n,$element:o,$timeout:s,$log:i}=this;let g=!1,r=null,a=null,A=o.find("> select");if(this.config=angular.merge({delimiter:",",postSelect:!1,termSelect:!1,allowClear:!0,multiple:!1,placeholder:"Select",minOptions:20},this.option.config),A.on("change",(e=>{g?(this.value=A.val()||this.option.default,t.apply()):g=!0})),this.config.multiple||A.removeAttr("multiple"),this.config.postSelect||this.config.termSelect){let t=this.config.postSelect?"ux_builder_get_posts":"ux_builder_get_terms",o=this.config.postSelect?"ux_builder_search_posts":"ux_builder_search_terms",s=angular.isString(this.value)&&this.config.multiple?this.value.split(this.config.delimiter):this.value,g=this.config.postSelect||this.config.termSelect;_t().get(e.ajaxUrl,{id:e.post.id,action:t,option:g,values:s,security:e.nonce}).done((({data:t})=>{t?(angular.forEach(t,(function(t,e){A.append(new Option(t.title,t.id,!0,!0))})),A.trigger("change"),n.$apply()):i.warn("An error occurred")})).fail((t=>{console.log(t)})),this.config.ajax={cache:!0,url:e.ajaxUrl,data:t=>({id:e.post.id,query:t.term,page:t.page,action:o,option:g,security:e.nonce}),processResults:function({data:t},e){return e.page=e.page||1,{results:_t().map(t,(({title:t,slug:e,id:n})=>({text:t,slug:e,id:n})))}}}}if(this.config.multiple||this.config.ajax){const t={...this.config};if("object"==typeof t.options){if(!0===t.sortable){const e=(this.value||"").split(this.config.delimiter);t.data=e.map((e=>({id:e,text:t.options[e]})));for(const n in t.options)e.indexOf(n)<0&&t.data.push({id:n,text:t.options[n]})}else{t.data=[];for(const e in t.options)t.data.push({id:e,text:t.options[e]})}delete t.options}r=A.select2(t).data("select2"),_t()("ul.select2-selection__rendered",o).on("keydown",this.handleSearch)}if(!this.config.ajax){let t=A;s((()=>{t.val(this.config.multiple?this.value.split(this.config.delimiter):this.value),t.trigger("change")}),0,!1)}this.config.multiple&&(this.config.ajax||this.config.sortable)&&(a=_t()("ul.select2-selection__rendered",o).sortable({axis:"y",items:"> .select2-selection__choice",start:function(t,e){const n=_t()("ul.select2-selection__rendered > .select2-selection__choice",o).index(e.item);e.item.data("sortableIndex",n)},stop:function(t,e){const s=_t()("ul.select2-selection__rendered > .select2-selection__choice",o),i=e.item.data("sortableIndex"),g=s.index(e.item),r=A.val()?.slice()||[];r.splice(g,0,r.splice(i,1)[0]),r.forEach((t=>{A.append(A.find('option[value="'+t+'"]')[0])})),A.trigger("change"),n.$apply()}}).data("uiSortable")),n.$on("$destroy",(()=>{A.off("change"),a&&(a._destroy(),a=null),r&&(r.destroy(),r=null,_t()("ul.select2-selection__rendered",o).off("keydown",this.handleSearch)),A=null}))}isSelected(t){return angular.isArray(this.value)?_t().inArray(t,this.value)>-1:this.value===t}handleSearch(t){if(8===t.keyCode&&0===_t()(t.currentTarget).find(".select2-search input").val().length)return!1}}Vt.$inject=["app","store","utils","$scope","$element","$timeout","$log"];var Gt={controller:Vt,bindings:{option:"=",value:"="},template:'\n    <select\n      width="100%"\n      multiple="multiple">\n      <option\n        ng-repeat="option in $ctrl.option.options track by option.value"\n        ng-selected="$ctrl.isSelected(option.value)"\n        value="{{:: option.value }}">\n        {{:: option.label }}\n      </option>\n    </select>\n  '};class Qt{constructor(t,e){this.store=t,this.$scope=e,this.posts=[],this.query=""}onChange(){if(this.query.length<=3)return this.posts=[];_t().get(this.store.ajaxUrl,{action:"ux_builder_search_posts",option:this.option.data,query:this.query}).done((t=>{this.posts=t.data,this.$scope.$digest()})).fail((()=>{}))}selectPost(t){this.option.value=this.post.permalink,this.query="",this.posts=[]}}Qt.$inject=["store","$scope"];var zt={controller:Qt,bindings:{option:"="},template:'\n        <input type="text" ng-model="$ctrl.option.value">\n        <input type="text" placeholder="Search"\n            ng-model="$ctrl.query"\n            ng-change="$ctrl.onChange()"\n            ng-model-options="{\n                updateOn: \'blur default\',\n                debounce: {\n                    blur: 0,\n                    default: 250\n                }\n            }"\n        >\n        <ul ng-if="$ctrl.posts.length">\n            <li ng-repeat="post in $ctrl.posts track by post.ID">\n                <button type="button" ng-click="$ctrl.selectPost(post)">\n                    <h3><span>{{:: post.post_type}}</span>{{:: post.post_title }}</h3>\n                    <small>{{:: post.permalink }}</small>\n                </button>\n            </li>\n        </ul>\n    '};class Ut{constructor(t,e,n){this.$element=e,this.$isToggled=!1,t.$watch((()=>this.$optionCtrl.$cache.isOpen),((t,e)=>{e&&t||(t?this.showOptions():this.hideOptions())}))}toggleOptions(){this.$isToggled=!0,this.$optionCtrl.$cache.isOpen=!this.$optionCtrl.$cache.isOpen}showOptions(t=300){this.$element.find(".ux-group-options").first().slideDown(t,(()=>{this.$element.removeAttr("style")}))}hideOptions(t=300){t=this.$isToggled?t:0,this.$element.find(".ux-group-options").first().slideUp(t)}}Ut.$inject=["$scope","$element","$timeout"];var Ft={controller:Ut,require:{$optionsCtrl:"^uxOptions",$optionCtrl:"^uxOption"},bindings:{option:"<",responsive:"=",model:"="},template:'\n      <button type="button"\n        class="option-group-heading"\n        ng-class="{ \'is-open\': $ctrl.$optionCtrl.$cache.isOpen }"\n        ng-click="$ctrl.toggleOptions()">\n        {{:: $ctrl.option.heading }}\n      </button>\n      <div class="ux-group-options">\n        <div class="option-description" ng-if="$ctrl.option.description" ng-bind-html="$ctrl.option.description | autop | html"></div>\n        <ux-options\n          options="$ctrl.option.options"\n          responsive="$ctrl.responsive"\n          shortcode="$ctrl.$optionsCtrl.shortcode"\n          model="$ctrl.model">\n        </ux-options>\n      </div>\n    '};C.directive("scrubfield",xt),C.component("uxOptionColorpicker",St),C.component("uxOptionEditor",Dt),C.component("uxOptionFile",Nt),C.component("uxOptionImage",Pt),C.component("uxOptionMargins",Yt),C.component("uxOptionGallery",jt),C.component("uxOptionSelect",Gt),C.component("uxOptionUrlfield",zt),C.component("uxOptionGroup",Ft);class Zt{constructor(t,e,n,o,s,i,g,r,a){this.app=t,this.utils=n,this.store=e,this.$element=i,this.$interpolate=s,this.reponsive=a,this.focused=!1,e.cache.option=e.cache.option||{},o.$watch((()=>this.option),(t=>{t.autoFocus&&g((()=>this.setFocus()),this.focused?0:600,!1)}))}$onInit(){const{store:t}=this;this.$cache=t.cache.option[this.option.$id]||{isOpen:!0}}setFocus(){this.$element.find("input, select, textarea, button, a").first().focus().select(),this.focused=!0}isNotDefaultBreakpoint(){return this.reponsive.currentBreakpoint()!==this.reponsive.defaultBreakpoint()}clearResponsiveValue(){const t=this.$optionsCtrl.shortcode.$id,e=this.getResponsiveValues(),n=this.reponsive.currentBreakpoint(),o=this.option.$name;this.store.$addAction("clearResponsiveValue",{key:`clearResponsiveValue-${t}-${o}-${n}`,value:angular.copy(e[n]),breakpointIndex:n,optionName:o,id:t}),e[n]=null,this.model=this.reponsive.getMediaValue(e)}hasResponsiveValue(){return null!==this.getResponsiveValues()[this.reponsive.currentBreakpoint()]}getResponsiveValues(){return this.$optionsCtrl.model.$responsive.hasOwnProperty(this.option.$name)?this.$optionsCtrl.model.$responsive[this.option.$name]:null}}Zt.$inject=["app","store","utils","$scope","$interpolate","$element","$timeout","$iframe","ResponsiveHelper"],C.component("uxOption",{controller:Zt,require:{$optionsCtrl:"^uxOptions"},bindings:{option:"<",model:"="},template:'\n    <h3 ng-if="$ctrl.option.heading" class="option-header">\n      {{:: $ctrl.option.heading }}\n    </h3>\n    <div class="option-body">\n      <div class="option-template">\n        <div ng-include="\'components/ux-option/types/\' + $ctrl.option.type + \'.html\'"></div>\n        <button type="button"\n          class="option-has-custom-value"\n          title="{{ $ctrl.hasResponsiveValue() ? \'Clear responsive option\' : \'\' }}"\n          ng-if="$ctrl.option.responsive"\n          ng-click="$ctrl.clearResponsiveValue()"\n          ng-class="{\n            \'visible\': $ctrl.isNotDefaultBreakpoint(),\n            \'active\': $ctrl.hasResponsiveValue()\n          }">\n        </button>\n      </div>\n      <div class="option-description"\n        ng-if="$ctrl.option.description && $ctrl.option.type !== \'group\'">\n        {{:: $ctrl.option.description }}\n      </div>\n    </div>\n  '});class Wt{constructor(t){this.$scope=t}validOptionConditions(t){if(t.require&&this.shortcode){if("string"==typeof t.require&&this.shortcode.parent.tag!==t.require)return!1;if(angular.isArray(t.require)&&t.require.indexOf(this.shortcode.parent.tag)<0)return!1}return!t.conditions||this.$scope.$eval(t.conditions)}}Wt.$inject=["$scope"],C.component("uxOptions",{controller:Wt,bindings:{options:"<",shortcode:"<",responsive:"=",model:"="},template:'\n      <p ng-if="$ctrl.options.length === 0">No options</p>\n      <ux-option class="option"\n        ng-repeat="option in $ctrl.options track by option.$id"\n        option="option"\n        model="$ctrl.model[option.$name]"\n        ng-if="$ctrl.validOptionConditions(option)"\n        ng-class="{\n          \'option-{{:: option.type }}\': true,\n          \'option-name-{{:: option.$orgName }}\': true,\n          \'is-responsive\': option.responsive,\n          \'is-full-width\': option.fullWidth\n        }">\n      </ux-option>\n    '});class Ht{constructor(t,e,n){this.wpEditor=n,this.wpEditorUrl=e.wpEditorUrl,t.register("wp-editor",this)}open(){this.wpEditor.open()}close(){this.wpEditor.close()}}Ht.$inject=["app","store","wpEditor"],C.component("wpEditor",{controller:Ht,template:'\n    <div class="backdrop" ng-click="$ctrl.close()"></div>\n    <div class="wrapper">\n      <div class="loading-spinner"></div>\n      <button type="button" class="close" ng-click="$ctrl.close()">&times;</button>\n      <div class="wrapper-inner">\n        <iframe class="wp-editor" ng-attr-src="{{ $ctrl.wpEditorUrl }}" scrolling="no" frameborder="0"></iframe>\n      </div>\n    </div>\n  '});class Xt{constructor(t,e,n){this.wpMediaUrl=e.wpMediaUrl}}Xt.$inject=["app","store","wpMedia"],C.component("wpMedia",{controller:Xt,template:'\n    <div class="wp-media-wrapper">\n      <iframe class="wp-media" ng-attr-src="{{ $ctrl.wpMediaUrl }}" scrolling="no" frameborder="0"></iframe>\n    </div>\n  '}),n.g.UxBuilder=A,C.run(["$window",t=>{const e=t.uxBuilderData;e.history=[],e.enabled=!0,e.currentAction=-1}]),C.requires.push("ngAnimate"),C.requires.push("app.filters"),C.requires.push("app.services");for(let t in K.modules.master)C.requires.push(K.modules.master[t])}()}();