!function(a){function i(){t().length&&(a.post(flatsome_variation_images_admin.ajaxurl,{action:"flatsome_additional_variation_images_load_images_ajax",nonce:flatsome_variation_images_admin.nonce.load_images,variation_ids:t()},(i=>{for(let t in i.images)if(i.images.hasOwnProperty(t)){const o=`<span class="ux-additional-variation-images" data-variation-id="${t}">\n\t\t             <h4 class="ux-additional-variation-images__title">Additional images</h4>\n\t\t             ${i.images[t]}\n\t\t             <a href="#" class="ux-additional-variation-images__add">Add additional images</a>\n\t            </span>`,e=a(`#variable_product_options .woocommerce_variation a.upload_image_button[rel="${t}"]`);e.parents(".upload_image").find(".ux-additional-variation-images").length||e.after(o)}o()})),a("#variable_product_options .woocommerce_variation").on("click","a.ux-additional-variation-images__add",(function(i){i.preventDefault();const t=a(this).parents(".upload_image").find("a.upload_image_button").prop("rel"),n=a(this).parents(".upload_image").find("ul.ux-additional-variation-images__list"),d=wp.media.frames.mediaFrame=wp.media({library:{type:"image"},multiple:!0}).on("select",(()=>{d.state().get("selection").map((function(a){if((a=a.toJSON()).id){const i=a.sizes.thumbnail?a.sizes.thumbnail.url:a.url,t=`<li class="ux-additional-variation-images__thumbnail" data-attachment-id="${a.id}">\n                   <img class="ux-additional-variation-images__thumbnail-img" src="${i}" alt="" width="64" height="64"/>\n                   <span class="actions"><a href="#" class="ux-additional-variation-images__delete"></a></span>\n                </li>`;n.append(t)}})),wp.media.model.settings.post.id=t,o(),e(t)}));d.open()})).on("click","a.ux-additional-variation-images__delete",(function(i){i.preventDefault();const t=a(this),o=t.parents(".upload_image").find("a.upload_image_button").prop("rel");t.closest(".ux-additional-variation-images__thumbnail").remove(),e(o)})))}function t(){let i=[];return a("#variable_product_options .woocommerce_variation").each(((a,t)=>{i.push(jQuery(t).find(".upload_image .upload_image_button").prop("rel"))})),i}function o(){a("#variable_product_options .woocommerce_variation").each(((a,i)=>{const t=jQuery(i),o=t.find("a.upload_image_button").prop("rel");t.find(".ux-additional-variation-images__list").sortable({update:function(){e(o)},opacity:.5,placeholder:"sortable-placeholder",tolerance:"pointer",cursor:"move"})}))}function e(i){let t=[];const o=a(`#variable_product_options .woocommerce_variation a.upload_image_button[rel="${i}"]`).parent(".upload_image"),e=o.find("ul.ux-additional-variation-images__list li");e.length?(e.each(((a,i)=>{t.push(i.dataset.attachmentId)})),o.find("input.ux-additional-variation-images-save").val(t.join(","))):o.find("input.ux-additional-variation-images-save").val(""),a("#variable_product_options").find("input").first().change(),o.parents(".woocommerce_variation").first().addClass("variation-needs-update")}a(document).ready((()=>{i()})),a("body").on("woocommerce_variations_added woocommerce_variations_loaded",(()=>{i()}))}(jQuery);