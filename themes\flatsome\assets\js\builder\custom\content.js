(function(){var __webpack_modules__={9371:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";function _asyncIterator(t){var e,i,r,n=2;for("undefined"!=typeof Symbol&&(i=Symbol.asyncIterator,r=Symbol.iterator);n--;){if(i&&null!=(e=t[i]))return e.call(t);if(r&&null!=(e=t[r]))return new AsyncFromSyncIterator(e.call(t));i="@@asyncIterator",r="@@iterator"}throw new TypeError("Object is not async iterable")}function AsyncFromSyncIterator(t){function e(t){if(Object(t)!==t)return Promise.reject(new TypeError(t+" is not an object."));var e=t.done;return Promise.resolve(t.value).then((function(t){return{value:t,done:e}}))}return AsyncFromSyncIterator=function(t){this.s=t,this.n=t.next},AsyncFromSyncIterator.prototype={s:null,n:null,next:function(){return e(this.n.apply(this.s,arguments))},return:function(t){var i=this.s.return;return void 0===i?Promise.resolve({value:t,done:!0}):e(i.apply(this.s,arguments))},throw:function(t){var i=this.s.return;return void 0===i?Promise.reject(t):e(i.apply(this.s,arguments))}},new AsyncFromSyncIterator(t)}var REACT_ELEMENT_TYPE;function _jsx(t,e,i,r){REACT_ELEMENT_TYPE||(REACT_ELEMENT_TYPE="function"==typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103);var n=t&&t.defaultProps,s=arguments.length-3;if(e||0===s||(e={children:void 0}),1===s)e.children=r;else if(s>1){for(var a=new Array(s),o=0;o<s;o++)a[o]=arguments[o+3];e.children=a}if(e&&n)for(var h in n)void 0===e[h]&&(e[h]=n[h]);else e||(e=n||{});return{$$typeof:REACT_ELEMENT_TYPE,type:t,key:void 0===i?null:""+i,ref:null,props:e,_owner:null}}function ownKeys(t,e){var i=Object.keys(t);if(Object.getOwnPropertySymbols){var r=Object.getOwnPropertySymbols(t);e&&(r=r.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),i.push.apply(i,r)}return i}function _objectSpread2(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(i),!0).forEach((function(e){_defineProperty(t,e,i[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(i)):ownKeys(Object(i)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(i,e))}))}return t}function _typeof(t){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_typeof(t)}function _wrapRegExp(){_wrapRegExp=function(t,e){return new i(t,void 0,e)};var t=RegExp.prototype,e=new WeakMap;function i(t,r,n){var s=new RegExp(t,r);return e.set(s,n||e.get(t)),_setPrototypeOf(s,i.prototype)}function r(t,i){var r=e.get(i);return Object.keys(r).reduce((function(e,i){return e[i]=t[r[i]],e}),Object.create(null))}return _inherits(i,RegExp),i.prototype.exec=function(e){var i=t.exec.call(this,e);return i&&(i.groups=r(i,this)),i},i.prototype[Symbol.replace]=function(i,n){if("string"==typeof n){var s=e.get(this);return t[Symbol.replace].call(this,i,n.replace(/\$<([^>]+)>/g,(function(t,e){return"$"+s[e]})))}if("function"==typeof n){var a=this;return t[Symbol.replace].call(this,i,(function(){var t=arguments;return"object"!=typeof t[t.length-1]&&(t=[].slice.call(t)).push(r(t,a)),n.apply(this,t)}))}return t[Symbol.replace].call(this,i,n)},_wrapRegExp.apply(this,arguments)}function _AwaitValue(t){this.wrapped=t}function _AsyncGenerator(t){var e,i;function r(e,i){try{var s=t[e](i),a=s.value,o=a instanceof _AwaitValue;Promise.resolve(o?a.wrapped:a).then((function(t){o?r("return"===e?"return":"next",t):n(s.done?"return":"normal",t)}),(function(t){r("throw",t)}))}catch(t){n("throw",t)}}function n(t,n){switch(t){case"return":e.resolve({value:n,done:!0});break;case"throw":e.reject(n);break;default:e.resolve({value:n,done:!1})}(e=e.next)?r(e.key,e.arg):i=null}this._invoke=function(t,n){return new Promise((function(s,a){var o={key:t,arg:n,resolve:s,reject:a,next:null};i?i=i.next=o:(e=i=o,r(t,n))}))},"function"!=typeof t.return&&(this.return=void 0)}function _wrapAsyncGenerator(t){return function(){return new _AsyncGenerator(t.apply(this,arguments))}}function _awaitAsyncGenerator(t){return new _AwaitValue(t)}function _asyncGeneratorDelegate(t,e){var i={},r=!1;function n(i,n){return r=!0,n=new Promise((function(e){e(t[i](n))})),{done:!1,value:e(n)}}return i["undefined"!=typeof Symbol&&Symbol.iterator||"@@iterator"]=function(){return this},i.next=function(t){return r?(r=!1,t):n("next",t)},"function"==typeof t.throw&&(i.throw=function(t){if(r)throw r=!1,t;return n("throw",t)}),"function"==typeof t.return&&(i.return=function(t){return r?(r=!1,t):n("return",t)}),i}function asyncGeneratorStep(t,e,i,r,n,s,a){try{var o=t[s](a),h=o.value}catch(t){return void i(t)}o.done?e(h):Promise.resolve(h).then(r,n)}function _asyncToGenerator(t){return function(){var e=this,i=arguments;return new Promise((function(r,n){var s=t.apply(e,i);function a(t){asyncGeneratorStep(s,r,n,a,o,"next",t)}function o(t){asyncGeneratorStep(s,r,n,a,o,"throw",t)}a(void 0)}))}}function _classCallCheck(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function _defineProperties(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}function _createClass(t,e,i){return e&&_defineProperties(t.prototype,e),i&&_defineProperties(t,i),Object.defineProperty(t,"prototype",{writable:!1}),t}function _defineEnumerableProperties(t,e){for(var i in e)(s=e[i]).configurable=s.enumerable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(t,i,s);if(Object.getOwnPropertySymbols)for(var r=Object.getOwnPropertySymbols(e),n=0;n<r.length;n++){var s,a=r[n];(s=e[a]).configurable=s.enumerable=!0,"value"in s&&(s.writable=!0),Object.defineProperty(t,a,s)}return t}function _defaults(t,e){for(var i=Object.getOwnPropertyNames(e),r=0;r<i.length;r++){var n=i[r],s=Object.getOwnPropertyDescriptor(e,n);s&&s.configurable&&void 0===t[n]&&Object.defineProperty(t,n,s)}return t}function _defineProperty(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function _extends(){return _extends=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var i=arguments[e];for(var r in i)Object.prototype.hasOwnProperty.call(i,r)&&(t[r]=i[r])}return t},_extends.apply(this,arguments)}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var i=null!=arguments[e]?Object(arguments[e]):{},r=Object.keys(i);"function"==typeof Object.getOwnPropertySymbols&&r.push.apply(r,Object.getOwnPropertySymbols(i).filter((function(t){return Object.getOwnPropertyDescriptor(i,t).enumerable}))),r.forEach((function(e){_defineProperty(t,e,i[e])}))}return t}function _inherits(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&_setPrototypeOf(t,e)}function _inheritsLoose(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,_setPrototypeOf(t,e)}function _getPrototypeOf(t){return _getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},_getPrototypeOf(t)}function _setPrototypeOf(t,e){return _setPrototypeOf=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},_setPrototypeOf(t,e)}function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}function _construct(t,e,i){return _construct=_isNativeReflectConstruct()?Reflect.construct:function(t,e,i){var r=[null];r.push.apply(r,e);var n=new(Function.bind.apply(t,r));return i&&_setPrototypeOf(n,i.prototype),n},_construct.apply(null,arguments)}function _isNativeFunction(t){return-1!==Function.toString.call(t).indexOf("[native code]")}function _wrapNativeSuper(t){var e="function"==typeof Map?new Map:void 0;return _wrapNativeSuper=function(t){if(null===t||!_isNativeFunction(t))return t;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if(void 0!==e){if(e.has(t))return e.get(t);e.set(t,i)}function i(){return _construct(t,arguments,_getPrototypeOf(this).constructor)}return i.prototype=Object.create(t.prototype,{constructor:{value:i,enumerable:!1,writable:!0,configurable:!0}}),_setPrototypeOf(i,t)},_wrapNativeSuper(t)}function _instanceof(t,e){return null!=e&&"undefined"!=typeof Symbol&&e[Symbol.hasInstance]?!!e[Symbol.hasInstance](t):t instanceof e}function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}}function _getRequireWildcardCache(t){if("function"!=typeof WeakMap)return null;var e=new WeakMap,i=new WeakMap;return(_getRequireWildcardCache=function(t){return t?i:e})(t)}function _interopRequireWildcard(t,e){if(!e&&t&&t.__esModule)return t;if(null===t||"object"!=typeof t&&"function"!=typeof t)return{default:t};var i=_getRequireWildcardCache(e);if(i&&i.has(t))return i.get(t);var r={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var s in t)if("default"!==s&&Object.prototype.hasOwnProperty.call(t,s)){var a=n?Object.getOwnPropertyDescriptor(t,s):null;a&&(a.get||a.set)?Object.defineProperty(r,s,a):r[s]=t[s]}return r.default=t,i&&i.set(t,r),r}function _newArrowCheck(t,e){if(t!==e)throw new TypeError("Cannot instantiate an arrow function")}function _objectDestructuringEmpty(t){if(null==t)throw new TypeError("Cannot destructure undefined")}function _objectWithoutPropertiesLoose(t,e){if(null==t)return{};var i,r,n={},s=Object.keys(t);for(r=0;r<s.length;r++)i=s[r],e.indexOf(i)>=0||(n[i]=t[i]);return n}function _objectWithoutProperties(t,e){if(null==t)return{};var i,r,n=_objectWithoutPropertiesLoose(t,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);for(r=0;r<s.length;r++)i=s[r],e.indexOf(i)>=0||Object.prototype.propertyIsEnumerable.call(t,i)&&(n[i]=t[i])}return n}function _assertThisInitialized(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function _possibleConstructorReturn(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(t)}function _createSuper(t){var e=_isNativeReflectConstruct();return function(){var i,r=_getPrototypeOf(t);if(e){var n=_getPrototypeOf(this).constructor;i=Reflect.construct(r,arguments,n)}else i=r.apply(this,arguments);return _possibleConstructorReturn(this,i)}}function _superPropBase(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=_getPrototypeOf(t)););return t}function _get(){return _get="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,i){var r=_superPropBase(t,e);if(r){var n=Object.getOwnPropertyDescriptor(r,e);return n.get?n.get.call(arguments.length<3?t:i):n.value}},_get.apply(this,arguments)}function set(t,e,i,r){return set="undefined"!=typeof Reflect&&Reflect.set?Reflect.set:function(t,e,i,r){var n,s=_superPropBase(t,e);if(s){if((n=Object.getOwnPropertyDescriptor(s,e)).set)return n.set.call(r,i),!0;if(!n.writable)return!1}if(n=Object.getOwnPropertyDescriptor(r,e)){if(!n.writable)return!1;n.value=i,Object.defineProperty(r,e,n)}else _defineProperty(r,e,i);return!0},set(t,e,i,r)}function _set(t,e,i,r,n){if(!set(t,e,i,r||t)&&n)throw new Error("failed to set property");return i}function _taggedTemplateLiteral(t,e){return e||(e=t.slice(0)),Object.freeze(Object.defineProperties(t,{raw:{value:Object.freeze(e)}}))}function _taggedTemplateLiteralLoose(t,e){return e||(e=t.slice(0)),t.raw=e,t}function _readOnlyError(t){throw new TypeError('"'+t+'" is read-only')}function _writeOnlyError(t){throw new TypeError('"'+t+'" is write-only')}function _classNameTDZError(t){throw new Error('Class "'+t+'" cannot be referenced in computed property keys.')}function _temporalUndefined(){}function _tdz(t){throw new ReferenceError(t+" is not defined - temporal dead zone")}function _temporalRef(t,e){return t===_temporalUndefined?_tdz(e):t}function _slicedToArray(t,e){return _arrayWithHoles(t)||_iterableToArrayLimit(t,e)||_unsupportedIterableToArray(t,e)||_nonIterableRest()}function _slicedToArrayLoose(t,e){return _arrayWithHoles(t)||_iterableToArrayLimitLoose(t,e)||_unsupportedIterableToArray(t,e)||_nonIterableRest()}function _toArray(t){return _arrayWithHoles(t)||_iterableToArray(t)||_unsupportedIterableToArray(t)||_nonIterableRest()}function _toConsumableArray(t){return _arrayWithoutHoles(t)||_iterableToArray(t)||_unsupportedIterableToArray(t)||_nonIterableSpread()}function _arrayWithoutHoles(t){if(Array.isArray(t))return _arrayLikeToArray(t)}function _arrayWithHoles(t){if(Array.isArray(t))return t}function _maybeArrayLike(t,e,i){if(e&&!Array.isArray(e)&&"number"==typeof e.length){var r=e.length;return _arrayLikeToArray(e,void 0!==i&&i<r?i:r)}return t(e,i)}function _iterableToArray(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function _iterableToArrayLimit(t,e){var i=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=i){var r,n,s=[],a=!0,o=!1;try{for(i=i.call(t);!(a=(r=i.next()).done)&&(s.push(r.value),!e||s.length!==e);a=!0);}catch(t){o=!0,n=t}finally{try{a||null==i.return||i.return()}finally{if(o)throw n}}return s}}function _iterableToArrayLimitLoose(t,e){var i=t&&("undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"]);if(null!=i){var r=[];for(i=i.call(t),_step;!(_step=i.next()).done&&(r.push(_step.value),!e||r.length!==e););return r}}function _unsupportedIterableToArray(t,e){if(t){if("string"==typeof t)return _arrayLikeToArray(t,e);var i=Object.prototype.toString.call(t).slice(8,-1);return"Object"===i&&t.constructor&&(i=t.constructor.name),"Map"===i||"Set"===i?Array.from(t):"Arguments"===i||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(i)?_arrayLikeToArray(t,e):void 0}}function _arrayLikeToArray(t,e){(null==e||e>t.length)&&(e=t.length);for(var i=0,r=new Array(e);i<e;i++)r[i]=t[i];return r}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _createForOfIteratorHelper(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!i){if(Array.isArray(t)||(i=_unsupportedIterableToArray(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var r=0,n=function(){};return{s:n,n:function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}},e:function(t){throw t},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var s,a=!0,o=!1;return{s:function(){i=i.call(t)},n:function(){var t=i.next();return a=t.done,t},e:function(t){o=!0,s=t},f:function(){try{a||null==i.return||i.return()}finally{if(o)throw s}}}}function _createForOfIteratorHelperLoose(t,e){var i="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(i)return(i=i.call(t)).next.bind(i);if(Array.isArray(t)||(i=_unsupportedIterableToArray(t))||e&&t&&"number"==typeof t.length){i&&(t=i);var r=0;return function(){return r>=t.length?{done:!0}:{done:!1,value:t[r++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _skipFirstGeneratorNext(t){return function(){var e=t.apply(this,arguments);return e.next(),e}}function _toPrimitive(t,e){if("object"!=typeof t||null===t)return t;var i=t[Symbol.toPrimitive];if(void 0!==i){var r=i.call(t,e||"default");if("object"!=typeof r)return r;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function _toPropertyKey(t){var e=_toPrimitive(t,"string");return"symbol"==typeof e?e:String(e)}function _initializerWarningHelper(t,e){throw new Error("Decorating class property failed. Please ensure that proposal-class-properties is enabled and runs after the decorators transform.")}function _initializerDefineProperty(t,e,i,r){i&&Object.defineProperty(t,e,{enumerable:i.enumerable,configurable:i.configurable,writable:i.writable,value:i.initializer?i.initializer.call(r):void 0})}function _applyDecoratedDescriptor(t,e,i,r,n){var s={};return Object.keys(r).forEach((function(t){s[t]=r[t]})),s.enumerable=!!s.enumerable,s.configurable=!!s.configurable,("value"in s||s.initializer)&&(s.writable=!0),s=i.slice().reverse().reduce((function(i,r){return r(t,e,i)||i}),s),n&&void 0!==s.initializer&&(s.value=s.initializer?s.initializer.call(n):void 0,s.initializer=void 0),void 0===s.initializer&&(Object.defineProperty(t,e,s),s=null),s}_AsyncGenerator.prototype["function"==typeof Symbol&&Symbol.asyncIterator||"@@asyncIterator"]=function(){return this},_AsyncGenerator.prototype.next=function(t){return this._invoke("next",t)},_AsyncGenerator.prototype.throw=function(t){return this._invoke("throw",t)},_AsyncGenerator.prototype.return=function(t){return this._invoke("return",t)};var id=0;function _classPrivateFieldLooseKey(t){return"__private_"+id+++"_"+t}function _classPrivateFieldLooseBase(t,e){if(!Object.prototype.hasOwnProperty.call(t,e))throw new TypeError("attempted to use private field on non-instance");return t}function _classPrivateFieldGet(t,e){return _classApplyDescriptorGet(t,_classExtractFieldDescriptor(t,e,"get"))}function _classPrivateFieldSet(t,e,i){return _classApplyDescriptorSet(t,_classExtractFieldDescriptor(t,e,"set"),i),i}function _classPrivateFieldDestructureSet(t,e){return _classApplyDescriptorDestructureSet(t,_classExtractFieldDescriptor(t,e,"set"))}function _classExtractFieldDescriptor(t,e,i){if(!e.has(t))throw new TypeError("attempted to "+i+" private field on non-instance");return e.get(t)}function _classStaticPrivateFieldSpecGet(t,e,i){return _classCheckPrivateStaticAccess(t,e),_classCheckPrivateStaticFieldDescriptor(i,"get"),_classApplyDescriptorGet(t,i)}function _classStaticPrivateFieldSpecSet(t,e,i,r){return _classCheckPrivateStaticAccess(t,e),_classCheckPrivateStaticFieldDescriptor(i,"set"),_classApplyDescriptorSet(t,i,r),r}function _classStaticPrivateMethodGet(t,e,i){return _classCheckPrivateStaticAccess(t,e),i}function _classStaticPrivateMethodSet(){throw new TypeError("attempted to set read only static private field")}function _classApplyDescriptorGet(t,e){return e.get?e.get.call(t):e.value}function _classApplyDescriptorSet(t,e,i){if(e.set)e.set.call(t,i);else{if(!e.writable)throw new TypeError("attempted to set read only private field");e.value=i}}function _classApplyDescriptorDestructureSet(t,e){if(e.set)return"__destrObj"in e||(e.__destrObj={set value(i){e.set.call(t,i)}}),e.__destrObj;if(!e.writable)throw new TypeError("attempted to set read only private field");return e}function _classStaticPrivateFieldDestructureSet(t,e,i){return _classCheckPrivateStaticAccess(t,e),_classCheckPrivateStaticFieldDescriptor(i,"set"),_classApplyDescriptorDestructureSet(t,i)}function _classCheckPrivateStaticAccess(t,e){if(t!==e)throw new TypeError("Private static access of wrong provenance")}function _classCheckPrivateStaticFieldDescriptor(t,e){if(void 0===t)throw new TypeError("attempted to "+e+" private static field before its declaration")}function _decorate(t,e,i,r){var n=_getDecoratorsApi();if(r)for(var s=0;s<r.length;s++)n=r[s](n);var a=e((function(t){n.initializeInstanceElements(t,o.elements)}),i),o=n.decorateClass(_coalesceClassElements(a.d.map(_createElementDescriptor)),t);return n.initializeClassElements(a.F,o.elements),n.runClassFinishers(a.F,o.finishers)}function _getDecoratorsApi(){_getDecoratorsApi=function(){return t};var t={elementsDefinitionOrder:[["method"],["field"]],initializeInstanceElements:function(t,e){["method","field"].forEach((function(i){e.forEach((function(e){e.kind===i&&"own"===e.placement&&this.defineClassElement(t,e)}),this)}),this)},initializeClassElements:function(t,e){var i=t.prototype;["method","field"].forEach((function(r){e.forEach((function(e){var n=e.placement;if(e.kind===r&&("static"===n||"prototype"===n)){var s="static"===n?t:i;this.defineClassElement(s,e)}}),this)}),this)},defineClassElement:function(t,e){var i=e.descriptor;if("field"===e.kind){var r=e.initializer;i={enumerable:i.enumerable,writable:i.writable,configurable:i.configurable,value:void 0===r?void 0:r.call(t)}}Object.defineProperty(t,e.key,i)},decorateClass:function(t,e){var i=[],r=[],n={static:[],prototype:[],own:[]};if(t.forEach((function(t){this.addElementPlacement(t,n)}),this),t.forEach((function(t){if(!_hasDecorators(t))return i.push(t);var e=this.decorateElement(t,n);i.push(e.element),i.push.apply(i,e.extras),r.push.apply(r,e.finishers)}),this),!e)return{elements:i,finishers:r};var s=this.decorateConstructor(i,e);return r.push.apply(r,s.finishers),s.finishers=r,s},addElementPlacement:function(t,e,i){var r=e[t.placement];if(!i&&-1!==r.indexOf(t.key))throw new TypeError("Duplicated element ("+t.key+")");r.push(t.key)},decorateElement:function(t,e){for(var i=[],r=[],n=t.decorators,s=n.length-1;s>=0;s--){var a=e[t.placement];a.splice(a.indexOf(t.key),1);var o=this.fromElementDescriptor(t),h=this.toElementFinisherExtras((0,n[s])(o)||o);t=h.element,this.addElementPlacement(t,e),h.finisher&&r.push(h.finisher);var l=h.extras;if(l){for(var p=0;p<l.length;p++)this.addElementPlacement(l[p],e);i.push.apply(i,l)}}return{element:t,finishers:r,extras:i}},decorateConstructor:function(t,e){for(var i=[],r=e.length-1;r>=0;r--){var n=this.fromClassDescriptor(t),s=this.toClassDescriptor((0,e[r])(n)||n);if(void 0!==s.finisher&&i.push(s.finisher),void 0!==s.elements){t=s.elements;for(var a=0;a<t.length-1;a++)for(var o=a+1;o<t.length;o++)if(t[a].key===t[o].key&&t[a].placement===t[o].placement)throw new TypeError("Duplicated element ("+t[a].key+")")}}return{elements:t,finishers:i}},fromElementDescriptor:function(t){var e={kind:t.kind,key:t.key,placement:t.placement,descriptor:t.descriptor};return Object.defineProperty(e,Symbol.toStringTag,{value:"Descriptor",configurable:!0}),"field"===t.kind&&(e.initializer=t.initializer),e},toElementDescriptors:function(t){if(void 0!==t)return _toArray(t).map((function(t){var e=this.toElementDescriptor(t);return this.disallowProperty(t,"finisher","An element descriptor"),this.disallowProperty(t,"extras","An element descriptor"),e}),this)},toElementDescriptor:function(t){var e=String(t.kind);if("method"!==e&&"field"!==e)throw new TypeError('An element descriptor\'s .kind property must be either "method" or "field", but a decorator created an element descriptor with .kind "'+e+'"');var i=_toPropertyKey(t.key),r=String(t.placement);if("static"!==r&&"prototype"!==r&&"own"!==r)throw new TypeError('An element descriptor\'s .placement property must be one of "static", "prototype" or "own", but a decorator created an element descriptor with .placement "'+r+'"');var n=t.descriptor;this.disallowProperty(t,"elements","An element descriptor");var s={kind:e,key:i,placement:r,descriptor:Object.assign({},n)};return"field"!==e?this.disallowProperty(t,"initializer","A method descriptor"):(this.disallowProperty(n,"get","The property descriptor of a field descriptor"),this.disallowProperty(n,"set","The property descriptor of a field descriptor"),this.disallowProperty(n,"value","The property descriptor of a field descriptor"),s.initializer=t.initializer),s},toElementFinisherExtras:function(t){return{element:this.toElementDescriptor(t),finisher:_optionalCallableProperty(t,"finisher"),extras:this.toElementDescriptors(t.extras)}},fromClassDescriptor:function(t){var e={kind:"class",elements:t.map(this.fromElementDescriptor,this)};return Object.defineProperty(e,Symbol.toStringTag,{value:"Descriptor",configurable:!0}),e},toClassDescriptor:function(t){var e=String(t.kind);if("class"!==e)throw new TypeError('A class descriptor\'s .kind property must be "class", but a decorator created a class descriptor with .kind "'+e+'"');this.disallowProperty(t,"key","A class descriptor"),this.disallowProperty(t,"placement","A class descriptor"),this.disallowProperty(t,"descriptor","A class descriptor"),this.disallowProperty(t,"initializer","A class descriptor"),this.disallowProperty(t,"extras","A class descriptor");var i=_optionalCallableProperty(t,"finisher");return{elements:this.toElementDescriptors(t.elements),finisher:i}},runClassFinishers:function(t,e){for(var i=0;i<e.length;i++){var r=(0,e[i])(t);if(void 0!==r){if("function"!=typeof r)throw new TypeError("Finishers must return a constructor.");t=r}}return t},disallowProperty:function(t,e,i){if(void 0!==t[e])throw new TypeError(i+" can't have a ."+e+" property.")}};return t}function _createElementDescriptor(t){var e,i=_toPropertyKey(t.key);"method"===t.kind?e={value:t.value,writable:!0,configurable:!0,enumerable:!1}:"get"===t.kind?e={get:t.value,configurable:!0,enumerable:!1}:"set"===t.kind?e={set:t.value,configurable:!0,enumerable:!1}:"field"===t.kind&&(e={configurable:!0,writable:!0,enumerable:!0});var r={kind:"field"===t.kind?"field":"method",key:i,placement:t.static?"static":"field"===t.kind?"own":"prototype",descriptor:e};return t.decorators&&(r.decorators=t.decorators),"field"===t.kind&&(r.initializer=t.value),r}function _coalesceGetterSetter(t,e){void 0!==t.descriptor.get?e.descriptor.get=t.descriptor.get:e.descriptor.set=t.descriptor.set}function _coalesceClassElements(t){for(var e=[],i=function(t){return"method"===t.kind&&t.key===s.key&&t.placement===s.placement},r=0;r<t.length;r++){var n,s=t[r];if("method"===s.kind&&(n=e.find(i)))if(_isDataDescriptor(s.descriptor)||_isDataDescriptor(n.descriptor)){if(_hasDecorators(s)||_hasDecorators(n))throw new ReferenceError("Duplicated methods ("+s.key+") can't be decorated.");n.descriptor=s.descriptor}else{if(_hasDecorators(s)){if(_hasDecorators(n))throw new ReferenceError("Decorators can't be placed on different accessors with for the same property ("+s.key+").");n.decorators=s.decorators}_coalesceGetterSetter(s,n)}else e.push(s)}return e}function _hasDecorators(t){return t.decorators&&t.decorators.length}function _isDataDescriptor(t){return void 0!==t&&!(void 0===t.value&&void 0===t.writable)}function _optionalCallableProperty(t,e){var i=t[e];if(void 0!==i&&"function"!=typeof i)throw new TypeError("Expected '"+e+"' to be a function");return i}function _classPrivateMethodGet(t,e,i){if(!e.has(t))throw new TypeError("attempted to get private field on non-instance");return i}function _checkPrivateRedeclaration(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function _classPrivateFieldInitSpec(t,e,i){_checkPrivateRedeclaration(t,e),e.set(t,i)}function _classPrivateMethodInitSpec(t,e){_checkPrivateRedeclaration(t,e),e.add(t)}function _classPrivateMethodSet(){throw new TypeError("attempted to reassign private method")}var _extendStatics=function(t,e){return _extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i])},_extendStatics(t,e)};function __extends(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function i(){this.constructor=t}_extendStatics(t,e),t.prototype=null===e?Object.create(e):(i.prototype=e.prototype,new i)}var _assign=function(){return _assign=Object.assign||function(t){for(var e,i=1,r=arguments.length;i<r;i++)for(var n in e=arguments[i])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t},_assign.apply(this,arguments)};function __rest(t,e){var i={};for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&e.indexOf(r)<0&&(i[r]=t[r]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(r=Object.getOwnPropertySymbols(t);n<r.length;n++)e.indexOf(r[n])<0&&Object.prototype.propertyIsEnumerable.call(t,r[n])&&(i[r[n]]=t[r[n]])}return i}function __decorate(t,e,i,r){var n,s=arguments.length,a=s<3?e:null===r?r=Object.getOwnPropertyDescriptor(e,i):r;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)a=Reflect.decorate(t,e,i,r);else for(var o=t.length-1;o>=0;o--)(n=t[o])&&(a=(s<3?n(a):s>3?n(e,i,a):n(e,i))||a);return s>3&&a&&Object.defineProperty(e,i,a),a}function __param(t,e){return function(i,r){e(i,r,t)}}function __metadata(t,e){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(t,e)}function __awaiter(t,e,i,r){return new(i||(i=Promise))((function(n,s){function a(t){try{h(r.next(t))}catch(t){s(t)}}function o(t){try{h(r.throw(t))}catch(t){s(t)}}function h(t){var e;t.done?n(t.value):(e=t.value,e instanceof i?e:new i((function(t){t(e)}))).then(a,o)}h((r=r.apply(t,e||[])).next())}))}function __generator(t,e){var i,r,n,s,a={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return s={next:o(0),throw:o(1),return:o(2)},"function"==typeof Symbol&&(s[Symbol.iterator]=function(){return this}),s;function o(s){return function(o){return function(s){if(i)throw new TypeError("Generator is already executing.");for(;a;)try{if(i=1,r&&(n=2&s[0]?r.return:s[0]?r.throw||((n=r.return)&&n.call(r),0):r.next)&&!(n=n.call(r,s[1])).done)return n;switch(r=0,n&&(s=[2&s[0],n.value]),s[0]){case 0:case 1:n=s;break;case 4:return a.label++,{value:s[1],done:!1};case 5:a.label++,r=s[1],s=[0];continue;case 7:s=a.ops.pop(),a.trys.pop();continue;default:if(!((n=(n=a.trys).length>0&&n[n.length-1])||6!==s[0]&&2!==s[0])){a=0;continue}if(3===s[0]&&(!n||s[1]>n[0]&&s[1]<n[3])){a.label=s[1];break}if(6===s[0]&&a.label<n[1]){a.label=n[1],n=s;break}if(n&&a.label<n[2]){a.label=n[2],a.ops.push(s);break}n[2]&&a.ops.pop(),a.trys.pop();continue}s=e.call(t,a)}catch(t){s=[6,t],r=0}finally{i=n=0}if(5&s[0])throw s[1];return{value:s[0]?s[1]:void 0,done:!0}}([s,o])}}}var __createBinding=Object.create?function(t,e,i,r){void 0===r&&(r=i),Object.defineProperty(t,r,{enumerable:!0,get:function(){return e[i]}})}:function(t,e,i,r){void 0===r&&(r=i),t[r]=e[i]};function __exportStar(t,e){for(var i in t)"default"===i||Object.prototype.hasOwnProperty.call(e,i)||__createBinding(e,t,i)}function __values(t){var e="function"==typeof Symbol&&Symbol.iterator,i=e&&t[e],r=0;if(i)return i.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&r>=t.length&&(t=void 0),{value:t&&t[r++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function __read(t,e){var i="function"==typeof Symbol&&t[Symbol.iterator];if(!i)return t;var r,n,s=i.call(t),a=[];try{for(;(void 0===e||e-- >0)&&!(r=s.next()).done;)a.push(r.value)}catch(t){n={error:t}}finally{try{r&&!r.done&&(i=s.return)&&i.call(s)}finally{if(n)throw n.error}}return a}function __spread(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(__read(arguments[e]));return t}function __spreadArrays(){for(var t=0,e=0,i=arguments.length;e<i;e++)t+=arguments[e].length;var r=Array(t),n=0;for(e=0;e<i;e++)for(var s=arguments[e],a=0,o=s.length;a<o;a++,n++)r[n]=s[a];return r}function __spreadArray(t,e,i){if(i||2===arguments.length)for(var r,n=0,s=e.length;n<s;n++)!r&&n in e||(r||(r=Array.prototype.slice.call(e,0,n)),r[n]=e[n]);return t.concat(r||Array.prototype.slice.call(e))}function __await(t){return this instanceof __await?(this.v=t,this):new __await(t)}function __asyncGenerator(t,e,i){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var r,n=i.apply(t,e||[]),s=[];return r={},a("next"),a("throw"),a("return"),r[Symbol.asyncIterator]=function(){return this},r;function a(t){n[t]&&(r[t]=function(e){return new Promise((function(i,r){s.push([t,e,i,r])>1||o(t,e)}))})}function o(t,e){try{!function(t){t.value instanceof __await?Promise.resolve(t.value.v).then(h,l):p(s[0][2],t)}(n[t](e))}catch(t){p(s[0][3],t)}}function h(t){o("next",t)}function l(t){o("throw",t)}function p(t,e){t(e),s.shift(),s.length&&o(s[0][0],s[0][1])}}function __asyncDelegator(t){var e,i;return e={},r("next"),r("throw",(function(t){throw t})),r("return"),e[Symbol.iterator]=function(){return this},e;function r(r,n){e[r]=t[r]?function(e){return(i=!i)?{value:__await(t[r](e)),done:"return"===r}:n?n(e):e}:n}}function __asyncValues(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,i=t[Symbol.asyncIterator];return i?i.call(t):(t="function"==typeof __values?__values(t):t[Symbol.iterator](),e={},r("next"),r("throw"),r("return"),e[Symbol.asyncIterator]=function(){return this},e);function r(i){e[i]=t[i]&&function(e){return new Promise((function(r,n){!function(t,e,i,r){Promise.resolve(r).then((function(e){t({value:e,done:i})}),e)}(r,n,(e=t[i](e)).done,e.value)}))}}}function __makeTemplateObject(t,e){return Object.defineProperty?Object.defineProperty(t,"raw",{value:e}):t.raw=e,t}var __setModuleDefault=Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e};function __importStar(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var i in t)"default"!==i&&Object.prototype.hasOwnProperty.call(t,i)&&__createBinding(e,t,i);return __setModuleDefault(e,t),e}function __importDefault(t){return t&&t.__esModule?t:{default:t}}function __classPrivateFieldGet(t,e,i,r){if("a"===i&&!r)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!r:!e.has(t))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===i?r:"a"===i?r.call(t):r?r.value:e.get(t)}function __classPrivateFieldSet(t,e,i,r,n){if("m"===r)throw new TypeError("Private method is not writable");if("a"===r&&!n)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!n:!e.has(t))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===r?n.call(t,i):n?n.value=i:e.set(t,i),i}var t$3=window.ShadowRoot&&(void 0===window.ShadyCSS||window.ShadyCSS.nativeShadow)&&"adoptedStyleSheets"in Document.prototype&&"replace"in CSSStyleSheet.prototype,e$8=Symbol(),n$5=new Map;class s$3{constructor(t,e){if(this._$cssResult$=!0,e!==e$8)throw Error("CSSResult is not constructable. Use `unsafeCSS` or `css` instead.");this.cssText=t}get styleSheet(){var t=n$5.get(this.cssText);return t$3&&void 0===t&&(n$5.set(this.cssText,t=new CSSStyleSheet),t.replaceSync(this.cssText)),t}toString(){return this.cssText}}var o$5=t=>new s$3("string"==typeof t?t:t+"",e$8),r$3=function(t){for(var e=arguments.length,i=new Array(e>1?e-1:0),r=1;r<e;r++)i[r-1]=arguments[r];var n=1===t.length?t[0]:i.reduce(((e,i,r)=>e+(t=>{if(!0===t._$cssResult$)return t.cssText;if("number"==typeof t)return t;throw Error("Value passed to 'css' function must be a 'css' function result: "+t+". Use 'unsafeCSS' to pass non-literal values, but take care to ensure page security.")})(i)+t[r+1]),t[0]);return new s$3(n,e$8)},i$3=(t,e)=>{t$3?t.adoptedStyleSheets=e.map((t=>t instanceof CSSStyleSheet?t:t.styleSheet)):e.forEach((e=>{var i=document.createElement("style"),r=window.litNonce;void 0!==r&&i.setAttribute("nonce",r),i.textContent=e.cssText,t.appendChild(i)}))},S$1=t$3?t=>t:t=>t instanceof CSSStyleSheet?(t=>{var e="";for(var i of t.cssRules)e+=i.cssText;return o$5(e)})(t):t,s$2,e$7=window.trustedTypes,r$2=e$7?e$7.emptyScript:"",h$2=window.reactiveElementPolyfillSupport,o$4={toAttribute(t,e){switch(e){case Boolean:t=t?r$2:null;break;case Object:case Array:t=null==t?t:JSON.stringify(t)}return t},fromAttribute(t,e){var i=t;switch(e){case Boolean:i=null!==t;break;case Number:i=null===t?null:Number(t);break;case Object:case Array:try{i=JSON.parse(t)}catch(t){i=null}}return i}},n$4=(t,e)=>e!==t&&(e==e||t==t),l$3={attribute:!0,type:String,converter:o$4,reflect:!1,hasChanged:n$4},t$2;class a$1 extends HTMLElement{constructor(){super(),this._$Et=new Map,this.isUpdatePending=!1,this.hasUpdated=!1,this._$Ei=null,this.o()}static addInitializer(t){var e;null!==(e=this.l)&&void 0!==e||(this.l=[]),this.l.push(t)}static get observedAttributes(){this.finalize();var t=[];return this.elementProperties.forEach(((e,i)=>{var r=this._$Eh(i,e);void 0!==r&&(this._$Eu.set(r,i),t.push(r))})),t}static createProperty(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:l$3;if(e.state&&(e.attribute=!1),this.finalize(),this.elementProperties.set(t,e),!e.noAccessor&&!this.prototype.hasOwnProperty(t)){var i="symbol"==typeof t?Symbol():"__"+t,r=this.getPropertyDescriptor(t,i,e);void 0!==r&&Object.defineProperty(this.prototype,t,r)}}static getPropertyDescriptor(t,e,i){return{get(){return this[e]},set(r){var n=this[t];this[e]=r,this.requestUpdate(t,n,i)},configurable:!0,enumerable:!0}}static getPropertyOptions(t){return this.elementProperties.get(t)||l$3}static finalize(){if(this.hasOwnProperty("finalized"))return!1;this.finalized=!0;var t=Object.getPrototypeOf(this);if(t.finalize(),this.elementProperties=new Map(t.elementProperties),this._$Eu=new Map,this.hasOwnProperty("properties")){var e=this.properties,i=[...Object.getOwnPropertyNames(e),...Object.getOwnPropertySymbols(e)];for(var r of i)this.createProperty(r,e[r])}return this.elementStyles=this.finalizeStyles(this.styles),!0}static finalizeStyles(t){var e=[];if(Array.isArray(t)){var i=new Set(t.flat(1/0).reverse());for(var r of i)e.unshift(S$1(r))}else void 0!==t&&e.push(S$1(t));return e}static _$Eh(t,e){var i=e.attribute;return!1===i?void 0:"string"==typeof i?i:"string"==typeof t?t.toLowerCase():void 0}o(){var t;this._$Ep=new Promise((t=>this.enableUpdating=t)),this._$AL=new Map,this._$Em(),this.requestUpdate(),null===(t=this.constructor.l)||void 0===t||t.forEach((t=>t(this)))}addController(t){var e,i;(null!==(e=this._$Eg)&&void 0!==e?e:this._$Eg=[]).push(t),void 0!==this.renderRoot&&this.isConnected&&(null===(i=t.hostConnected)||void 0===i||i.call(t))}removeController(t){var e;null===(e=this._$Eg)||void 0===e||e.splice(this._$Eg.indexOf(t)>>>0,1)}_$Em(){this.constructor.elementProperties.forEach(((t,e)=>{this.hasOwnProperty(e)&&(this._$Et.set(e,this[e]),delete this[e])}))}createRenderRoot(){var t,e=null!==(t=this.shadowRoot)&&void 0!==t?t:this.attachShadow(this.constructor.shadowRootOptions);return i$3(e,this.constructor.elementStyles),e}connectedCallback(){var t;void 0===this.renderRoot&&(this.renderRoot=this.createRenderRoot()),this.enableUpdating(!0),null===(t=this._$Eg)||void 0===t||t.forEach((t=>{var e;return null===(e=t.hostConnected)||void 0===e?void 0:e.call(t)}))}enableUpdating(t){}disconnectedCallback(){var t;null===(t=this._$Eg)||void 0===t||t.forEach((t=>{var e;return null===(e=t.hostDisconnected)||void 0===e?void 0:e.call(t)}))}attributeChangedCallback(t,e,i){this._$AK(t,i)}_$ES(t,e){var i,r,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:l$3,s=this.constructor._$Eh(t,n);if(void 0!==s&&!0===n.reflect){var a=(null!==(r=null===(i=n.converter)||void 0===i?void 0:i.toAttribute)&&void 0!==r?r:o$4.toAttribute)(e,n.type);this._$Ei=t,null==a?this.removeAttribute(s):this.setAttribute(s,a),this._$Ei=null}}_$AK(t,e){var i,r,n,s=this.constructor,a=s._$Eu.get(t);if(void 0!==a&&this._$Ei!==a){var o=s.getPropertyOptions(a),h=o.converter,l=null!==(n=null!==(r=null===(i=h)||void 0===i?void 0:i.fromAttribute)&&void 0!==r?r:"function"==typeof h?h:null)&&void 0!==n?n:o$4.fromAttribute;this._$Ei=a,this[a]=l(e,o.type),this._$Ei=null}}requestUpdate(t,e,i){var r=!0;void 0!==t&&(((i=i||this.constructor.getPropertyOptions(t)).hasChanged||n$4)(this[t],e)?(this._$AL.has(t)||this._$AL.set(t,e),!0===i.reflect&&this._$Ei!==t&&(void 0===this._$E_&&(this._$E_=new Map),this._$E_.set(t,i))):r=!1),!this.isUpdatePending&&r&&(this._$Ep=this._$EC())}_$EC(){var t=this;return _asyncToGenerator((function*(){t.isUpdatePending=!0;try{yield t._$Ep}catch(e){Promise.reject(e)}var e=t.scheduleUpdate();return null!=e&&(yield e),!t.isUpdatePending}))()}scheduleUpdate(){return this.performUpdate()}performUpdate(){var t;if(this.isUpdatePending){this.hasUpdated,this._$Et&&(this._$Et.forEach(((t,e)=>this[e]=t)),this._$Et=void 0);var e=!1,i=this._$AL;try{(e=this.shouldUpdate(i))?(this.willUpdate(i),null===(t=this._$Eg)||void 0===t||t.forEach((t=>{var e;return null===(e=t.hostUpdate)||void 0===e?void 0:e.call(t)})),this.update(i)):this._$EU()}catch(t){throw e=!1,this._$EU(),t}e&&this._$AE(i)}}willUpdate(t){}_$AE(t){var e;null===(e=this._$Eg)||void 0===e||e.forEach((t=>{var e;return null===(e=t.hostUpdated)||void 0===e?void 0:e.call(t)})),this.hasUpdated||(this.hasUpdated=!0,this.firstUpdated(t)),this.updated(t)}_$EU(){this._$AL=new Map,this.isUpdatePending=!1}get updateComplete(){return this.getUpdateComplete()}getUpdateComplete(){return this._$Ep}shouldUpdate(t){return!0}update(t){void 0!==this._$E_&&(this._$E_.forEach(((t,e)=>this._$ES(e,this[e],t))),this._$E_=void 0),this._$EU()}updated(t){}firstUpdated(t){}}a$1.finalized=!0,a$1.elementProperties=new Map,a$1.elementStyles=[],a$1.shadowRootOptions={mode:"open"},null==h$2||h$2({ReactiveElement:a$1}),(null!==(s$2=globalThis.reactiveElementVersions)&&void 0!==s$2?s$2:globalThis.reactiveElementVersions=[]).push("1.2.1");var i$2=globalThis.trustedTypes,s$1=i$2?i$2.createPolicy("lit-html",{createHTML:t=>t}):void 0,e$6="lit$".concat((Math.random()+"").slice(9),"$"),o$3="?"+e$6,n$3="<".concat(o$3,">"),l$2=document,h$1=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return l$2.createComment(t)},r$1=t=>null===t||"object"!=typeof t&&"function"!=typeof t,d=Array.isArray,u=t=>{var e;return d(t)||"function"==typeof(null===(e=t)||void 0===e?void 0:e[Symbol.iterator])},c=/<(?:(!--|\/[^a-zA-Z])|(\/?[a-zA-Z][^>\s]*)|(\/?$))/g,v=/-->/g,a=/>/g,f=/>|[ 	\n\r](?:([^\s"'>=/]+)([ 	\n\r]*=[ 	\n\r]*(?:[^ 	\n\r"'`<>=]|("|')|))|$)/g,_=/'/g,m=/"/g,g=/^(?:script|style|textarea)$/i,p=t=>function(e){for(var i=arguments.length,r=new Array(i>1?i-1:0),n=1;n<i;n++)r[n-1]=arguments[n];return{_$litType$:t,strings:e,values:r}},$=p(1),y=p(2),b=Symbol.for("lit-noChange"),w=Symbol.for("lit-nothing"),T=new WeakMap,x=(t,e,i)=>{var r,n,s=null!==(r=null==i?void 0:i.renderBefore)&&void 0!==r?r:e,a=s._$litPart$;if(void 0===a){var o=null!==(n=null==i?void 0:i.renderBefore)&&void 0!==n?n:null;s._$litPart$=a=new N(e.insertBefore(h$1(),o),o,void 0,null!=i?i:{})}return a._$AI(t),a},A=l$2.createTreeWalker(l$2,129,null,!1),C=(t,e)=>{for(var i,r=t.length-1,n=[],s=2===e?"<svg>":"",o=c,h=0;h<r;h++){for(var l=t[h],p=void 0,d=void 0,u=-1,y=0;y<l.length&&(o.lastIndex=y,null!==(d=o.exec(l)));)y=o.lastIndex,o===c?"!--"===d[1]?o=v:void 0!==d[1]?o=a:void 0!==d[2]?(g.test(d[2])&&(i=RegExp("</"+d[2],"g")),o=f):void 0!==d[3]&&(o=f):o===f?">"===d[0]?(o=null!=i?i:c,u=-1):void 0===d[1]?u=-2:(u=o.lastIndex-d[2].length,p=d[1],o=void 0===d[3]?f:'"'===d[3]?m:_):o===m||o===_?o=f:o===v||o===a?o=c:(o=f,i=void 0);var b=o===f&&t[h+1].startsWith("/>")?" ":"";s+=o===c?l+n$3:u>=0?(n.push(p),l.slice(0,u)+"$lit$"+l.slice(u)+e$6+b):l+e$6+(-2===u?(n.push(void 0),h):b)}var E=s+(t[r]||"<?>")+(2===e?"</svg>":"");if(!Array.isArray(t)||!t.hasOwnProperty("raw"))throw Error("invalid template strings array");return[void 0!==s$1?s$1.createHTML(E):E,n]};class E{constructor(t,e){var i,{strings:r,_$litType$:n}=t;this.parts=[];var s=0,a=0,o=r.length-1,h=this.parts,[l,p]=C(r,n);if(this.el=E.createElement(l,e),A.currentNode=this.el.content,2===n){var c=this.el.content,d=c.firstChild;d.remove(),c.append(...d.childNodes)}for(;null!==(i=A.nextNode())&&h.length<o;){if(1===i.nodeType){if(i.hasAttributes()){var f=[];for(var u of i.getAttributeNames())if(u.endsWith("$lit$")||u.startsWith(e$6)){var m=p[a++];if(f.push(u),void 0!==m){var y=i.getAttribute(m.toLowerCase()+"$lit$").split(e$6),v=/([.?@])?(.*)/.exec(m);h.push({type:1,index:s,name:v[2],strings:y,ctor:"."===v[1]?M:"?"===v[1]?H:"@"===v[1]?I:S})}else h.push({type:6,index:s})}for(var b of f)i.removeAttribute(b)}if(g.test(i.tagName)){var _=i.textContent.split(e$6),P=_.length-1;if(P>0){i.textContent=i$2?i$2.emptyScript:"";for(var x=0;x<P;x++)i.append(_[x],h$1()),A.nextNode(),h.push({type:2,index:++s});i.append(_[P],h$1())}}}else if(8===i.nodeType)if(i.data===o$3)h.push({type:2,index:s});else for(var w=-1;-1!==(w=i.data.indexOf(e$6,w+1));)h.push({type:7,index:s}),w+=e$6.length-1;s++}}static createElement(t,e){var i=l$2.createElement("template");return i.innerHTML=t,i}}function P(t,e){var i,r,n,s,a=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t,o=arguments.length>3?arguments[3]:void 0;if(e===b)return e;var h=void 0!==o?null===(i=a._$Cl)||void 0===i?void 0:i[o]:a._$Cu,l=r$1(e)?void 0:e._$litDirective$;return(null==h?void 0:h.constructor)!==l&&(null===(r=null==h?void 0:h._$AO)||void 0===r||r.call(h,!1),void 0===l?h=void 0:(h=new l(t))._$AT(t,a,o),void 0!==o?(null!==(n=(s=a)._$Cl)&&void 0!==n?n:s._$Cl=[])[o]=h:a._$Cu=h),void 0!==h&&(e=P(t,h._$AS(t,e.values),h,o)),e}class V{constructor(t,e){this.v=[],this._$AN=void 0,this._$AD=t,this._$AM=e}get parentNode(){return this._$AM.parentNode}get _$AU(){return this._$AM._$AU}p(t){var e,{el:{content:i},parts:r}=this._$AD,n=(null!==(e=null==t?void 0:t.creationScope)&&void 0!==e?e:l$2).importNode(i,!0);A.currentNode=n;for(var s=A.nextNode(),a=0,o=0,h=r[0];void 0!==h;){if(a===h.index){var l=void 0;2===h.type?l=new N(s,s.nextSibling,this,t):1===h.type?l=new h.ctor(s,h.name,h.strings,this,t):6===h.type&&(l=new L(s,this,t)),this.v.push(l),h=r[++o]}a!==(null==h?void 0:h.index)&&(s=A.nextNode(),a++)}return n}m(t){var e=0;for(var i of this.v)void 0!==i&&(void 0!==i.strings?(i._$AI(t,i,e),e+=i.strings.length-2):i._$AI(t[e])),e++}}class N{constructor(t,e,i,r){var n;this.type=2,this._$AH=w,this._$AN=void 0,this._$AA=t,this._$AB=e,this._$AM=i,this.options=r,this._$Cg=null===(n=null==r?void 0:r.isConnected)||void 0===n||n}get _$AU(){var t,e;return null!==(e=null===(t=this._$AM)||void 0===t?void 0:t._$AU)&&void 0!==e?e:this._$Cg}get parentNode(){var t=this._$AA.parentNode,e=this._$AM;return void 0!==e&&11===t.nodeType&&(t=e.parentNode),t}get startNode(){return this._$AA}get endNode(){return this._$AB}_$AI(t){t=P(this,t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:this),r$1(t)?t===w||null==t||""===t?(this._$AH!==w&&this._$AR(),this._$AH=w):t!==this._$AH&&t!==b&&this.$(t):void 0!==t._$litType$?this.T(t):void 0!==t.nodeType?this.S(t):u(t)?this.A(t):this.$(t)}M(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this._$AB;return this._$AA.parentNode.insertBefore(t,e)}S(t){this._$AH!==t&&(this._$AR(),this._$AH=this.M(t))}$(t){this._$AH!==w&&r$1(this._$AH)?this._$AA.nextSibling.data=t:this.S(l$2.createTextNode(t)),this._$AH=t}T(t){var e,{values:i,_$litType$:r}=t,n="number"==typeof r?this._$AC(t):(void 0===r.el&&(r.el=E.createElement(r.h,this.options)),r);if((null===(e=this._$AH)||void 0===e?void 0:e._$AD)===n)this._$AH.m(i);else{var s=new V(n,this),a=s.p(this.options);s.m(i),this.S(a),this._$AH=s}}_$AC(t){var e=T.get(t.strings);return void 0===e&&T.set(t.strings,e=new E(t)),e}A(t){d(this._$AH)||(this._$AH=[],this._$AR());var e,i=this._$AH,r=0;for(var n of t)r===i.length?i.push(e=new N(this.M(h$1()),this.M(h$1()),this,this.options)):e=i[r],e._$AI(n),r++;r<i.length&&(this._$AR(e&&e._$AB.nextSibling,r),i.length=r)}_$AR(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this._$AA.nextSibling,i=arguments.length>1?arguments[1]:void 0;for(null===(t=this._$AP)||void 0===t||t.call(this,!1,!0,i);e&&e!==this._$AB;){var r=e.nextSibling;e.remove(),e=r}}setConnected(t){var e;void 0===this._$AM&&(this._$Cg=t,null===(e=this._$AP)||void 0===e||e.call(this,t))}}class S{constructor(t,e,i,r,n){this.type=1,this._$AH=w,this._$AN=void 0,this.element=t,this.name=e,this._$AM=r,this.options=n,i.length>2||""!==i[0]||""!==i[1]?(this._$AH=Array(i.length-1).fill(new String),this.strings=i):this._$AH=w}get tagName(){return this.element.tagName}get _$AU(){return this._$AM._$AU}_$AI(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this,i=arguments.length>2?arguments[2]:void 0,r=arguments.length>3?arguments[3]:void 0,n=this.strings,s=!1;if(void 0===n)t=P(this,t,e,0),(s=!r$1(t)||t!==this._$AH&&t!==b)&&(this._$AH=t);else{var a,o,h=t;for(t=n[0],a=0;a<n.length-1;a++)(o=P(this,h[i+a],e,a))===b&&(o=this._$AH[a]),s||(s=!r$1(o)||o!==this._$AH[a]),o===w?t=w:t!==w&&(t+=(null!=o?o:"")+n[a+1]),this._$AH[a]=o}s&&!r&&this.k(t)}k(t){t===w?this.element.removeAttribute(this.name):this.element.setAttribute(this.name,null!=t?t:"")}}class M extends S{constructor(){super(...arguments),this.type=3}k(t){this.element[this.name]=t===w?void 0:t}}var k=i$2?i$2.emptyScript:"";class H extends S{constructor(){super(...arguments),this.type=4}k(t){t&&t!==w?this.element.setAttribute(this.name,k):this.element.removeAttribute(this.name)}}class I extends S{constructor(t,e,i,r,n){super(t,e,i,r,n),this.type=5}_$AI(t){var e;if((t=null!==(e=P(this,t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:this,0))&&void 0!==e?e:w)!==b){var i=this._$AH,r=t===w&&i!==w||t.capture!==i.capture||t.once!==i.once||t.passive!==i.passive,n=t!==w&&(i===w||r);r&&this.element.removeEventListener(this.name,this,i),n&&this.element.addEventListener(this.name,this,t),this._$AH=t}}handleEvent(t){var e,i;"function"==typeof this._$AH?this._$AH.call(null!==(i=null===(e=this.options)||void 0===e?void 0:e.host)&&void 0!==i?i:this.element,t):this._$AH.handleEvent(t)}}class L{constructor(t,e,i){this.element=t,this.type=6,this._$AN=void 0,this._$AM=e,this.options=i}get _$AU(){return this._$AM._$AU}_$AI(t){P(this,t)}}var R={P:"$lit$",V:e$6,L:o$3,I:1,N:C,R:V,D:u,j:P,H:N,O:S,F:H,B:I,W:M,Z:L},z=window.litHtmlPolyfillSupport,l$1,o$2;null==z||z(E,N),(null!==(t$2=globalThis.litHtmlVersions)&&void 0!==t$2?t$2:globalThis.litHtmlVersions=[]).push("2.1.2");var r=a$1;class s extends a$1{constructor(){super(...arguments),this.renderOptions={host:this},this._$Dt=void 0}createRenderRoot(){var t,e,i=super.createRenderRoot();return null!==(t=(e=this.renderOptions).renderBefore)&&void 0!==t||(e.renderBefore=i.firstChild),i}update(t){var e=this.render();this.hasUpdated||(this.renderOptions.isConnected=this.isConnected),super.update(t),this._$Dt=x(e,this.renderRoot,this.renderOptions)}connectedCallback(){var t;super.connectedCallback(),null===(t=this._$Dt)||void 0===t||t.setConnected(!0)}disconnectedCallback(){var t;super.disconnectedCallback(),null===(t=this._$Dt)||void 0===t||t.setConnected(!1)}render(){return b}}s.finalized=!0,s._$litElement$=!0,null===(l$1=globalThis.litElementHydrateSupport)||void 0===l$1||l$1.call(globalThis,{LitElement:s});var n$2=globalThis.litElementPolyfillSupport;null==n$2||n$2({LitElement:s});var h={_$AK:(t,e,i)=>{t._$AK(e,i)},_$AL:t=>t._$AL};(null!==(o$2=globalThis.litElementVersions)&&void 0!==o$2?o$2:globalThis.litElementVersions=[]).push("3.1.2");var n$1=t=>e=>"function"==typeof e?((t,e)=>(window.customElements.define(t,e),e))(t,e):((t,e)=>{var{kind:i,elements:r}=e;return{kind:i,elements:r,finisher(e){window.customElements.define(t,e)}}})(t,e),i$1=(t,e)=>"method"===e.kind&&e.descriptor&&!("value"in e.descriptor)?_objectSpread2(_objectSpread2({},e),{},{finisher(i){i.createProperty(e.key,t)}}):{kind:"field",key:Symbol(),placement:"own",descriptor:{},originalKey:e.key,initializer(){"function"==typeof e.initializer&&(this[e.key]=e.initializer.call(this))},finisher(i){i.createProperty(e.key,t)}};function e$5(t){return(e,i)=>void 0!==i?((t,e,i)=>{e.constructor.createProperty(i,t)})(t,e,i):i$1(t,e)}function t$1(t){return e$5(_objectSpread2(_objectSpread2({},t),{},{state:!0}))}var e$4=(t,e,i)=>{Object.defineProperty(e,i,t)},t=(t,e)=>({kind:"method",placement:"prototype",key:e.key,descriptor:t}),o$1=t=>{var{finisher:e,descriptor:i}=t;return(t,r)=>{var n;if(void 0===r){var s=null!==(n=t.originalKey)&&void 0!==n?n:t.key,a=null!=i?{kind:"method",placement:"prototype",key:s,descriptor:i(t.key)}:_objectSpread2(_objectSpread2({},t),{},{key:s});return null!=e&&(a.finisher=function(t){e(t,s)}),a}var o=t.constructor;void 0!==i&&Object.defineProperty(t,r,i(r)),null==e||e(o,r)}},n;function e$3(t){return o$1({finisher:(e,i)=>{Object.assign(e.prototype[i],t)}})}function i(t,e){return o$1({descriptor:i=>{var r={get(){var e,i;return null!==(i=null===(e=this.renderRoot)||void 0===e?void 0:e.querySelector(t))&&void 0!==i?i:null},enumerable:!0,configurable:!0};if(e){var n="symbol"==typeof i?Symbol():"__"+i;r.get=function(){var e,i;return void 0===this[n]&&(this[n]=null!==(i=null===(e=this.renderRoot)||void 0===e?void 0:e.querySelector(t))&&void 0!==i?i:null),this[n]}}return r}})}function e$2(t){return o$1({descriptor:e=>({get(){var e,i;return null!==(i=null===(e=this.renderRoot)||void 0===e?void 0:e.querySelectorAll(t))&&void 0!==i?i:[]},enumerable:!0,configurable:!0})})}function e$1(t){return o$1({descriptor:e=>({get(){var e=this;return _asyncToGenerator((function*(){var i;return yield e.updateComplete,null===(i=e.renderRoot)||void 0===i?void 0:i.querySelector(t)}))()},enumerable:!0,configurable:!0})})}var e=null!=(null===(n=window.HTMLSlotElement)||void 0===n?void 0:n.prototype.assignedElements)?(t,e)=>t.assignedElements(e):(t,e)=>t.assignedNodes(e).filter((t=>t.nodeType===Node.ELEMENT_NODE));function l(t){var{slot:i,selector:r}=null!=t?t:{};return o$1({descriptor:n=>({get(){var n,s="slot"+(i?"[name=".concat(i,"]"):":not([name])"),a=null===(n=this.renderRoot)||void 0===n?void 0:n.querySelector(s),o=null!=a?e(a,t):[];return r?o.filter((t=>t.matches(r))):o},enumerable:!0,configurable:!0})})}function o(t,e,i){var r,n=t;return"object"==typeof t?(n=t.slot,r=t):r={flatten:e},i?l({slot:n,flatten:e,selector:i}):o$1({descriptor:t=>({get(){var t,e,i="slot"+(n?"[name=".concat(n,"]"):":not([name])"),s=null===(t=this.renderRoot)||void 0===t?void 0:t.querySelector(i);return null!==(e=null==s?void 0:s.assignedNodes(r))&&void 0!==e?e:[]},enumerable:!0,configurable:!0})})}var commonjsGlobal="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:void 0!==__webpack_require__.g?__webpack_require__.g:"undefined"!=typeof self?self:{};function getDefaultExportFromCjs(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function getDefaultExportFromNamespaceIfPresent(t){return t&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function getDefaultExportFromNamespaceIfNotNamed(t){return t&&Object.prototype.hasOwnProperty.call(t,"default")&&1===Object.keys(t).length?t.default:t}function getAugmentedNamespace(t){if(t.__esModule)return t;var e=Object.defineProperty({},"__esModule",{value:!0});return Object.keys(t).forEach((function(i){var r=Object.getOwnPropertyDescriptor(t,i);Object.defineProperty(e,i,r.get?r:{enumerable:!0,get:function(){return t[i]}})})),e}function commonjsRequire(t){throw new Error('Could not dynamically require "'+t+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var lottie$1={exports:{}};(function(module,exports){var factory;"undefined"!=typeof navigator&&(factory=function(){var svgNS="http://www.w3.org/2000/svg",locationHref="",_useWebWorker=!1,initialDefaultFrame=-999999,setWebWorker=function(t){_useWebWorker=!!t},getWebWorker=function(){return _useWebWorker},setLocationHref=function(t){locationHref=t},getLocationHref=function(){return locationHref};function createTag(t){return document.createElement(t)}function extendPrototype(t,e){var i,r,n=t.length;for(i=0;i<n;i+=1)for(var s in r=t[i].prototype)Object.prototype.hasOwnProperty.call(r,s)&&(e.prototype[s]=r[s])}function getDescriptor(t,e){return Object.getOwnPropertyDescriptor(t,e)}function createProxyFunction(t){function e(){}return e.prototype=t,e}var audioControllerFactory=function(){function t(t){this.audios=[],this.audioFactory=t,this._volume=1,this._isMuted=!1}return t.prototype={addAudio:function(t){this.audios.push(t)},pause:function(){var t,e=this.audios.length;for(t=0;t<e;t+=1)this.audios[t].pause()},resume:function(){var t,e=this.audios.length;for(t=0;t<e;t+=1)this.audios[t].resume()},setRate:function(t){var e,i=this.audios.length;for(e=0;e<i;e+=1)this.audios[e].setRate(t)},createAudio:function(t){return this.audioFactory?this.audioFactory(t):window.Howl?new window.Howl({src:[t]}):{isPlaying:!1,play:function(){this.isPlaying=!0},seek:function(){this.isPlaying=!1},playing:function(){},rate:function(){},setVolume:function(){}}},setAudioFactory:function(t){this.audioFactory=t},setVolume:function(t){this._volume=t,this._updateVolume()},mute:function(){this._isMuted=!0,this._updateVolume()},unmute:function(){this._isMuted=!1,this._updateVolume()},getVolume:function(){return this._volume},_updateVolume:function(){var t,e=this.audios.length;for(t=0;t<e;t+=1)this.audios[t].volume(this._volume*(this._isMuted?0:1))}},function(){return new t}}(),createTypedArray=function(){function t(t,e){var i,r=0,n=[];switch(t){case"int16":case"uint8c":i=1;break;default:i=1.1}for(r=0;r<e;r+=1)n.push(i);return n}return"function"==typeof Uint8ClampedArray&&"function"==typeof Float32Array?function(e,i){return"float32"===e?new Float32Array(i):"int16"===e?new Int16Array(i):"uint8c"===e?new Uint8ClampedArray(i):t(e,i)}:t}();function createSizedArray(t){return Array.apply(null,{length:t})}function _typeof$6(t){return _typeof$6="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_typeof$6(t)}var subframeEnabled=!0,expressionsPlugin=null,expressionsInterfaces=null,idPrefix$1="",isSafari=/^((?!chrome|android).)*safari/i.test(navigator.userAgent),_shouldRoundValues=!1,bmPow=Math.pow,bmSqrt=Math.sqrt,bmFloor=Math.floor,bmMax=Math.max,bmMin=Math.min,BMMath={};function ProjectInterface$1(){return{}}!function(){var t,e=["abs","acos","acosh","asin","asinh","atan","atanh","atan2","ceil","cbrt","expm1","clz32","cos","cosh","exp","floor","fround","hypot","imul","log","log1p","log2","log10","max","min","pow","random","round","sign","sin","sinh","sqrt","tan","tanh","trunc","E","LN10","LN2","LOG10E","LOG2E","PI","SQRT1_2","SQRT2"],i=e.length;for(t=0;t<i;t+=1)BMMath[e[t]]=Math[e[t]]}(),BMMath.random=Math.random,BMMath.abs=function(t){if("object"===_typeof$6(t)&&t.length){var e,i=createSizedArray(t.length),r=t.length;for(e=0;e<r;e+=1)i[e]=Math.abs(t[e]);return i}return Math.abs(t)};var defaultCurveSegments=150,degToRads=Math.PI/180,roundCorner=.5519;function roundValues(t){_shouldRoundValues=!!t}function bmRnd(t){return _shouldRoundValues?Math.round(t):t}function styleDiv(t){t.style.position="absolute",t.style.top=0,t.style.left=0,t.style.display="block",t.style.transformOrigin="0 0",t.style.webkitTransformOrigin="0 0",t.style.backfaceVisibility="visible",t.style.webkitBackfaceVisibility="visible",t.style.transformStyle="preserve-3d",t.style.webkitTransformStyle="preserve-3d",t.style.mozTransformStyle="preserve-3d"}function BMEnterFrameEvent(t,e,i,r){this.type=t,this.currentTime=e,this.totalTime=i,this.direction=r<0?-1:1}function BMCompleteEvent(t,e){this.type=t,this.direction=e<0?-1:1}function BMCompleteLoopEvent(t,e,i,r){this.type=t,this.currentLoop=i,this.totalLoops=e,this.direction=r<0?-1:1}function BMSegmentStartEvent(t,e,i){this.type=t,this.firstFrame=e,this.totalFrames=i}function BMDestroyEvent(t,e){this.type=t,this.target=e}function BMRenderFrameErrorEvent(t,e){this.type="renderFrameError",this.nativeError=t,this.currentTime=e}function BMConfigErrorEvent(t){this.type="configError",this.nativeError=t}function BMAnimationConfigErrorEvent(t,e){this.type=t,this.nativeError=e}var createElementID=(_count=0,function(){return idPrefix$1+"__lottie_element_"+(_count+=1)}),_count;function HSVtoRGB(t,e,i){var r,n,s,a,o,h,l,p;switch(h=i*(1-e),l=i*(1-(o=6*t-(a=Math.floor(6*t)))*e),p=i*(1-(1-o)*e),a%6){case 0:r=i,n=p,s=h;break;case 1:r=l,n=i,s=h;break;case 2:r=h,n=i,s=p;break;case 3:r=h,n=l,s=i;break;case 4:r=p,n=h,s=i;break;case 5:r=i,n=h,s=l}return[r,n,s]}function RGBtoHSV(t,e,i){var r,n=Math.max(t,e,i),s=Math.min(t,e,i),a=n-s,o=0===n?0:a/n,h=n/255;switch(n){case s:r=0;break;case t:r=e-i+a*(e<i?6:0),r/=6*a;break;case e:r=i-t+2*a,r/=6*a;break;case i:r=t-e+4*a,r/=6*a}return[r,o,h]}function addSaturationToRGB(t,e){var i=RGBtoHSV(255*t[0],255*t[1],255*t[2]);return i[1]+=e,i[1]>1?i[1]=1:i[1]<=0&&(i[1]=0),HSVtoRGB(i[0],i[1],i[2])}function addBrightnessToRGB(t,e){var i=RGBtoHSV(255*t[0],255*t[1],255*t[2]);return i[2]+=e,i[2]>1?i[2]=1:i[2]<0&&(i[2]=0),HSVtoRGB(i[0],i[1],i[2])}function addHueToRGB(t,e){var i=RGBtoHSV(255*t[0],255*t[1],255*t[2]);return i[0]+=e/360,i[0]>1?i[0]-=1:i[0]<0&&(i[0]+=1),HSVtoRGB(i[0],i[1],i[2])}var rgbToHex=function(){var t,e,i=[];for(t=0;t<256;t+=1)e=t.toString(16),i[t]=1===e.length?"0"+e:e;return function(t,e,r){return t<0&&(t=0),e<0&&(e=0),r<0&&(r=0),"#"+i[t]+i[e]+i[r]}}(),setSubframeEnabled=function(t){subframeEnabled=!!t},getSubframeEnabled=function(){return subframeEnabled},setExpressionsPlugin=function(t){expressionsPlugin=t},getExpressionsPlugin=function(){return expressionsPlugin},setExpressionInterfaces=function(t){expressionsInterfaces=t},getExpressionInterfaces=function(){return expressionsInterfaces},setDefaultCurveSegments=function(t){defaultCurveSegments=t},getDefaultCurveSegments=function(){return defaultCurveSegments},setIdPrefix=function(t){idPrefix$1=t},getIdPrefix=function(){return idPrefix$1};function createNS(t){return document.createElementNS(svgNS,t)}function _typeof$5(t){return _typeof$5="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_typeof$5(t)}var dataManager=function(){var t,e,i=1,r=[],n={onmessage:function(){},postMessage:function(e){t({data:e})}},s={postMessage:function(t){n.onmessage({data:t})}};function a(){e||(e=function(e){if(window.Worker&&window.Blob&&getWebWorker()){var i=new Blob(["var _workerSelf = self; self.onmessage = ",e.toString()],{type:"text/javascript"}),r=URL.createObjectURL(i);return new Worker(r)}return t=e,n}((function(t){if(s.dataManager||(s.dataManager=function(){function t(n,s){var a,o,h,l,p,d,f=n.length;for(o=0;o<f;o+=1)if("ks"in(a=n[o])&&!a.completed){if(a.completed=!0,a.hasMask){var u=a.masksProperties;for(l=u.length,h=0;h<l;h+=1)if(u[h].pt.k.i)r(u[h].pt.k);else for(d=u[h].pt.k.length,p=0;p<d;p+=1)u[h].pt.k[p].s&&r(u[h].pt.k[p].s[0]),u[h].pt.k[p].e&&r(u[h].pt.k[p].e[0])}0===a.ty?(a.layers=e(a.refId,s),t(a.layers,s)):4===a.ty?i(a.shapes):5===a.ty&&c(a)}}function e(t,e){var i=function(t,e){for(var i=0,r=e.length;i<r;){if(e[i].id===t)return e[i];i+=1}return null}(t,e);return i?i.layers.__used?JSON.parse(JSON.stringify(i.layers)):(i.layers.__used=!0,i.layers):null}function i(t){var e,n,s;for(e=t.length-1;e>=0;e-=1)if("sh"===t[e].ty)if(t[e].ks.k.i)r(t[e].ks.k);else for(s=t[e].ks.k.length,n=0;n<s;n+=1)t[e].ks.k[n].s&&r(t[e].ks.k[n].s[0]),t[e].ks.k[n].e&&r(t[e].ks.k[n].e[0]);else"gr"===t[e].ty&&i(t[e].it)}function r(t){var e,i=t.i.length;for(e=0;e<i;e+=1)t.i[e][0]+=t.v[e][0],t.i[e][1]+=t.v[e][1],t.o[e][0]+=t.v[e][0],t.o[e][1]+=t.v[e][1]}function n(t,e){var i=e?e.split("."):[100,100,100];return t[0]>i[0]||!(i[0]>t[0])&&(t[1]>i[1]||!(i[1]>t[1])&&(t[2]>i[2]||!(i[2]>t[2])&&null))}var s,a=function(){var t=[4,4,14];function e(t){var e,i,r,n=t.length;for(e=0;e<n;e+=1)5===t[e].ty&&(r=(i=t[e]).t.d,i.t.d={k:[{s:r,t:0}]})}return function(i){if(n(t,i.v)&&(e(i.layers),i.assets)){var r,s=i.assets.length;for(r=0;r<s;r+=1)i.assets[r].layers&&e(i.assets[r].layers)}}}(),o=(s=[4,7,99],function(t){if(t.chars&&!n(s,t.v)){var e,r=t.chars.length;for(e=0;e<r;e+=1){var a=t.chars[e];a.data&&a.data.shapes&&(i(a.data.shapes),a.data.ip=0,a.data.op=99999,a.data.st=0,a.data.sr=1,a.data.ks={p:{k:[0,0],a:0},s:{k:[100,100],a:0},a:{k:[0,0],a:0},r:{k:0,a:0},o:{k:100,a:0}},t.chars[e].t||(a.data.shapes.push({ty:"no"}),a.data.shapes[0].it.push({p:{k:[0,0],a:0},s:{k:[100,100],a:0},a:{k:[0,0],a:0},r:{k:0,a:0},o:{k:100,a:0},sk:{k:0,a:0},sa:{k:0,a:0},ty:"tr"})))}}}),h=function(){var t=[5,7,15];function e(t){var e,i,r=t.length;for(e=0;e<r;e+=1)5===t[e].ty&&(i=void 0,"number"==typeof(i=t[e].t.p).a&&(i.a={a:0,k:i.a}),"number"==typeof i.p&&(i.p={a:0,k:i.p}),"number"==typeof i.r&&(i.r={a:0,k:i.r}))}return function(i){if(n(t,i.v)&&(e(i.layers),i.assets)){var r,s=i.assets.length;for(r=0;r<s;r+=1)i.assets[r].layers&&e(i.assets[r].layers)}}}(),l=function(){var t=[4,1,9];function e(t){var i,r,n,s=t.length;for(i=0;i<s;i+=1)if("gr"===t[i].ty)e(t[i].it);else if("fl"===t[i].ty||"st"===t[i].ty)if(t[i].c.k&&t[i].c.k[0].i)for(n=t[i].c.k.length,r=0;r<n;r+=1)t[i].c.k[r].s&&(t[i].c.k[r].s[0]/=255,t[i].c.k[r].s[1]/=255,t[i].c.k[r].s[2]/=255,t[i].c.k[r].s[3]/=255),t[i].c.k[r].e&&(t[i].c.k[r].e[0]/=255,t[i].c.k[r].e[1]/=255,t[i].c.k[r].e[2]/=255,t[i].c.k[r].e[3]/=255);else t[i].c.k[0]/=255,t[i].c.k[1]/=255,t[i].c.k[2]/=255,t[i].c.k[3]/=255}function i(t){var i,r=t.length;for(i=0;i<r;i+=1)4===t[i].ty&&e(t[i].shapes)}return function(e){if(n(t,e.v)&&(i(e.layers),e.assets)){var r,s=e.assets.length;for(r=0;r<s;r+=1)e.assets[r].layers&&i(e.assets[r].layers)}}}(),p=function(){var t=[4,4,18];function e(t){var i,r,n;for(i=t.length-1;i>=0;i-=1)if("sh"===t[i].ty)if(t[i].ks.k.i)t[i].ks.k.c=t[i].closed;else for(n=t[i].ks.k.length,r=0;r<n;r+=1)t[i].ks.k[r].s&&(t[i].ks.k[r].s[0].c=t[i].closed),t[i].ks.k[r].e&&(t[i].ks.k[r].e[0].c=t[i].closed);else"gr"===t[i].ty&&e(t[i].it)}function i(t){var i,r,n,s,a,o,h=t.length;for(r=0;r<h;r+=1){if((i=t[r]).hasMask){var l=i.masksProperties;for(s=l.length,n=0;n<s;n+=1)if(l[n].pt.k.i)l[n].pt.k.c=l[n].cl;else for(o=l[n].pt.k.length,a=0;a<o;a+=1)l[n].pt.k[a].s&&(l[n].pt.k[a].s[0].c=l[n].cl),l[n].pt.k[a].e&&(l[n].pt.k[a].e[0].c=l[n].cl)}4===i.ty&&e(i.shapes)}}return function(e){if(n(t,e.v)&&(i(e.layers),e.assets)){var r,s=e.assets.length;for(r=0;r<s;r+=1)e.assets[r].layers&&i(e.assets[r].layers)}}}();function c(t){0===t.t.a.length&&t.t.p}var d={completeData:function(i){i.__complete||(l(i),a(i),o(i),h(i),p(i),t(i.layers,i.assets),function(i,r){if(i){var n=0,s=i.length;for(n=0;n<s;n+=1)1===i[n].t&&(i[n].data.layers=e(i[n].data.refId,r),t(i[n].data.layers,r))}}(i.chars,i.assets),i.__complete=!0)}};return d.checkColors=l,d.checkChars=o,d.checkPathProperties=h,d.checkShapes=p,d.completeLayers=t,d}()),s.assetLoader||(s.assetLoader=function(){function t(t){var e=t.getResponseHeader("content-type");return e&&"json"===t.responseType&&-1!==e.indexOf("json")||t.response&&"object"===_typeof$5(t.response)?t.response:t.response&&"string"==typeof t.response?JSON.parse(t.response):t.responseText?JSON.parse(t.responseText):null}return{load:function(e,i,r,n){var s,a=new XMLHttpRequest;try{a.responseType="json"}catch(t){}a.onreadystatechange=function(){if(4===a.readyState)if(200===a.status)s=t(a),r(s);else try{s=t(a),r(s)}catch(t){n&&n(t)}};try{a.open(["G","E","T"].join(""),e,!0)}catch(t){a.open(["G","E","T"].join(""),i+"/"+e,!0)}a.send()}}}()),"loadAnimation"===t.data.type)s.assetLoader.load(t.data.path,t.data.fullPath,(function(e){s.dataManager.completeData(e),s.postMessage({id:t.data.id,payload:e,status:"success"})}),(function(){s.postMessage({id:t.data.id,status:"error"})}));else if("complete"===t.data.type){var e=t.data.animation;s.dataManager.completeData(e),s.postMessage({id:t.data.id,payload:e,status:"success"})}else"loadData"===t.data.type&&s.assetLoader.load(t.data.path,t.data.fullPath,(function(e){s.postMessage({id:t.data.id,payload:e,status:"success"})}),(function(){s.postMessage({id:t.data.id,status:"error"})}))})),e.onmessage=function(t){var e=t.data,i=e.id,n=r[i];r[i]=null,"success"===e.status?n.onComplete(e.payload):n.onError&&n.onError()})}function o(t,e){var n="processId_"+(i+=1);return r[n]={onComplete:t,onError:e},n}return{loadAnimation:function(t,i,r){a();var n=o(i,r);e.postMessage({type:"loadAnimation",path:t,fullPath:window.location.origin+window.location.pathname,id:n})},loadData:function(t,i,r){a();var n=o(i,r);e.postMessage({type:"loadData",path:t,fullPath:window.location.origin+window.location.pathname,id:n})},completeAnimation:function(t,i,r){a();var n=o(i,r);e.postMessage({type:"complete",animation:t,id:n})}}}(),ImagePreloader=function(){var t=function(){var t=createTag("canvas");t.width=1,t.height=1;var e=t.getContext("2d");return e.fillStyle="rgba(0,0,0,0)",e.fillRect(0,0,1,1),t}();function e(){this.loadedAssets+=1,this.loadedAssets===this.totalImages&&this.loadedFootagesCount===this.totalFootages&&this.imagesLoadedCb&&this.imagesLoadedCb(null)}function i(){this.loadedFootagesCount+=1,this.loadedAssets===this.totalImages&&this.loadedFootagesCount===this.totalFootages&&this.imagesLoadedCb&&this.imagesLoadedCb(null)}function r(t,e,i){var r="";if(t.e)r=t.p;else if(e){var n=t.p;-1!==n.indexOf("images/")&&(n=n.split("/")[1]),r=e+n}else r=i,r+=t.u?t.u:"",r+=t.p;return r}function n(t){var e=0,i=setInterval(function(){(t.getBBox().width||e>500)&&(this._imageLoaded(),clearInterval(i)),e+=1}.bind(this),50)}function s(t){var e={assetData:t},i=r(t,this.assetsPath,this.path);return dataManager.loadData(i,function(t){e.img=t,this._footageLoaded()}.bind(this),function(){e.img={},this._footageLoaded()}.bind(this)),e}function a(){this._imageLoaded=e.bind(this),this._footageLoaded=i.bind(this),this.testImageLoaded=n.bind(this),this.createFootageData=s.bind(this),this.assetsPath="",this.path="",this.totalImages=0,this.totalFootages=0,this.loadedAssets=0,this.loadedFootagesCount=0,this.imagesLoadedCb=null,this.images=[]}return a.prototype={loadAssets:function(t,e){var i;this.imagesLoadedCb=e;var r=t.length;for(i=0;i<r;i+=1)t[i].layers||(t[i].t&&"seq"!==t[i].t?3===t[i].t&&(this.totalFootages+=1,this.images.push(this.createFootageData(t[i]))):(this.totalImages+=1,this.images.push(this._createImageData(t[i]))))},setAssetsPath:function(t){this.assetsPath=t||""},setPath:function(t){this.path=t||""},loadedImages:function(){return this.totalImages===this.loadedAssets},loadedFootages:function(){return this.totalFootages===this.loadedFootagesCount},destroy:function(){this.imagesLoadedCb=null,this.images.length=0},getAsset:function(t){for(var e=0,i=this.images.length;e<i;){if(this.images[e].assetData===t)return this.images[e].img;e+=1}return null},createImgData:function(e){var i=r(e,this.assetsPath,this.path),n=createTag("img");n.crossOrigin="anonymous",n.addEventListener("load",this._imageLoaded,!1),n.addEventListener("error",function(){s.img=t,this._imageLoaded()}.bind(this),!1),n.src=i;var s={img:n,assetData:e};return s},createImageData:function(e){var i=r(e,this.assetsPath,this.path),n=createNS("image");isSafari?this.testImageLoaded(n):n.addEventListener("load",this._imageLoaded,!1),n.addEventListener("error",function(){s.img=t,this._imageLoaded()}.bind(this),!1),n.setAttributeNS("http://www.w3.org/1999/xlink","href",i),this._elementHelper.append?this._elementHelper.append(n):this._elementHelper.appendChild(n);var s={img:n,assetData:e};return s},imageLoaded:e,footageLoaded:i,setCacheType:function(t,e){"svg"===t?(this._elementHelper=e,this._createImageData=this.createImageData.bind(this)):this._createImageData=this.createImgData.bind(this)}},a}();function BaseEvent(){}BaseEvent.prototype={triggerEvent:function(t,e){if(this._cbs[t])for(var i=this._cbs[t],r=0;r<i.length;r+=1)i[r](e)},addEventListener:function(t,e){return this._cbs[t]||(this._cbs[t]=[]),this._cbs[t].push(e),function(){this.removeEventListener(t,e)}.bind(this)},removeEventListener:function(t,e){if(e){if(this._cbs[t]){for(var i=0,r=this._cbs[t].length;i<r;)this._cbs[t][i]===e&&(this._cbs[t].splice(i,1),i-=1,r-=1),i+=1;this._cbs[t].length||(this._cbs[t]=null)}}else this._cbs[t]=null}};var markerParser=function(){function t(t){for(var e,i=t.split("\r\n"),r={},n=0,s=0;s<i.length;s+=1)2===(e=i[s].split(":")).length&&(r[e[0]]=e[1].trim(),n+=1);if(0===n)throw new Error;return r}return function(e){for(var i=[],r=0;r<e.length;r+=1){var n=e[r],s={time:n.tm,duration:n.dr};try{s.payload=JSON.parse(e[r].cm)}catch(i){try{s.payload=t(e[r].cm)}catch(t){s.payload={name:e[r].cm}}}i.push(s)}return i}}(),ProjectInterface=function(){function t(t){this.compositions.push(t)}return function(){function e(t){for(var e=0,i=this.compositions.length;e<i;){if(this.compositions[e].data&&this.compositions[e].data.nm===t)return this.compositions[e].prepareFrame&&this.compositions[e].data.xt&&this.compositions[e].prepareFrame(this.currentFrame),this.compositions[e].compInterface;e+=1}return null}return e.compositions=[],e.currentFrame=0,e.registerComposition=t,e}}(),renderers={},registerRenderer=function(t,e){renderers[t]=e};function getRenderer(t){return renderers[t]}function _typeof$4(t){return _typeof$4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_typeof$4(t)}var AnimationItem=function(){this._cbs=[],this.name="",this.path="",this.isLoaded=!1,this.currentFrame=0,this.currentRawFrame=0,this.firstFrame=0,this.totalFrames=0,this.frameRate=0,this.frameMult=0,this.playSpeed=1,this.playDirection=1,this.playCount=0,this.animationData={},this.assets=[],this.isPaused=!0,this.autoplay=!1,this.loop=!0,this.renderer=null,this.animationID=createElementID(),this.assetsPath="",this.timeCompleted=0,this.segmentPos=0,this.isSubframeEnabled=getSubframeEnabled(),this.segments=[],this._idle=!0,this._completedLoop=!1,this.projectInterface=ProjectInterface(),this.imagePreloader=new ImagePreloader,this.audioController=audioControllerFactory(),this.markers=[],this.configAnimation=this.configAnimation.bind(this),this.onSetupError=this.onSetupError.bind(this),this.onSegmentComplete=this.onSegmentComplete.bind(this),this.drawnFrameEvent=new BMEnterFrameEvent("drawnFrame",0,0,0)};extendPrototype([BaseEvent],AnimationItem),AnimationItem.prototype.setParams=function(t){(t.wrapper||t.container)&&(this.wrapper=t.wrapper||t.container);var e="svg";t.animType?e=t.animType:t.renderer&&(e=t.renderer);var i=getRenderer(e);this.renderer=new i(this,t.rendererSettings),this.imagePreloader.setCacheType(e,this.renderer.globalData.defs),this.renderer.setProjectInterface(this.projectInterface),this.animType=e,""===t.loop||null===t.loop||void 0===t.loop||!0===t.loop?this.loop=!0:!1===t.loop?this.loop=!1:this.loop=parseInt(t.loop,10),this.autoplay=!("autoplay"in t)||t.autoplay,this.name=t.name?t.name:"",this.autoloadSegments=!Object.prototype.hasOwnProperty.call(t,"autoloadSegments")||t.autoloadSegments,this.assetsPath=t.assetsPath,this.initialSegment=t.initialSegment,t.audioFactory&&this.audioController.setAudioFactory(t.audioFactory),t.animationData?this.setupAnimation(t.animationData):t.path&&(-1!==t.path.lastIndexOf("\\")?this.path=t.path.substr(0,t.path.lastIndexOf("\\")+1):this.path=t.path.substr(0,t.path.lastIndexOf("/")+1),this.fileName=t.path.substr(t.path.lastIndexOf("/")+1),this.fileName=this.fileName.substr(0,this.fileName.lastIndexOf(".json")),dataManager.loadAnimation(t.path,this.configAnimation,this.onSetupError))},AnimationItem.prototype.onSetupError=function(){this.trigger("data_failed")},AnimationItem.prototype.setupAnimation=function(t){dataManager.completeAnimation(t,this.configAnimation)},AnimationItem.prototype.setData=function(t,e){e&&"object"!==_typeof$4(e)&&(e=JSON.parse(e));var i={wrapper:t,animationData:e},r=t.attributes;i.path=r.getNamedItem("data-animation-path")?r.getNamedItem("data-animation-path").value:r.getNamedItem("data-bm-path")?r.getNamedItem("data-bm-path").value:r.getNamedItem("bm-path")?r.getNamedItem("bm-path").value:"",i.animType=r.getNamedItem("data-anim-type")?r.getNamedItem("data-anim-type").value:r.getNamedItem("data-bm-type")?r.getNamedItem("data-bm-type").value:r.getNamedItem("bm-type")?r.getNamedItem("bm-type").value:r.getNamedItem("data-bm-renderer")?r.getNamedItem("data-bm-renderer").value:r.getNamedItem("bm-renderer")?r.getNamedItem("bm-renderer").value:"canvas";var n=r.getNamedItem("data-anim-loop")?r.getNamedItem("data-anim-loop").value:r.getNamedItem("data-bm-loop")?r.getNamedItem("data-bm-loop").value:r.getNamedItem("bm-loop")?r.getNamedItem("bm-loop").value:"";"false"===n?i.loop=!1:"true"===n?i.loop=!0:""!==n&&(i.loop=parseInt(n,10));var s=r.getNamedItem("data-anim-autoplay")?r.getNamedItem("data-anim-autoplay").value:r.getNamedItem("data-bm-autoplay")?r.getNamedItem("data-bm-autoplay").value:!r.getNamedItem("bm-autoplay")||r.getNamedItem("bm-autoplay").value;i.autoplay="false"!==s,i.name=r.getNamedItem("data-name")?r.getNamedItem("data-name").value:r.getNamedItem("data-bm-name")?r.getNamedItem("data-bm-name").value:r.getNamedItem("bm-name")?r.getNamedItem("bm-name").value:"","false"===(r.getNamedItem("data-anim-prerender")?r.getNamedItem("data-anim-prerender").value:r.getNamedItem("data-bm-prerender")?r.getNamedItem("data-bm-prerender").value:r.getNamedItem("bm-prerender")?r.getNamedItem("bm-prerender").value:"")&&(i.prerender=!1),this.setParams(i)},AnimationItem.prototype.includeLayers=function(t){t.op>this.animationData.op&&(this.animationData.op=t.op,this.totalFrames=Math.floor(t.op-this.animationData.ip));var e,i,r=this.animationData.layers,n=r.length,s=t.layers,a=s.length;for(i=0;i<a;i+=1)for(e=0;e<n;){if(r[e].id===s[i].id){r[e]=s[i];break}e+=1}if((t.chars||t.fonts)&&(this.renderer.globalData.fontManager.addChars(t.chars),this.renderer.globalData.fontManager.addFonts(t.fonts,this.renderer.globalData.defs)),t.assets)for(n=t.assets.length,e=0;e<n;e+=1)this.animationData.assets.push(t.assets[e]);this.animationData.__complete=!1,dataManager.completeAnimation(this.animationData,this.onSegmentComplete)},AnimationItem.prototype.onSegmentComplete=function(t){this.animationData=t;var e=getExpressionsPlugin();e&&e.initExpressions(this),this.loadNextSegment()},AnimationItem.prototype.loadNextSegment=function(){var t=this.animationData.segments;if(!t||0===t.length||!this.autoloadSegments)return this.trigger("data_ready"),void(this.timeCompleted=this.totalFrames);var e=t.shift();this.timeCompleted=e.time*this.frameRate;var i=this.path+this.fileName+"_"+this.segmentPos+".json";this.segmentPos+=1,dataManager.loadData(i,this.includeLayers.bind(this),function(){this.trigger("data_failed")}.bind(this))},AnimationItem.prototype.loadSegments=function(){this.animationData.segments||(this.timeCompleted=this.totalFrames),this.loadNextSegment()},AnimationItem.prototype.imagesLoaded=function(){this.trigger("loaded_images"),this.checkLoaded()},AnimationItem.prototype.preloadImages=function(){this.imagePreloader.setAssetsPath(this.assetsPath),this.imagePreloader.setPath(this.path),this.imagePreloader.loadAssets(this.animationData.assets,this.imagesLoaded.bind(this))},AnimationItem.prototype.configAnimation=function(t){if(this.renderer)try{this.animationData=t,this.initialSegment?(this.totalFrames=Math.floor(this.initialSegment[1]-this.initialSegment[0]),this.firstFrame=Math.round(this.initialSegment[0])):(this.totalFrames=Math.floor(this.animationData.op-this.animationData.ip),this.firstFrame=Math.round(this.animationData.ip)),this.renderer.configAnimation(t),t.assets||(t.assets=[]),this.assets=this.animationData.assets,this.frameRate=this.animationData.fr,this.frameMult=this.animationData.fr/1e3,this.renderer.searchExtraCompositions(t.assets),this.markers=markerParser(t.markers||[]),this.trigger("config_ready"),this.preloadImages(),this.loadSegments(),this.updaFrameModifier(),this.waitForFontsLoaded(),this.isPaused&&this.audioController.pause()}catch(t){this.triggerConfigError(t)}},AnimationItem.prototype.waitForFontsLoaded=function(){this.renderer&&(this.renderer.globalData.fontManager.isLoaded?this.checkLoaded():setTimeout(this.waitForFontsLoaded.bind(this),20))},AnimationItem.prototype.checkLoaded=function(){if(!this.isLoaded&&this.renderer.globalData.fontManager.isLoaded&&(this.imagePreloader.loadedImages()||"canvas"!==this.renderer.rendererType)&&this.imagePreloader.loadedFootages()){this.isLoaded=!0;var t=getExpressionsPlugin();t&&t.initExpressions(this),this.renderer.initItems(),setTimeout(function(){this.trigger("DOMLoaded")}.bind(this),0),this.gotoFrame(),this.autoplay&&this.play()}},AnimationItem.prototype.resize=function(t,e){var i="number"==typeof t?t:void 0,r="number"==typeof e?e:void 0;this.renderer.updateContainerSize(i,r)},AnimationItem.prototype.setSubframe=function(t){this.isSubframeEnabled=!!t},AnimationItem.prototype.gotoFrame=function(){this.currentFrame=this.isSubframeEnabled?this.currentRawFrame:~~this.currentRawFrame,this.timeCompleted!==this.totalFrames&&this.currentFrame>this.timeCompleted&&(this.currentFrame=this.timeCompleted),this.trigger("enterFrame"),this.renderFrame(),this.trigger("drawnFrame")},AnimationItem.prototype.renderFrame=function(){if(!1!==this.isLoaded&&this.renderer)try{this.renderer.renderFrame(this.currentFrame+this.firstFrame)}catch(t){this.triggerRenderFrameError(t)}},AnimationItem.prototype.play=function(t){t&&this.name!==t||!0===this.isPaused&&(this.isPaused=!1,this.trigger("_pause"),this.audioController.resume(),this._idle&&(this._idle=!1,this.trigger("_active")))},AnimationItem.prototype.pause=function(t){t&&this.name!==t||!1===this.isPaused&&(this.isPaused=!0,this.trigger("_play"),this._idle=!0,this.trigger("_idle"),this.audioController.pause())},AnimationItem.prototype.togglePause=function(t){t&&this.name!==t||(!0===this.isPaused?this.play():this.pause())},AnimationItem.prototype.stop=function(t){t&&this.name!==t||(this.pause(),this.playCount=0,this._completedLoop=!1,this.setCurrentRawFrameValue(0))},AnimationItem.prototype.getMarkerData=function(t){for(var e,i=0;i<this.markers.length;i+=1)if((e=this.markers[i]).payload&&e.payload.name===t)return e;return null},AnimationItem.prototype.goToAndStop=function(t,e,i){if(!i||this.name===i){var r=Number(t);if(isNaN(r)){var n=this.getMarkerData(t);n&&this.goToAndStop(n.time,!0)}else e?this.setCurrentRawFrameValue(t):this.setCurrentRawFrameValue(t*this.frameModifier);this.pause()}},AnimationItem.prototype.goToAndPlay=function(t,e,i){if(!i||this.name===i){var r=Number(t);if(isNaN(r)){var n=this.getMarkerData(t);n&&(n.duration?this.playSegments([n.time,n.time+n.duration],!0):this.goToAndStop(n.time,!0))}else this.goToAndStop(r,e,i);this.play()}},AnimationItem.prototype.advanceTime=function(t){if(!0!==this.isPaused&&!1!==this.isLoaded){var e=this.currentRawFrame+t*this.frameModifier,i=!1;e>=this.totalFrames-1&&this.frameModifier>0?this.loop&&this.playCount!==this.loop?e>=this.totalFrames?(this.playCount+=1,this.checkSegments(e%this.totalFrames)||(this.setCurrentRawFrameValue(e%this.totalFrames),this._completedLoop=!0,this.trigger("loopComplete"))):this.setCurrentRawFrameValue(e):this.checkSegments(e>this.totalFrames?e%this.totalFrames:0)||(i=!0,e=this.totalFrames-1):e<0?this.checkSegments(e%this.totalFrames)||(!this.loop||this.playCount--<=0&&!0!==this.loop?(i=!0,e=0):(this.setCurrentRawFrameValue(this.totalFrames+e%this.totalFrames),this._completedLoop?this.trigger("loopComplete"):this._completedLoop=!0)):this.setCurrentRawFrameValue(e),i&&(this.setCurrentRawFrameValue(e),this.pause(),this.trigger("complete"))}},AnimationItem.prototype.adjustSegment=function(t,e){this.playCount=0,t[1]<t[0]?(this.frameModifier>0&&(this.playSpeed<0?this.setSpeed(-this.playSpeed):this.setDirection(-1)),this.totalFrames=t[0]-t[1],this.timeCompleted=this.totalFrames,this.firstFrame=t[1],this.setCurrentRawFrameValue(this.totalFrames-.001-e)):t[1]>t[0]&&(this.frameModifier<0&&(this.playSpeed<0?this.setSpeed(-this.playSpeed):this.setDirection(1)),this.totalFrames=t[1]-t[0],this.timeCompleted=this.totalFrames,this.firstFrame=t[0],this.setCurrentRawFrameValue(.001+e)),this.trigger("segmentStart")},AnimationItem.prototype.setSegment=function(t,e){var i=-1;this.isPaused&&(this.currentRawFrame+this.firstFrame<t?i=t:this.currentRawFrame+this.firstFrame>e&&(i=e-t)),this.firstFrame=t,this.totalFrames=e-t,this.timeCompleted=this.totalFrames,-1!==i&&this.goToAndStop(i,!0)},AnimationItem.prototype.playSegments=function(t,e){if(e&&(this.segments.length=0),"object"===_typeof$4(t[0])){var i,r=t.length;for(i=0;i<r;i+=1)this.segments.push(t[i])}else this.segments.push(t);this.segments.length&&e&&this.adjustSegment(this.segments.shift(),0),this.isPaused&&this.play()},AnimationItem.prototype.resetSegments=function(t){this.segments.length=0,this.segments.push([this.animationData.ip,this.animationData.op]),t&&this.checkSegments(0)},AnimationItem.prototype.checkSegments=function(t){return!!this.segments.length&&(this.adjustSegment(this.segments.shift(),t),!0)},AnimationItem.prototype.destroy=function(t){t&&this.name!==t||!this.renderer||(this.renderer.destroy(),this.imagePreloader.destroy(),this.trigger("destroy"),this._cbs=null,this.onEnterFrame=null,this.onLoopComplete=null,this.onComplete=null,this.onSegmentStart=null,this.onDestroy=null,this.renderer=null,this.renderer=null,this.imagePreloader=null,this.projectInterface=null)},AnimationItem.prototype.setCurrentRawFrameValue=function(t){this.currentRawFrame=t,this.gotoFrame()},AnimationItem.prototype.setSpeed=function(t){this.playSpeed=t,this.updaFrameModifier()},AnimationItem.prototype.setDirection=function(t){this.playDirection=t<0?-1:1,this.updaFrameModifier()},AnimationItem.prototype.setVolume=function(t,e){e&&this.name!==e||this.audioController.setVolume(t)},AnimationItem.prototype.getVolume=function(){return this.audioController.getVolume()},AnimationItem.prototype.mute=function(t){t&&this.name!==t||this.audioController.mute()},AnimationItem.prototype.unmute=function(t){t&&this.name!==t||this.audioController.unmute()},AnimationItem.prototype.updaFrameModifier=function(){this.frameModifier=this.frameMult*this.playSpeed*this.playDirection,this.audioController.setRate(this.playSpeed*this.playDirection)},AnimationItem.prototype.getPath=function(){return this.path},AnimationItem.prototype.getAssetsPath=function(t){var e="";if(t.e)e=t.p;else if(this.assetsPath){var i=t.p;-1!==i.indexOf("images/")&&(i=i.split("/")[1]),e=this.assetsPath+i}else e=this.path,e+=t.u?t.u:"",e+=t.p;return e},AnimationItem.prototype.getAssetData=function(t){for(var e=0,i=this.assets.length;e<i;){if(t===this.assets[e].id)return this.assets[e];e+=1}return null},AnimationItem.prototype.hide=function(){this.renderer.hide()},AnimationItem.prototype.show=function(){this.renderer.show()},AnimationItem.prototype.getDuration=function(t){return t?this.totalFrames:this.totalFrames/this.frameRate},AnimationItem.prototype.updateDocumentData=function(t,e,i){try{this.renderer.getElementByPath(t).updateDocumentData(e,i)}catch(t){}},AnimationItem.prototype.trigger=function(t){if(this._cbs&&this._cbs[t])switch(t){case"enterFrame":this.triggerEvent(t,new BMEnterFrameEvent(t,this.currentFrame,this.totalFrames,this.frameModifier));break;case"drawnFrame":this.drawnFrameEvent.currentTime=this.currentFrame,this.drawnFrameEvent.totalTime=this.totalFrames,this.drawnFrameEvent.direction=this.frameModifier,this.triggerEvent(t,this.drawnFrameEvent);break;case"loopComplete":this.triggerEvent(t,new BMCompleteLoopEvent(t,this.loop,this.playCount,this.frameMult));break;case"complete":this.triggerEvent(t,new BMCompleteEvent(t,this.frameMult));break;case"segmentStart":this.triggerEvent(t,new BMSegmentStartEvent(t,this.firstFrame,this.totalFrames));break;case"destroy":this.triggerEvent(t,new BMDestroyEvent(t,this));break;default:this.triggerEvent(t)}"enterFrame"===t&&this.onEnterFrame&&this.onEnterFrame.call(this,new BMEnterFrameEvent(t,this.currentFrame,this.totalFrames,this.frameMult)),"loopComplete"===t&&this.onLoopComplete&&this.onLoopComplete.call(this,new BMCompleteLoopEvent(t,this.loop,this.playCount,this.frameMult)),"complete"===t&&this.onComplete&&this.onComplete.call(this,new BMCompleteEvent(t,this.frameMult)),"segmentStart"===t&&this.onSegmentStart&&this.onSegmentStart.call(this,new BMSegmentStartEvent(t,this.firstFrame,this.totalFrames)),"destroy"===t&&this.onDestroy&&this.onDestroy.call(this,new BMDestroyEvent(t,this))},AnimationItem.prototype.triggerRenderFrameError=function(t){var e=new BMRenderFrameErrorEvent(t,this.currentFrame);this.triggerEvent("error",e),this.onError&&this.onError.call(this,e)},AnimationItem.prototype.triggerConfigError=function(t){var e=new BMConfigErrorEvent(t,this.currentFrame);this.triggerEvent("error",e),this.onError&&this.onError.call(this,e)};var animationManager=function(){var t={},e=[],i=0,r=0,n=0,s=!0,a=!1;function o(t){for(var i=0,n=t.target;i<r;)e[i].animation===n&&(e.splice(i,1),i-=1,r-=1,n.isPaused||p()),i+=1}function h(t,i){if(!t)return null;for(var n=0;n<r;){if(e[n].elem===t&&null!==e[n].elem)return e[n].animation;n+=1}var s=new AnimationItem;return c(s,t),s.setData(t,i),s}function l(){n+=1,u()}function p(){n-=1}function c(t,i){t.addEventListener("destroy",o),t.addEventListener("_active",l),t.addEventListener("_idle",p),e.push({elem:i,animation:t}),r+=1}function d(t){var o,h=t-i;for(o=0;o<r;o+=1)e[o].animation.advanceTime(h);i=t,n&&!a?window.requestAnimationFrame(d):s=!0}function f(t){i=t,window.requestAnimationFrame(d)}function u(){!a&&n&&s&&(window.requestAnimationFrame(f),s=!1)}return t.registerAnimation=h,t.loadAnimation=function(t){var e=new AnimationItem;return c(e,null),e.setParams(t),e},t.setSpeed=function(t,i){var n;for(n=0;n<r;n+=1)e[n].animation.setSpeed(t,i)},t.setDirection=function(t,i){var n;for(n=0;n<r;n+=1)e[n].animation.setDirection(t,i)},t.play=function(t){var i;for(i=0;i<r;i+=1)e[i].animation.play(t)},t.pause=function(t){var i;for(i=0;i<r;i+=1)e[i].animation.pause(t)},t.stop=function(t){var i;for(i=0;i<r;i+=1)e[i].animation.stop(t)},t.togglePause=function(t){var i;for(i=0;i<r;i+=1)e[i].animation.togglePause(t)},t.searchAnimations=function(t,e,i){var r,n=[].concat([].slice.call(document.getElementsByClassName("lottie")),[].slice.call(document.getElementsByClassName("bodymovin"))),s=n.length;for(r=0;r<s;r+=1)i&&n[r].setAttribute("data-bm-type",i),h(n[r],t);if(e&&0===s){i||(i="svg");var a=document.getElementsByTagName("body")[0];a.innerText="";var o=createTag("div");o.style.width="100%",o.style.height="100%",o.setAttribute("data-bm-type",i),a.appendChild(o),h(o,t)}},t.resize=function(){var t;for(t=0;t<r;t+=1)e[t].animation.resize()},t.goToAndStop=function(t,i,n){var s;for(s=0;s<r;s+=1)e[s].animation.goToAndStop(t,i,n)},t.destroy=function(t){var i;for(i=r-1;i>=0;i-=1)e[i].animation.destroy(t)},t.freeze=function(){a=!0},t.unfreeze=function(){a=!1,u()},t.setVolume=function(t,i){var n;for(n=0;n<r;n+=1)e[n].animation.setVolume(t,i)},t.mute=function(t){var i;for(i=0;i<r;i+=1)e[i].animation.mute(t)},t.unmute=function(t){var i;for(i=0;i<r;i+=1)e[i].animation.unmute(t)},t.getRegisteredAnimations=function(){var t,i=e.length,r=[];for(t=0;t<i;t+=1)r.push(e[t].animation);return r},t}(),BezierFactory=function(){var t={getBezierEasing:function(t,i,r,n,s){var a=s||("bez_"+t+"_"+i+"_"+r+"_"+n).replace(/\./g,"p");if(e[a])return e[a];var o=new l([t,i,r,n]);return e[a]=o,o}},e={},i=.1,r="function"==typeof Float32Array;function n(t,e){return 1-3*e+3*t}function s(t,e){return 3*e-6*t}function a(t){return 3*t}function o(t,e,i){return((n(e,i)*t+s(e,i))*t+a(e))*t}function h(t,e,i){return 3*n(e,i)*t*t+2*s(e,i)*t+a(e)}function l(t){this._p=t,this._mSampleValues=r?new Float32Array(11):new Array(11),this._precomputed=!1,this.get=this.get.bind(this)}return l.prototype={get:function(t){var e=this._p[0],i=this._p[1],r=this._p[2],n=this._p[3];return this._precomputed||this._precompute(),e===i&&r===n?t:0===t?0:1===t?1:o(this._getTForX(t),i,n)},_precompute:function(){var t=this._p[0],e=this._p[1],i=this._p[2],r=this._p[3];this._precomputed=!0,t===e&&i===r||this._calcSampleValues()},_calcSampleValues:function(){for(var t=this._p[0],e=this._p[2],r=0;r<11;++r)this._mSampleValues[r]=o(r*i,t,e)},_getTForX:function(t){for(var e=this._p[0],r=this._p[2],n=this._mSampleValues,s=0,a=1;10!==a&&n[a]<=t;++a)s+=i;var l=s+(t-n[--a])/(n[a+1]-n[a])*i,p=h(l,e,r);return p>=.001?function(t,e,i,r){for(var n=0;n<4;++n){var s=h(e,i,r);if(0===s)return e;e-=(o(e,i,r)-t)/s}return e}(t,l,e,r):0===p?l:function(t,e,i,r,n){var s,a,h=0;do{(s=o(a=e+(i-e)/2,r,n)-t)>0?i=a:e=a}while(Math.abs(s)>1e-7&&++h<10);return a}(t,s,s+i,e,r)}},t}(),pooling={double:function(t){return t.concat(createSizedArray(t.length))}},poolFactory=function(t,e,i){var r=0,n=t,s=createSizedArray(n);return{newElement:function(){return r?s[r-=1]:e()},release:function(t){r===n&&(s=pooling.double(s),n*=2),i&&i(t),s[r]=t,r+=1}}},bezierLengthPool=poolFactory(8,(function(){return{addedLength:0,percents:createTypedArray("float32",getDefaultCurveSegments()),lengths:createTypedArray("float32",getDefaultCurveSegments())}})),segmentsLengthPool=poolFactory(8,(function(){return{lengths:[],totalLength:0}}),(function(t){var e,i=t.lengths.length;for(e=0;e<i;e+=1)bezierLengthPool.release(t.lengths[e]);t.lengths.length=0}));function bezFunction(){var t=Math;function e(t,e,i,r,n,s){var a=t*r+e*n+i*s-n*r-s*t-i*e;return a>-.001&&a<.001}var i=function(t,e,i,r){var n,s,a,o,h,l,p=getDefaultCurveSegments(),c=0,d=[],f=[],u=bezierLengthPool.newElement();for(a=i.length,n=0;n<p;n+=1){for(h=n/(p-1),l=0,s=0;s<a;s+=1)o=bmPow(1-h,3)*t[s]+3*bmPow(1-h,2)*h*i[s]+3*(1-h)*bmPow(h,2)*r[s]+bmPow(h,3)*e[s],d[s]=o,null!==f[s]&&(l+=bmPow(d[s]-f[s],2)),f[s]=d[s];l&&(c+=l=bmSqrt(l)),u.percents[n]=h,u.lengths[n]=c}return u.addedLength=c,u};function r(t){this.segmentLength=0,this.points=new Array(t)}function n(t,e){this.partialLength=t,this.point=e}var s,a=(s={},function(t,i,a,o){var h=(t[0]+"_"+t[1]+"_"+i[0]+"_"+i[1]+"_"+a[0]+"_"+a[1]+"_"+o[0]+"_"+o[1]).replace(/\./g,"p");if(!s[h]){var l,p,c,d,f,u,m,y=getDefaultCurveSegments(),g=0,v=null;2===t.length&&(t[0]!==i[0]||t[1]!==i[1])&&e(t[0],t[1],i[0],i[1],t[0]+a[0],t[1]+a[1])&&e(t[0],t[1],i[0],i[1],i[0]+o[0],i[1]+o[1])&&(y=2);var b=new r(y);for(c=a.length,l=0;l<y;l+=1){for(m=createSizedArray(c),f=l/(y-1),u=0,p=0;p<c;p+=1)d=bmPow(1-f,3)*t[p]+3*bmPow(1-f,2)*f*(t[p]+a[p])+3*(1-f)*bmPow(f,2)*(i[p]+o[p])+bmPow(f,3)*i[p],m[p]=d,null!==v&&(u+=bmPow(m[p]-v[p],2));g+=u=bmSqrt(u),b.points[l]=new n(u,m),v=m}b.segmentLength=g,s[h]=b}return s[h]});function o(t,e){var i=e.percents,r=e.lengths,n=i.length,s=bmFloor((n-1)*t),a=t*e.addedLength,o=0;if(s===n-1||0===s||a===r[s])return i[s];for(var h=r[s]>a?-1:1,l=!0;l;)if(r[s]<=a&&r[s+1]>a?(o=(a-r[s])/(r[s+1]-r[s]),l=!1):s+=h,s<0||s>=n-1){if(s===n-1)return i[s];l=!1}return i[s]+(i[s+1]-i[s])*o}var h=createTypedArray("float32",8);return{getSegmentsLength:function(t){var e,r=segmentsLengthPool.newElement(),n=t.c,s=t.v,a=t.o,o=t.i,h=t._length,l=r.lengths,p=0;for(e=0;e<h-1;e+=1)l[e]=i(s[e],s[e+1],a[e],o[e+1]),p+=l[e].addedLength;return n&&h&&(l[e]=i(s[e],s[0],a[e],o[0]),p+=l[e].addedLength),r.totalLength=p,r},getNewSegment:function(e,i,r,n,s,a,l){s<0?s=0:s>1&&(s=1);var p,c=o(s,l),d=o(a=a>1?1:a,l),f=e.length,u=1-c,m=1-d,y=u*u*u,g=c*u*u*3,v=c*c*u*3,b=c*c*c,_=u*u*m,E=c*u*m+u*c*m+u*u*d,S=c*c*m+u*c*d+c*u*d,P=c*c*d,x=u*m*m,w=c*m*m+u*d*m+u*m*d,C=c*d*m+u*d*d+c*m*d,A=c*d*d,k=m*m*m,D=d*m*m+m*d*m+m*m*d,T=d*d*m+m*d*d+d*m*d,M=d*d*d;for(p=0;p<f;p+=1)h[4*p]=t.round(1e3*(y*e[p]+g*r[p]+v*n[p]+b*i[p]))/1e3,h[4*p+1]=t.round(1e3*(_*e[p]+E*r[p]+S*n[p]+P*i[p]))/1e3,h[4*p+2]=t.round(1e3*(x*e[p]+w*r[p]+C*n[p]+A*i[p]))/1e3,h[4*p+3]=t.round(1e3*(k*e[p]+D*r[p]+T*n[p]+M*i[p]))/1e3;return h},getPointInSegment:function(e,i,r,n,s,a){var h=o(s,a),l=1-h;return[t.round(1e3*(l*l*l*e[0]+(h*l*l+l*h*l+l*l*h)*r[0]+(h*h*l+l*h*h+h*l*h)*n[0]+h*h*h*i[0]))/1e3,t.round(1e3*(l*l*l*e[1]+(h*l*l+l*h*l+l*l*h)*r[1]+(h*h*l+l*h*h+h*l*h)*n[1]+h*h*h*i[1]))/1e3]},buildBezierData:a,pointOnLine2D:e,pointOnLine3D:function(i,r,n,s,a,o,h,l,p){if(0===n&&0===o&&0===p)return e(i,r,s,a,h,l);var c,d=t.sqrt(t.pow(s-i,2)+t.pow(a-r,2)+t.pow(o-n,2)),f=t.sqrt(t.pow(h-i,2)+t.pow(l-r,2)+t.pow(p-n,2)),u=t.sqrt(t.pow(h-s,2)+t.pow(l-a,2)+t.pow(p-o,2));return(c=d>f?d>u?d-f-u:u-f-d:u>f?u-f-d:f-d-u)>-1e-4&&c<1e-4}}}var bez=bezFunction(),PropertyFactory=function(){var t=initialDefaultFrame,e=Math.abs;function i(t,e){var i,n=this.offsetTime;"multidimensional"===this.propType&&(i=createTypedArray("float32",this.pv.length));for(var s,a,o,h,l,p,c,d,f,u=e.lastIndex,m=u,y=this.keyframes.length-1,g=!0;g;){if(s=this.keyframes[m],a=this.keyframes[m+1],m===y-1&&t>=a.t-n){s.h&&(s=a),u=0;break}if(a.t-n>t){u=m;break}m<y-1?m+=1:(u=0,g=!1)}o=this.keyframesMetadata[m]||{};var v,b,_,E,S,P,x,w,C,A,k=a.t-n,D=s.t-n;if(s.to){o.bezierData||(o.bezierData=bez.buildBezierData(s.s,a.s||s.e,s.to,s.ti));var T=o.bezierData;if(t>=k||t<D){var M=t>=k?T.points.length-1:0;for(l=T.points[M].point.length,h=0;h<l;h+=1)i[h]=T.points[M].point[h]}else{o.__fnct?f=o.__fnct:(f=BezierFactory.getBezierEasing(s.o.x,s.o.y,s.i.x,s.i.y,s.n).get,o.__fnct=f),p=f((t-D)/(k-D));var I,F=T.segmentLength*p,L=e.lastFrame<t&&e._lastKeyframeIndex===m?e._lastAddedLength:0;for(d=e.lastFrame<t&&e._lastKeyframeIndex===m?e._lastPoint:0,g=!0,c=T.points.length;g;){if(L+=T.points[d].partialLength,0===F||0===p||d===T.points.length-1){for(l=T.points[d].point.length,h=0;h<l;h+=1)i[h]=T.points[d].point[h];break}if(F>=L&&F<L+T.points[d+1].partialLength){for(I=(F-L)/T.points[d+1].partialLength,l=T.points[d].point.length,h=0;h<l;h+=1)i[h]=T.points[d].point[h]+(T.points[d+1].point[h]-T.points[d].point[h])*I;break}d<c-1?d+=1:g=!1}e._lastPoint=d,e._lastAddedLength=L-T.points[d].partialLength,e._lastKeyframeIndex=m}}else{var R,B,O,$,V;if(y=s.s.length,v=a.s||s.e,this.sh&&1!==s.h)t>=k?(i[0]=v[0],i[1]=v[1],i[2]=v[2]):t<=D?(i[0]=s.s[0],i[1]=s.s[1],i[2]=s.s[2]):(b=i,_=function(t,e,i){var r,n,s,a,o,h=[],l=t[0],p=t[1],c=t[2],d=t[3],f=e[0],u=e[1],m=e[2],y=e[3];return(n=l*f+p*u+c*m+d*y)<0&&(n=-n,f=-f,u=-u,m=-m,y=-y),1-n>1e-6?(r=Math.acos(n),s=Math.sin(r),a=Math.sin((1-i)*r)/s,o=Math.sin(i*r)/s):(a=1-i,o=i),h[0]=a*l+o*f,h[1]=a*p+o*u,h[2]=a*c+o*m,h[3]=a*d+o*y,h}(r(s.s),r(v),(t-D)/(k-D)),E=_[0],S=_[1],P=_[2],x=_[3],w=Math.atan2(2*S*x-2*E*P,1-2*S*S-2*P*P),C=Math.asin(2*E*S+2*P*x),A=Math.atan2(2*E*x-2*S*P,1-2*E*E-2*P*P),b[0]=w/degToRads,b[1]=C/degToRads,b[2]=A/degToRads);else for(m=0;m<y;m+=1)1!==s.h&&(t>=k?p=1:t<D?p=0:(s.o.x.constructor===Array?(o.__fnct||(o.__fnct=[]),o.__fnct[m]?f=o.__fnct[m]:(R=void 0===s.o.x[m]?s.o.x[0]:s.o.x[m],B=void 0===s.o.y[m]?s.o.y[0]:s.o.y[m],O=void 0===s.i.x[m]?s.i.x[0]:s.i.x[m],$=void 0===s.i.y[m]?s.i.y[0]:s.i.y[m],f=BezierFactory.getBezierEasing(R,B,O,$).get,o.__fnct[m]=f)):o.__fnct?f=o.__fnct:(R=s.o.x,B=s.o.y,O=s.i.x,$=s.i.y,f=BezierFactory.getBezierEasing(R,B,O,$).get,s.keyframeMetadata=f),p=f((t-D)/(k-D)))),v=a.s||s.e,V=1===s.h?s.s[m]:s.s[m]+(v[m]-s.s[m])*p,"multidimensional"===this.propType?i[m]=V:i=V}return e.lastIndex=u,i}function r(t){var e=t[0]*degToRads,i=t[1]*degToRads,r=t[2]*degToRads,n=Math.cos(e/2),s=Math.cos(i/2),a=Math.cos(r/2),o=Math.sin(e/2),h=Math.sin(i/2),l=Math.sin(r/2);return[o*h*a+n*s*l,o*s*a+n*h*l,n*h*a-o*s*l,n*s*a-o*h*l]}function n(){var e=this.comp.renderedFrame-this.offsetTime,i=this.keyframes[0].t-this.offsetTime,r=this.keyframes[this.keyframes.length-1].t-this.offsetTime;if(!(e===this._caching.lastFrame||this._caching.lastFrame!==t&&(this._caching.lastFrame>=r&&e>=r||this._caching.lastFrame<i&&e<i))){this._caching.lastFrame>=e&&(this._caching._lastKeyframeIndex=-1,this._caching.lastIndex=0);var n=this.interpolateValue(e,this._caching);this.pv=n}return this._caching.lastFrame=e,this.pv}function s(t){var i;if("unidimensional"===this.propType)i=t*this.mult,e(this.v-i)>1e-5&&(this.v=i,this._mdf=!0);else for(var r=0,n=this.v.length;r<n;)i=t[r]*this.mult,e(this.v[r]-i)>1e-5&&(this.v[r]=i,this._mdf=!0),r+=1}function a(){if(this.elem.globalData.frameId!==this.frameId&&this.effectsSequence.length)if(this.lock)this.setVValue(this.pv);else{var t;this.lock=!0,this._mdf=this._isFirstFrame;var e=this.effectsSequence.length,i=this.kf?this.pv:this.data.k;for(t=0;t<e;t+=1)i=this.effectsSequence[t](i);this.setVValue(i),this._isFirstFrame=!1,this.lock=!1,this.frameId=this.elem.globalData.frameId}}function o(t){this.effectsSequence.push(t),this.container.addDynamicProperty(this)}function h(t,e,i,r){this.propType="unidimensional",this.mult=i||1,this.data=e,this.v=i?e.k*i:e.k,this.pv=e.k,this._mdf=!1,this.elem=t,this.container=r,this.comp=t.comp,this.k=!1,this.kf=!1,this.vel=0,this.effectsSequence=[],this._isFirstFrame=!0,this.getValue=a,this.setVValue=s,this.addEffect=o}function l(t,e,i,r){var n;this.propType="multidimensional",this.mult=i||1,this.data=e,this._mdf=!1,this.elem=t,this.container=r,this.comp=t.comp,this.k=!1,this.kf=!1,this.frameId=-1;var h=e.k.length;for(this.v=createTypedArray("float32",h),this.pv=createTypedArray("float32",h),this.vel=createTypedArray("float32",h),n=0;n<h;n+=1)this.v[n]=e.k[n]*this.mult,this.pv[n]=e.k[n];this._isFirstFrame=!0,this.effectsSequence=[],this.getValue=a,this.setVValue=s,this.addEffect=o}function p(e,r,h,l){this.propType="unidimensional",this.keyframes=r.k,this.keyframesMetadata=[],this.offsetTime=e.data.st,this.frameId=-1,this._caching={lastFrame:t,lastIndex:0,value:0,_lastKeyframeIndex:-1},this.k=!0,this.kf=!0,this.data=r,this.mult=h||1,this.elem=e,this.container=l,this.comp=e.comp,this.v=t,this.pv=t,this._isFirstFrame=!0,this.getValue=a,this.setVValue=s,this.interpolateValue=i,this.effectsSequence=[n.bind(this)],this.addEffect=o}function c(e,r,h,l){var p;this.propType="multidimensional";var c,d,f,u,m=r.k.length;for(p=0;p<m-1;p+=1)r.k[p].to&&r.k[p].s&&r.k[p+1]&&r.k[p+1].s&&(c=r.k[p].s,d=r.k[p+1].s,f=r.k[p].to,u=r.k[p].ti,(2===c.length&&(c[0]!==d[0]||c[1]!==d[1])&&bez.pointOnLine2D(c[0],c[1],d[0],d[1],c[0]+f[0],c[1]+f[1])&&bez.pointOnLine2D(c[0],c[1],d[0],d[1],d[0]+u[0],d[1]+u[1])||3===c.length&&(c[0]!==d[0]||c[1]!==d[1]||c[2]!==d[2])&&bez.pointOnLine3D(c[0],c[1],c[2],d[0],d[1],d[2],c[0]+f[0],c[1]+f[1],c[2]+f[2])&&bez.pointOnLine3D(c[0],c[1],c[2],d[0],d[1],d[2],d[0]+u[0],d[1]+u[1],d[2]+u[2]))&&(r.k[p].to=null,r.k[p].ti=null),c[0]===d[0]&&c[1]===d[1]&&0===f[0]&&0===f[1]&&0===u[0]&&0===u[1]&&(2===c.length||c[2]===d[2]&&0===f[2]&&0===u[2])&&(r.k[p].to=null,r.k[p].ti=null));this.effectsSequence=[n.bind(this)],this.data=r,this.keyframes=r.k,this.keyframesMetadata=[],this.offsetTime=e.data.st,this.k=!0,this.kf=!0,this._isFirstFrame=!0,this.mult=h||1,this.elem=e,this.container=l,this.comp=e.comp,this.getValue=a,this.setVValue=s,this.interpolateValue=i,this.frameId=-1;var y=r.k[0].s.length;for(this.v=createTypedArray("float32",y),this.pv=createTypedArray("float32",y),p=0;p<y;p+=1)this.v[p]=t,this.pv[p]=t;this._caching={lastFrame:t,lastIndex:0,value:createTypedArray("float32",y)},this.addEffect=o}var d={getProp:function(t,e,i,r,n){var s;if(e.k.length)if("number"==typeof e.k[0])s=new l(t,e,r,n);else switch(i){case 0:s=new p(t,e,r,n);break;case 1:s=new c(t,e,r,n)}else s=new h(t,e,r,n);return s.effectsSequence.length&&n.addDynamicProperty(s),s}};return d}();function DynamicPropertyContainer(){}DynamicPropertyContainer.prototype={addDynamicProperty:function(t){-1===this.dynamicProperties.indexOf(t)&&(this.dynamicProperties.push(t),this.container.addDynamicProperty(this),this._isAnimated=!0)},iterateDynamicProperties:function(){var t;this._mdf=!1;var e=this.dynamicProperties.length;for(t=0;t<e;t+=1)this.dynamicProperties[t].getValue(),this.dynamicProperties[t]._mdf&&(this._mdf=!0)},initDynamicPropertyContainer:function(t){this.container=t,this.dynamicProperties=[],this._mdf=!1,this._isAnimated=!1}};var pointPool=poolFactory(8,(function(){return createTypedArray("float32",2)}));function ShapePath(){this.c=!1,this._length=0,this._maxLength=8,this.v=createSizedArray(this._maxLength),this.o=createSizedArray(this._maxLength),this.i=createSizedArray(this._maxLength)}ShapePath.prototype.setPathData=function(t,e){this.c=t,this.setLength(e);for(var i=0;i<e;)this.v[i]=pointPool.newElement(),this.o[i]=pointPool.newElement(),this.i[i]=pointPool.newElement(),i+=1},ShapePath.prototype.setLength=function(t){for(;this._maxLength<t;)this.doubleArrayLength();this._length=t},ShapePath.prototype.doubleArrayLength=function(){this.v=this.v.concat(createSizedArray(this._maxLength)),this.i=this.i.concat(createSizedArray(this._maxLength)),this.o=this.o.concat(createSizedArray(this._maxLength)),this._maxLength*=2},ShapePath.prototype.setXYAt=function(t,e,i,r,n){var s;switch(this._length=Math.max(this._length,r+1),this._length>=this._maxLength&&this.doubleArrayLength(),i){case"v":s=this.v;break;case"i":s=this.i;break;case"o":s=this.o;break;default:s=[]}(!s[r]||s[r]&&!n)&&(s[r]=pointPool.newElement()),s[r][0]=t,s[r][1]=e},ShapePath.prototype.setTripleAt=function(t,e,i,r,n,s,a,o){this.setXYAt(t,e,"v",a,o),this.setXYAt(i,r,"o",a,o),this.setXYAt(n,s,"i",a,o)},ShapePath.prototype.reverse=function(){var t=new ShapePath;t.setPathData(this.c,this._length);var e=this.v,i=this.o,r=this.i,n=0;this.c&&(t.setTripleAt(e[0][0],e[0][1],r[0][0],r[0][1],i[0][0],i[0][1],0,!1),n=1);var s,a=this._length-1,o=this._length;for(s=n;s<o;s+=1)t.setTripleAt(e[a][0],e[a][1],r[a][0],r[a][1],i[a][0],i[a][1],s,!1),a-=1;return t},ShapePath.prototype.length=function(){return this._length};var shapePool=(factory=poolFactory(4,(function(){return new ShapePath}),(function(t){var e,i=t._length;for(e=0;e<i;e+=1)pointPool.release(t.v[e]),pointPool.release(t.i[e]),pointPool.release(t.o[e]),t.v[e]=null,t.i[e]=null,t.o[e]=null;t._length=0,t.c=!1})),factory.clone=function(t){var e,i=factory.newElement(),r=void 0===t._length?t.v.length:t._length;for(i.setLength(r),i.c=t.c,e=0;e<r;e+=1)i.setTripleAt(t.v[e][0],t.v[e][1],t.o[e][0],t.o[e][1],t.i[e][0],t.i[e][1],e);return i},factory),factory;function ShapeCollection(){this._length=0,this._maxLength=4,this.shapes=createSizedArray(this._maxLength)}ShapeCollection.prototype.addShape=function(t){this._length===this._maxLength&&(this.shapes=this.shapes.concat(createSizedArray(this._maxLength)),this._maxLength*=2),this.shapes[this._length]=t,this._length+=1},ShapeCollection.prototype.releaseShapes=function(){var t;for(t=0;t<this._length;t+=1)shapePool.release(this.shapes[t]);this._length=0};var shapeCollectionPool=(ob={newShapeCollection:function(){return _length?pool[_length-=1]:new ShapeCollection},release:function(t){var e,i=t._length;for(e=0;e<i;e+=1)shapePool.release(t.shapes[e]);t._length=0,_length===_maxLength&&(pool=pooling.double(pool),_maxLength*=2),pool[_length]=t,_length+=1}},_length=0,_maxLength=4,pool=createSizedArray(_maxLength),ob),ob,_length,_maxLength,pool,ShapePropertyFactory=function(){var t=-999999;function e(t,e,i){var r,n,s,a,o,h,l,p,c,d=i.lastIndex,f=this.keyframes;if(t<f[0].t-this.offsetTime)r=f[0].s[0],s=!0,d=0;else if(t>=f[f.length-1].t-this.offsetTime)r=f[f.length-1].s?f[f.length-1].s[0]:f[f.length-2].e[0],s=!0;else{for(var u,m,y,g=d,v=f.length-1,b=!0;b&&(u=f[g],!((m=f[g+1]).t-this.offsetTime>t));)g<v-1?g+=1:b=!1;if(y=this.keyframesMetadata[g]||{},d=g,!(s=1===u.h)){if(t>=m.t-this.offsetTime)p=1;else if(t<u.t-this.offsetTime)p=0;else{var _;y.__fnct?_=y.__fnct:(_=BezierFactory.getBezierEasing(u.o.x,u.o.y,u.i.x,u.i.y).get,y.__fnct=_),p=_((t-(u.t-this.offsetTime))/(m.t-this.offsetTime-(u.t-this.offsetTime)))}n=m.s?m.s[0]:u.e[0]}r=u.s[0]}for(h=e._length,l=r.i[0].length,i.lastIndex=d,a=0;a<h;a+=1)for(o=0;o<l;o+=1)c=s?r.i[a][o]:r.i[a][o]+(n.i[a][o]-r.i[a][o])*p,e.i[a][o]=c,c=s?r.o[a][o]:r.o[a][o]+(n.o[a][o]-r.o[a][o])*p,e.o[a][o]=c,c=s?r.v[a][o]:r.v[a][o]+(n.v[a][o]-r.v[a][o])*p,e.v[a][o]=c}function i(){var e=this.comp.renderedFrame-this.offsetTime,i=this.keyframes[0].t-this.offsetTime,r=this.keyframes[this.keyframes.length-1].t-this.offsetTime,n=this._caching.lastFrame;return n!==t&&(n<i&&e<i||n>r&&e>r)||(this._caching.lastIndex=n<e?this._caching.lastIndex:0,this.interpolateShape(e,this.pv,this._caching)),this._caching.lastFrame=e,this.pv}function r(){this.paths=this.localShapeCollection}function n(t){(function(t,e){if(t._length!==e._length||t.c!==e.c)return!1;var i,r=t._length;for(i=0;i<r;i+=1)if(t.v[i][0]!==e.v[i][0]||t.v[i][1]!==e.v[i][1]||t.o[i][0]!==e.o[i][0]||t.o[i][1]!==e.o[i][1]||t.i[i][0]!==e.i[i][0]||t.i[i][1]!==e.i[i][1])return!1;return!0})(this.v,t)||(this.v=shapePool.clone(t),this.localShapeCollection.releaseShapes(),this.localShapeCollection.addShape(this.v),this._mdf=!0,this.paths=this.localShapeCollection)}function s(){if(this.elem.globalData.frameId!==this.frameId)if(this.effectsSequence.length)if(this.lock)this.setVValue(this.pv);else{var t,e;this.lock=!0,this._mdf=!1,t=this.kf?this.pv:this.data.ks?this.data.ks.k:this.data.pt.k;var i=this.effectsSequence.length;for(e=0;e<i;e+=1)t=this.effectsSequence[e](t);this.setVValue(t),this.lock=!1,this.frameId=this.elem.globalData.frameId}else this._mdf=!1}function a(t,e,i){this.propType="shape",this.comp=t.comp,this.container=t,this.elem=t,this.data=e,this.k=!1,this.kf=!1,this._mdf=!1;var n=3===i?e.pt.k:e.ks.k;this.v=shapePool.clone(n),this.pv=shapePool.clone(this.v),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.paths=this.localShapeCollection,this.paths.addShape(this.v),this.reset=r,this.effectsSequence=[]}function o(t){this.effectsSequence.push(t),this.container.addDynamicProperty(this)}function h(e,n,s){this.propType="shape",this.comp=e.comp,this.elem=e,this.container=e,this.offsetTime=e.data.st,this.keyframes=3===s?n.pt.k:n.ks.k,this.keyframesMetadata=[],this.k=!0,this.kf=!0;var a=this.keyframes[0].s[0].i.length;this.v=shapePool.newElement(),this.v.setPathData(this.keyframes[0].s[0].c,a),this.pv=shapePool.clone(this.v),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.paths=this.localShapeCollection,this.paths.addShape(this.v),this.lastFrame=t,this.reset=r,this._caching={lastFrame:t,lastIndex:0},this.effectsSequence=[i.bind(this)]}a.prototype.interpolateShape=e,a.prototype.getValue=s,a.prototype.setVValue=n,a.prototype.addEffect=o,h.prototype.getValue=s,h.prototype.interpolateShape=e,h.prototype.setVValue=n,h.prototype.addEffect=o;var l=function(){var t=roundCorner;function e(t,e){this.v=shapePool.newElement(),this.v.setPathData(!0,4),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.paths=this.localShapeCollection,this.localShapeCollection.addShape(this.v),this.d=e.d,this.elem=t,this.comp=t.comp,this.frameId=-1,this.initDynamicPropertyContainer(t),this.p=PropertyFactory.getProp(t,e.p,1,0,this),this.s=PropertyFactory.getProp(t,e.s,1,0,this),this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertEllToPath())}return e.prototype={reset:r,getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf&&this.convertEllToPath())},convertEllToPath:function(){var e=this.p.v[0],i=this.p.v[1],r=this.s.v[0]/2,n=this.s.v[1]/2,s=3!==this.d,a=this.v;a.v[0][0]=e,a.v[0][1]=i-n,a.v[1][0]=s?e+r:e-r,a.v[1][1]=i,a.v[2][0]=e,a.v[2][1]=i+n,a.v[3][0]=s?e-r:e+r,a.v[3][1]=i,a.i[0][0]=s?e-r*t:e+r*t,a.i[0][1]=i-n,a.i[1][0]=s?e+r:e-r,a.i[1][1]=i-n*t,a.i[2][0]=s?e+r*t:e-r*t,a.i[2][1]=i+n,a.i[3][0]=s?e-r:e+r,a.i[3][1]=i+n*t,a.o[0][0]=s?e+r*t:e-r*t,a.o[0][1]=i-n,a.o[1][0]=s?e+r:e-r,a.o[1][1]=i+n*t,a.o[2][0]=s?e-r*t:e+r*t,a.o[2][1]=i+n,a.o[3][0]=s?e-r:e+r,a.o[3][1]=i-n*t}},extendPrototype([DynamicPropertyContainer],e),e}(),p=function(){function t(t,e){this.v=shapePool.newElement(),this.v.setPathData(!0,0),this.elem=t,this.comp=t.comp,this.data=e,this.frameId=-1,this.d=e.d,this.initDynamicPropertyContainer(t),1===e.sy?(this.ir=PropertyFactory.getProp(t,e.ir,0,0,this),this.is=PropertyFactory.getProp(t,e.is,0,.01,this),this.convertToPath=this.convertStarToPath):this.convertToPath=this.convertPolygonToPath,this.pt=PropertyFactory.getProp(t,e.pt,0,0,this),this.p=PropertyFactory.getProp(t,e.p,1,0,this),this.r=PropertyFactory.getProp(t,e.r,0,degToRads,this),this.or=PropertyFactory.getProp(t,e.or,0,0,this),this.os=PropertyFactory.getProp(t,e.os,0,.01,this),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.localShapeCollection.addShape(this.v),this.paths=this.localShapeCollection,this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertToPath())}return t.prototype={reset:r,getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf&&this.convertToPath())},convertStarToPath:function(){var t,e,i,r,n=2*Math.floor(this.pt.v),s=2*Math.PI/n,a=!0,o=this.or.v,h=this.ir.v,l=this.os.v,p=this.is.v,c=2*Math.PI*o/(2*n),d=2*Math.PI*h/(2*n),f=-Math.PI/2;f+=this.r.v;var u=3===this.data.d?-1:1;for(this.v._length=0,t=0;t<n;t+=1){i=a?l:p,r=a?c:d;var m=(e=a?o:h)*Math.cos(f),y=e*Math.sin(f),g=0===m&&0===y?0:y/Math.sqrt(m*m+y*y),v=0===m&&0===y?0:-m/Math.sqrt(m*m+y*y);m+=+this.p.v[0],y+=+this.p.v[1],this.v.setTripleAt(m,y,m-g*r*i*u,y-v*r*i*u,m+g*r*i*u,y+v*r*i*u,t,!0),a=!a,f+=s*u}},convertPolygonToPath:function(){var t,e=Math.floor(this.pt.v),i=2*Math.PI/e,r=this.or.v,n=this.os.v,s=2*Math.PI*r/(4*e),a=.5*-Math.PI,o=3===this.data.d?-1:1;for(a+=this.r.v,this.v._length=0,t=0;t<e;t+=1){var h=r*Math.cos(a),l=r*Math.sin(a),p=0===h&&0===l?0:l/Math.sqrt(h*h+l*l),c=0===h&&0===l?0:-h/Math.sqrt(h*h+l*l);h+=+this.p.v[0],l+=+this.p.v[1],this.v.setTripleAt(h,l,h-p*s*n*o,l-c*s*n*o,h+p*s*n*o,l+c*s*n*o,t,!0),a+=i*o}this.paths.length=0,this.paths[0]=this.v}},extendPrototype([DynamicPropertyContainer],t),t}(),c=function(){function t(t,e){this.v=shapePool.newElement(),this.v.c=!0,this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.localShapeCollection.addShape(this.v),this.paths=this.localShapeCollection,this.elem=t,this.comp=t.comp,this.frameId=-1,this.d=e.d,this.initDynamicPropertyContainer(t),this.p=PropertyFactory.getProp(t,e.p,1,0,this),this.s=PropertyFactory.getProp(t,e.s,1,0,this),this.r=PropertyFactory.getProp(t,e.r,0,0,this),this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertRectToPath())}return t.prototype={convertRectToPath:function(){var t=this.p.v[0],e=this.p.v[1],i=this.s.v[0]/2,r=this.s.v[1]/2,n=bmMin(i,r,this.r.v),s=n*(1-roundCorner);this.v._length=0,2===this.d||1===this.d?(this.v.setTripleAt(t+i,e-r+n,t+i,e-r+n,t+i,e-r+s,0,!0),this.v.setTripleAt(t+i,e+r-n,t+i,e+r-s,t+i,e+r-n,1,!0),0!==n?(this.v.setTripleAt(t+i-n,e+r,t+i-n,e+r,t+i-s,e+r,2,!0),this.v.setTripleAt(t-i+n,e+r,t-i+s,e+r,t-i+n,e+r,3,!0),this.v.setTripleAt(t-i,e+r-n,t-i,e+r-n,t-i,e+r-s,4,!0),this.v.setTripleAt(t-i,e-r+n,t-i,e-r+s,t-i,e-r+n,5,!0),this.v.setTripleAt(t-i+n,e-r,t-i+n,e-r,t-i+s,e-r,6,!0),this.v.setTripleAt(t+i-n,e-r,t+i-s,e-r,t+i-n,e-r,7,!0)):(this.v.setTripleAt(t-i,e+r,t-i+s,e+r,t-i,e+r,2),this.v.setTripleAt(t-i,e-r,t-i,e-r+s,t-i,e-r,3))):(this.v.setTripleAt(t+i,e-r+n,t+i,e-r+s,t+i,e-r+n,0,!0),0!==n?(this.v.setTripleAt(t+i-n,e-r,t+i-n,e-r,t+i-s,e-r,1,!0),this.v.setTripleAt(t-i+n,e-r,t-i+s,e-r,t-i+n,e-r,2,!0),this.v.setTripleAt(t-i,e-r+n,t-i,e-r+n,t-i,e-r+s,3,!0),this.v.setTripleAt(t-i,e+r-n,t-i,e+r-s,t-i,e+r-n,4,!0),this.v.setTripleAt(t-i+n,e+r,t-i+n,e+r,t-i+s,e+r,5,!0),this.v.setTripleAt(t+i-n,e+r,t+i-s,e+r,t+i-n,e+r,6,!0),this.v.setTripleAt(t+i,e+r-n,t+i,e+r-n,t+i,e+r-s,7,!0)):(this.v.setTripleAt(t-i,e-r,t-i+s,e-r,t-i,e-r,1,!0),this.v.setTripleAt(t-i,e+r,t-i,e+r-s,t-i,e+r,2,!0),this.v.setTripleAt(t+i,e+r,t+i-s,e+r,t+i,e+r,3,!0)))},getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf&&this.convertRectToPath())},reset:r},extendPrototype([DynamicPropertyContainer],t),t}(),d={getShapeProp:function(t,e,i){var r;return 3===i||4===i?r=(3===i?e.pt:e.ks).k.length?new h(t,e,i):new a(t,e,i):5===i?r=new c(t,e):6===i?r=new l(t,e):7===i&&(r=new p(t,e)),r.k&&t.addDynamicProperty(r),r},getConstructorFunction:function(){return a},getKeyframedConstructorFunction:function(){return h}};return d}(),Matrix=function(){var t=Math.cos,e=Math.sin,i=Math.tan,r=Math.round;function n(){return this.props[0]=1,this.props[1]=0,this.props[2]=0,this.props[3]=0,this.props[4]=0,this.props[5]=1,this.props[6]=0,this.props[7]=0,this.props[8]=0,this.props[9]=0,this.props[10]=1,this.props[11]=0,this.props[12]=0,this.props[13]=0,this.props[14]=0,this.props[15]=1,this}function s(i){if(0===i)return this;var r=t(i),n=e(i);return this._t(r,-n,0,0,n,r,0,0,0,0,1,0,0,0,0,1)}function a(i){if(0===i)return this;var r=t(i),n=e(i);return this._t(1,0,0,0,0,r,-n,0,0,n,r,0,0,0,0,1)}function o(i){if(0===i)return this;var r=t(i),n=e(i);return this._t(r,0,n,0,0,1,0,0,-n,0,r,0,0,0,0,1)}function h(i){if(0===i)return this;var r=t(i),n=e(i);return this._t(r,-n,0,0,n,r,0,0,0,0,1,0,0,0,0,1)}function l(t,e){return this._t(1,e,t,1,0,0)}function p(t,e){return this.shear(i(t),i(e))}function c(r,n){var s=t(n),a=e(n);return this._t(s,a,0,0,-a,s,0,0,0,0,1,0,0,0,0,1)._t(1,0,0,0,i(r),1,0,0,0,0,1,0,0,0,0,1)._t(s,-a,0,0,a,s,0,0,0,0,1,0,0,0,0,1)}function d(t,e,i){return i||0===i||(i=1),1===t&&1===e&&1===i?this:this._t(t,0,0,0,0,e,0,0,0,0,i,0,0,0,0,1)}function f(t,e,i,r,n,s,a,o,h,l,p,c,d,f,u,m){return this.props[0]=t,this.props[1]=e,this.props[2]=i,this.props[3]=r,this.props[4]=n,this.props[5]=s,this.props[6]=a,this.props[7]=o,this.props[8]=h,this.props[9]=l,this.props[10]=p,this.props[11]=c,this.props[12]=d,this.props[13]=f,this.props[14]=u,this.props[15]=m,this}function u(t,e,i){return i=i||0,0!==t||0!==e||0!==i?this._t(1,0,0,0,0,1,0,0,0,0,1,0,t,e,i,1):this}function m(t,e,i,r,n,s,a,o,h,l,p,c,d,f,u,m){var y=this.props;if(1===t&&0===e&&0===i&&0===r&&0===n&&1===s&&0===a&&0===o&&0===h&&0===l&&1===p&&0===c)return y[12]=y[12]*t+y[15]*d,y[13]=y[13]*s+y[15]*f,y[14]=y[14]*p+y[15]*u,y[15]*=m,this._identityCalculated=!1,this;var g=y[0],v=y[1],b=y[2],_=y[3],E=y[4],S=y[5],P=y[6],x=y[7],w=y[8],C=y[9],A=y[10],k=y[11],D=y[12],T=y[13],M=y[14],I=y[15];return y[0]=g*t+v*n+b*h+_*d,y[1]=g*e+v*s+b*l+_*f,y[2]=g*i+v*a+b*p+_*u,y[3]=g*r+v*o+b*c+_*m,y[4]=E*t+S*n+P*h+x*d,y[5]=E*e+S*s+P*l+x*f,y[6]=E*i+S*a+P*p+x*u,y[7]=E*r+S*o+P*c+x*m,y[8]=w*t+C*n+A*h+k*d,y[9]=w*e+C*s+A*l+k*f,y[10]=w*i+C*a+A*p+k*u,y[11]=w*r+C*o+A*c+k*m,y[12]=D*t+T*n+M*h+I*d,y[13]=D*e+T*s+M*l+I*f,y[14]=D*i+T*a+M*p+I*u,y[15]=D*r+T*o+M*c+I*m,this._identityCalculated=!1,this}function y(){return this._identityCalculated||(this._identity=!(1!==this.props[0]||0!==this.props[1]||0!==this.props[2]||0!==this.props[3]||0!==this.props[4]||1!==this.props[5]||0!==this.props[6]||0!==this.props[7]||0!==this.props[8]||0!==this.props[9]||1!==this.props[10]||0!==this.props[11]||0!==this.props[12]||0!==this.props[13]||0!==this.props[14]||1!==this.props[15]),this._identityCalculated=!0),this._identity}function g(t){for(var e=0;e<16;){if(t.props[e]!==this.props[e])return!1;e+=1}return!0}function v(t){var e;for(e=0;e<16;e+=1)t.props[e]=this.props[e];return t}function b(t){var e;for(e=0;e<16;e+=1)this.props[e]=t[e]}function _(t,e,i){return{x:t*this.props[0]+e*this.props[4]+i*this.props[8]+this.props[12],y:t*this.props[1]+e*this.props[5]+i*this.props[9]+this.props[13],z:t*this.props[2]+e*this.props[6]+i*this.props[10]+this.props[14]}}function E(t,e,i){return t*this.props[0]+e*this.props[4]+i*this.props[8]+this.props[12]}function S(t,e,i){return t*this.props[1]+e*this.props[5]+i*this.props[9]+this.props[13]}function P(t,e,i){return t*this.props[2]+e*this.props[6]+i*this.props[10]+this.props[14]}function x(){var t=this.props[0]*this.props[5]-this.props[1]*this.props[4],e=this.props[5]/t,i=-this.props[1]/t,r=-this.props[4]/t,n=this.props[0]/t,s=(this.props[4]*this.props[13]-this.props[5]*this.props[12])/t,a=-(this.props[0]*this.props[13]-this.props[1]*this.props[12])/t,o=new Matrix;return o.props[0]=e,o.props[1]=i,o.props[4]=r,o.props[5]=n,o.props[12]=s,o.props[13]=a,o}function w(t){return this.getInverseMatrix().applyToPointArray(t[0],t[1],t[2]||0)}function C(t){var e,i=t.length,r=[];for(e=0;e<i;e+=1)r[e]=w(t[e]);return r}function A(t,e,i){var r=createTypedArray("float32",6);if(this.isIdentity())r[0]=t[0],r[1]=t[1],r[2]=e[0],r[3]=e[1],r[4]=i[0],r[5]=i[1];else{var n=this.props[0],s=this.props[1],a=this.props[4],o=this.props[5],h=this.props[12],l=this.props[13];r[0]=t[0]*n+t[1]*a+h,r[1]=t[0]*s+t[1]*o+l,r[2]=e[0]*n+e[1]*a+h,r[3]=e[0]*s+e[1]*o+l,r[4]=i[0]*n+i[1]*a+h,r[5]=i[0]*s+i[1]*o+l}return r}function k(t,e,i){return this.isIdentity()?[t,e,i]:[t*this.props[0]+e*this.props[4]+i*this.props[8]+this.props[12],t*this.props[1]+e*this.props[5]+i*this.props[9]+this.props[13],t*this.props[2]+e*this.props[6]+i*this.props[10]+this.props[14]]}function D(t,e){if(this.isIdentity())return t+","+e;var i=this.props;return Math.round(100*(t*i[0]+e*i[4]+i[12]))/100+","+Math.round(100*(t*i[1]+e*i[5]+i[13]))/100}function T(){for(var t=0,e=this.props,i="matrix3d(";t<16;)i+=r(1e4*e[t])/1e4,i+=15===t?")":",",t+=1;return i}function M(t){return t<1e-6&&t>0||t>-1e-6&&t<0?r(1e4*t)/1e4:t}function I(){var t=this.props;return"matrix("+M(t[0])+","+M(t[1])+","+M(t[4])+","+M(t[5])+","+M(t[12])+","+M(t[13])+")"}return function(){this.reset=n,this.rotate=s,this.rotateX=a,this.rotateY=o,this.rotateZ=h,this.skew=p,this.skewFromAxis=c,this.shear=l,this.scale=d,this.setTransform=f,this.translate=u,this.transform=m,this.applyToPoint=_,this.applyToX=E,this.applyToY=S,this.applyToZ=P,this.applyToPointArray=k,this.applyToTriplePoints=A,this.applyToPointStringified=D,this.toCSS=T,this.to2dCSS=I,this.clone=v,this.cloneFromProps=b,this.equals=g,this.inversePoints=C,this.inversePoint=w,this.getInverseMatrix=x,this._t=this.transform,this.isIdentity=y,this._identity=!0,this._identityCalculated=!1,this.props=createTypedArray("float32",16),this.reset()}}();function _typeof$3(t){return _typeof$3="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_typeof$3(t)}var lottie={},standalone="__[STANDALONE]__",animationData="__[ANIMATIONDATA]__",renderer="";function setLocation(t){setLocationHref(t)}function searchAnimations(){!0===standalone?animationManager.searchAnimations(animationData,standalone,renderer):animationManager.searchAnimations()}function setSubframeRendering(t){setSubframeEnabled(t)}function setPrefix(t){setIdPrefix(t)}function loadAnimation(t){return!0===standalone&&(t.animationData=JSON.parse(animationData)),animationManager.loadAnimation(t)}function setQuality(t){if("string"==typeof t)switch(t){case"high":setDefaultCurveSegments(200);break;default:case"medium":setDefaultCurveSegments(50);break;case"low":setDefaultCurveSegments(10)}else!isNaN(t)&&t>1&&setDefaultCurveSegments(t);getDefaultCurveSegments()>=50?roundValues(!1):roundValues(!0)}function inBrowser(){return"undefined"!=typeof navigator}function installPlugin(t,e){"expressions"===t&&setExpressionsPlugin(e)}function getFactory(t){switch(t){case"propertyFactory":return PropertyFactory;case"shapePropertyFactory":return ShapePropertyFactory;case"matrix":return Matrix;default:return null}}function checkReady(){"complete"===document.readyState&&(clearInterval(readyStateCheckInterval),searchAnimations())}function getQueryVariable(t){for(var e=queryString.split("&"),i=0;i<e.length;i+=1){var r=e[i].split("=");if(decodeURIComponent(r[0])==t)return decodeURIComponent(r[1])}return null}lottie.play=animationManager.play,lottie.pause=animationManager.pause,lottie.setLocationHref=setLocation,lottie.togglePause=animationManager.togglePause,lottie.setSpeed=animationManager.setSpeed,lottie.setDirection=animationManager.setDirection,lottie.stop=animationManager.stop,lottie.searchAnimations=searchAnimations,lottie.registerAnimation=animationManager.registerAnimation,lottie.loadAnimation=loadAnimation,lottie.setSubframeRendering=setSubframeRendering,lottie.resize=animationManager.resize,lottie.goToAndStop=animationManager.goToAndStop,lottie.destroy=animationManager.destroy,lottie.setQuality=setQuality,lottie.inBrowser=inBrowser,lottie.installPlugin=installPlugin,lottie.freeze=animationManager.freeze,lottie.unfreeze=animationManager.unfreeze,lottie.setVolume=animationManager.setVolume,lottie.mute=animationManager.mute,lottie.unmute=animationManager.unmute,lottie.getRegisteredAnimations=animationManager.getRegisteredAnimations,lottie.useWebWorker=setWebWorker,lottie.setIDPrefix=setPrefix,lottie.__getFactory=getFactory,lottie.version="5.10.0";var queryString="";if(standalone){var scripts=document.getElementsByTagName("script"),index=scripts.length-1,myScript=scripts[index]||{src:""};queryString=myScript.src?myScript.src.replace(/^[^\?]+\??/,""):"",renderer=getQueryVariable("renderer")}var readyStateCheckInterval=setInterval(checkReady,100);try{"object"!==_typeof$3(exports)&&(window.bodymovin=lottie)}catch(t){}var ShapeModifiers=function(){var t={},e={};return t.registerModifier=function(t,i){e[t]||(e[t]=i)},t.getModifier=function(t,i,r){return new e[t](i,r)},t}();function ShapeModifier(){}function TrimModifier(){}function PuckerAndBloatModifier(){}ShapeModifier.prototype.initModifierProperties=function(){},ShapeModifier.prototype.addShapeToModifier=function(){},ShapeModifier.prototype.addShape=function(t){if(!this.closed){t.sh.container.addDynamicProperty(t.sh);var e={shape:t.sh,data:t,localShapeCollection:shapeCollectionPool.newShapeCollection()};this.shapes.push(e),this.addShapeToModifier(e),this._isAnimated&&t.setAsAnimated()}},ShapeModifier.prototype.init=function(t,e){this.shapes=[],this.elem=t,this.initDynamicPropertyContainer(t),this.initModifierProperties(t,e),this.frameId=initialDefaultFrame,this.closed=!1,this.k=!1,this.dynamicProperties.length?this.k=!0:this.getValue(!0)},ShapeModifier.prototype.processKeys=function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties())},extendPrototype([DynamicPropertyContainer],ShapeModifier),extendPrototype([ShapeModifier],TrimModifier),TrimModifier.prototype.initModifierProperties=function(t,e){this.s=PropertyFactory.getProp(t,e.s,0,.01,this),this.e=PropertyFactory.getProp(t,e.e,0,.01,this),this.o=PropertyFactory.getProp(t,e.o,0,0,this),this.sValue=0,this.eValue=0,this.getValue=this.processKeys,this.m=e.m,this._isAnimated=!!this.s.effectsSequence.length||!!this.e.effectsSequence.length||!!this.o.effectsSequence.length},TrimModifier.prototype.addShapeToModifier=function(t){t.pathsData=[]},TrimModifier.prototype.calculateShapeEdges=function(t,e,i,r,n){var s=[];e<=1?s.push({s:t,e:e}):t>=1?s.push({s:t-1,e:e-1}):(s.push({s:t,e:1}),s.push({s:0,e:e-1}));var a,o,h=[],l=s.length;for(a=0;a<l;a+=1){var p,c;(o=s[a]).e*n<r||o.s*n>r+i||(p=o.s*n<=r?0:(o.s*n-r)/i,c=o.e*n>=r+i?1:(o.e*n-r)/i,h.push([p,c]))}return h.length||h.push([0,0]),h},TrimModifier.prototype.releasePathsData=function(t){var e,i=t.length;for(e=0;e<i;e+=1)segmentsLengthPool.release(t[e]);return t.length=0,t},TrimModifier.prototype.processShapes=function(t){var e,i,r,n;if(this._mdf||t){var s=this.o.v%360/360;if(s<0&&(s+=1),(e=this.s.v>1?1+s:this.s.v<0?0+s:this.s.v+s)>(i=this.e.v>1?1+s:this.e.v<0?0+s:this.e.v+s)){var a=e;e=i,i=a}e=1e-4*Math.round(1e4*e),i=1e-4*Math.round(1e4*i),this.sValue=e,this.eValue=i}else e=this.sValue,i=this.eValue;var o,h,l,p,c,d=this.shapes.length,f=0;if(i===e)for(n=0;n<d;n+=1)this.shapes[n].localShapeCollection.releaseShapes(),this.shapes[n].shape._mdf=!0,this.shapes[n].shape.paths=this.shapes[n].localShapeCollection,this._mdf&&(this.shapes[n].pathsData.length=0);else if(1===i&&0===e||0===i&&1===e){if(this._mdf)for(n=0;n<d;n+=1)this.shapes[n].pathsData.length=0,this.shapes[n].shape._mdf=!0}else{var u,m,y=[];for(n=0;n<d;n+=1)if((u=this.shapes[n]).shape._mdf||this._mdf||t||2===this.m){if(h=(r=u.shape.paths)._length,c=0,!u.shape._mdf&&u.pathsData.length)c=u.totalShapeLength;else{for(l=this.releasePathsData(u.pathsData),o=0;o<h;o+=1)p=bez.getSegmentsLength(r.shapes[o]),l.push(p),c+=p.totalLength;u.totalShapeLength=c,u.pathsData=l}f+=c,u.shape._mdf=!0}else u.shape.paths=u.localShapeCollection;var g,v=e,b=i,_=0;for(n=d-1;n>=0;n-=1)if((u=this.shapes[n]).shape._mdf){for((m=u.localShapeCollection).releaseShapes(),2===this.m&&d>1?(g=this.calculateShapeEdges(e,i,u.totalShapeLength,_,f),_+=u.totalShapeLength):g=[[v,b]],h=g.length,o=0;o<h;o+=1){v=g[o][0],b=g[o][1],y.length=0,b<=1?y.push({s:u.totalShapeLength*v,e:u.totalShapeLength*b}):v>=1?y.push({s:u.totalShapeLength*(v-1),e:u.totalShapeLength*(b-1)}):(y.push({s:u.totalShapeLength*v,e:u.totalShapeLength}),y.push({s:0,e:u.totalShapeLength*(b-1)}));var E=this.addShapes(u,y[0]);if(y[0].s!==y[0].e){if(y.length>1)if(u.shape.paths.shapes[u.shape.paths._length-1].c){var S=E.pop();this.addPaths(E,m),E=this.addShapes(u,y[1],S)}else this.addPaths(E,m),E=this.addShapes(u,y[1]);this.addPaths(E,m)}}u.shape.paths=m}}},TrimModifier.prototype.addPaths=function(t,e){var i,r=t.length;for(i=0;i<r;i+=1)e.addShape(t[i])},TrimModifier.prototype.addSegment=function(t,e,i,r,n,s,a){n.setXYAt(e[0],e[1],"o",s),n.setXYAt(i[0],i[1],"i",s+1),a&&n.setXYAt(t[0],t[1],"v",s),n.setXYAt(r[0],r[1],"v",s+1)},TrimModifier.prototype.addSegmentFromArray=function(t,e,i,r){e.setXYAt(t[1],t[5],"o",i),e.setXYAt(t[2],t[6],"i",i+1),r&&e.setXYAt(t[0],t[4],"v",i),e.setXYAt(t[3],t[7],"v",i+1)},TrimModifier.prototype.addShapes=function(t,e,i){var r,n,s,a,o,h,l,p,c=t.pathsData,d=t.shape.paths.shapes,f=t.shape.paths._length,u=0,m=[],y=!0;for(i?(o=i._length,p=i._length):(i=shapePool.newElement(),o=0,p=0),m.push(i),r=0;r<f;r+=1){for(h=c[r].lengths,i.c=d[r].c,s=d[r].c?h.length:h.length+1,n=1;n<s;n+=1)if(u+(a=h[n-1]).addedLength<e.s)u+=a.addedLength,i.c=!1;else{if(u>e.e){i.c=!1;break}e.s<=u&&e.e>=u+a.addedLength?(this.addSegment(d[r].v[n-1],d[r].o[n-1],d[r].i[n],d[r].v[n],i,o,y),y=!1):(l=bez.getNewSegment(d[r].v[n-1],d[r].v[n],d[r].o[n-1],d[r].i[n],(e.s-u)/a.addedLength,(e.e-u)/a.addedLength,h[n-1]),this.addSegmentFromArray(l,i,o,y),y=!1,i.c=!1),u+=a.addedLength,o+=1}if(d[r].c&&h.length){if(a=h[n-1],u<=e.e){var g=h[n-1].addedLength;e.s<=u&&e.e>=u+g?(this.addSegment(d[r].v[n-1],d[r].o[n-1],d[r].i[0],d[r].v[0],i,o,y),y=!1):(l=bez.getNewSegment(d[r].v[n-1],d[r].v[0],d[r].o[n-1],d[r].i[0],(e.s-u)/g,(e.e-u)/g,h[n-1]),this.addSegmentFromArray(l,i,o,y),y=!1,i.c=!1)}else i.c=!1;u+=a.addedLength,o+=1}if(i._length&&(i.setXYAt(i.v[p][0],i.v[p][1],"i",p),i.setXYAt(i.v[i._length-1][0],i.v[i._length-1][1],"o",i._length-1)),u>e.e)break;r<f-1&&(i=shapePool.newElement(),y=!0,m.push(i),o=0)}return m},extendPrototype([ShapeModifier],PuckerAndBloatModifier),PuckerAndBloatModifier.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.amount=PropertyFactory.getProp(t,e.a,0,null,this),this._isAnimated=!!this.amount.effectsSequence.length},PuckerAndBloatModifier.prototype.processPath=function(t,e){var i=e/100,r=[0,0],n=t._length,s=0;for(s=0;s<n;s+=1)r[0]+=t.v[s][0],r[1]+=t.v[s][1];r[0]/=n,r[1]/=n;var a,o,h,l,p,c,d=shapePool.newElement();for(d.c=t.c,s=0;s<n;s+=1)a=t.v[s][0]+(r[0]-t.v[s][0])*i,o=t.v[s][1]+(r[1]-t.v[s][1])*i,h=t.o[s][0]+(r[0]-t.o[s][0])*-i,l=t.o[s][1]+(r[1]-t.o[s][1])*-i,p=t.i[s][0]+(r[0]-t.i[s][0])*-i,c=t.i[s][1]+(r[1]-t.i[s][1])*-i,d.setTripleAt(a,o,h,l,p,c,s);return d},PuckerAndBloatModifier.prototype.processShapes=function(t){var e,i,r,n,s,a,o=this.shapes.length,h=this.amount.v;if(0!==h)for(i=0;i<o;i+=1){if(a=(s=this.shapes[i]).localShapeCollection,s.shape._mdf||this._mdf||t)for(a.releaseShapes(),s.shape._mdf=!0,e=s.shape.paths.shapes,n=s.shape.paths._length,r=0;r<n;r+=1)a.addShape(this.processPath(e[r],h));s.shape.paths=s.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)};var TransformPropertyFactory=function(){var t=[0,0];function e(t,e,i){if(this.elem=t,this.frameId=-1,this.propType="transform",this.data=e,this.v=new Matrix,this.pre=new Matrix,this.appliedTransformations=0,this.initDynamicPropertyContainer(i||t),e.p&&e.p.s?(this.px=PropertyFactory.getProp(t,e.p.x,0,0,this),this.py=PropertyFactory.getProp(t,e.p.y,0,0,this),e.p.z&&(this.pz=PropertyFactory.getProp(t,e.p.z,0,0,this))):this.p=PropertyFactory.getProp(t,e.p||{k:[0,0,0]},1,0,this),e.rx){if(this.rx=PropertyFactory.getProp(t,e.rx,0,degToRads,this),this.ry=PropertyFactory.getProp(t,e.ry,0,degToRads,this),this.rz=PropertyFactory.getProp(t,e.rz,0,degToRads,this),e.or.k[0].ti){var r,n=e.or.k.length;for(r=0;r<n;r+=1)e.or.k[r].to=null,e.or.k[r].ti=null}this.or=PropertyFactory.getProp(t,e.or,1,degToRads,this),this.or.sh=!0}else this.r=PropertyFactory.getProp(t,e.r||{k:0},0,degToRads,this);e.sk&&(this.sk=PropertyFactory.getProp(t,e.sk,0,degToRads,this),this.sa=PropertyFactory.getProp(t,e.sa,0,degToRads,this)),this.a=PropertyFactory.getProp(t,e.a||{k:[0,0,0]},1,0,this),this.s=PropertyFactory.getProp(t,e.s||{k:[100,100,100]},1,.01,this),e.o?this.o=PropertyFactory.getProp(t,e.o,0,.01,t):this.o={_mdf:!1,v:1},this._isDirty=!0,this.dynamicProperties.length||this.getValue(!0)}return e.prototype={applyToMatrix:function(t){var e=this._mdf;this.iterateDynamicProperties(),this._mdf=this._mdf||e,this.a&&t.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.s&&t.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.sk&&t.skewFromAxis(-this.sk.v,this.sa.v),this.r?t.rotate(-this.r.v):t.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.data.p.s?this.data.p.z?t.translate(this.px.v,this.py.v,-this.pz.v):t.translate(this.px.v,this.py.v,0):t.translate(this.p.v[0],this.p.v[1],-this.p.v[2])},getValue:function(e){if(this.elem.globalData.frameId!==this.frameId){if(this._isDirty&&(this.precalculateMatrix(),this._isDirty=!1),this.iterateDynamicProperties(),this._mdf||e){var i;if(this.v.cloneFromProps(this.pre.props),this.appliedTransformations<1&&this.v.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.appliedTransformations<2&&this.v.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.sk&&this.appliedTransformations<3&&this.v.skewFromAxis(-this.sk.v,this.sa.v),this.r&&this.appliedTransformations<4?this.v.rotate(-this.r.v):!this.r&&this.appliedTransformations<4&&this.v.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.autoOriented){var r,n;if(i=this.elem.globalData.frameRate,this.p&&this.p.keyframes&&this.p.getValueAtTime)this.p._caching.lastFrame+this.p.offsetTime<=this.p.keyframes[0].t?(r=this.p.getValueAtTime((this.p.keyframes[0].t+.01)/i,0),n=this.p.getValueAtTime(this.p.keyframes[0].t/i,0)):this.p._caching.lastFrame+this.p.offsetTime>=this.p.keyframes[this.p.keyframes.length-1].t?(r=this.p.getValueAtTime(this.p.keyframes[this.p.keyframes.length-1].t/i,0),n=this.p.getValueAtTime((this.p.keyframes[this.p.keyframes.length-1].t-.05)/i,0)):(r=this.p.pv,n=this.p.getValueAtTime((this.p._caching.lastFrame+this.p.offsetTime-.01)/i,this.p.offsetTime));else if(this.px&&this.px.keyframes&&this.py.keyframes&&this.px.getValueAtTime&&this.py.getValueAtTime){r=[],n=[];var s=this.px,a=this.py;s._caching.lastFrame+s.offsetTime<=s.keyframes[0].t?(r[0]=s.getValueAtTime((s.keyframes[0].t+.01)/i,0),r[1]=a.getValueAtTime((a.keyframes[0].t+.01)/i,0),n[0]=s.getValueAtTime(s.keyframes[0].t/i,0),n[1]=a.getValueAtTime(a.keyframes[0].t/i,0)):s._caching.lastFrame+s.offsetTime>=s.keyframes[s.keyframes.length-1].t?(r[0]=s.getValueAtTime(s.keyframes[s.keyframes.length-1].t/i,0),r[1]=a.getValueAtTime(a.keyframes[a.keyframes.length-1].t/i,0),n[0]=s.getValueAtTime((s.keyframes[s.keyframes.length-1].t-.01)/i,0),n[1]=a.getValueAtTime((a.keyframes[a.keyframes.length-1].t-.01)/i,0)):(r=[s.pv,a.pv],n[0]=s.getValueAtTime((s._caching.lastFrame+s.offsetTime-.01)/i,s.offsetTime),n[1]=a.getValueAtTime((a._caching.lastFrame+a.offsetTime-.01)/i,a.offsetTime))}else r=n=t;this.v.rotate(-Math.atan2(r[1]-n[1],r[0]-n[0]))}this.data.p&&this.data.p.s?this.data.p.z?this.v.translate(this.px.v,this.py.v,-this.pz.v):this.v.translate(this.px.v,this.py.v,0):this.v.translate(this.p.v[0],this.p.v[1],-this.p.v[2])}this.frameId=this.elem.globalData.frameId}},precalculateMatrix:function(){if(!this.a.k&&(this.pre.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.appliedTransformations=1,!this.s.effectsSequence.length)){if(this.pre.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.appliedTransformations=2,this.sk){if(this.sk.effectsSequence.length||this.sa.effectsSequence.length)return;this.pre.skewFromAxis(-this.sk.v,this.sa.v),this.appliedTransformations=3}this.r?this.r.effectsSequence.length||(this.pre.rotate(-this.r.v),this.appliedTransformations=4):this.rz.effectsSequence.length||this.ry.effectsSequence.length||this.rx.effectsSequence.length||this.or.effectsSequence.length||(this.pre.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.appliedTransformations=4)}},autoOrient:function(){}},extendPrototype([DynamicPropertyContainer],e),e.prototype.addDynamicProperty=function(t){this._addDynamicProperty(t),this.elem.addDynamicProperty(t),this._isDirty=!0},e.prototype._addDynamicProperty=DynamicPropertyContainer.prototype.addDynamicProperty,{getTransformProperty:function(t,i,r){return new e(t,i,r)}}}();function RepeaterModifier(){}function RoundCornersModifier(){}function floatEqual(t,e){return 1e5*Math.abs(t-e)<=Math.min(Math.abs(t),Math.abs(e))}function floatZero(t){return Math.abs(t)<=1e-5}function lerp(t,e,i){return t*(1-i)+e*i}function lerpPoint(t,e,i){return[lerp(t[0],e[0],i),lerp(t[1],e[1],i)]}function quadRoots(t,e,i){if(0===t)return[];var r=e*e-4*t*i;if(r<0)return[];var n=-e/(2*t);if(0===r)return[n];var s=Math.sqrt(r)/(2*t);return[n-s,n+s]}function polynomialCoefficients(t,e,i,r){return[3*e-t-3*i+r,3*t-6*e+3*i,-3*t+3*e,t]}function singlePoint(t){return new PolynomialBezier(t,t,t,t,!1)}function PolynomialBezier(t,e,i,r,n){n&&pointEqual(t,e)&&(e=lerpPoint(t,r,1/3)),n&&pointEqual(i,r)&&(i=lerpPoint(t,r,2/3));var s=polynomialCoefficients(t[0],e[0],i[0],r[0]),a=polynomialCoefficients(t[1],e[1],i[1],r[1]);this.a=[s[0],a[0]],this.b=[s[1],a[1]],this.c=[s[2],a[2]],this.d=[s[3],a[3]],this.points=[t,e,i,r]}function extrema(t,e){var i=t.points[0][e],r=t.points[t.points.length-1][e];if(i>r){var n=r;r=i,i=n}for(var s=quadRoots(3*t.a[e],2*t.b[e],t.c[e]),a=0;a<s.length;a+=1)if(s[a]>0&&s[a]<1){var o=t.point(s[a])[e];o<i?i=o:o>r&&(r=o)}return{min:i,max:r}}function intersectData(t,e,i){var r=t.boundingBox();return{cx:r.cx,cy:r.cy,width:r.width,height:r.height,bez:t,t:(e+i)/2,t1:e,t2:i}}function splitData(t){var e=t.bez.split(.5);return[intersectData(e[0],t.t1,t.t),intersectData(e[1],t.t,t.t2)]}function boxIntersect(t,e){return 2*Math.abs(t.cx-e.cx)<t.width+e.width&&2*Math.abs(t.cy-e.cy)<t.height+e.height}function intersectsImpl(t,e,i,r,n,s){if(boxIntersect(t,e))if(i>=s||t.width<=r&&t.height<=r&&e.width<=r&&e.height<=r)n.push([t.t,e.t]);else{var a=splitData(t),o=splitData(e);intersectsImpl(a[0],o[0],i+1,r,n,s),intersectsImpl(a[0],o[1],i+1,r,n,s),intersectsImpl(a[1],o[0],i+1,r,n,s),intersectsImpl(a[1],o[1],i+1,r,n,s)}}function crossProduct(t,e){return[t[1]*e[2]-t[2]*e[1],t[2]*e[0]-t[0]*e[2],t[0]*e[1]-t[1]*e[0]]}function lineIntersection(t,e,i,r){var n=[t[0],t[1],1],s=[e[0],e[1],1],a=[i[0],i[1],1],o=[r[0],r[1],1],h=crossProduct(crossProduct(n,s),crossProduct(a,o));return floatZero(h[2])?null:[h[0]/h[2],h[1]/h[2]]}function polarOffset(t,e,i){return[t[0]+Math.cos(e)*i,t[1]-Math.sin(e)*i]}function pointDistance(t,e){return Math.hypot(t[0]-e[0],t[1]-e[1])}function pointEqual(t,e){return floatEqual(t[0],e[0])&&floatEqual(t[1],e[1])}function ZigZagModifier(){}function setPoint(t,e,i,r,n,s,a){var o=i-Math.PI/2,h=i+Math.PI/2,l=e[0]+Math.cos(i)*r*n,p=e[1]-Math.sin(i)*r*n;t.setTripleAt(l,p,l+Math.cos(o)*s,p-Math.sin(o)*s,l+Math.cos(h)*a,p-Math.sin(h)*a,t.length())}function getPerpendicularVector(t,e){var i=[e[0]-t[0],e[1]-t[1]],r=.5*-Math.PI;return[Math.cos(r)*i[0]-Math.sin(r)*i[1],Math.sin(r)*i[0]+Math.cos(r)*i[1]]}function getProjectingAngle(t,e){var i=0===e?t.length()-1:e-1,r=(e+1)%t.length(),n=getPerpendicularVector(t.v[i],t.v[r]);return Math.atan2(0,1)-Math.atan2(n[1],n[0])}function zigZagCorner(t,e,i,r,n,s,a){var o=getProjectingAngle(e,i),h=e.v[i%e._length],l=e.v[0===i?e._length-1:i-1],p=e.v[(i+1)%e._length],c=2===s?Math.sqrt(Math.pow(h[0]-l[0],2)+Math.pow(h[1]-l[1],2)):0,d=2===s?Math.sqrt(Math.pow(h[0]-p[0],2)+Math.pow(h[1]-p[1],2)):0;setPoint(t,e.v[i%e._length],o,a,r,d/(2*(n+1)),c/(2*(n+1)),s)}function zigZagSegment(t,e,i,r,n,s){for(var a=0;a<r;a+=1){var o=(a+1)/(r+1),h=2===n?Math.sqrt(Math.pow(e.points[3][0]-e.points[0][0],2)+Math.pow(e.points[3][1]-e.points[0][1],2)):0,l=e.normalAngle(o);setPoint(t,e.point(o),l,s,i,h/(2*(r+1)),h/(2*(r+1)),n),s=-s}return s}function linearOffset(t,e,i){var r=Math.atan2(e[0]-t[0],e[1]-t[1]);return[polarOffset(t,r,i),polarOffset(e,r,i)]}function offsetSegment(t,e){var i,r,n,s,a,o,h;i=(h=linearOffset(t.points[0],t.points[1],e))[0],r=h[1],n=(h=linearOffset(t.points[1],t.points[2],e))[0],s=h[1],a=(h=linearOffset(t.points[2],t.points[3],e))[0],o=h[1];var l=lineIntersection(i,r,n,s);null===l&&(l=r);var p=lineIntersection(a,o,n,s);return null===p&&(p=a),new PolynomialBezier(i,l,p,o)}function joinLines(t,e,i,r,n){var s=e.points[3],a=i.points[0];if(3===r)return s;if(pointEqual(s,a))return s;if(2===r){var o=-e.tangentAngle(1),h=-i.tangentAngle(0)+Math.PI,l=lineIntersection(s,polarOffset(s,o+Math.PI/2,100),a,polarOffset(a,o+Math.PI/2,100)),p=l?pointDistance(l,s):pointDistance(s,a)/2,c=polarOffset(s,o,2*p*roundCorner);return t.setXYAt(c[0],c[1],"o",t.length()-1),c=polarOffset(a,h,2*p*roundCorner),t.setTripleAt(a[0],a[1],a[0],a[1],c[0],c[1],t.length()),a}var d=lineIntersection(pointEqual(s,e.points[2])?e.points[0]:e.points[2],s,a,pointEqual(a,i.points[1])?i.points[3]:i.points[1]);return d&&pointDistance(d,s)<n?(t.setTripleAt(d[0],d[1],d[0],d[1],d[0],d[1],t.length()),d):s}function getIntersection(t,e){var i=t.intersections(e);return i.length&&floatEqual(i[0][0],1)&&i.shift(),i.length?i[0]:null}function pruneSegmentIntersection(t,e){var i=t.slice(),r=e.slice(),n=getIntersection(t[t.length-1],e[0]);return n&&(i[t.length-1]=t[t.length-1].split(n[0])[0],r[0]=e[0].split(n[1])[1]),t.length>1&&e.length>1&&(n=getIntersection(t[0],e[e.length-1]))?[[t[0].split(n[0])[0]],[e[e.length-1].split(n[1])[1]]]:[i,r]}function pruneIntersections(t){for(var e,i=1;i<t.length;i+=1)e=pruneSegmentIntersection(t[i-1],t[i]),t[i-1]=e[0],t[i]=e[1];return t.length>1&&(e=pruneSegmentIntersection(t[t.length-1],t[0]),t[t.length-1]=e[0],t[0]=e[1]),t}function offsetSegmentSplit(t,e){var i,r,n,s,a=t.inflectionPoints();if(0===a.length)return[offsetSegment(t,e)];if(1===a.length||floatEqual(a[1],1))return i=(n=t.split(a[0]))[0],r=n[1],[offsetSegment(i,e),offsetSegment(r,e)];i=(n=t.split(a[0]))[0];var o=(a[1]-a[0])/(1-a[0]);return s=(n=n[1].split(o))[0],r=n[1],[offsetSegment(i,e),offsetSegment(s,e),offsetSegment(r,e)]}function OffsetPathModifier(){}function getFontProperties(t){for(var e=t.fStyle?t.fStyle.split(" "):[],i="normal",r="normal",n=e.length,s=0;s<n;s+=1)switch(e[s].toLowerCase()){case"italic":r="italic";break;case"bold":i="700";break;case"black":i="900";break;case"medium":i="500";break;case"regular":case"normal":i="400";break;case"light":case"thin":i="200"}return{style:r,weight:t.fWeight||i}}extendPrototype([ShapeModifier],RepeaterModifier),RepeaterModifier.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.c=PropertyFactory.getProp(t,e.c,0,null,this),this.o=PropertyFactory.getProp(t,e.o,0,null,this),this.tr=TransformPropertyFactory.getTransformProperty(t,e.tr,this),this.so=PropertyFactory.getProp(t,e.tr.so,0,.01,this),this.eo=PropertyFactory.getProp(t,e.tr.eo,0,.01,this),this.data=e,this.dynamicProperties.length||this.getValue(!0),this._isAnimated=!!this.dynamicProperties.length,this.pMatrix=new Matrix,this.rMatrix=new Matrix,this.sMatrix=new Matrix,this.tMatrix=new Matrix,this.matrix=new Matrix},RepeaterModifier.prototype.applyTransforms=function(t,e,i,r,n,s){var a=s?-1:1,o=r.s.v[0]+(1-r.s.v[0])*(1-n),h=r.s.v[1]+(1-r.s.v[1])*(1-n);t.translate(r.p.v[0]*a*n,r.p.v[1]*a*n,r.p.v[2]),e.translate(-r.a.v[0],-r.a.v[1],r.a.v[2]),e.rotate(-r.r.v*a*n),e.translate(r.a.v[0],r.a.v[1],r.a.v[2]),i.translate(-r.a.v[0],-r.a.v[1],r.a.v[2]),i.scale(s?1/o:o,s?1/h:h),i.translate(r.a.v[0],r.a.v[1],r.a.v[2])},RepeaterModifier.prototype.init=function(t,e,i,r){for(this.elem=t,this.arr=e,this.pos=i,this.elemsData=r,this._currentCopies=0,this._elements=[],this._groups=[],this.frameId=-1,this.initDynamicPropertyContainer(t),this.initModifierProperties(t,e[i]);i>0;)i-=1,this._elements.unshift(e[i]);this.dynamicProperties.length?this.k=!0:this.getValue(!0)},RepeaterModifier.prototype.resetElements=function(t){var e,i=t.length;for(e=0;e<i;e+=1)t[e]._processed=!1,"gr"===t[e].ty&&this.resetElements(t[e].it)},RepeaterModifier.prototype.cloneElements=function(t){var e=JSON.parse(JSON.stringify(t));return this.resetElements(e),e},RepeaterModifier.prototype.changeGroupRender=function(t,e){var i,r=t.length;for(i=0;i<r;i+=1)t[i]._render=e,"gr"===t[i].ty&&this.changeGroupRender(t[i].it,e)},RepeaterModifier.prototype.processShapes=function(t){var e,i,r,n,s,a=!1;if(this._mdf||t){var o,h=Math.ceil(this.c.v);if(this._groups.length<h){for(;this._groups.length<h;){var l={it:this.cloneElements(this._elements),ty:"gr"};l.it.push({a:{a:0,ix:1,k:[0,0]},nm:"Transform",o:{a:0,ix:7,k:100},p:{a:0,ix:2,k:[0,0]},r:{a:1,ix:6,k:[{s:0,e:0,t:0},{s:0,e:0,t:1}]},s:{a:0,ix:3,k:[100,100]},sa:{a:0,ix:5,k:0},sk:{a:0,ix:4,k:0},ty:"tr"}),this.arr.splice(0,0,l),this._groups.splice(0,0,l),this._currentCopies+=1}this.elem.reloadShapes(),a=!0}for(s=0,r=0;r<=this._groups.length-1;r+=1){if(o=s<h,this._groups[r]._render=o,this.changeGroupRender(this._groups[r].it,o),!o){var p=this.elemsData[r].it,c=p[p.length-1];0!==c.transform.op.v?(c.transform.op._mdf=!0,c.transform.op.v=0):c.transform.op._mdf=!1}s+=1}this._currentCopies=h;var d=this.o.v,f=d%1,u=d>0?Math.floor(d):Math.ceil(d),m=this.pMatrix.props,y=this.rMatrix.props,g=this.sMatrix.props;this.pMatrix.reset(),this.rMatrix.reset(),this.sMatrix.reset(),this.tMatrix.reset(),this.matrix.reset();var v,b,_=0;if(d>0){for(;_<u;)this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!1),_+=1;f&&(this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,f,!1),_+=f)}else if(d<0){for(;_>u;)this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!0),_-=1;f&&(this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,-f,!0),_-=f)}for(r=1===this.data.m?0:this._currentCopies-1,n=1===this.data.m?1:-1,s=this._currentCopies;s;){if(b=(i=(e=this.elemsData[r].it)[e.length-1].transform.mProps.v.props).length,e[e.length-1].transform.mProps._mdf=!0,e[e.length-1].transform.op._mdf=!0,e[e.length-1].transform.op.v=1===this._currentCopies?this.so.v:this.so.v+(this.eo.v-this.so.v)*(r/(this._currentCopies-1)),0!==_){for((0!==r&&1===n||r!==this._currentCopies-1&&-1===n)&&this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!1),this.matrix.transform(y[0],y[1],y[2],y[3],y[4],y[5],y[6],y[7],y[8],y[9],y[10],y[11],y[12],y[13],y[14],y[15]),this.matrix.transform(g[0],g[1],g[2],g[3],g[4],g[5],g[6],g[7],g[8],g[9],g[10],g[11],g[12],g[13],g[14],g[15]),this.matrix.transform(m[0],m[1],m[2],m[3],m[4],m[5],m[6],m[7],m[8],m[9],m[10],m[11],m[12],m[13],m[14],m[15]),v=0;v<b;v+=1)i[v]=this.matrix.props[v];this.matrix.reset()}else for(this.matrix.reset(),v=0;v<b;v+=1)i[v]=this.matrix.props[v];_+=1,s-=1,r+=n}}else for(s=this._currentCopies,r=0,n=1;s;)i=(e=this.elemsData[r].it)[e.length-1].transform.mProps.v.props,e[e.length-1].transform.mProps._mdf=!1,e[e.length-1].transform.op._mdf=!1,s-=1,r+=n;return a},RepeaterModifier.prototype.addShape=function(){},extendPrototype([ShapeModifier],RoundCornersModifier),RoundCornersModifier.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.rd=PropertyFactory.getProp(t,e.r,0,null,this),this._isAnimated=!!this.rd.effectsSequence.length},RoundCornersModifier.prototype.processPath=function(t,e){var i,r=shapePool.newElement();r.c=t.c;var n,s,a,o,h,l,p,c,d,f,u,m,y=t._length,g=0;for(i=0;i<y;i+=1)n=t.v[i],a=t.o[i],s=t.i[i],n[0]===a[0]&&n[1]===a[1]&&n[0]===s[0]&&n[1]===s[1]?0!==i&&i!==y-1||t.c?(o=0===i?t.v[y-1]:t.v[i-1],l=(h=Math.sqrt(Math.pow(n[0]-o[0],2)+Math.pow(n[1]-o[1],2)))?Math.min(h/2,e)/h:0,p=u=n[0]+(o[0]-n[0])*l,c=m=n[1]-(n[1]-o[1])*l,d=p-(p-n[0])*roundCorner,f=c-(c-n[1])*roundCorner,r.setTripleAt(p,c,d,f,u,m,g),g+=1,o=i===y-1?t.v[0]:t.v[i+1],l=(h=Math.sqrt(Math.pow(n[0]-o[0],2)+Math.pow(n[1]-o[1],2)))?Math.min(h/2,e)/h:0,p=d=n[0]+(o[0]-n[0])*l,c=f=n[1]+(o[1]-n[1])*l,u=p-(p-n[0])*roundCorner,m=c-(c-n[1])*roundCorner,r.setTripleAt(p,c,d,f,u,m,g),g+=1):(r.setTripleAt(n[0],n[1],a[0],a[1],s[0],s[1],g),g+=1):(r.setTripleAt(t.v[i][0],t.v[i][1],t.o[i][0],t.o[i][1],t.i[i][0],t.i[i][1],g),g+=1);return r},RoundCornersModifier.prototype.processShapes=function(t){var e,i,r,n,s,a,o=this.shapes.length,h=this.rd.v;if(0!==h)for(i=0;i<o;i+=1){if(a=(s=this.shapes[i]).localShapeCollection,s.shape._mdf||this._mdf||t)for(a.releaseShapes(),s.shape._mdf=!0,e=s.shape.paths.shapes,n=s.shape.paths._length,r=0;r<n;r+=1)a.addShape(this.processPath(e[r],h));s.shape.paths=s.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)},PolynomialBezier.prototype.point=function(t){return[((this.a[0]*t+this.b[0])*t+this.c[0])*t+this.d[0],((this.a[1]*t+this.b[1])*t+this.c[1])*t+this.d[1]]},PolynomialBezier.prototype.derivative=function(t){return[(3*t*this.a[0]+2*this.b[0])*t+this.c[0],(3*t*this.a[1]+2*this.b[1])*t+this.c[1]]},PolynomialBezier.prototype.tangentAngle=function(t){var e=this.derivative(t);return Math.atan2(e[1],e[0])},PolynomialBezier.prototype.normalAngle=function(t){var e=this.derivative(t);return Math.atan2(e[0],e[1])},PolynomialBezier.prototype.inflectionPoints=function(){var t=this.a[1]*this.b[0]-this.a[0]*this.b[1];if(floatZero(t))return[];var e=-.5*(this.a[1]*this.c[0]-this.a[0]*this.c[1])/t,i=e*e-1/3*(this.b[1]*this.c[0]-this.b[0]*this.c[1])/t;if(i<0)return[];var r=Math.sqrt(i);return floatZero(r)?r>0&&r<1?[e]:[]:[e-r,e+r].filter((function(t){return t>0&&t<1}))},PolynomialBezier.prototype.split=function(t){if(t<=0)return[singlePoint(this.points[0]),this];if(t>=1)return[this,singlePoint(this.points[this.points.length-1])];var e=lerpPoint(this.points[0],this.points[1],t),i=lerpPoint(this.points[1],this.points[2],t),r=lerpPoint(this.points[2],this.points[3],t),n=lerpPoint(e,i,t),s=lerpPoint(i,r,t),a=lerpPoint(n,s,t);return[new PolynomialBezier(this.points[0],e,n,a,!0),new PolynomialBezier(a,s,r,this.points[3],!0)]},PolynomialBezier.prototype.bounds=function(){return{x:extrema(this,0),y:extrema(this,1)}},PolynomialBezier.prototype.boundingBox=function(){var t=this.bounds();return{left:t.x.min,right:t.x.max,top:t.y.min,bottom:t.y.max,width:t.x.max-t.x.min,height:t.y.max-t.y.min,cx:(t.x.max+t.x.min)/2,cy:(t.y.max+t.y.min)/2}},PolynomialBezier.prototype.intersections=function(t,e,i){void 0===e&&(e=2),void 0===i&&(i=7);var r=[];return intersectsImpl(intersectData(this,0,1),intersectData(t,0,1),0,e,r,i),r},PolynomialBezier.shapeSegment=function(t,e){var i=(e+1)%t.length();return new PolynomialBezier(t.v[e],t.o[e],t.i[i],t.v[i],!0)},PolynomialBezier.shapeSegmentInverted=function(t,e){var i=(e+1)%t.length();return new PolynomialBezier(t.v[i],t.i[i],t.o[e],t.v[e],!0)},extendPrototype([ShapeModifier],ZigZagModifier),ZigZagModifier.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.amplitude=PropertyFactory.getProp(t,e.s,0,null,this),this.frequency=PropertyFactory.getProp(t,e.r,0,null,this),this.pointsType=PropertyFactory.getProp(t,e.pt,0,null,this),this._isAnimated=0!==this.amplitude.effectsSequence.length||0!==this.frequency.effectsSequence.length||0!==this.pointsType.effectsSequence.length},ZigZagModifier.prototype.processPath=function(t,e,i,r){var n=t._length,s=shapePool.newElement();if(s.c=t.c,t.c||(n-=1),0===n)return s;var a=-1,o=PolynomialBezier.shapeSegment(t,0);zigZagCorner(s,t,0,e,i,r,a);for(var h=0;h<n;h+=1)a=zigZagSegment(s,o,e,i,r,-a),o=h!==n-1||t.c?PolynomialBezier.shapeSegment(t,(h+1)%n):null,zigZagCorner(s,t,h+1,e,i,r,a);return s},ZigZagModifier.prototype.processShapes=function(t){var e,i,r,n,s,a,o=this.shapes.length,h=this.amplitude.v,l=Math.max(0,Math.round(this.frequency.v)),p=this.pointsType.v;if(0!==h)for(i=0;i<o;i+=1){if(a=(s=this.shapes[i]).localShapeCollection,s.shape._mdf||this._mdf||t)for(a.releaseShapes(),s.shape._mdf=!0,e=s.shape.paths.shapes,n=s.shape.paths._length,r=0;r<n;r+=1)a.addShape(this.processPath(e[r],h,l,p));s.shape.paths=s.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)},extendPrototype([ShapeModifier],OffsetPathModifier),OffsetPathModifier.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.amount=PropertyFactory.getProp(t,e.a,0,null,this),this.miterLimit=PropertyFactory.getProp(t,e.ml,0,null,this),this.lineJoin=e.lj,this._isAnimated=0!==this.amount.effectsSequence.length},OffsetPathModifier.prototype.processPath=function(t,e,i,r){var n=shapePool.newElement();n.c=t.c;var s,a,o,h=t.length();t.c||(h-=1);var l=[];for(s=0;s<h;s+=1)o=PolynomialBezier.shapeSegment(t,s),l.push(offsetSegmentSplit(o,e));if(!t.c)for(s=h-1;s>=0;s-=1)o=PolynomialBezier.shapeSegmentInverted(t,s),l.push(offsetSegmentSplit(o,e));l=pruneIntersections(l);var p=null,c=null;for(s=0;s<l.length;s+=1){var d=l[s];for(c&&(p=joinLines(n,c,d[0],i,r)),c=d[d.length-1],a=0;a<d.length;a+=1)o=d[a],p&&pointEqual(o.points[0],p)?n.setXYAt(o.points[1][0],o.points[1][1],"o",n.length()-1):n.setTripleAt(o.points[0][0],o.points[0][1],o.points[1][0],o.points[1][1],o.points[0][0],o.points[0][1],n.length()),n.setTripleAt(o.points[3][0],o.points[3][1],o.points[3][0],o.points[3][1],o.points[2][0],o.points[2][1],n.length()),p=o.points[3]}return l.length&&joinLines(n,c,l[0][0],i,r),n},OffsetPathModifier.prototype.processShapes=function(t){var e,i,r,n,s,a,o=this.shapes.length,h=this.amount.v,l=this.miterLimit.v,p=this.lineJoin;if(0!==h)for(i=0;i<o;i+=1){if(a=(s=this.shapes[i]).localShapeCollection,s.shape._mdf||this._mdf||t)for(a.releaseShapes(),s.shape._mdf=!0,e=s.shape.paths.shapes,n=s.shape.paths._length,r=0;r<n;r+=1)a.addShape(this.processPath(e[r],h,p,l));s.shape.paths=s.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)};var FontManager=function(){var t={w:0,size:0,shapes:[],data:{shapes:[]}},e=[];e=e.concat([2304,2305,2306,2307,2362,2363,2364,2364,2366,2367,2368,2369,2370,2371,2372,2373,2374,2375,2376,2377,2378,2379,2380,2381,2382,2383,2387,2388,2389,2390,2391,2402,2403]);var i=["d83cdffb","d83cdffc","d83cdffd","d83cdffe","d83cdfff"],r=[65039,8205];function n(t,e){var i=createTag("span");i.setAttribute("aria-hidden",!0),i.style.fontFamily=e;var r=createTag("span");r.innerText="giItT1WQy@!-/#",i.style.position="absolute",i.style.left="-10000px",i.style.top="-10000px",i.style.fontSize="300px",i.style.fontVariant="normal",i.style.fontStyle="normal",i.style.fontWeight="normal",i.style.letterSpacing="0",i.appendChild(r),document.body.appendChild(i);var n=r.offsetWidth;return r.style.fontFamily=function(t){var e,i=t.split(","),r=i.length,n=[];for(e=0;e<r;e+=1)"sans-serif"!==i[e]&&"monospace"!==i[e]&&n.push(i[e]);return n.join(",")}(t)+", "+e,{node:r,w:n,parent:i}}function s(t,e){var i,r=document.body&&e?"svg":"canvas",n=getFontProperties(t);if("svg"===r){var s=createNS("text");s.style.fontSize="100px",s.setAttribute("font-family",t.fFamily),s.setAttribute("font-style",n.style),s.setAttribute("font-weight",n.weight),s.textContent="1",t.fClass?(s.style.fontFamily="inherit",s.setAttribute("class",t.fClass)):s.style.fontFamily=t.fFamily,e.appendChild(s),i=s}else{var a=new OffscreenCanvas(500,500).getContext("2d");a.font=n.style+" "+n.weight+" 100px "+t.fFamily,i=a}return{measureText:function(t){return"svg"===r?(i.textContent=t,i.getComputedTextLength()):i.measureText(t).width}}}var a=function(){this.fonts=[],this.chars=null,this.typekitLoaded=0,this.isLoaded=!1,this._warned=!1,this.initTime=Date.now(),this.setIsLoadedBinded=this.setIsLoaded.bind(this),this.checkLoadedFontsBinded=this.checkLoadedFonts.bind(this)};a.isModifier=function(t,e){var r=t.toString(16)+e.toString(16);return-1!==i.indexOf(r)},a.isZeroWidthJoiner=function(t,e){return e?t===r[0]&&e===r[1]:t===r[1]},a.isCombinedCharacter=function(t){return-1!==e.indexOf(t)};var o={addChars:function(t){if(t){var e;this.chars||(this.chars=[]);var i,r,n=t.length,s=this.chars.length;for(e=0;e<n;e+=1){for(i=0,r=!1;i<s;)this.chars[i].style===t[e].style&&this.chars[i].fFamily===t[e].fFamily&&this.chars[i].ch===t[e].ch&&(r=!0),i+=1;r||(this.chars.push(t[e]),s+=1)}}},addFonts:function(t,e){if(t){if(this.chars)return this.isLoaded=!0,void(this.fonts=t.list);if(!document.body)return this.isLoaded=!0,t.list.forEach((function(t){t.helper=s(t),t.cache={}})),void(this.fonts=t.list);var i,r=t.list,a=r.length,o=a;for(i=0;i<a;i+=1){var h,l,p=!0;if(r[i].loaded=!1,r[i].monoCase=n(r[i].fFamily,"monospace"),r[i].sansCase=n(r[i].fFamily,"sans-serif"),r[i].fPath){if("p"===r[i].fOrigin||3===r[i].origin){if((h=document.querySelectorAll('style[f-forigin="p"][f-family="'+r[i].fFamily+'"], style[f-origin="3"][f-family="'+r[i].fFamily+'"]')).length>0&&(p=!1),p){var c=createTag("style");c.setAttribute("f-forigin",r[i].fOrigin),c.setAttribute("f-origin",r[i].origin),c.setAttribute("f-family",r[i].fFamily),c.type="text/css",c.innerText="@font-face {font-family: "+r[i].fFamily+"; font-style: normal; src: url('"+r[i].fPath+"');}",e.appendChild(c)}}else if("g"===r[i].fOrigin||1===r[i].origin){for(h=document.querySelectorAll('link[f-forigin="g"], link[f-origin="1"]'),l=0;l<h.length;l+=1)-1!==h[l].href.indexOf(r[i].fPath)&&(p=!1);if(p){var d=createTag("link");d.setAttribute("f-forigin",r[i].fOrigin),d.setAttribute("f-origin",r[i].origin),d.type="text/css",d.rel="stylesheet",d.href=r[i].fPath,document.body.appendChild(d)}}else if("t"===r[i].fOrigin||2===r[i].origin){for(h=document.querySelectorAll('script[f-forigin="t"], script[f-origin="2"]'),l=0;l<h.length;l+=1)r[i].fPath===h[l].src&&(p=!1);if(p){var f=createTag("link");f.setAttribute("f-forigin",r[i].fOrigin),f.setAttribute("f-origin",r[i].origin),f.setAttribute("rel","stylesheet"),f.setAttribute("href",r[i].fPath),e.appendChild(f)}}}else r[i].loaded=!0,o-=1;r[i].helper=s(r[i],e),r[i].cache={},this.fonts.push(r[i])}0===o?this.isLoaded=!0:setTimeout(this.checkLoadedFonts.bind(this),100)}else this.isLoaded=!0},getCharData:function(e,i,r){for(var n=0,s=this.chars.length;n<s;){if(this.chars[n].ch===e&&this.chars[n].style===i&&this.chars[n].fFamily===r)return this.chars[n];n+=1}return("string"==typeof e&&13!==e.charCodeAt(0)||!e)&&console&&console.warn&&!this._warned&&(this._warned=!0,console.warn("Missing character from exported characters list: ",e,i,r)),t},getFontByName:function(t){for(var e=0,i=this.fonts.length;e<i;){if(this.fonts[e].fName===t)return this.fonts[e];e+=1}return this.fonts[0]},measureText:function(t,e,i){var r=this.getFontByName(e),n=t.charCodeAt(0);if(!r.cache[n+1]){var s=r.helper;if(" "===t){var a=s.measureText("|"+t+"|"),o=s.measureText("||");r.cache[n+1]=(a-o)/100}else r.cache[n+1]=s.measureText(t)/100}return r.cache[n+1]*i},checkLoadedFonts:function(){var t,e,i,r=this.fonts.length,n=r;for(t=0;t<r;t+=1)this.fonts[t].loaded?n-=1:"n"===this.fonts[t].fOrigin||0===this.fonts[t].origin?this.fonts[t].loaded=!0:(e=this.fonts[t].monoCase.node,i=this.fonts[t].monoCase.w,e.offsetWidth!==i?(n-=1,this.fonts[t].loaded=!0):(e=this.fonts[t].sansCase.node,i=this.fonts[t].sansCase.w,e.offsetWidth!==i&&(n-=1,this.fonts[t].loaded=!0)),this.fonts[t].loaded&&(this.fonts[t].sansCase.parent.parentNode.removeChild(this.fonts[t].sansCase.parent),this.fonts[t].monoCase.parent.parentNode.removeChild(this.fonts[t].monoCase.parent)));0!==n&&Date.now()-this.initTime<5e3?setTimeout(this.checkLoadedFontsBinded,20):setTimeout(this.setIsLoadedBinded,10)},setIsLoaded:function(){this.isLoaded=!0}};return a.prototype=o,a}();function RenderableElement(){}RenderableElement.prototype={initRenderable:function(){this.isInRange=!1,this.hidden=!1,this.isTransparent=!1,this.renderableComponents=[]},addRenderableComponent:function(t){-1===this.renderableComponents.indexOf(t)&&this.renderableComponents.push(t)},removeRenderableComponent:function(t){-1!==this.renderableComponents.indexOf(t)&&this.renderableComponents.splice(this.renderableComponents.indexOf(t),1)},prepareRenderableFrame:function(t){this.checkLayerLimits(t)},checkTransparency:function(){this.finalTransform.mProp.o.v<=0?!this.isTransparent&&this.globalData.renderConfig.hideOnTransparent&&(this.isTransparent=!0,this.hide()):this.isTransparent&&(this.isTransparent=!1,this.show())},checkLayerLimits:function(t){this.data.ip-this.data.st<=t&&this.data.op-this.data.st>t?!0!==this.isInRange&&(this.globalData._mdf=!0,this._mdf=!0,this.isInRange=!0,this.show()):!1!==this.isInRange&&(this.globalData._mdf=!0,this.isInRange=!1,this.hide())},renderRenderable:function(){var t,e=this.renderableComponents.length;for(t=0;t<e;t+=1)this.renderableComponents[t].renderFrame(this._isFirstFrame)},sourceRectAtTime:function(){return{top:0,left:0,width:100,height:100}},getLayerSize:function(){return 5===this.data.ty?{w:this.data.textData.width,h:this.data.textData.height}:{w:this.data.width,h:this.data.height}}};var getBlendMode=(blendModeEnums={0:"source-over",1:"multiply",2:"screen",3:"overlay",4:"darken",5:"lighten",6:"color-dodge",7:"color-burn",8:"hard-light",9:"soft-light",10:"difference",11:"exclusion",12:"hue",13:"saturation",14:"color",15:"luminosity"},function(t){return blendModeEnums[t]||""}),blendModeEnums;function SliderEffect(t,e,i){this.p=PropertyFactory.getProp(e,t.v,0,0,i)}function AngleEffect(t,e,i){this.p=PropertyFactory.getProp(e,t.v,0,0,i)}function ColorEffect(t,e,i){this.p=PropertyFactory.getProp(e,t.v,1,0,i)}function PointEffect(t,e,i){this.p=PropertyFactory.getProp(e,t.v,1,0,i)}function LayerIndexEffect(t,e,i){this.p=PropertyFactory.getProp(e,t.v,0,0,i)}function MaskIndexEffect(t,e,i){this.p=PropertyFactory.getProp(e,t.v,0,0,i)}function CheckboxEffect(t,e,i){this.p=PropertyFactory.getProp(e,t.v,0,0,i)}function NoValueEffect(){this.p={}}function EffectsManager(t,e){var i,r=t.ef||[];this.effectElements=[];var n,s=r.length;for(i=0;i<s;i+=1)n=new GroupEffect(r[i],e),this.effectElements.push(n)}function GroupEffect(t,e){this.init(t,e)}function BaseElement(){}function FrameElement(){}function FootageElement(t,e,i){this.initFrame(),this.initRenderable(),this.assetData=e.getAssetData(t.refId),this.footageData=e.imageLoader.getAsset(this.assetData),this.initBaseData(t,e,i)}function AudioElement(t,e,i){this.initFrame(),this.initRenderable(),this.assetData=e.getAssetData(t.refId),this.initBaseData(t,e,i),this._isPlaying=!1,this._canPlay=!1;var r=this.globalData.getAssetsPath(this.assetData);this.audio=this.globalData.audioController.createAudio(r),this._currentTime=0,this.globalData.audioController.addAudio(this),this._volumeMultiplier=1,this._volume=1,this._previousVolume=null,this.tm=t.tm?PropertyFactory.getProp(this,t.tm,0,e.frameRate,this):{_placeholder:!0},this.lv=PropertyFactory.getProp(this,t.au&&t.au.lv?t.au.lv:{k:[100]},1,.01,this)}function BaseRenderer(){}function TransformElement(){}function MaskElement(t,e,i){this.data=t,this.element=e,this.globalData=i,this.storedData=[],this.masksProperties=this.data.masksProperties||[],this.maskElement=null;var r,n,s=this.globalData.defs,a=this.masksProperties?this.masksProperties.length:0;this.viewData=createSizedArray(a),this.solidPath="";var o,h,l,p,c,d,f=this.masksProperties,u=0,m=[],y=createElementID(),g="clipPath",v="clip-path";for(r=0;r<a;r+=1)if(("a"!==f[r].mode&&"n"!==f[r].mode||f[r].inv||100!==f[r].o.k||f[r].o.x)&&(g="mask",v="mask"),"s"!==f[r].mode&&"i"!==f[r].mode||0!==u?l=null:((l=createNS("rect")).setAttribute("fill","#ffffff"),l.setAttribute("width",this.element.comp.data.w||0),l.setAttribute("height",this.element.comp.data.h||0),m.push(l)),n=createNS("path"),"n"===f[r].mode)this.viewData[r]={op:PropertyFactory.getProp(this.element,f[r].o,0,.01,this.element),prop:ShapePropertyFactory.getShapeProp(this.element,f[r],3),elem:n,lastPath:""},s.appendChild(n);else{var b;if(u+=1,n.setAttribute("fill","s"===f[r].mode?"#000000":"#ffffff"),n.setAttribute("clip-rule","nonzero"),0!==f[r].x.k?(g="mask",v="mask",d=PropertyFactory.getProp(this.element,f[r].x,0,null,this.element),b=createElementID(),(p=createNS("filter")).setAttribute("id",b),(c=createNS("feMorphology")).setAttribute("operator","erode"),c.setAttribute("in","SourceGraphic"),c.setAttribute("radius","0"),p.appendChild(c),s.appendChild(p),n.setAttribute("stroke","s"===f[r].mode?"#000000":"#ffffff")):(c=null,d=null),this.storedData[r]={elem:n,x:d,expan:c,lastPath:"",lastOperator:"",filterId:b,lastRadius:0},"i"===f[r].mode){h=m.length;var _=createNS("g");for(o=0;o<h;o+=1)_.appendChild(m[o]);var E=createNS("mask");E.setAttribute("mask-type","alpha"),E.setAttribute("id",y+"_"+u),E.appendChild(n),s.appendChild(E),_.setAttribute("mask","url("+getLocationHref()+"#"+y+"_"+u+")"),m.length=0,m.push(_)}else m.push(n);f[r].inv&&!this.solidPath&&(this.solidPath=this.createLayerSolidPath()),this.viewData[r]={elem:n,lastPath:"",op:PropertyFactory.getProp(this.element,f[r].o,0,.01,this.element),prop:ShapePropertyFactory.getShapeProp(this.element,f[r],3),invRect:l},this.viewData[r].prop.k||this.drawPath(f[r],this.viewData[r].prop.v,this.viewData[r])}for(this.maskElement=createNS(g),a=m.length,r=0;r<a;r+=1)this.maskElement.appendChild(m[r]);u>0&&(this.maskElement.setAttribute("id",y),this.element.maskedElement.setAttribute(v,"url("+getLocationHref()+"#"+y+")"),s.appendChild(this.maskElement)),this.viewData.length&&this.element.addRenderableComponent(this)}extendPrototype([DynamicPropertyContainer],GroupEffect),GroupEffect.prototype.getValue=GroupEffect.prototype.iterateDynamicProperties,GroupEffect.prototype.init=function(t,e){var i;this.data=t,this.effectElements=[],this.initDynamicPropertyContainer(e);var r,n=this.data.ef.length,s=this.data.ef;for(i=0;i<n;i+=1){switch(r=null,s[i].ty){case 0:r=new SliderEffect(s[i],e,this);break;case 1:r=new AngleEffect(s[i],e,this);break;case 2:r=new ColorEffect(s[i],e,this);break;case 3:r=new PointEffect(s[i],e,this);break;case 4:case 7:r=new CheckboxEffect(s[i],e,this);break;case 10:r=new LayerIndexEffect(s[i],e,this);break;case 11:r=new MaskIndexEffect(s[i],e,this);break;case 5:r=new EffectsManager(s[i],e,this);break;default:r=new NoValueEffect(s[i],e,this)}r&&this.effectElements.push(r)}},BaseElement.prototype={checkMasks:function(){if(!this.data.hasMask)return!1;for(var t=0,e=this.data.masksProperties.length;t<e;){if("n"!==this.data.masksProperties[t].mode&&!1!==this.data.masksProperties[t].cl)return!0;t+=1}return!1},initExpressions:function(){var t=getExpressionInterfaces();if(t){var e=t("layer"),i=t("effects"),r=t("shape"),n=t("text"),s=t("comp");this.layerInterface=e(this),this.data.hasMask&&this.maskManager&&this.layerInterface.registerMaskInterface(this.maskManager);var a=i.createEffectsInterface(this,this.layerInterface);this.layerInterface.registerEffectsInterface(a),0===this.data.ty||this.data.xt?this.compInterface=s(this):4===this.data.ty?(this.layerInterface.shapeInterface=r(this.shapesData,this.itemsData,this.layerInterface),this.layerInterface.content=this.layerInterface.shapeInterface):5===this.data.ty&&(this.layerInterface.textInterface=n(this),this.layerInterface.text=this.layerInterface.textInterface)}},setBlendMode:function(){var t=getBlendMode(this.data.bm);(this.baseElement||this.layerElement).style["mix-blend-mode"]=t},initBaseData:function(t,e,i){this.globalData=e,this.comp=i,this.data=t,this.layerId=createElementID(),this.data.sr||(this.data.sr=1),this.effectsManager=new EffectsManager(this.data,this,this.dynamicProperties)},getType:function(){return this.type},sourceRectAtTime:function(){}},FrameElement.prototype={initFrame:function(){this._isFirstFrame=!1,this.dynamicProperties=[],this._mdf=!1},prepareProperties:function(t,e){var i,r=this.dynamicProperties.length;for(i=0;i<r;i+=1)(e||this._isParent&&"transform"===this.dynamicProperties[i].propType)&&(this.dynamicProperties[i].getValue(),this.dynamicProperties[i]._mdf&&(this.globalData._mdf=!0,this._mdf=!0))},addDynamicProperty:function(t){-1===this.dynamicProperties.indexOf(t)&&this.dynamicProperties.push(t)}},FootageElement.prototype.prepareFrame=function(){},extendPrototype([RenderableElement,BaseElement,FrameElement],FootageElement),FootageElement.prototype.getBaseElement=function(){return null},FootageElement.prototype.renderFrame=function(){},FootageElement.prototype.destroy=function(){},FootageElement.prototype.initExpressions=function(){var t=getExpressionInterfaces();if(t){var e=t("footage");this.layerInterface=e(this)}},FootageElement.prototype.getFootageData=function(){return this.footageData},AudioElement.prototype.prepareFrame=function(t){if(this.prepareRenderableFrame(t,!0),this.prepareProperties(t,!0),this.tm._placeholder)this._currentTime=t/this.data.sr;else{var e=this.tm.v;this._currentTime=e}this._volume=this.lv.v[0];var i=this._volume*this._volumeMultiplier;this._previousVolume!==i&&(this._previousVolume=i,this.audio.volume(i))},extendPrototype([RenderableElement,BaseElement,FrameElement],AudioElement),AudioElement.prototype.renderFrame=function(){this.isInRange&&this._canPlay&&(this._isPlaying?(!this.audio.playing()||Math.abs(this._currentTime/this.globalData.frameRate-this.audio.seek())>.1)&&this.audio.seek(this._currentTime/this.globalData.frameRate):(this.audio.play(),this.audio.seek(this._currentTime/this.globalData.frameRate),this._isPlaying=!0))},AudioElement.prototype.show=function(){},AudioElement.prototype.hide=function(){this.audio.pause(),this._isPlaying=!1},AudioElement.prototype.pause=function(){this.audio.pause(),this._isPlaying=!1,this._canPlay=!1},AudioElement.prototype.resume=function(){this._canPlay=!0},AudioElement.prototype.setRate=function(t){this.audio.rate(t)},AudioElement.prototype.volume=function(t){this._volumeMultiplier=t,this._previousVolume=t*this._volume,this.audio.volume(this._previousVolume)},AudioElement.prototype.getBaseElement=function(){return null},AudioElement.prototype.destroy=function(){},AudioElement.prototype.sourceRectAtTime=function(){},AudioElement.prototype.initExpressions=function(){},BaseRenderer.prototype.checkLayers=function(t){var e,i,r=this.layers.length;for(this.completeLayers=!0,e=r-1;e>=0;e-=1)this.elements[e]||(i=this.layers[e]).ip-i.st<=t-this.layers[e].st&&i.op-i.st>t-this.layers[e].st&&this.buildItem(e),this.completeLayers=!!this.elements[e]&&this.completeLayers;this.checkPendingElements()},BaseRenderer.prototype.createItem=function(t){switch(t.ty){case 2:return this.createImage(t);case 0:return this.createComp(t);case 1:return this.createSolid(t);case 3:default:return this.createNull(t);case 4:return this.createShape(t);case 5:return this.createText(t);case 6:return this.createAudio(t);case 13:return this.createCamera(t);case 15:return this.createFootage(t)}},BaseRenderer.prototype.createCamera=function(){throw new Error("You're using a 3d camera. Try the html renderer.")},BaseRenderer.prototype.createAudio=function(t){return new AudioElement(t,this.globalData,this)},BaseRenderer.prototype.createFootage=function(t){return new FootageElement(t,this.globalData,this)},BaseRenderer.prototype.buildAllItems=function(){var t,e=this.layers.length;for(t=0;t<e;t+=1)this.buildItem(t);this.checkPendingElements()},BaseRenderer.prototype.includeLayers=function(t){var e;this.completeLayers=!1;var i,r=t.length,n=this.layers.length;for(e=0;e<r;e+=1)for(i=0;i<n;){if(this.layers[i].id===t[e].id){this.layers[i]=t[e];break}i+=1}},BaseRenderer.prototype.setProjectInterface=function(t){this.globalData.projectInterface=t},BaseRenderer.prototype.initItems=function(){this.globalData.progressiveLoad||this.buildAllItems()},BaseRenderer.prototype.buildElementParenting=function(t,e,i){for(var r=this.elements,n=this.layers,s=0,a=n.length;s<a;)n[s].ind==e&&(r[s]&&!0!==r[s]?(i.push(r[s]),r[s].setAsParent(),void 0!==n[s].parent?this.buildElementParenting(t,n[s].parent,i):t.setHierarchy(i)):(this.buildItem(s),this.addPendingElement(t))),s+=1},BaseRenderer.prototype.addPendingElement=function(t){this.pendingElements.push(t)},BaseRenderer.prototype.searchExtraCompositions=function(t){var e,i=t.length;for(e=0;e<i;e+=1)if(t[e].xt){var r=this.createComp(t[e]);r.initExpressions(),this.globalData.projectInterface.registerComposition(r)}},BaseRenderer.prototype.getElementByPath=function(t){var e,i=t.shift();if("number"==typeof i)e=this.elements[i];else{var r,n=this.elements.length;for(r=0;r<n;r+=1)if(this.elements[r].data.nm===i){e=this.elements[r];break}}return 0===t.length?e:e.getElementByPath(t)},BaseRenderer.prototype.setupGlobalData=function(t,e){this.globalData.fontManager=new FontManager,this.globalData.fontManager.addChars(t.chars),this.globalData.fontManager.addFonts(t.fonts,e),this.globalData.getAssetData=this.animationItem.getAssetData.bind(this.animationItem),this.globalData.getAssetsPath=this.animationItem.getAssetsPath.bind(this.animationItem),this.globalData.imageLoader=this.animationItem.imagePreloader,this.globalData.audioController=this.animationItem.audioController,this.globalData.frameId=0,this.globalData.frameRate=t.fr,this.globalData.nm=t.nm,this.globalData.compSize={w:t.w,h:t.h}},TransformElement.prototype={initTransform:function(){this.finalTransform={mProp:this.data.ks?TransformPropertyFactory.getTransformProperty(this,this.data.ks,this):{o:0},_matMdf:!1,_opMdf:!1,mat:new Matrix},this.data.ao&&(this.finalTransform.mProp.autoOriented=!0),this.data.ty},renderTransform:function(){if(this.finalTransform._opMdf=this.finalTransform.mProp.o._mdf||this._isFirstFrame,this.finalTransform._matMdf=this.finalTransform.mProp._mdf||this._isFirstFrame,this.hierarchy){var t,e=this.finalTransform.mat,i=0,r=this.hierarchy.length;if(!this.finalTransform._matMdf)for(;i<r;){if(this.hierarchy[i].finalTransform.mProp._mdf){this.finalTransform._matMdf=!0;break}i+=1}if(this.finalTransform._matMdf)for(t=this.finalTransform.mProp.v.props,e.cloneFromProps(t),i=0;i<r;i+=1)t=this.hierarchy[i].finalTransform.mProp.v.props,e.transform(t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8],t[9],t[10],t[11],t[12],t[13],t[14],t[15])}},globalToLocal:function(t){var e=[];e.push(this.finalTransform);for(var i,r=!0,n=this.comp;r;)n.finalTransform?(n.data.hasMask&&e.splice(0,0,n.finalTransform),n=n.comp):r=!1;var s,a=e.length;for(i=0;i<a;i+=1)s=e[i].mat.applyToPointArray(0,0,0),t=[t[0]-s[0],t[1]-s[1],0];return t},mHelper:new Matrix},MaskElement.prototype.getMaskProperty=function(t){return this.viewData[t].prop},MaskElement.prototype.renderFrame=function(t){var e,i=this.element.finalTransform.mat,r=this.masksProperties.length;for(e=0;e<r;e+=1)if((this.viewData[e].prop._mdf||t)&&this.drawPath(this.masksProperties[e],this.viewData[e].prop.v,this.viewData[e]),(this.viewData[e].op._mdf||t)&&this.viewData[e].elem.setAttribute("fill-opacity",this.viewData[e].op.v),"n"!==this.masksProperties[e].mode&&(this.viewData[e].invRect&&(this.element.finalTransform.mProp._mdf||t)&&this.viewData[e].invRect.setAttribute("transform",i.getInverseMatrix().to2dCSS()),this.storedData[e].x&&(this.storedData[e].x._mdf||t))){var n=this.storedData[e].expan;this.storedData[e].x.v<0?("erode"!==this.storedData[e].lastOperator&&(this.storedData[e].lastOperator="erode",this.storedData[e].elem.setAttribute("filter","url("+getLocationHref()+"#"+this.storedData[e].filterId+")")),n.setAttribute("radius",-this.storedData[e].x.v)):("dilate"!==this.storedData[e].lastOperator&&(this.storedData[e].lastOperator="dilate",this.storedData[e].elem.setAttribute("filter",null)),this.storedData[e].elem.setAttribute("stroke-width",2*this.storedData[e].x.v))}},MaskElement.prototype.getMaskelement=function(){return this.maskElement},MaskElement.prototype.createLayerSolidPath=function(){var t="M0,0 ";return t+=" h"+this.globalData.compSize.w,t+=" v"+this.globalData.compSize.h,(t+=" h-"+this.globalData.compSize.w)+" v-"+this.globalData.compSize.h+" "},MaskElement.prototype.drawPath=function(t,e,i){var r,n,s=" M"+e.v[0][0]+","+e.v[0][1];for(n=e._length,r=1;r<n;r+=1)s+=" C"+e.o[r-1][0]+","+e.o[r-1][1]+" "+e.i[r][0]+","+e.i[r][1]+" "+e.v[r][0]+","+e.v[r][1];if(e.c&&n>1&&(s+=" C"+e.o[r-1][0]+","+e.o[r-1][1]+" "+e.i[0][0]+","+e.i[0][1]+" "+e.v[0][0]+","+e.v[0][1]),i.lastPath!==s){var a="";i.elem&&(e.c&&(a=t.inv?this.solidPath+s:s),i.elem.setAttribute("d",a)),i.lastPath=s}},MaskElement.prototype.destroy=function(){this.element=null,this.globalData=null,this.maskElement=null,this.data=null,this.masksProperties=null};var filtersFactory=function(){var t={createFilter:function(t,e){var i=createNS("filter");return i.setAttribute("id",t),!0!==e&&(i.setAttribute("filterUnits","objectBoundingBox"),i.setAttribute("x","0%"),i.setAttribute("y","0%"),i.setAttribute("width","100%"),i.setAttribute("height","100%")),i},createAlphaToLuminanceFilter:function(){var t=createNS("feColorMatrix");return t.setAttribute("type","matrix"),t.setAttribute("color-interpolation-filters","sRGB"),t.setAttribute("values","0 0 0 1 0  0 0 0 1 0  0 0 0 1 0  0 0 0 1 1"),t}};return t}(),featureSupport=function(){var t={maskType:!0};return(/MSIE 10/i.test(navigator.userAgent)||/MSIE 9/i.test(navigator.userAgent)||/rv:11.0/i.test(navigator.userAgent)||/Edge\/\d./i.test(navigator.userAgent))&&(t.maskType=!1),t}(),registeredEffects={},idPrefix="filter_result_";function SVGEffects(t){var e,i,r="SourceGraphic",n=t.data.ef?t.data.ef.length:0,s=createElementID(),a=filtersFactory.createFilter(s,!0),o=0;for(this.filters=[],e=0;e<n;e+=1){i=null;var h=t.data.ef[e].ty;registeredEffects[h]&&(i=new(0,registeredEffects[h].effect)(a,t.effectsManager.effectElements[e],t,idPrefix+o,r),r=idPrefix+o,registeredEffects[h].countsAsEffect&&(o+=1)),i&&this.filters.push(i)}o&&(t.globalData.defs.appendChild(a),t.layerElement.setAttribute("filter","url("+getLocationHref()+"#"+s+")")),this.filters.length&&t.addRenderableComponent(this)}function registerEffect(t,e,i){registeredEffects[t]={effect:e,countsAsEffect:i}}function SVGBaseElement(){}function HierarchyElement(){}function RenderableDOMElement(){}function IImageElement(t,e,i){this.assetData=e.getAssetData(t.refId),this.initElement(t,e,i),this.sourceRect={top:0,left:0,width:this.assetData.w,height:this.assetData.h}}function ProcessedElement(t,e){this.elem=t,this.pos=e}function IShapeElement(){}SVGEffects.prototype.renderFrame=function(t){var e,i=this.filters.length;for(e=0;e<i;e+=1)this.filters[e].renderFrame(t)},SVGBaseElement.prototype={initRendererElement:function(){this.layerElement=createNS("g")},createContainerElements:function(){this.matteElement=createNS("g"),this.transformedElement=this.layerElement,this.maskedElement=this.layerElement,this._sizeChanged=!1;var t=null;if(this.data.td){this.matteMasks={};var e=createNS("symbol");e.setAttribute("id",this.layerId);var i=createNS("g");i.appendChild(this.layerElement),e.appendChild(i),t=i,this.globalData.defs.appendChild(e)}else this.data.tt?(this.matteElement.appendChild(this.layerElement),t=this.matteElement,this.baseElement=this.matteElement):this.baseElement=this.layerElement;if(this.data.ln&&this.layerElement.setAttribute("id",this.data.ln),this.data.cl&&this.layerElement.setAttribute("class",this.data.cl),0===this.data.ty&&!this.data.hd){var r=createNS("clipPath"),n=createNS("path");n.setAttribute("d","M0,0 L"+this.data.w+",0 L"+this.data.w+","+this.data.h+" L0,"+this.data.h+"z");var s=createElementID();if(r.setAttribute("id",s),r.appendChild(n),this.globalData.defs.appendChild(r),this.checkMasks()){var a=createNS("g");a.setAttribute("clip-path","url("+getLocationHref()+"#"+s+")"),a.appendChild(this.layerElement),this.transformedElement=a,t?t.appendChild(this.transformedElement):this.baseElement=this.transformedElement}else this.layerElement.setAttribute("clip-path","url("+getLocationHref()+"#"+s+")")}0!==this.data.bm&&this.setBlendMode()},renderElement:function(){this.finalTransform._matMdf&&this.transformedElement.setAttribute("transform",this.finalTransform.mat.to2dCSS()),this.finalTransform._opMdf&&this.transformedElement.setAttribute("opacity",this.finalTransform.mProp.o.v)},destroyBaseElement:function(){this.layerElement=null,this.matteElement=null,this.maskManager.destroy()},getBaseElement:function(){return this.data.hd?null:this.baseElement},createRenderableComponents:function(){this.maskManager=new MaskElement(this.data,this,this.globalData),this.renderableEffectsManager=new SVGEffects(this)},getMatte:function(t){if(!this.matteMasks[t]){var e,i,r,n,s=this.layerId+"_"+t;if(1===t||3===t){var a=createNS("mask");a.setAttribute("id",s),a.setAttribute("mask-type",3===t?"luminance":"alpha"),(r=createNS("use")).setAttributeNS("http://www.w3.org/1999/xlink","href","#"+this.layerId),a.appendChild(r),this.globalData.defs.appendChild(a),featureSupport.maskType||1!==t||(a.setAttribute("mask-type","luminance"),e=createElementID(),i=filtersFactory.createFilter(e),this.globalData.defs.appendChild(i),i.appendChild(filtersFactory.createAlphaToLuminanceFilter()),(n=createNS("g")).appendChild(r),a.appendChild(n),n.setAttribute("filter","url("+getLocationHref()+"#"+e+")"))}else if(2===t){var o=createNS("mask");o.setAttribute("id",s),o.setAttribute("mask-type","alpha");var h=createNS("g");o.appendChild(h),e=createElementID(),i=filtersFactory.createFilter(e);var l=createNS("feComponentTransfer");l.setAttribute("in","SourceGraphic"),i.appendChild(l);var p=createNS("feFuncA");p.setAttribute("type","table"),p.setAttribute("tableValues","1.0 0.0"),l.appendChild(p),this.globalData.defs.appendChild(i);var c=createNS("rect");c.setAttribute("width",this.comp.data.w),c.setAttribute("height",this.comp.data.h),c.setAttribute("x","0"),c.setAttribute("y","0"),c.setAttribute("fill","#ffffff"),c.setAttribute("opacity","0"),h.setAttribute("filter","url("+getLocationHref()+"#"+e+")"),h.appendChild(c),(r=createNS("use")).setAttributeNS("http://www.w3.org/1999/xlink","href","#"+this.layerId),h.appendChild(r),featureSupport.maskType||(o.setAttribute("mask-type","luminance"),i.appendChild(filtersFactory.createAlphaToLuminanceFilter()),n=createNS("g"),h.appendChild(c),n.appendChild(this.layerElement),h.appendChild(n)),this.globalData.defs.appendChild(o)}this.matteMasks[t]=s}return this.matteMasks[t]},setMatte:function(t){this.matteElement&&this.matteElement.setAttribute("mask","url("+getLocationHref()+"#"+t+")")}},HierarchyElement.prototype={initHierarchy:function(){this.hierarchy=[],this._isParent=!1,this.checkParenting()},setHierarchy:function(t){this.hierarchy=t},setAsParent:function(){this._isParent=!0},checkParenting:function(){void 0!==this.data.parent&&this.comp.buildElementParenting(this,this.data.parent,[])}},extendPrototype([RenderableElement,createProxyFunction({initElement:function(t,e,i){this.initFrame(),this.initBaseData(t,e,i),this.initTransform(t,e,i),this.initHierarchy(),this.initRenderable(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),this.createContent(),this.hide()},hide:function(){this.hidden||this.isInRange&&!this.isTransparent||((this.baseElement||this.layerElement).style.display="none",this.hidden=!0)},show:function(){this.isInRange&&!this.isTransparent&&(this.data.hd||((this.baseElement||this.layerElement).style.display="block"),this.hidden=!1,this._isFirstFrame=!0)},renderFrame:function(){this.data.hd||this.hidden||(this.renderTransform(),this.renderRenderable(),this.renderElement(),this.renderInnerContent(),this._isFirstFrame&&(this._isFirstFrame=!1))},renderInnerContent:function(){},prepareFrame:function(t){this._mdf=!1,this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange),this.checkTransparency()},destroy:function(){this.innerElem=null,this.destroyBaseElement()}})],RenderableDOMElement),extendPrototype([BaseElement,TransformElement,SVGBaseElement,HierarchyElement,FrameElement,RenderableDOMElement],IImageElement),IImageElement.prototype.createContent=function(){var t=this.globalData.getAssetsPath(this.assetData);this.innerElem=createNS("image"),this.innerElem.setAttribute("width",this.assetData.w+"px"),this.innerElem.setAttribute("height",this.assetData.h+"px"),this.innerElem.setAttribute("preserveAspectRatio",this.assetData.pr||this.globalData.renderConfig.imagePreserveAspectRatio),this.innerElem.setAttributeNS("http://www.w3.org/1999/xlink","href",t),this.layerElement.appendChild(this.innerElem)},IImageElement.prototype.sourceRectAtTime=function(){return this.sourceRect},IShapeElement.prototype={addShapeToModifiers:function(t){var e,i=this.shapeModifiers.length;for(e=0;e<i;e+=1)this.shapeModifiers[e].addShape(t)},isShapeInAnimatedModifiers:function(t){for(var e=this.shapeModifiers.length;0<e;)if(this.shapeModifiers[0].isAnimatedWithShape(t))return!0;return!1},renderModifiers:function(){if(this.shapeModifiers.length){var t,e=this.shapes.length;for(t=0;t<e;t+=1)this.shapes[t].sh.reset();for(t=(e=this.shapeModifiers.length)-1;t>=0&&!this.shapeModifiers[t].processShapes(this._isFirstFrame);t-=1);}},searchProcessedElement:function(t){for(var e=this.processedElements,i=0,r=e.length;i<r;){if(e[i].elem===t)return e[i].pos;i+=1}return 0},addProcessedElement:function(t,e){for(var i=this.processedElements,r=i.length;r;)if(i[r-=1].elem===t)return void(i[r].pos=e);i.push(new ProcessedElement(t,e))},prepareFrame:function(t){this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange)}};var lineCapEnum={1:"butt",2:"round",3:"square"},lineJoinEnum={1:"miter",2:"round",3:"bevel"};function SVGShapeData(t,e,i){this.caches=[],this.styles=[],this.transformers=t,this.lStr="",this.sh=i,this.lvl=e,this._isAnimated=!!i.k;for(var r=0,n=t.length;r<n;){if(t[r].mProps.dynamicProperties.length){this._isAnimated=!0;break}r+=1}}function SVGStyleData(t,e){this.data=t,this.type=t.ty,this.d="",this.lvl=e,this._mdf=!1,this.closed=!0===t.hd,this.pElem=createNS("path"),this.msElem=null}function DashProperty(t,e,i,r){var n;this.elem=t,this.frameId=-1,this.dataProps=createSizedArray(e.length),this.renderer=i,this.k=!1,this.dashStr="",this.dashArray=createTypedArray("float32",e.length?e.length-1:0),this.dashoffset=createTypedArray("float32",1),this.initDynamicPropertyContainer(r);var s,a=e.length||0;for(n=0;n<a;n+=1)s=PropertyFactory.getProp(t,e[n].v,0,0,this),this.k=s.k||this.k,this.dataProps[n]={n:e[n].n,p:s};this.k||this.getValue(!0),this._isAnimated=this.k}function SVGStrokeStyleData(t,e,i){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.o=PropertyFactory.getProp(t,e.o,0,.01,this),this.w=PropertyFactory.getProp(t,e.w,0,null,this),this.d=new DashProperty(t,e.d||{},"svg",this),this.c=PropertyFactory.getProp(t,e.c,1,255,this),this.style=i,this._isAnimated=!!this._isAnimated}function SVGFillStyleData(t,e,i){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.o=PropertyFactory.getProp(t,e.o,0,.01,this),this.c=PropertyFactory.getProp(t,e.c,1,255,this),this.style=i}function SVGNoStyleData(t,e,i){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.style=i}function GradientProperty(t,e,i){this.data=e,this.c=createTypedArray("uint8c",4*e.p);var r=e.k.k[0].s?e.k.k[0].s.length-4*e.p:e.k.k.length-4*e.p;this.o=createTypedArray("float32",r),this._cmdf=!1,this._omdf=!1,this._collapsable=this.checkCollapsable(),this._hasOpacity=r,this.initDynamicPropertyContainer(i),this.prop=PropertyFactory.getProp(t,e.k,1,null,this),this.k=this.prop.k,this.getValue(!0)}function SVGGradientFillStyleData(t,e,i){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.initGradientData(t,e,i)}function SVGGradientStrokeStyleData(t,e,i){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.w=PropertyFactory.getProp(t,e.w,0,null,this),this.d=new DashProperty(t,e.d||{},"svg",this),this.initGradientData(t,e,i),this._isAnimated=!!this._isAnimated}function ShapeGroupData(){this.it=[],this.prevViewData=[],this.gr=createNS("g")}function SVGTransformData(t,e,i){this.transform={mProps:t,op:e,container:i},this.elements=[],this._isAnimated=this.transform.mProps.dynamicProperties.length||this.transform.op.effectsSequence.length}SVGShapeData.prototype.setAsAnimated=function(){this._isAnimated=!0},SVGStyleData.prototype.reset=function(){this.d="",this._mdf=!1},DashProperty.prototype.getValue=function(t){if((this.elem.globalData.frameId!==this.frameId||t)&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf=this._mdf||t,this._mdf)){var e=0,i=this.dataProps.length;for("svg"===this.renderer&&(this.dashStr=""),e=0;e<i;e+=1)"o"!==this.dataProps[e].n?"svg"===this.renderer?this.dashStr+=" "+this.dataProps[e].p.v:this.dashArray[e]=this.dataProps[e].p.v:this.dashoffset[0]=this.dataProps[e].p.v}},extendPrototype([DynamicPropertyContainer],DashProperty),extendPrototype([DynamicPropertyContainer],SVGStrokeStyleData),extendPrototype([DynamicPropertyContainer],SVGFillStyleData),extendPrototype([DynamicPropertyContainer],SVGNoStyleData),GradientProperty.prototype.comparePoints=function(t,e){for(var i=0,r=this.o.length/2;i<r;){if(Math.abs(t[4*i]-t[4*e+2*i])>.01)return!1;i+=1}return!0},GradientProperty.prototype.checkCollapsable=function(){if(this.o.length/2!=this.c.length/4)return!1;if(this.data.k.k[0].s)for(var t=0,e=this.data.k.k.length;t<e;){if(!this.comparePoints(this.data.k.k[t].s,this.data.p))return!1;t+=1}else if(!this.comparePoints(this.data.k.k,this.data.p))return!1;return!0},GradientProperty.prototype.getValue=function(t){if(this.prop.getValue(),this._mdf=!1,this._cmdf=!1,this._omdf=!1,this.prop._mdf||t){var e,i,r,n=4*this.data.p;for(e=0;e<n;e+=1)i=e%4==0?100:255,r=Math.round(this.prop.v[e]*i),this.c[e]!==r&&(this.c[e]=r,this._cmdf=!t);if(this.o.length)for(n=this.prop.v.length,e=4*this.data.p;e<n;e+=1)i=e%2==0?100:1,r=e%2==0?Math.round(100*this.prop.v[e]):this.prop.v[e],this.o[e-4*this.data.p]!==r&&(this.o[e-4*this.data.p]=r,this._omdf=!t);this._mdf=!t}},extendPrototype([DynamicPropertyContainer],GradientProperty),SVGGradientFillStyleData.prototype.initGradientData=function(t,e,i){this.o=PropertyFactory.getProp(t,e.o,0,.01,this),this.s=PropertyFactory.getProp(t,e.s,1,null,this),this.e=PropertyFactory.getProp(t,e.e,1,null,this),this.h=PropertyFactory.getProp(t,e.h||{k:0},0,.01,this),this.a=PropertyFactory.getProp(t,e.a||{k:0},0,degToRads,this),this.g=new GradientProperty(t,e.g,this),this.style=i,this.stops=[],this.setGradientData(i.pElem,e),this.setGradientOpacity(e,i),this._isAnimated=!!this._isAnimated},SVGGradientFillStyleData.prototype.setGradientData=function(t,e){var i=createElementID(),r=createNS(1===e.t?"linearGradient":"radialGradient");r.setAttribute("id",i),r.setAttribute("spreadMethod","pad"),r.setAttribute("gradientUnits","userSpaceOnUse");var n,s,a,o=[];for(a=4*e.g.p,s=0;s<a;s+=4)n=createNS("stop"),r.appendChild(n),o.push(n);t.setAttribute("gf"===e.ty?"fill":"stroke","url("+getLocationHref()+"#"+i+")"),this.gf=r,this.cst=o},SVGGradientFillStyleData.prototype.setGradientOpacity=function(t,e){if(this.g._hasOpacity&&!this.g._collapsable){var i,r,n,s=createNS("mask"),a=createNS("path");s.appendChild(a);var o=createElementID(),h=createElementID();s.setAttribute("id",h);var l=createNS(1===t.t?"linearGradient":"radialGradient");l.setAttribute("id",o),l.setAttribute("spreadMethod","pad"),l.setAttribute("gradientUnits","userSpaceOnUse"),n=t.g.k.k[0].s?t.g.k.k[0].s.length:t.g.k.k.length;var p=this.stops;for(r=4*t.g.p;r<n;r+=2)(i=createNS("stop")).setAttribute("stop-color","rgb(255,255,255)"),l.appendChild(i),p.push(i);a.setAttribute("gf"===t.ty?"fill":"stroke","url("+getLocationHref()+"#"+o+")"),"gs"===t.ty&&(a.setAttribute("stroke-linecap",lineCapEnum[t.lc||2]),a.setAttribute("stroke-linejoin",lineJoinEnum[t.lj||2]),1===t.lj&&a.setAttribute("stroke-miterlimit",t.ml)),this.of=l,this.ms=s,this.ost=p,this.maskId=h,e.msElem=a}},extendPrototype([DynamicPropertyContainer],SVGGradientFillStyleData),extendPrototype([SVGGradientFillStyleData,DynamicPropertyContainer],SVGGradientStrokeStyleData);var buildShapeString=function(t,e,i,r){if(0===e)return"";var n,s=t.o,a=t.i,o=t.v,h=" M"+r.applyToPointStringified(o[0][0],o[0][1]);for(n=1;n<e;n+=1)h+=" C"+r.applyToPointStringified(s[n-1][0],s[n-1][1])+" "+r.applyToPointStringified(a[n][0],a[n][1])+" "+r.applyToPointStringified(o[n][0],o[n][1]);return i&&e&&(h+=" C"+r.applyToPointStringified(s[n-1][0],s[n-1][1])+" "+r.applyToPointStringified(a[0][0],a[0][1])+" "+r.applyToPointStringified(o[0][0],o[0][1]),h+="z"),h},SVGElementsRenderer=function(){var t=new Matrix,e=new Matrix;function i(t,e,i){(i||e.transform.op._mdf)&&e.transform.container.setAttribute("opacity",e.transform.op.v),(i||e.transform.mProps._mdf)&&e.transform.container.setAttribute("transform",e.transform.mProps.v.to2dCSS())}function r(){}function n(i,r,n){var s,a,o,h,l,p,c,d,f,u,m,y=r.styles.length,g=r.lvl;for(p=0;p<y;p+=1){if(h=r.sh._mdf||n,r.styles[p].lvl<g){for(d=e.reset(),u=g-r.styles[p].lvl,m=r.transformers.length-1;!h&&u>0;)h=r.transformers[m].mProps._mdf||h,u-=1,m-=1;if(h)for(u=g-r.styles[p].lvl,m=r.transformers.length-1;u>0;)f=r.transformers[m].mProps.v.props,d.transform(f[0],f[1],f[2],f[3],f[4],f[5],f[6],f[7],f[8],f[9],f[10],f[11],f[12],f[13],f[14],f[15]),u-=1,m-=1}else d=t;if(a=(c=r.sh.paths)._length,h){for(o="",s=0;s<a;s+=1)(l=c.shapes[s])&&l._length&&(o+=buildShapeString(l,l._length,l.c,d));r.caches[p]=o}else o=r.caches[p];r.styles[p].d+=!0===i.hd?"":o,r.styles[p]._mdf=h||r.styles[p]._mdf}}function s(t,e,i){var r=e.style;(e.c._mdf||i)&&r.pElem.setAttribute("fill","rgb("+bmFloor(e.c.v[0])+","+bmFloor(e.c.v[1])+","+bmFloor(e.c.v[2])+")"),(e.o._mdf||i)&&r.pElem.setAttribute("fill-opacity",e.o.v)}function a(t,e,i){o(t,e,i),h(0,e,i)}function o(t,e,i){var r,n,s,a,o,h=e.gf,l=e.g._hasOpacity,p=e.s.v,c=e.e.v;if(e.o._mdf||i){var d="gf"===t.ty?"fill-opacity":"stroke-opacity";e.style.pElem.setAttribute(d,e.o.v)}if(e.s._mdf||i){var f=1===t.t?"x1":"cx",u="x1"===f?"y1":"cy";h.setAttribute(f,p[0]),h.setAttribute(u,p[1]),l&&!e.g._collapsable&&(e.of.setAttribute(f,p[0]),e.of.setAttribute(u,p[1]))}if(e.g._cmdf||i){r=e.cst;var m=e.g.c;for(s=r.length,n=0;n<s;n+=1)(a=r[n]).setAttribute("offset",m[4*n]+"%"),a.setAttribute("stop-color","rgb("+m[4*n+1]+","+m[4*n+2]+","+m[4*n+3]+")")}if(l&&(e.g._omdf||i)){var y=e.g.o;for(s=(r=e.g._collapsable?e.cst:e.ost).length,n=0;n<s;n+=1)a=r[n],e.g._collapsable||a.setAttribute("offset",y[2*n]+"%"),a.setAttribute("stop-opacity",y[2*n+1])}if(1===t.t)(e.e._mdf||i)&&(h.setAttribute("x2",c[0]),h.setAttribute("y2",c[1]),l&&!e.g._collapsable&&(e.of.setAttribute("x2",c[0]),e.of.setAttribute("y2",c[1])));else if((e.s._mdf||e.e._mdf||i)&&(o=Math.sqrt(Math.pow(p[0]-c[0],2)+Math.pow(p[1]-c[1],2)),h.setAttribute("r",o),l&&!e.g._collapsable&&e.of.setAttribute("r",o)),e.e._mdf||e.h._mdf||e.a._mdf||i){o||(o=Math.sqrt(Math.pow(p[0]-c[0],2)+Math.pow(p[1]-c[1],2)));var g=Math.atan2(c[1]-p[1],c[0]-p[0]),v=e.h.v;v>=1?v=.99:v<=-1&&(v=-.99);var b=o*v,_=Math.cos(g+e.a.v)*b+p[0],E=Math.sin(g+e.a.v)*b+p[1];h.setAttribute("fx",_),h.setAttribute("fy",E),l&&!e.g._collapsable&&(e.of.setAttribute("fx",_),e.of.setAttribute("fy",E))}}function h(t,e,i){var r=e.style,n=e.d;n&&(n._mdf||i)&&n.dashStr&&(r.pElem.setAttribute("stroke-dasharray",n.dashStr),r.pElem.setAttribute("stroke-dashoffset",n.dashoffset[0])),e.c&&(e.c._mdf||i)&&r.pElem.setAttribute("stroke","rgb("+bmFloor(e.c.v[0])+","+bmFloor(e.c.v[1])+","+bmFloor(e.c.v[2])+")"),(e.o._mdf||i)&&r.pElem.setAttribute("stroke-opacity",e.o.v),(e.w._mdf||i)&&(r.pElem.setAttribute("stroke-width",e.w.v),r.msElem&&r.msElem.setAttribute("stroke-width",e.w.v))}return{createRenderFunction:function(t){switch(t.ty){case"fl":return s;case"gf":return o;case"gs":return a;case"st":return h;case"sh":case"el":case"rc":case"sr":return n;case"tr":return i;case"no":return r;default:return null}}}}();function SVGShapeElement(t,e,i){this.shapes=[],this.shapesData=t.shapes,this.stylesList=[],this.shapeModifiers=[],this.itemsData=[],this.processedElements=[],this.animatedContents=[],this.initElement(t,e,i),this.prevViewData=[]}function LetterProps(t,e,i,r,n,s){this.o=t,this.sw=e,this.sc=i,this.fc=r,this.m=n,this.p=s,this._mdf={o:!0,sw:!!e,sc:!!i,fc:!!r,m:!0,p:!0}}function TextProperty(t,e){this._frameId=initialDefaultFrame,this.pv="",this.v="",this.kf=!1,this._isFirstFrame=!0,this._mdf=!1,this.data=e,this.elem=t,this.comp=this.elem.comp,this.keysIndex=0,this.canResize=!1,this.minimumFontSize=1,this.effectsSequence=[],this.currentData={ascent:0,boxWidth:this.defaultBoxWidth,f:"",fStyle:"",fWeight:"",fc:"",j:"",justifyOffset:"",l:[],lh:0,lineWidths:[],ls:"",of:"",s:"",sc:"",sw:0,t:0,tr:0,sz:0,ps:null,fillColorAnim:!1,strokeColorAnim:!1,strokeWidthAnim:!1,yOffset:0,finalSize:0,finalText:[],finalLineHeight:0,__complete:!1},this.copyData(this.currentData,this.data.d.k[0].s),this.searchProperty()||this.completeTextData(this.currentData)}extendPrototype([BaseElement,TransformElement,SVGBaseElement,IShapeElement,HierarchyElement,FrameElement,RenderableDOMElement],SVGShapeElement),SVGShapeElement.prototype.initSecondaryElement=function(){},SVGShapeElement.prototype.identityMatrix=new Matrix,SVGShapeElement.prototype.buildExpressionInterface=function(){},SVGShapeElement.prototype.createContent=function(){this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,this.layerElement,0,[],!0),this.filterUniqueShapes()},SVGShapeElement.prototype.filterUniqueShapes=function(){var t,e,i,r,n=this.shapes.length,s=this.stylesList.length,a=[],o=!1;for(i=0;i<s;i+=1){for(r=this.stylesList[i],o=!1,a.length=0,t=0;t<n;t+=1)-1!==(e=this.shapes[t]).styles.indexOf(r)&&(a.push(e),o=e._isAnimated||o);a.length>1&&o&&this.setShapesAsAnimated(a)}},SVGShapeElement.prototype.setShapesAsAnimated=function(t){var e,i=t.length;for(e=0;e<i;e+=1)t[e].setAsAnimated()},SVGShapeElement.prototype.createStyleElement=function(t,e){var i,r=new SVGStyleData(t,e),n=r.pElem;return"st"===t.ty?i=new SVGStrokeStyleData(this,t,r):"fl"===t.ty?i=new SVGFillStyleData(this,t,r):"gf"===t.ty||"gs"===t.ty?(i=new("gf"===t.ty?SVGGradientFillStyleData:SVGGradientStrokeStyleData)(this,t,r),this.globalData.defs.appendChild(i.gf),i.maskId&&(this.globalData.defs.appendChild(i.ms),this.globalData.defs.appendChild(i.of),n.setAttribute("mask","url("+getLocationHref()+"#"+i.maskId+")"))):"no"===t.ty&&(i=new SVGNoStyleData(this,t,r)),"st"!==t.ty&&"gs"!==t.ty||(n.setAttribute("stroke-linecap",lineCapEnum[t.lc||2]),n.setAttribute("stroke-linejoin",lineJoinEnum[t.lj||2]),n.setAttribute("fill-opacity","0"),1===t.lj&&n.setAttribute("stroke-miterlimit",t.ml)),2===t.r&&n.setAttribute("fill-rule","evenodd"),t.ln&&n.setAttribute("id",t.ln),t.cl&&n.setAttribute("class",t.cl),t.bm&&(n.style["mix-blend-mode"]=getBlendMode(t.bm)),this.stylesList.push(r),this.addToAnimatedContents(t,i),i},SVGShapeElement.prototype.createGroupElement=function(t){var e=new ShapeGroupData;return t.ln&&e.gr.setAttribute("id",t.ln),t.cl&&e.gr.setAttribute("class",t.cl),t.bm&&(e.gr.style["mix-blend-mode"]=getBlendMode(t.bm)),e},SVGShapeElement.prototype.createTransformElement=function(t,e){var i=TransformPropertyFactory.getTransformProperty(this,t,this),r=new SVGTransformData(i,i.o,e);return this.addToAnimatedContents(t,r),r},SVGShapeElement.prototype.createShapeElement=function(t,e,i){var r=4;"rc"===t.ty?r=5:"el"===t.ty?r=6:"sr"===t.ty&&(r=7);var n=new SVGShapeData(e,i,ShapePropertyFactory.getShapeProp(this,t,r,this));return this.shapes.push(n),this.addShapeToModifiers(n),this.addToAnimatedContents(t,n),n},SVGShapeElement.prototype.addToAnimatedContents=function(t,e){for(var i=0,r=this.animatedContents.length;i<r;){if(this.animatedContents[i].element===e)return;i+=1}this.animatedContents.push({fn:SVGElementsRenderer.createRenderFunction(t),element:e,data:t})},SVGShapeElement.prototype.setElementStyles=function(t){var e,i=t.styles,r=this.stylesList.length;for(e=0;e<r;e+=1)this.stylesList[e].closed||i.push(this.stylesList[e])},SVGShapeElement.prototype.reloadShapes=function(){var t;this._isFirstFrame=!0;var e=this.itemsData.length;for(t=0;t<e;t+=1)this.prevViewData[t]=this.itemsData[t];for(this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,this.layerElement,0,[],!0),this.filterUniqueShapes(),e=this.dynamicProperties.length,t=0;t<e;t+=1)this.dynamicProperties[t].getValue();this.renderModifiers()},SVGShapeElement.prototype.searchShapes=function(t,e,i,r,n,s,a){var o,h,l,p,c,d,f=[].concat(s),u=t.length-1,m=[],y=[];for(o=u;o>=0;o-=1){if((d=this.searchProcessedElement(t[o]))?e[o]=i[d-1]:t[o]._render=a,"fl"===t[o].ty||"st"===t[o].ty||"gf"===t[o].ty||"gs"===t[o].ty||"no"===t[o].ty)d?e[o].style.closed=!1:e[o]=this.createStyleElement(t[o],n),t[o]._render&&e[o].style.pElem.parentNode!==r&&r.appendChild(e[o].style.pElem),m.push(e[o].style);else if("gr"===t[o].ty){if(d)for(l=e[o].it.length,h=0;h<l;h+=1)e[o].prevViewData[h]=e[o].it[h];else e[o]=this.createGroupElement(t[o]);this.searchShapes(t[o].it,e[o].it,e[o].prevViewData,e[o].gr,n+1,f,a),t[o]._render&&e[o].gr.parentNode!==r&&r.appendChild(e[o].gr)}else"tr"===t[o].ty?(d||(e[o]=this.createTransformElement(t[o],r)),p=e[o].transform,f.push(p)):"sh"===t[o].ty||"rc"===t[o].ty||"el"===t[o].ty||"sr"===t[o].ty?(d||(e[o]=this.createShapeElement(t[o],f,n)),this.setElementStyles(e[o])):"tm"===t[o].ty||"rd"===t[o].ty||"ms"===t[o].ty||"pb"===t[o].ty||"zz"===t[o].ty||"op"===t[o].ty?(d?(c=e[o]).closed=!1:((c=ShapeModifiers.getModifier(t[o].ty)).init(this,t[o]),e[o]=c,this.shapeModifiers.push(c)),y.push(c)):"rp"===t[o].ty&&(d?(c=e[o]).closed=!0:(c=ShapeModifiers.getModifier(t[o].ty),e[o]=c,c.init(this,t,o,e),this.shapeModifiers.push(c),a=!1),y.push(c));this.addProcessedElement(t[o],o+1)}for(u=m.length,o=0;o<u;o+=1)m[o].closed=!0;for(u=y.length,o=0;o<u;o+=1)y[o].closed=!0},SVGShapeElement.prototype.renderInnerContent=function(){var t;this.renderModifiers();var e=this.stylesList.length;for(t=0;t<e;t+=1)this.stylesList[t].reset();for(this.renderShape(),t=0;t<e;t+=1)(this.stylesList[t]._mdf||this._isFirstFrame)&&(this.stylesList[t].msElem&&(this.stylesList[t].msElem.setAttribute("d",this.stylesList[t].d),this.stylesList[t].d="M0 0"+this.stylesList[t].d),this.stylesList[t].pElem.setAttribute("d",this.stylesList[t].d||"M0 0"))},SVGShapeElement.prototype.renderShape=function(){var t,e,i=this.animatedContents.length;for(t=0;t<i;t+=1)e=this.animatedContents[t],(this._isFirstFrame||e.element._isAnimated)&&!0!==e.data&&e.fn(e.data,e.element,this._isFirstFrame)},SVGShapeElement.prototype.destroy=function(){this.destroyBaseElement(),this.shapesData=null,this.itemsData=null},LetterProps.prototype.update=function(t,e,i,r,n,s){this._mdf.o=!1,this._mdf.sw=!1,this._mdf.sc=!1,this._mdf.fc=!1,this._mdf.m=!1,this._mdf.p=!1;var a=!1;return this.o!==t&&(this.o=t,this._mdf.o=!0,a=!0),this.sw!==e&&(this.sw=e,this._mdf.sw=!0,a=!0),this.sc!==i&&(this.sc=i,this._mdf.sc=!0,a=!0),this.fc!==r&&(this.fc=r,this._mdf.fc=!0,a=!0),this.m!==n&&(this.m=n,this._mdf.m=!0,a=!0),!s.length||this.p[0]===s[0]&&this.p[1]===s[1]&&this.p[4]===s[4]&&this.p[5]===s[5]&&this.p[12]===s[12]&&this.p[13]===s[13]||(this.p=s,this._mdf.p=!0,a=!0),a},TextProperty.prototype.defaultBoxWidth=[0,0],TextProperty.prototype.copyData=function(t,e){for(var i in e)Object.prototype.hasOwnProperty.call(e,i)&&(t[i]=e[i]);return t},TextProperty.prototype.setCurrentData=function(t){t.__complete||this.completeTextData(t),this.currentData=t,this.currentData.boxWidth=this.currentData.boxWidth||this.defaultBoxWidth,this._mdf=!0},TextProperty.prototype.searchProperty=function(){return this.searchKeyframes()},TextProperty.prototype.searchKeyframes=function(){return this.kf=this.data.d.k.length>1,this.kf&&this.addEffect(this.getKeyframeValue.bind(this)),this.kf},TextProperty.prototype.addEffect=function(t){this.effectsSequence.push(t),this.elem.addDynamicProperty(this)},TextProperty.prototype.getValue=function(t){if(this.elem.globalData.frameId!==this.frameId&&this.effectsSequence.length||t){this.currentData.t=this.data.d.k[this.keysIndex].s.t;var e=this.currentData,i=this.keysIndex;if(this.lock)this.setCurrentData(this.currentData);else{var r;this.lock=!0,this._mdf=!1;var n=this.effectsSequence.length,s=t||this.data.d.k[this.keysIndex].s;for(r=0;r<n;r+=1)s=i!==this.keysIndex?this.effectsSequence[r](s,s.t):this.effectsSequence[r](this.currentData,s.t);e!==s&&this.setCurrentData(s),this.v=this.currentData,this.pv=this.v,this.lock=!1,this.frameId=this.elem.globalData.frameId}}},TextProperty.prototype.getKeyframeValue=function(){for(var t=this.data.d.k,e=this.elem.comp.renderedFrame,i=0,r=t.length;i<=r-1&&!(i===r-1||t[i+1].t>e);)i+=1;return this.keysIndex!==i&&(this.keysIndex=i),this.data.d.k[this.keysIndex].s},TextProperty.prototype.buildFinalText=function(t){for(var e,i,r=[],n=0,s=t.length,a=!1;n<s;)e=t.charCodeAt(n),FontManager.isCombinedCharacter(e)?r[r.length-1]+=t.charAt(n):e>=55296&&e<=56319?(i=t.charCodeAt(n+1))>=56320&&i<=57343?(a||FontManager.isModifier(e,i)?(r[r.length-1]+=t.substr(n,2),a=!1):r.push(t.substr(n,2)),n+=1):r.push(t.charAt(n)):e>56319?(i=t.charCodeAt(n+1),FontManager.isZeroWidthJoiner(e,i)?(a=!0,r[r.length-1]+=t.substr(n,2),n+=1):r.push(t.charAt(n))):FontManager.isZeroWidthJoiner(e)?(r[r.length-1]+=t.charAt(n),a=!0):r.push(t.charAt(n)),n+=1;return r},TextProperty.prototype.completeTextData=function(t){t.__complete=!0;var e,i,r,n,s,a,o,h=this.elem.globalData.fontManager,l=this.data,p=[],c=0,d=l.m.g,f=0,u=0,m=0,y=[],g=0,v=0,b=h.getFontByName(t.f),_=0,E=getFontProperties(b);t.fWeight=E.weight,t.fStyle=E.style,t.finalSize=t.s,t.finalText=this.buildFinalText(t.t),i=t.finalText.length,t.finalLineHeight=t.lh;var S,P=t.tr/1e3*t.finalSize;if(t.sz)for(var x,w,C=!0,A=t.sz[0],k=t.sz[1];C;){x=0,g=0,i=(w=this.buildFinalText(t.t)).length,P=t.tr/1e3*t.finalSize;var D=-1;for(e=0;e<i;e+=1)S=w[e].charCodeAt(0),r=!1," "===w[e]?D=e:13!==S&&3!==S||(g=0,r=!0,x+=t.finalLineHeight||1.2*t.finalSize),h.chars?(o=h.getCharData(w[e],b.fStyle,b.fFamily),_=r?0:o.w*t.finalSize/100):_=h.measureText(w[e],t.f,t.finalSize),g+_>A&&" "!==w[e]?(-1===D?i+=1:e=D,x+=t.finalLineHeight||1.2*t.finalSize,w.splice(e,D===e?1:0,"\r"),D=-1,g=0):(g+=_,g+=P);x+=b.ascent*t.finalSize/100,this.canResize&&t.finalSize>this.minimumFontSize&&k<x?(t.finalSize-=1,t.finalLineHeight=t.finalSize*t.lh/t.s):(t.finalText=w,i=t.finalText.length,C=!1)}g=-P,_=0;var T,M=0;for(e=0;e<i;e+=1)if(r=!1,13===(S=(T=t.finalText[e]).charCodeAt(0))||3===S?(M=0,y.push(g),v=g>v?g:v,g=-2*P,n="",r=!0,m+=1):n=T,h.chars?(o=h.getCharData(T,b.fStyle,h.getFontByName(t.f).fFamily),_=r?0:o.w*t.finalSize/100):_=h.measureText(n,t.f,t.finalSize)," "===T?M+=_+P:(g+=_+P+M,M=0),p.push({l:_,an:_,add:f,n:r,anIndexes:[],val:n,line:m,animatorJustifyOffset:0}),2==d){if(f+=_,""===n||" "===n||e===i-1){for(""!==n&&" "!==n||(f-=_);u<=e;)p[u].an=f,p[u].ind=c,p[u].extra=_,u+=1;c+=1,f=0}}else if(3==d){if(f+=_,""===n||e===i-1){for(""===n&&(f-=_);u<=e;)p[u].an=f,p[u].ind=c,p[u].extra=_,u+=1;f=0,c+=1}}else p[c].ind=c,p[c].extra=0,c+=1;if(t.l=p,v=g>v?g:v,y.push(g),t.sz)t.boxWidth=t.sz[0],t.justifyOffset=0;else switch(t.boxWidth=v,t.j){case 1:t.justifyOffset=-t.boxWidth;break;case 2:t.justifyOffset=-t.boxWidth/2;break;default:t.justifyOffset=0}t.lineWidths=y;var I,F,L,R,B=l.a;a=B.length;var O=[];for(s=0;s<a;s+=1){for((I=B[s]).a.sc&&(t.strokeColorAnim=!0),I.a.sw&&(t.strokeWidthAnim=!0),(I.a.fc||I.a.fh||I.a.fs||I.a.fb)&&(t.fillColorAnim=!0),R=0,L=I.s.b,e=0;e<i;e+=1)(F=p[e]).anIndexes[s]=R,(1==L&&""!==F.val||2==L&&""!==F.val&&" "!==F.val||3==L&&(F.n||" "==F.val||e==i-1)||4==L&&(F.n||e==i-1))&&(1===I.s.rn&&O.push(R),R+=1);l.a[s].s.totalChars=R;var $,V=-1;if(1===I.s.rn)for(e=0;e<i;e+=1)V!=(F=p[e]).anIndexes[s]&&(V=F.anIndexes[s],$=O.splice(Math.floor(Math.random()*O.length),1)[0]),F.anIndexes[s]=$}t.yOffset=t.finalLineHeight||1.2*t.finalSize,t.ls=t.ls||0,t.ascent=b.ascent*t.finalSize/100},TextProperty.prototype.updateDocumentData=function(t,e){e=void 0===e?this.keysIndex:e;var i=this.copyData({},this.data.d.k[e].s);i=this.copyData(i,t),this.data.d.k[e].s=i,this.recalculate(e),this.elem.addDynamicProperty(this)},TextProperty.prototype.recalculate=function(t){var e=this.data.d.k[t].s;e.__complete=!1,this.keysIndex=0,this._isFirstFrame=!0,this.getValue(e)},TextProperty.prototype.canResizeFont=function(t){this.canResize=t,this.recalculate(this.keysIndex),this.elem.addDynamicProperty(this)},TextProperty.prototype.setMinimumFontSize=function(t){this.minimumFontSize=Math.floor(t)||1,this.recalculate(this.keysIndex),this.elem.addDynamicProperty(this)};var TextSelectorProp=function(){var t=Math.max,e=Math.min,i=Math.floor;function r(t,e){this._currentTextLength=-1,this.k=!1,this.data=e,this.elem=t,this.comp=t.comp,this.finalS=0,this.finalE=0,this.initDynamicPropertyContainer(t),this.s=PropertyFactory.getProp(t,e.s||{k:0},0,0,this),this.e="e"in e?PropertyFactory.getProp(t,e.e,0,0,this):{v:100},this.o=PropertyFactory.getProp(t,e.o||{k:0},0,0,this),this.xe=PropertyFactory.getProp(t,e.xe||{k:0},0,0,this),this.ne=PropertyFactory.getProp(t,e.ne||{k:0},0,0,this),this.sm=PropertyFactory.getProp(t,e.sm||{k:100},0,0,this),this.a=PropertyFactory.getProp(t,e.a,0,.01,this),this.dynamicProperties.length||this.getValue()}return r.prototype={getMult:function(r){this._currentTextLength!==this.elem.textProperty.currentData.l.length&&this.getValue();var n=0,s=0,a=1,o=1;this.ne.v>0?n=this.ne.v/100:s=-this.ne.v/100,this.xe.v>0?a=1-this.xe.v/100:o=1+this.xe.v/100;var h=BezierFactory.getBezierEasing(n,s,a,o).get,l=0,p=this.finalS,c=this.finalE,d=this.data.sh;if(2===d)l=h(l=c===p?r>=c?1:0:t(0,e(.5/(c-p)+(r-p)/(c-p),1)));else if(3===d)l=h(l=c===p?r>=c?0:1:1-t(0,e(.5/(c-p)+(r-p)/(c-p),1)));else if(4===d)c===p?l=0:(l=t(0,e(.5/(c-p)+(r-p)/(c-p),1)))<.5?l*=2:l=1-2*(l-.5),l=h(l);else if(5===d){if(c===p)l=0;else{var f=c-p,u=-f/2+(r=e(t(0,r+.5-p),c-p)),m=f/2;l=Math.sqrt(1-u*u/(m*m))}l=h(l)}else 6===d?(c===p?l=0:(r=e(t(0,r+.5-p),c-p),l=(1+Math.cos(Math.PI+2*Math.PI*r/(c-p)))/2),l=h(l)):(r>=i(p)&&(l=t(0,e(r-p<0?e(c,1)-(p-r):c-r,1))),l=h(l));if(100!==this.sm.v){var y=.01*this.sm.v;0===y&&(y=1e-8);var g=.5-.5*y;l<g?l=0:(l=(l-g)/y)>1&&(l=1)}return l*this.a.v},getValue:function(t){this.iterateDynamicProperties(),this._mdf=t||this._mdf,this._currentTextLength=this.elem.textProperty.currentData.l.length||0,t&&2===this.data.r&&(this.e.v=this._currentTextLength);var e=2===this.data.r?1:100/this.data.totalChars,i=this.o.v/e,r=this.s.v/e+i,n=this.e.v/e+i;if(r>n){var s=r;r=n,n=s}this.finalS=r,this.finalE=n}},extendPrototype([DynamicPropertyContainer],r),{getTextSelectorProp:function(t,e,i){return new r(t,e,i)}}}();function TextAnimatorDataProperty(t,e,i){var r={propType:!1},n=PropertyFactory.getProp,s=e.a;this.a={r:s.r?n(t,s.r,0,degToRads,i):r,rx:s.rx?n(t,s.rx,0,degToRads,i):r,ry:s.ry?n(t,s.ry,0,degToRads,i):r,sk:s.sk?n(t,s.sk,0,degToRads,i):r,sa:s.sa?n(t,s.sa,0,degToRads,i):r,s:s.s?n(t,s.s,1,.01,i):r,a:s.a?n(t,s.a,1,0,i):r,o:s.o?n(t,s.o,0,.01,i):r,p:s.p?n(t,s.p,1,0,i):r,sw:s.sw?n(t,s.sw,0,0,i):r,sc:s.sc?n(t,s.sc,1,0,i):r,fc:s.fc?n(t,s.fc,1,0,i):r,fh:s.fh?n(t,s.fh,0,0,i):r,fs:s.fs?n(t,s.fs,0,.01,i):r,fb:s.fb?n(t,s.fb,0,.01,i):r,t:s.t?n(t,s.t,0,0,i):r},this.s=TextSelectorProp.getTextSelectorProp(t,e.s,i),this.s.t=e.s.t}function TextAnimatorProperty(t,e,i){this._isFirstFrame=!0,this._hasMaskedPath=!1,this._frameId=-1,this._textData=t,this._renderType=e,this._elem=i,this._animatorsData=createSizedArray(this._textData.a.length),this._pathData={},this._moreOptions={alignment:{}},this.renderedLetters=[],this.lettersChangedFlag=!1,this.initDynamicPropertyContainer(i)}function ITextElement(){}TextAnimatorProperty.prototype.searchProperties=function(){var t,e,i=this._textData.a.length,r=PropertyFactory.getProp;for(t=0;t<i;t+=1)e=this._textData.a[t],this._animatorsData[t]=new TextAnimatorDataProperty(this._elem,e,this);this._textData.p&&"m"in this._textData.p?(this._pathData={a:r(this._elem,this._textData.p.a,0,0,this),f:r(this._elem,this._textData.p.f,0,0,this),l:r(this._elem,this._textData.p.l,0,0,this),r:r(this._elem,this._textData.p.r,0,0,this),p:r(this._elem,this._textData.p.p,0,0,this),m:this._elem.maskManager.getMaskProperty(this._textData.p.m)},this._hasMaskedPath=!0):this._hasMaskedPath=!1,this._moreOptions.alignment=r(this._elem,this._textData.m.a,1,0,this)},TextAnimatorProperty.prototype.getMeasures=function(t,e){if(this.lettersChangedFlag=e,this._mdf||this._isFirstFrame||e||this._hasMaskedPath&&this._pathData.m._mdf){this._isFirstFrame=!1;var i,r,n,s,a,o,h,l,p,c,d,f,u,m,y,g,v,b,_,E=this._moreOptions.alignment.v,S=this._animatorsData,P=this._textData,x=this.mHelper,w=this._renderType,C=this.renderedLetters.length,A=t.l;if(this._hasMaskedPath){if(_=this._pathData.m,!this._pathData.n||this._pathData._mdf){var k,D=_.v;for(this._pathData.r.v&&(D=D.reverse()),a={tLength:0,segments:[]},s=D._length-1,g=0,n=0;n<s;n+=1)k=bez.buildBezierData(D.v[n],D.v[n+1],[D.o[n][0]-D.v[n][0],D.o[n][1]-D.v[n][1]],[D.i[n+1][0]-D.v[n+1][0],D.i[n+1][1]-D.v[n+1][1]]),a.tLength+=k.segmentLength,a.segments.push(k),g+=k.segmentLength;n=s,_.v.c&&(k=bez.buildBezierData(D.v[n],D.v[0],[D.o[n][0]-D.v[n][0],D.o[n][1]-D.v[n][1]],[D.i[0][0]-D.v[0][0],D.i[0][1]-D.v[0][1]]),a.tLength+=k.segmentLength,a.segments.push(k),g+=k.segmentLength),this._pathData.pi=a}if(a=this._pathData.pi,o=this._pathData.f.v,d=0,c=1,l=0,p=!0,m=a.segments,o<0&&_.v.c)for(a.tLength<Math.abs(o)&&(o=-Math.abs(o)%a.tLength),c=(u=m[d=m.length-1].points).length-1;o<0;)o+=u[c].partialLength,(c-=1)<0&&(c=(u=m[d-=1].points).length-1);f=(u=m[d].points)[c-1],y=(h=u[c]).partialLength}s=A.length,i=0,r=0;var T,M,I,F,L,R=1.2*t.finalSize*.714,B=!0;I=S.length;var O,$,V,z,N,G,j,H,W,q,U,X,Y=-1,Z=o,K=d,J=c,Q=-1,tt="",et=this.defaultPropsArray;if(2===t.j||1===t.j){var it=0,rt=0,nt=2===t.j?-.5:-1,st=0,at=!0;for(n=0;n<s;n+=1)if(A[n].n){for(it&&(it+=rt);st<n;)A[st].animatorJustifyOffset=it,st+=1;it=0,at=!0}else{for(M=0;M<I;M+=1)(T=S[M].a).t.propType&&(at&&2===t.j&&(rt+=T.t.v*nt),(L=S[M].s.getMult(A[n].anIndexes[M],P.a[M].s.totalChars)).length?it+=T.t.v*L[0]*nt:it+=T.t.v*L*nt);at=!1}for(it&&(it+=rt);st<n;)A[st].animatorJustifyOffset=it,st+=1}for(n=0;n<s;n+=1){if(x.reset(),z=1,A[n].n)i=0,r+=t.yOffset,r+=B?1:0,o=Z,B=!1,this._hasMaskedPath&&(c=J,f=(u=m[d=K].points)[c-1],y=(h=u[c]).partialLength,l=0),tt="",U="",W="",X="",et=this.defaultPropsArray;else{if(this._hasMaskedPath){if(Q!==A[n].line){switch(t.j){case 1:o+=g-t.lineWidths[A[n].line];break;case 2:o+=(g-t.lineWidths[A[n].line])/2}Q=A[n].line}Y!==A[n].ind&&(A[Y]&&(o+=A[Y].extra),o+=A[n].an/2,Y=A[n].ind),o+=E[0]*A[n].an*.005;var ot=0;for(M=0;M<I;M+=1)(T=S[M].a).p.propType&&((L=S[M].s.getMult(A[n].anIndexes[M],P.a[M].s.totalChars)).length?ot+=T.p.v[0]*L[0]:ot+=T.p.v[0]*L),T.a.propType&&((L=S[M].s.getMult(A[n].anIndexes[M],P.a[M].s.totalChars)).length?ot+=T.a.v[0]*L[0]:ot+=T.a.v[0]*L);for(p=!0,this._pathData.a.v&&(o=.5*A[0].an+(g-this._pathData.f.v-.5*A[0].an-.5*A[A.length-1].an)*Y/(s-1),o+=this._pathData.f.v);p;)l+y>=o+ot||!u?(v=(o+ot-l)/h.partialLength,$=f.point[0]+(h.point[0]-f.point[0])*v,V=f.point[1]+(h.point[1]-f.point[1])*v,x.translate(-E[0]*A[n].an*.005,-E[1]*R*.01),p=!1):u&&(l+=h.partialLength,(c+=1)>=u.length&&(c=0,m[d+=1]?u=m[d].points:_.v.c?(c=0,u=m[d=0].points):(l-=h.partialLength,u=null)),u&&(f=h,y=(h=u[c]).partialLength));O=A[n].an/2-A[n].add,x.translate(-O,0,0)}else O=A[n].an/2-A[n].add,x.translate(-O,0,0),x.translate(-E[0]*A[n].an*.005,-E[1]*R*.01,0);for(M=0;M<I;M+=1)(T=S[M].a).t.propType&&(L=S[M].s.getMult(A[n].anIndexes[M],P.a[M].s.totalChars),0===i&&0===t.j||(this._hasMaskedPath?L.length?o+=T.t.v*L[0]:o+=T.t.v*L:L.length?i+=T.t.v*L[0]:i+=T.t.v*L));for(t.strokeWidthAnim&&(G=t.sw||0),t.strokeColorAnim&&(N=t.sc?[t.sc[0],t.sc[1],t.sc[2]]:[0,0,0]),t.fillColorAnim&&t.fc&&(j=[t.fc[0],t.fc[1],t.fc[2]]),M=0;M<I;M+=1)(T=S[M].a).a.propType&&((L=S[M].s.getMult(A[n].anIndexes[M],P.a[M].s.totalChars)).length?x.translate(-T.a.v[0]*L[0],-T.a.v[1]*L[1],T.a.v[2]*L[2]):x.translate(-T.a.v[0]*L,-T.a.v[1]*L,T.a.v[2]*L));for(M=0;M<I;M+=1)(T=S[M].a).s.propType&&((L=S[M].s.getMult(A[n].anIndexes[M],P.a[M].s.totalChars)).length?x.scale(1+(T.s.v[0]-1)*L[0],1+(T.s.v[1]-1)*L[1],1):x.scale(1+(T.s.v[0]-1)*L,1+(T.s.v[1]-1)*L,1));for(M=0;M<I;M+=1){if(T=S[M].a,L=S[M].s.getMult(A[n].anIndexes[M],P.a[M].s.totalChars),T.sk.propType&&(L.length?x.skewFromAxis(-T.sk.v*L[0],T.sa.v*L[1]):x.skewFromAxis(-T.sk.v*L,T.sa.v*L)),T.r.propType&&(L.length?x.rotateZ(-T.r.v*L[2]):x.rotateZ(-T.r.v*L)),T.ry.propType&&(L.length?x.rotateY(T.ry.v*L[1]):x.rotateY(T.ry.v*L)),T.rx.propType&&(L.length?x.rotateX(T.rx.v*L[0]):x.rotateX(T.rx.v*L)),T.o.propType&&(L.length?z+=(T.o.v*L[0]-z)*L[0]:z+=(T.o.v*L-z)*L),t.strokeWidthAnim&&T.sw.propType&&(L.length?G+=T.sw.v*L[0]:G+=T.sw.v*L),t.strokeColorAnim&&T.sc.propType)for(H=0;H<3;H+=1)L.length?N[H]+=(T.sc.v[H]-N[H])*L[0]:N[H]+=(T.sc.v[H]-N[H])*L;if(t.fillColorAnim&&t.fc){if(T.fc.propType)for(H=0;H<3;H+=1)L.length?j[H]+=(T.fc.v[H]-j[H])*L[0]:j[H]+=(T.fc.v[H]-j[H])*L;T.fh.propType&&(j=L.length?addHueToRGB(j,T.fh.v*L[0]):addHueToRGB(j,T.fh.v*L)),T.fs.propType&&(j=L.length?addSaturationToRGB(j,T.fs.v*L[0]):addSaturationToRGB(j,T.fs.v*L)),T.fb.propType&&(j=L.length?addBrightnessToRGB(j,T.fb.v*L[0]):addBrightnessToRGB(j,T.fb.v*L))}}for(M=0;M<I;M+=1)(T=S[M].a).p.propType&&(L=S[M].s.getMult(A[n].anIndexes[M],P.a[M].s.totalChars),this._hasMaskedPath?L.length?x.translate(0,T.p.v[1]*L[0],-T.p.v[2]*L[1]):x.translate(0,T.p.v[1]*L,-T.p.v[2]*L):L.length?x.translate(T.p.v[0]*L[0],T.p.v[1]*L[1],-T.p.v[2]*L[2]):x.translate(T.p.v[0]*L,T.p.v[1]*L,-T.p.v[2]*L));if(t.strokeWidthAnim&&(W=G<0?0:G),t.strokeColorAnim&&(q="rgb("+Math.round(255*N[0])+","+Math.round(255*N[1])+","+Math.round(255*N[2])+")"),t.fillColorAnim&&t.fc&&(U="rgb("+Math.round(255*j[0])+","+Math.round(255*j[1])+","+Math.round(255*j[2])+")"),this._hasMaskedPath){if(x.translate(0,-t.ls),x.translate(0,E[1]*R*.01+r,0),this._pathData.p.v){b=(h.point[1]-f.point[1])/(h.point[0]-f.point[0]);var ht=180*Math.atan(b)/Math.PI;h.point[0]<f.point[0]&&(ht+=180),x.rotate(-ht*Math.PI/180)}x.translate($,V,0),o-=E[0]*A[n].an*.005,A[n+1]&&Y!==A[n+1].ind&&(o+=A[n].an/2,o+=.001*t.tr*t.finalSize)}else{switch(x.translate(i,r,0),t.ps&&x.translate(t.ps[0],t.ps[1]+t.ascent,0),t.j){case 1:x.translate(A[n].animatorJustifyOffset+t.justifyOffset+(t.boxWidth-t.lineWidths[A[n].line]),0,0);break;case 2:x.translate(A[n].animatorJustifyOffset+t.justifyOffset+(t.boxWidth-t.lineWidths[A[n].line])/2,0,0)}x.translate(0,-t.ls),x.translate(O,0,0),x.translate(E[0]*A[n].an*.005,E[1]*R*.01,0),i+=A[n].l+.001*t.tr*t.finalSize}"html"===w?tt=x.toCSS():"svg"===w?tt=x.to2dCSS():et=[x.props[0],x.props[1],x.props[2],x.props[3],x.props[4],x.props[5],x.props[6],x.props[7],x.props[8],x.props[9],x.props[10],x.props[11],x.props[12],x.props[13],x.props[14],x.props[15]],X=z}C<=n?(F=new LetterProps(X,W,q,U,tt,et),this.renderedLetters.push(F),C+=1,this.lettersChangedFlag=!0):(F=this.renderedLetters[n],this.lettersChangedFlag=F.update(X,W,q,U,tt,et)||this.lettersChangedFlag)}}},TextAnimatorProperty.prototype.getValue=function(){this._elem.globalData.frameId!==this._frameId&&(this._frameId=this._elem.globalData.frameId,this.iterateDynamicProperties())},TextAnimatorProperty.prototype.mHelper=new Matrix,TextAnimatorProperty.prototype.defaultPropsArray=[],extendPrototype([DynamicPropertyContainer],TextAnimatorProperty),ITextElement.prototype.initElement=function(t,e,i){this.lettersChangedFlag=!0,this.initFrame(),this.initBaseData(t,e,i),this.textProperty=new TextProperty(this,t.t,this.dynamicProperties),this.textAnimator=new TextAnimatorProperty(t.t,this.renderType,this),this.initTransform(t,e,i),this.initHierarchy(),this.initRenderable(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),this.createContent(),this.hide(),this.textAnimator.searchProperties(this.dynamicProperties)},ITextElement.prototype.prepareFrame=function(t){this._mdf=!1,this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange),(this.textProperty._mdf||this.textProperty._isFirstFrame)&&(this.buildNewText(),this.textProperty._isFirstFrame=!1,this.textProperty._mdf=!1)},ITextElement.prototype.createPathShape=function(t,e){var i,r,n=e.length,s="";for(i=0;i<n;i+=1)"sh"===e[i].ty&&(r=e[i].ks.k,s+=buildShapeString(r,r.i.length,!0,t));return s},ITextElement.prototype.updateDocumentData=function(t,e){this.textProperty.updateDocumentData(t,e)},ITextElement.prototype.canResizeFont=function(t){this.textProperty.canResizeFont(t)},ITextElement.prototype.setMinimumFontSize=function(t){this.textProperty.setMinimumFontSize(t)},ITextElement.prototype.applyTextPropertiesToMatrix=function(t,e,i,r,n){switch(t.ps&&e.translate(t.ps[0],t.ps[1]+t.ascent,0),e.translate(0,-t.ls,0),t.j){case 1:e.translate(t.justifyOffset+(t.boxWidth-t.lineWidths[i]),0,0);break;case 2:e.translate(t.justifyOffset+(t.boxWidth-t.lineWidths[i])/2,0,0)}e.translate(r,n,0)},ITextElement.prototype.buildColor=function(t){return"rgb("+Math.round(255*t[0])+","+Math.round(255*t[1])+","+Math.round(255*t[2])+")"},ITextElement.prototype.emptyProp=new LetterProps,ITextElement.prototype.destroy=function(){};var emptyShapeData={shapes:[]};function SVGTextLottieElement(t,e,i){this.textSpans=[],this.renderType="svg",this.initElement(t,e,i)}function ISolidElement(t,e,i){this.initElement(t,e,i)}function NullElement(t,e,i){this.initFrame(),this.initBaseData(t,e,i),this.initFrame(),this.initTransform(t,e,i),this.initHierarchy()}function SVGRendererBase(){}function ICompElement(){}function SVGCompElement(t,e,i){this.layers=t.layers,this.supports3d=!0,this.completeLayers=!1,this.pendingElements=[],this.elements=this.layers?createSizedArray(this.layers.length):[],this.initElement(t,e,i),this.tm=t.tm?PropertyFactory.getProp(this,t.tm,0,e.frameRate,this):{_placeholder:!0}}function SVGRenderer(t,e){this.animationItem=t,this.layers=null,this.renderedFrame=-1,this.svgElement=createNS("svg");var i="";if(e&&e.title){var r=createNS("title"),n=createElementID();r.setAttribute("id",n),r.textContent=e.title,this.svgElement.appendChild(r),i+=n}if(e&&e.description){var s=createNS("desc"),a=createElementID();s.setAttribute("id",a),s.textContent=e.description,this.svgElement.appendChild(s),i+=" "+a}i&&this.svgElement.setAttribute("aria-labelledby",i);var o=createNS("defs");this.svgElement.appendChild(o);var h=createNS("g");this.svgElement.appendChild(h),this.layerElement=h,this.renderConfig={preserveAspectRatio:e&&e.preserveAspectRatio||"xMidYMid meet",imagePreserveAspectRatio:e&&e.imagePreserveAspectRatio||"xMidYMid slice",contentVisibility:e&&e.contentVisibility||"visible",progressiveLoad:e&&e.progressiveLoad||!1,hideOnTransparent:!(e&&!1===e.hideOnTransparent),viewBoxOnly:e&&e.viewBoxOnly||!1,viewBoxSize:e&&e.viewBoxSize||!1,className:e&&e.className||"",id:e&&e.id||"",focusable:e&&e.focusable,filterSize:{width:e&&e.filterSize&&e.filterSize.width||"100%",height:e&&e.filterSize&&e.filterSize.height||"100%",x:e&&e.filterSize&&e.filterSize.x||"0%",y:e&&e.filterSize&&e.filterSize.y||"0%"},width:e&&e.width,height:e&&e.height,runExpressions:!e||void 0===e.runExpressions||e.runExpressions},this.globalData={_mdf:!1,frameNum:-1,defs:o,renderConfig:this.renderConfig},this.elements=[],this.pendingElements=[],this.destroyed=!1,this.rendererType="svg"}function CVContextData(){var t;for(this.saved=[],this.cArrPos=0,this.cTr=new Matrix,this.cO=1,this.savedOp=createTypedArray("float32",15),t=0;t<15;t+=1)this.saved[t]=createTypedArray("float32",16);this._length=15}function ShapeTransformManager(){this.sequences={},this.sequenceList=[],this.transform_key_count=0}function CVEffects(){}function CVMaskElement(t,e){var i;this.data=t,this.element=e,this.masksProperties=this.data.masksProperties||[],this.viewData=createSizedArray(this.masksProperties.length);var r=this.masksProperties.length,n=!1;for(i=0;i<r;i+=1)"n"!==this.masksProperties[i].mode&&(n=!0),this.viewData[i]=ShapePropertyFactory.getShapeProp(this.element,this.masksProperties[i],3);this.hasMasks=n,n&&this.element.addRenderableComponent(this)}function CVBaseElement(){}function CVShapeData(t,e,i,r){this.styledShapes=[],this.tr=[0,0,0,0,0,0];var n,s=4;"rc"===e.ty?s=5:"el"===e.ty?s=6:"sr"===e.ty&&(s=7),this.sh=ShapePropertyFactory.getShapeProp(t,e,s,t);var a,o=i.length;for(n=0;n<o;n+=1)i[n].closed||(a={transforms:r.addTransformSequence(i[n].transforms),trNodes:[]},this.styledShapes.push(a),i[n].elements.push(a))}function CVShapeElement(t,e,i){this.shapes=[],this.shapesData=t.shapes,this.stylesList=[],this.itemsData=[],this.prevViewData=[],this.shapeModifiers=[],this.processedElements=[],this.transformsManager=new ShapeTransformManager,this.initElement(t,e,i)}function CVTextElement(t,e,i){this.textSpans=[],this.yOffset=0,this.fillColorAnim=!1,this.strokeColorAnim=!1,this.strokeWidthAnim=!1,this.stroke=!1,this.fill=!1,this.justifyOffset=0,this.currentRender=null,this.renderType="canvas",this.values={fill:"rgba(0,0,0,0)",stroke:"rgba(0,0,0,0)",sWidth:0,fValue:""},this.initElement(t,e,i)}function CVImageElement(t,e,i){this.assetData=e.getAssetData(t.refId),this.img=e.imageLoader.getAsset(this.assetData),this.initElement(t,e,i)}function CVSolidElement(t,e,i){this.initElement(t,e,i)}function CanvasRendererBase(t,e){this.animationItem=t,this.renderConfig={clearCanvas:!e||void 0===e.clearCanvas||e.clearCanvas,context:e&&e.context||null,progressiveLoad:e&&e.progressiveLoad||!1,preserveAspectRatio:e&&e.preserveAspectRatio||"xMidYMid meet",imagePreserveAspectRatio:e&&e.imagePreserveAspectRatio||"xMidYMid slice",contentVisibility:e&&e.contentVisibility||"visible",className:e&&e.className||"",id:e&&e.id||""},this.renderConfig.dpr=e&&e.dpr||1,this.animationItem.wrapper&&(this.renderConfig.dpr=e&&e.dpr||window.devicePixelRatio||1),this.renderedFrame=-1,this.globalData={frameNum:-1,_mdf:!1,renderConfig:this.renderConfig,currentGlobalAlpha:-1},this.contextData=new CVContextData,this.elements=[],this.pendingElements=[],this.transformMat=new Matrix,this.completeLayers=!1,this.rendererType="canvas"}function CVCompElement(t,e,i){this.completeLayers=!1,this.layers=t.layers,this.pendingElements=[],this.elements=createSizedArray(this.layers.length),this.initElement(t,e,i),this.tm=t.tm?PropertyFactory.getProp(this,t.tm,0,e.frameRate,this):{_placeholder:!0}}function CanvasRenderer(t,e){this.animationItem=t,this.renderConfig={clearCanvas:!e||void 0===e.clearCanvas||e.clearCanvas,context:e&&e.context||null,progressiveLoad:e&&e.progressiveLoad||!1,preserveAspectRatio:e&&e.preserveAspectRatio||"xMidYMid meet",imagePreserveAspectRatio:e&&e.imagePreserveAspectRatio||"xMidYMid slice",contentVisibility:e&&e.contentVisibility||"visible",className:e&&e.className||"",id:e&&e.id||"",runExpressions:!e||void 0===e.runExpressions||e.runExpressions},this.renderConfig.dpr=e&&e.dpr||1,this.animationItem.wrapper&&(this.renderConfig.dpr=e&&e.dpr||window.devicePixelRatio||1),this.renderedFrame=-1,this.globalData={frameNum:-1,_mdf:!1,renderConfig:this.renderConfig,currentGlobalAlpha:-1},this.contextData=new CVContextData,this.elements=[],this.pendingElements=[],this.transformMat=new Matrix,this.completeLayers=!1,this.rendererType="canvas"}function HBaseElement(){}function HSolidElement(t,e,i){this.initElement(t,e,i)}function HShapeElement(t,e,i){this.shapes=[],this.shapesData=t.shapes,this.stylesList=[],this.shapeModifiers=[],this.itemsData=[],this.processedElements=[],this.animatedContents=[],this.shapesContainer=createNS("g"),this.initElement(t,e,i),this.prevViewData=[],this.currentBBox={x:999999,y:-999999,h:0,w:0}}function HTextElement(t,e,i){this.textSpans=[],this.textPaths=[],this.currentBBox={x:999999,y:-999999,h:0,w:0},this.renderType="svg",this.isMasked=!1,this.initElement(t,e,i)}function HCameraElement(t,e,i){this.initFrame(),this.initBaseData(t,e,i),this.initHierarchy();var r=PropertyFactory.getProp;if(this.pe=r(this,t.pe,0,0,this),t.ks.p.s?(this.px=r(this,t.ks.p.x,1,0,this),this.py=r(this,t.ks.p.y,1,0,this),this.pz=r(this,t.ks.p.z,1,0,this)):this.p=r(this,t.ks.p,1,0,this),t.ks.a&&(this.a=r(this,t.ks.a,1,0,this)),t.ks.or.k.length&&t.ks.or.k[0].to){var n,s=t.ks.or.k.length;for(n=0;n<s;n+=1)t.ks.or.k[n].to=null,t.ks.or.k[n].ti=null}this.or=r(this,t.ks.or,1,degToRads,this),this.or.sh=!0,this.rx=r(this,t.ks.rx,0,degToRads,this),this.ry=r(this,t.ks.ry,0,degToRads,this),this.rz=r(this,t.ks.rz,0,degToRads,this),this.mat=new Matrix,this._prevMat=new Matrix,this._isFirstFrame=!0,this.finalTransform={mProp:this}}function HImageElement(t,e,i){this.assetData=e.getAssetData(t.refId),this.initElement(t,e,i)}function HybridRendererBase(t,e){this.animationItem=t,this.layers=null,this.renderedFrame=-1,this.renderConfig={className:e&&e.className||"",imagePreserveAspectRatio:e&&e.imagePreserveAspectRatio||"xMidYMid slice",hideOnTransparent:!(e&&!1===e.hideOnTransparent),filterSize:{width:e&&e.filterSize&&e.filterSize.width||"400%",height:e&&e.filterSize&&e.filterSize.height||"400%",x:e&&e.filterSize&&e.filterSize.x||"-100%",y:e&&e.filterSize&&e.filterSize.y||"-100%"}},this.globalData={_mdf:!1,frameNum:-1,renderConfig:this.renderConfig},this.pendingElements=[],this.elements=[],this.threeDElements=[],this.destroyed=!1,this.camera=null,this.supports3d=!0,this.rendererType="html"}function HCompElement(t,e,i){this.layers=t.layers,this.supports3d=!t.hasMask,this.completeLayers=!1,this.pendingElements=[],this.elements=this.layers?createSizedArray(this.layers.length):[],this.initElement(t,e,i),this.tm=t.tm?PropertyFactory.getProp(this,t.tm,0,e.frameRate,this):{_placeholder:!0}}function HybridRenderer(t,e){this.animationItem=t,this.layers=null,this.renderedFrame=-1,this.renderConfig={className:e&&e.className||"",imagePreserveAspectRatio:e&&e.imagePreserveAspectRatio||"xMidYMid slice",hideOnTransparent:!(e&&!1===e.hideOnTransparent),filterSize:{width:e&&e.filterSize&&e.filterSize.width||"400%",height:e&&e.filterSize&&e.filterSize.height||"400%",x:e&&e.filterSize&&e.filterSize.x||"-100%",y:e&&e.filterSize&&e.filterSize.y||"-100%"},runExpressions:!e||void 0===e.runExpressions||e.runExpressions},this.globalData={_mdf:!1,frameNum:-1,renderConfig:this.renderConfig},this.pendingElements=[],this.elements=[],this.threeDElements=[],this.destroyed=!1,this.camera=null,this.supports3d=!0,this.rendererType="html"}extendPrototype([BaseElement,TransformElement,SVGBaseElement,HierarchyElement,FrameElement,RenderableDOMElement,ITextElement],SVGTextLottieElement),SVGTextLottieElement.prototype.createContent=function(){this.data.singleShape&&!this.globalData.fontManager.chars&&(this.textContainer=createNS("text"))},SVGTextLottieElement.prototype.buildTextContents=function(t){for(var e=0,i=t.length,r=[],n="";e<i;)t[e]===String.fromCharCode(13)||t[e]===String.fromCharCode(3)?(r.push(n),n=""):n+=t[e],e+=1;return r.push(n),r},SVGTextLottieElement.prototype.buildShapeData=function(t,e){if(t.shapes&&t.shapes.length){var i=t.shapes[0];if(i.it){var r=i.it[i.it.length-1];r.s&&(r.s.k[0]=e,r.s.k[1]=e)}}return t},SVGTextLottieElement.prototype.buildNewText=function(){var t,e;this.addDynamicProperty(this);var i=this.textProperty.currentData;this.renderedLetters=createSizedArray(i?i.l.length:0),i.fc?this.layerElement.setAttribute("fill",this.buildColor(i.fc)):this.layerElement.setAttribute("fill","rgba(0,0,0,0)"),i.sc&&(this.layerElement.setAttribute("stroke",this.buildColor(i.sc)),this.layerElement.setAttribute("stroke-width",i.sw)),this.layerElement.setAttribute("font-size",i.finalSize);var r=this.globalData.fontManager.getFontByName(i.f);if(r.fClass)this.layerElement.setAttribute("class",r.fClass);else{this.layerElement.setAttribute("font-family",r.fFamily);var n=i.fWeight,s=i.fStyle;this.layerElement.setAttribute("font-style",s),this.layerElement.setAttribute("font-weight",n)}this.layerElement.setAttribute("aria-label",i.t);var a,o=i.l||[],h=!!this.globalData.fontManager.chars;e=o.length;var l=this.mHelper,p=this.data.singleShape,c=0,d=0,f=!0,u=.001*i.tr*i.finalSize;if(!p||h||i.sz){var m,y=this.textSpans.length;for(t=0;t<e;t+=1){if(this.textSpans[t]||(this.textSpans[t]={span:null,childSpan:null,glyph:null}),!h||!p||0===t){if(a=y>t?this.textSpans[t].span:createNS(h?"g":"text"),y<=t){if(a.setAttribute("stroke-linecap","butt"),a.setAttribute("stroke-linejoin","round"),a.setAttribute("stroke-miterlimit","4"),this.textSpans[t].span=a,h){var g=createNS("g");a.appendChild(g),this.textSpans[t].childSpan=g}this.textSpans[t].span=a,this.layerElement.appendChild(a)}a.style.display="inherit"}if(l.reset(),p&&(o[t].n&&(c=-u,d+=i.yOffset,d+=f?1:0,f=!1),this.applyTextPropertiesToMatrix(i,l,o[t].line,c,d),c+=o[t].l||0,c+=u),h){var v;if(1===(m=this.globalData.fontManager.getCharData(i.finalText[t],r.fStyle,this.globalData.fontManager.getFontByName(i.f).fFamily)).t)v=new SVGCompElement(m.data,this.globalData,this);else{var b=emptyShapeData;m.data&&m.data.shapes&&(b=this.buildShapeData(m.data,i.finalSize)),v=new SVGShapeElement(b,this.globalData,this)}if(this.textSpans[t].glyph){var _=this.textSpans[t].glyph;this.textSpans[t].childSpan.removeChild(_.layerElement),_.destroy()}this.textSpans[t].glyph=v,v._debug=!0,v.prepareFrame(0),v.renderFrame(),this.textSpans[t].childSpan.appendChild(v.layerElement),1===m.t&&this.textSpans[t].childSpan.setAttribute("transform","scale("+i.finalSize/100+","+i.finalSize/100+")")}else p&&a.setAttribute("transform","translate("+l.props[12]+","+l.props[13]+")"),a.textContent=o[t].val,a.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve")}p&&a&&a.setAttribute("d","")}else{var E=this.textContainer,S="start";switch(i.j){case 1:S="end";break;case 2:S="middle";break;default:S="start"}E.setAttribute("text-anchor",S),E.setAttribute("letter-spacing",u);var P=this.buildTextContents(i.finalText);for(e=P.length,d=i.ps?i.ps[1]+i.ascent:0,t=0;t<e;t+=1)(a=this.textSpans[t].span||createNS("tspan")).textContent=P[t],a.setAttribute("x",0),a.setAttribute("y",d),a.style.display="inherit",E.appendChild(a),this.textSpans[t]||(this.textSpans[t]={span:null,glyph:null}),this.textSpans[t].span=a,d+=i.finalLineHeight;this.layerElement.appendChild(E)}for(;t<this.textSpans.length;)this.textSpans[t].span.style.display="none",t+=1;this._sizeChanged=!0},SVGTextLottieElement.prototype.sourceRectAtTime=function(){if(this.prepareFrame(this.comp.renderedFrame-this.data.st),this.renderInnerContent(),this._sizeChanged){this._sizeChanged=!1;var t=this.layerElement.getBBox();this.bbox={top:t.y,left:t.x,width:t.width,height:t.height}}return this.bbox},SVGTextLottieElement.prototype.getValue=function(){var t,e,i=this.textSpans.length;for(this.renderedFrame=this.comp.renderedFrame,t=0;t<i;t+=1)(e=this.textSpans[t].glyph)&&(e.prepareFrame(this.comp.renderedFrame-this.data.st),e._mdf&&(this._mdf=!0))},SVGTextLottieElement.prototype.renderInnerContent=function(){if((!this.data.singleShape||this._mdf)&&(this.textAnimator.getMeasures(this.textProperty.currentData,this.lettersChangedFlag),this.lettersChangedFlag||this.textAnimator.lettersChangedFlag)){var t,e;this._sizeChanged=!0;var i,r,n,s=this.textAnimator.renderedLetters,a=this.textProperty.currentData.l;for(e=a.length,t=0;t<e;t+=1)a[t].n||(i=s[t],r=this.textSpans[t].span,(n=this.textSpans[t].glyph)&&n.renderFrame(),i._mdf.m&&r.setAttribute("transform",i.m),i._mdf.o&&r.setAttribute("opacity",i.o),i._mdf.sw&&r.setAttribute("stroke-width",i.sw),i._mdf.sc&&r.setAttribute("stroke",i.sc),i._mdf.fc&&r.setAttribute("fill",i.fc))}},extendPrototype([IImageElement],ISolidElement),ISolidElement.prototype.createContent=function(){var t=createNS("rect");t.setAttribute("width",this.data.sw),t.setAttribute("height",this.data.sh),t.setAttribute("fill",this.data.sc),this.layerElement.appendChild(t)},NullElement.prototype.prepareFrame=function(t){this.prepareProperties(t,!0)},NullElement.prototype.renderFrame=function(){},NullElement.prototype.getBaseElement=function(){return null},NullElement.prototype.destroy=function(){},NullElement.prototype.sourceRectAtTime=function(){},NullElement.prototype.hide=function(){},extendPrototype([BaseElement,TransformElement,HierarchyElement,FrameElement],NullElement),extendPrototype([BaseRenderer],SVGRendererBase),SVGRendererBase.prototype.createNull=function(t){return new NullElement(t,this.globalData,this)},SVGRendererBase.prototype.createShape=function(t){return new SVGShapeElement(t,this.globalData,this)},SVGRendererBase.prototype.createText=function(t){return new SVGTextLottieElement(t,this.globalData,this)},SVGRendererBase.prototype.createImage=function(t){return new IImageElement(t,this.globalData,this)},SVGRendererBase.prototype.createSolid=function(t){return new ISolidElement(t,this.globalData,this)},SVGRendererBase.prototype.configAnimation=function(t){this.svgElement.setAttribute("xmlns","http://www.w3.org/2000/svg"),this.svgElement.setAttribute("xmlns:xlink","http://www.w3.org/1999/xlink"),this.renderConfig.viewBoxSize?this.svgElement.setAttribute("viewBox",this.renderConfig.viewBoxSize):this.svgElement.setAttribute("viewBox","0 0 "+t.w+" "+t.h),this.renderConfig.viewBoxOnly||(this.svgElement.setAttribute("width",t.w),this.svgElement.setAttribute("height",t.h),this.svgElement.style.width="100%",this.svgElement.style.height="100%",this.svgElement.style.transform="translate3d(0,0,0)",this.svgElement.style.contentVisibility=this.renderConfig.contentVisibility),this.renderConfig.width&&this.svgElement.setAttribute("width",this.renderConfig.width),this.renderConfig.height&&this.svgElement.setAttribute("height",this.renderConfig.height),this.renderConfig.className&&this.svgElement.setAttribute("class",this.renderConfig.className),this.renderConfig.id&&this.svgElement.setAttribute("id",this.renderConfig.id),void 0!==this.renderConfig.focusable&&this.svgElement.setAttribute("focusable",this.renderConfig.focusable),this.svgElement.setAttribute("preserveAspectRatio",this.renderConfig.preserveAspectRatio),this.animationItem.wrapper.appendChild(this.svgElement);var e=this.globalData.defs;this.setupGlobalData(t,e),this.globalData.progressiveLoad=this.renderConfig.progressiveLoad,this.data=t;var i=createNS("clipPath"),r=createNS("rect");r.setAttribute("width",t.w),r.setAttribute("height",t.h),r.setAttribute("x",0),r.setAttribute("y",0);var n=createElementID();i.setAttribute("id",n),i.appendChild(r),this.layerElement.setAttribute("clip-path","url("+getLocationHref()+"#"+n+")"),e.appendChild(i),this.layers=t.layers,this.elements=createSizedArray(t.layers.length)},SVGRendererBase.prototype.destroy=function(){var t;this.animationItem.wrapper&&(this.animationItem.wrapper.innerText=""),this.layerElement=null,this.globalData.defs=null;var e=this.layers?this.layers.length:0;for(t=0;t<e;t+=1)this.elements[t]&&this.elements[t].destroy();this.elements.length=0,this.destroyed=!0,this.animationItem=null},SVGRendererBase.prototype.updateContainerSize=function(){},SVGRendererBase.prototype.findIndexByInd=function(t){var e=0,i=this.layers.length;for(e=0;e<i;e+=1)if(this.layers[e].ind===t)return e;return-1},SVGRendererBase.prototype.buildItem=function(t){var e=this.elements;if(!e[t]&&99!==this.layers[t].ty){e[t]=!0;var i=this.createItem(this.layers[t]);if(e[t]=i,getExpressionsPlugin()&&(0===this.layers[t].ty&&this.globalData.projectInterface.registerComposition(i),i.initExpressions()),this.appendElementInPos(i,t),this.layers[t].tt){var r="tp"in this.layers[t]?this.findIndexByInd(this.layers[t].tp):t-1;if(-1===r)return;if(this.elements[r]&&!0!==this.elements[r]){var n=e[r].getMatte(this.layers[t].tt);i.setMatte(n)}else this.buildItem(r),this.addPendingElement(i)}}},SVGRendererBase.prototype.checkPendingElements=function(){for(;this.pendingElements.length;){var t=this.pendingElements.pop();if(t.checkParenting(),t.data.tt)for(var e=0,i=this.elements.length;e<i;){if(this.elements[e]===t){var r="tp"in t.data?this.findIndexByInd(t.data.tp):e-1,n=this.elements[r].getMatte(this.layers[e].tt);t.setMatte(n);break}e+=1}}},SVGRendererBase.prototype.renderFrame=function(t){if(this.renderedFrame!==t&&!this.destroyed){var e;null===t?t=this.renderedFrame:this.renderedFrame=t,this.globalData.frameNum=t,this.globalData.frameId+=1,this.globalData.projectInterface.currentFrame=t,this.globalData._mdf=!1;var i=this.layers.length;for(this.completeLayers||this.checkLayers(t),e=i-1;e>=0;e-=1)(this.completeLayers||this.elements[e])&&this.elements[e].prepareFrame(t-this.layers[e].st);if(this.globalData._mdf)for(e=0;e<i;e+=1)(this.completeLayers||this.elements[e])&&this.elements[e].renderFrame()}},SVGRendererBase.prototype.appendElementInPos=function(t,e){var i=t.getBaseElement();if(i){for(var r,n=0;n<e;)this.elements[n]&&!0!==this.elements[n]&&this.elements[n].getBaseElement()&&(r=this.elements[n].getBaseElement()),n+=1;r?this.layerElement.insertBefore(i,r):this.layerElement.appendChild(i)}},SVGRendererBase.prototype.hide=function(){this.layerElement.style.display="none"},SVGRendererBase.prototype.show=function(){this.layerElement.style.display="block"},extendPrototype([BaseElement,TransformElement,HierarchyElement,FrameElement,RenderableDOMElement],ICompElement),ICompElement.prototype.initElement=function(t,e,i){this.initFrame(),this.initBaseData(t,e,i),this.initTransform(t,e,i),this.initRenderable(),this.initHierarchy(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),!this.data.xt&&e.progressiveLoad||this.buildAllItems(),this.hide()},ICompElement.prototype.prepareFrame=function(t){if(this._mdf=!1,this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange),this.isInRange||this.data.xt){if(this.tm._placeholder)this.renderedFrame=t/this.data.sr;else{var e=this.tm.v;e===this.data.op&&(e=this.data.op-1),this.renderedFrame=e}var i,r=this.elements.length;for(this.completeLayers||this.checkLayers(this.renderedFrame),i=r-1;i>=0;i-=1)(this.completeLayers||this.elements[i])&&(this.elements[i].prepareFrame(this.renderedFrame-this.layers[i].st),this.elements[i]._mdf&&(this._mdf=!0))}},ICompElement.prototype.renderInnerContent=function(){var t,e=this.layers.length;for(t=0;t<e;t+=1)(this.completeLayers||this.elements[t])&&this.elements[t].renderFrame()},ICompElement.prototype.setElements=function(t){this.elements=t},ICompElement.prototype.getElements=function(){return this.elements},ICompElement.prototype.destroyElements=function(){var t,e=this.layers.length;for(t=0;t<e;t+=1)this.elements[t]&&this.elements[t].destroy()},ICompElement.prototype.destroy=function(){this.destroyElements(),this.destroyBaseElement()},extendPrototype([SVGRendererBase,ICompElement,SVGBaseElement],SVGCompElement),SVGCompElement.prototype.createComp=function(t){return new SVGCompElement(t,this.globalData,this)},extendPrototype([SVGRendererBase],SVGRenderer),SVGRenderer.prototype.createComp=function(t){return new SVGCompElement(t,this.globalData,this)},CVContextData.prototype.duplicate=function(){var t=2*this._length,e=this.savedOp;this.savedOp=createTypedArray("float32",t),this.savedOp.set(e);var i=0;for(i=this._length;i<t;i+=1)this.saved[i]=createTypedArray("float32",16);this._length=t},CVContextData.prototype.reset=function(){this.cArrPos=0,this.cTr.reset(),this.cO=1},ShapeTransformManager.prototype={addTransformSequence:function(t){var e,i=t.length,r="_";for(e=0;e<i;e+=1)r+=t[e].transform.key+"_";var n=this.sequences[r];return n||(n={transforms:[].concat(t),finalTransform:new Matrix,_mdf:!1},this.sequences[r]=n,this.sequenceList.push(n)),n},processSequence:function(t,e){for(var i,r=0,n=t.transforms.length,s=e;r<n&&!e;){if(t.transforms[r].transform.mProps._mdf){s=!0;break}r+=1}if(s)for(t.finalTransform.reset(),r=n-1;r>=0;r-=1)i=t.transforms[r].transform.mProps.v.props,t.finalTransform.transform(i[0],i[1],i[2],i[3],i[4],i[5],i[6],i[7],i[8],i[9],i[10],i[11],i[12],i[13],i[14],i[15]);t._mdf=s},processSequences:function(t){var e,i=this.sequenceList.length;for(e=0;e<i;e+=1)this.processSequence(this.sequenceList[e],t)},getNewKey:function(){return this.transform_key_count+=1,"_"+this.transform_key_count}},CVEffects.prototype.renderFrame=function(){},CVMaskElement.prototype.renderFrame=function(){if(this.hasMasks){var t,e,i,r,n=this.element.finalTransform.mat,s=this.element.canvasContext,a=this.masksProperties.length;for(s.beginPath(),t=0;t<a;t+=1)if("n"!==this.masksProperties[t].mode){var o;this.masksProperties[t].inv&&(s.moveTo(0,0),s.lineTo(this.element.globalData.compSize.w,0),s.lineTo(this.element.globalData.compSize.w,this.element.globalData.compSize.h),s.lineTo(0,this.element.globalData.compSize.h),s.lineTo(0,0)),r=this.viewData[t].v,e=n.applyToPointArray(r.v[0][0],r.v[0][1],0),s.moveTo(e[0],e[1]);var h=r._length;for(o=1;o<h;o+=1)i=n.applyToTriplePoints(r.o[o-1],r.i[o],r.v[o]),s.bezierCurveTo(i[0],i[1],i[2],i[3],i[4],i[5]);i=n.applyToTriplePoints(r.o[o-1],r.i[0],r.v[0]),s.bezierCurveTo(i[0],i[1],i[2],i[3],i[4],i[5])}this.element.globalData.renderer.save(!0),s.clip()}},CVMaskElement.prototype.getMaskProperty=MaskElement.prototype.getMaskProperty,CVMaskElement.prototype.destroy=function(){this.element=null},CVBaseElement.prototype={createElements:function(){},initRendererElement:function(){},createContainerElements:function(){this.canvasContext=this.globalData.canvasContext,this.renderableEffectsManager=new CVEffects(this)},createContent:function(){},setBlendMode:function(){var t=this.globalData;if(t.blendMode!==this.data.bm){t.blendMode=this.data.bm;var e=getBlendMode(this.data.bm);t.canvasContext.globalCompositeOperation=e}},createRenderableComponents:function(){this.maskManager=new CVMaskElement(this.data,this)},hideElement:function(){this.hidden||this.isInRange&&!this.isTransparent||(this.hidden=!0)},showElement:function(){this.isInRange&&!this.isTransparent&&(this.hidden=!1,this._isFirstFrame=!0,this.maskManager._isFirstFrame=!0)},renderFrame:function(){if(!this.hidden&&!this.data.hd){this.renderTransform(),this.renderRenderable(),this.setBlendMode();var t=0===this.data.ty;this.globalData.renderer.save(t),this.globalData.renderer.ctxTransform(this.finalTransform.mat.props),this.globalData.renderer.ctxOpacity(this.finalTransform.mProp.o.v),this.renderInnerContent(),this.globalData.renderer.restore(t),this.maskManager.hasMasks&&this.globalData.renderer.restore(!0),this._isFirstFrame&&(this._isFirstFrame=!1)}},destroy:function(){this.canvasContext=null,this.data=null,this.globalData=null,this.maskManager.destroy()},mHelper:new Matrix},CVBaseElement.prototype.hide=CVBaseElement.prototype.hideElement,CVBaseElement.prototype.show=CVBaseElement.prototype.showElement,CVShapeData.prototype.setAsAnimated=SVGShapeData.prototype.setAsAnimated,extendPrototype([BaseElement,TransformElement,CVBaseElement,IShapeElement,HierarchyElement,FrameElement,RenderableElement],CVShapeElement),CVShapeElement.prototype.initElement=RenderableDOMElement.prototype.initElement,CVShapeElement.prototype.transformHelper={opacity:1,_opMdf:!1},CVShapeElement.prototype.dashResetter=[],CVShapeElement.prototype.createContent=function(){this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,!0,[])},CVShapeElement.prototype.createStyleElement=function(t,e){var i={data:t,type:t.ty,preTransforms:this.transformsManager.addTransformSequence(e),transforms:[],elements:[],closed:!0===t.hd},r={};if("fl"===t.ty||"st"===t.ty?(r.c=PropertyFactory.getProp(this,t.c,1,255,this),r.c.k||(i.co="rgb("+bmFloor(r.c.v[0])+","+bmFloor(r.c.v[1])+","+bmFloor(r.c.v[2])+")")):"gf"!==t.ty&&"gs"!==t.ty||(r.s=PropertyFactory.getProp(this,t.s,1,null,this),r.e=PropertyFactory.getProp(this,t.e,1,null,this),r.h=PropertyFactory.getProp(this,t.h||{k:0},0,.01,this),r.a=PropertyFactory.getProp(this,t.a||{k:0},0,degToRads,this),r.g=new GradientProperty(this,t.g,this)),r.o=PropertyFactory.getProp(this,t.o,0,.01,this),"st"===t.ty||"gs"===t.ty){if(i.lc=lineCapEnum[t.lc||2],i.lj=lineJoinEnum[t.lj||2],1==t.lj&&(i.ml=t.ml),r.w=PropertyFactory.getProp(this,t.w,0,null,this),r.w.k||(i.wi=r.w.v),t.d){var n=new DashProperty(this,t.d,"canvas",this);r.d=n,r.d.k||(i.da=r.d.dashArray,i.do=r.d.dashoffset[0])}}else i.r=2===t.r?"evenodd":"nonzero";return this.stylesList.push(i),r.style=i,r},CVShapeElement.prototype.createGroupElement=function(){return{it:[],prevViewData:[]}},CVShapeElement.prototype.createTransformElement=function(t){return{transform:{opacity:1,_opMdf:!1,key:this.transformsManager.getNewKey(),op:PropertyFactory.getProp(this,t.o,0,.01,this),mProps:TransformPropertyFactory.getTransformProperty(this,t,this)}}},CVShapeElement.prototype.createShapeElement=function(t){var e=new CVShapeData(this,t,this.stylesList,this.transformsManager);return this.shapes.push(e),this.addShapeToModifiers(e),e},CVShapeElement.prototype.reloadShapes=function(){var t;this._isFirstFrame=!0;var e=this.itemsData.length;for(t=0;t<e;t+=1)this.prevViewData[t]=this.itemsData[t];for(this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,!0,[]),e=this.dynamicProperties.length,t=0;t<e;t+=1)this.dynamicProperties[t].getValue();this.renderModifiers(),this.transformsManager.processSequences(this._isFirstFrame)},CVShapeElement.prototype.addTransformToStyleList=function(t){var e,i=this.stylesList.length;for(e=0;e<i;e+=1)this.stylesList[e].closed||this.stylesList[e].transforms.push(t)},CVShapeElement.prototype.removeTransformFromStyleList=function(){var t,e=this.stylesList.length;for(t=0;t<e;t+=1)this.stylesList[t].closed||this.stylesList[t].transforms.pop()},CVShapeElement.prototype.closeStyles=function(t){var e,i=t.length;for(e=0;e<i;e+=1)t[e].closed=!0},CVShapeElement.prototype.searchShapes=function(t,e,i,r,n){var s,a,o,h,l,p,c=t.length-1,d=[],f=[],u=[].concat(n);for(s=c;s>=0;s-=1){if((h=this.searchProcessedElement(t[s]))?e[s]=i[h-1]:t[s]._shouldRender=r,"fl"===t[s].ty||"st"===t[s].ty||"gf"===t[s].ty||"gs"===t[s].ty)h?e[s].style.closed=!1:e[s]=this.createStyleElement(t[s],u),d.push(e[s].style);else if("gr"===t[s].ty){if(h)for(o=e[s].it.length,a=0;a<o;a+=1)e[s].prevViewData[a]=e[s].it[a];else e[s]=this.createGroupElement(t[s]);this.searchShapes(t[s].it,e[s].it,e[s].prevViewData,r,u)}else"tr"===t[s].ty?(h||(p=this.createTransformElement(t[s]),e[s]=p),u.push(e[s]),this.addTransformToStyleList(e[s])):"sh"===t[s].ty||"rc"===t[s].ty||"el"===t[s].ty||"sr"===t[s].ty?h||(e[s]=this.createShapeElement(t[s])):"tm"===t[s].ty||"rd"===t[s].ty||"pb"===t[s].ty||"zz"===t[s].ty||"op"===t[s].ty?(h?(l=e[s]).closed=!1:((l=ShapeModifiers.getModifier(t[s].ty)).init(this,t[s]),e[s]=l,this.shapeModifiers.push(l)),f.push(l)):"rp"===t[s].ty&&(h?(l=e[s]).closed=!0:(l=ShapeModifiers.getModifier(t[s].ty),e[s]=l,l.init(this,t,s,e),this.shapeModifiers.push(l),r=!1),f.push(l));this.addProcessedElement(t[s],s+1)}for(this.removeTransformFromStyleList(),this.closeStyles(d),c=f.length,s=0;s<c;s+=1)f[s].closed=!0},CVShapeElement.prototype.renderInnerContent=function(){this.transformHelper.opacity=1,this.transformHelper._opMdf=!1,this.renderModifiers(),this.transformsManager.processSequences(this._isFirstFrame),this.renderShape(this.transformHelper,this.shapesData,this.itemsData,!0)},CVShapeElement.prototype.renderShapeTransform=function(t,e){(t._opMdf||e.op._mdf||this._isFirstFrame)&&(e.opacity=t.opacity,e.opacity*=e.op.v,e._opMdf=!0)},CVShapeElement.prototype.drawLayer=function(){var t,e,i,r,n,s,a,o,h,l=this.stylesList.length,p=this.globalData.renderer,c=this.globalData.canvasContext;for(t=0;t<l;t+=1)if(("st"!==(o=(h=this.stylesList[t]).type)&&"gs"!==o||0!==h.wi)&&h.data._shouldRender&&0!==h.coOp&&0!==this.globalData.currentGlobalAlpha){for(p.save(),s=h.elements,"st"===o||"gs"===o?(c.strokeStyle="st"===o?h.co:h.grd,c.lineWidth=h.wi,c.lineCap=h.lc,c.lineJoin=h.lj,c.miterLimit=h.ml||0):c.fillStyle="fl"===o?h.co:h.grd,p.ctxOpacity(h.coOp),"st"!==o&&"gs"!==o&&c.beginPath(),p.ctxTransform(h.preTransforms.finalTransform.props),i=s.length,e=0;e<i;e+=1){for("st"!==o&&"gs"!==o||(c.beginPath(),h.da&&(c.setLineDash(h.da),c.lineDashOffset=h.do)),n=(a=s[e].trNodes).length,r=0;r<n;r+=1)"m"===a[r].t?c.moveTo(a[r].p[0],a[r].p[1]):"c"===a[r].t?c.bezierCurveTo(a[r].pts[0],a[r].pts[1],a[r].pts[2],a[r].pts[3],a[r].pts[4],a[r].pts[5]):c.closePath();"st"!==o&&"gs"!==o||(c.stroke(),h.da&&c.setLineDash(this.dashResetter))}"st"!==o&&"gs"!==o&&c.fill(h.r),p.restore()}},CVShapeElement.prototype.renderShape=function(t,e,i,r){var n,s;for(s=t,n=e.length-1;n>=0;n-=1)"tr"===e[n].ty?(s=i[n].transform,this.renderShapeTransform(t,s)):"sh"===e[n].ty||"el"===e[n].ty||"rc"===e[n].ty||"sr"===e[n].ty?this.renderPath(e[n],i[n]):"fl"===e[n].ty?this.renderFill(e[n],i[n],s):"st"===e[n].ty?this.renderStroke(e[n],i[n],s):"gf"===e[n].ty||"gs"===e[n].ty?this.renderGradientFill(e[n],i[n],s):"gr"===e[n].ty?this.renderShape(s,e[n].it,i[n].it):e[n].ty;r&&this.drawLayer()},CVShapeElement.prototype.renderStyledShape=function(t,e){if(this._isFirstFrame||e._mdf||t.transforms._mdf){var i,r,n,s=t.trNodes,a=e.paths,o=a._length;s.length=0;var h=t.transforms.finalTransform;for(n=0;n<o;n+=1){var l=a.shapes[n];if(l&&l.v){for(r=l._length,i=1;i<r;i+=1)1===i&&s.push({t:"m",p:h.applyToPointArray(l.v[0][0],l.v[0][1],0)}),s.push({t:"c",pts:h.applyToTriplePoints(l.o[i-1],l.i[i],l.v[i])});1===r&&s.push({t:"m",p:h.applyToPointArray(l.v[0][0],l.v[0][1],0)}),l.c&&r&&(s.push({t:"c",pts:h.applyToTriplePoints(l.o[i-1],l.i[0],l.v[0])}),s.push({t:"z"}))}}t.trNodes=s}},CVShapeElement.prototype.renderPath=function(t,e){if(!0!==t.hd&&t._shouldRender){var i,r=e.styledShapes.length;for(i=0;i<r;i+=1)this.renderStyledShape(e.styledShapes[i],e.sh)}},CVShapeElement.prototype.renderFill=function(t,e,i){var r=e.style;(e.c._mdf||this._isFirstFrame)&&(r.co="rgb("+bmFloor(e.c.v[0])+","+bmFloor(e.c.v[1])+","+bmFloor(e.c.v[2])+")"),(e.o._mdf||i._opMdf||this._isFirstFrame)&&(r.coOp=e.o.v*i.opacity)},CVShapeElement.prototype.renderGradientFill=function(t,e,i){var r,n=e.style;if(!n.grd||e.g._mdf||e.s._mdf||e.e._mdf||1!==t.t&&(e.h._mdf||e.a._mdf)){var s,a=this.globalData.canvasContext,o=e.s.v,h=e.e.v;if(1===t.t)r=a.createLinearGradient(o[0],o[1],h[0],h[1]);else{var l=Math.sqrt(Math.pow(o[0]-h[0],2)+Math.pow(o[1]-h[1],2)),p=Math.atan2(h[1]-o[1],h[0]-o[0]),c=e.h.v;c>=1?c=.99:c<=-1&&(c=-.99);var d=l*c,f=Math.cos(p+e.a.v)*d+o[0],u=Math.sin(p+e.a.v)*d+o[1];r=a.createRadialGradient(f,u,0,o[0],o[1],l)}var m=t.g.p,y=e.g.c,g=1;for(s=0;s<m;s+=1)e.g._hasOpacity&&e.g._collapsable&&(g=e.g.o[2*s+1]),r.addColorStop(y[4*s]/100,"rgba("+y[4*s+1]+","+y[4*s+2]+","+y[4*s+3]+","+g+")");n.grd=r}n.coOp=e.o.v*i.opacity},CVShapeElement.prototype.renderStroke=function(t,e,i){var r=e.style,n=e.d;n&&(n._mdf||this._isFirstFrame)&&(r.da=n.dashArray,r.do=n.dashoffset[0]),(e.c._mdf||this._isFirstFrame)&&(r.co="rgb("+bmFloor(e.c.v[0])+","+bmFloor(e.c.v[1])+","+bmFloor(e.c.v[2])+")"),(e.o._mdf||i._opMdf||this._isFirstFrame)&&(r.coOp=e.o.v*i.opacity),(e.w._mdf||this._isFirstFrame)&&(r.wi=e.w.v)},CVShapeElement.prototype.destroy=function(){this.shapesData=null,this.globalData=null,this.canvasContext=null,this.stylesList.length=0,this.itemsData.length=0},extendPrototype([BaseElement,TransformElement,CVBaseElement,HierarchyElement,FrameElement,RenderableElement,ITextElement],CVTextElement),CVTextElement.prototype.tHelper=createTag("canvas").getContext("2d"),CVTextElement.prototype.buildNewText=function(){var t=this.textProperty.currentData;this.renderedLetters=createSizedArray(t.l?t.l.length:0);var e=!1;t.fc?(e=!0,this.values.fill=this.buildColor(t.fc)):this.values.fill="rgba(0,0,0,0)",this.fill=e;var i=!1;t.sc&&(i=!0,this.values.stroke=this.buildColor(t.sc),this.values.sWidth=t.sw);var r,n,s,a,o,h,l,p,c,d,f,u,m=this.globalData.fontManager.getFontByName(t.f),y=t.l,g=this.mHelper;this.stroke=i,this.values.fValue=t.finalSize+"px "+this.globalData.fontManager.getFontByName(t.f).fFamily,n=t.finalText.length;var v=this.data.singleShape,b=.001*t.tr*t.finalSize,_=0,E=0,S=!0,P=0;for(r=0;r<n;r+=1){a=(s=this.globalData.fontManager.getCharData(t.finalText[r],m.fStyle,this.globalData.fontManager.getFontByName(t.f).fFamily))&&s.data||{},g.reset(),v&&y[r].n&&(_=-b,E+=t.yOffset,E+=S?1:0,S=!1),c=(l=a.shapes?a.shapes[0].it:[]).length,g.scale(t.finalSize/100,t.finalSize/100),v&&this.applyTextPropertiesToMatrix(t,g,y[r].line,_,E),f=createSizedArray(c-1);var x=0;for(p=0;p<c;p+=1)if("sh"===l[p].ty){for(h=l[p].ks.k.i.length,d=l[p].ks.k,u=[],o=1;o<h;o+=1)1===o&&u.push(g.applyToX(d.v[0][0],d.v[0][1],0),g.applyToY(d.v[0][0],d.v[0][1],0)),u.push(g.applyToX(d.o[o-1][0],d.o[o-1][1],0),g.applyToY(d.o[o-1][0],d.o[o-1][1],0),g.applyToX(d.i[o][0],d.i[o][1],0),g.applyToY(d.i[o][0],d.i[o][1],0),g.applyToX(d.v[o][0],d.v[o][1],0),g.applyToY(d.v[o][0],d.v[o][1],0));u.push(g.applyToX(d.o[o-1][0],d.o[o-1][1],0),g.applyToY(d.o[o-1][0],d.o[o-1][1],0),g.applyToX(d.i[0][0],d.i[0][1],0),g.applyToY(d.i[0][0],d.i[0][1],0),g.applyToX(d.v[0][0],d.v[0][1],0),g.applyToY(d.v[0][0],d.v[0][1],0)),f[x]=u,x+=1}v&&(_+=y[r].l,_+=b),this.textSpans[P]?this.textSpans[P].elem=f:this.textSpans[P]={elem:f},P+=1}},CVTextElement.prototype.renderInnerContent=function(){var t,e,i,r,n,s,a=this.canvasContext;a.font=this.values.fValue,a.lineCap="butt",a.lineJoin="miter",a.miterLimit=4,this.data.singleShape||this.textAnimator.getMeasures(this.textProperty.currentData,this.lettersChangedFlag);var o,h=this.textAnimator.renderedLetters,l=this.textProperty.currentData.l;e=l.length;var p,c,d=null,f=null,u=null;for(t=0;t<e;t+=1)if(!l[t].n){if((o=h[t])&&(this.globalData.renderer.save(),this.globalData.renderer.ctxTransform(o.p),this.globalData.renderer.ctxOpacity(o.o)),this.fill){for(o&&o.fc?d!==o.fc&&(d=o.fc,a.fillStyle=o.fc):d!==this.values.fill&&(d=this.values.fill,a.fillStyle=this.values.fill),r=(p=this.textSpans[t].elem).length,this.globalData.canvasContext.beginPath(),i=0;i<r;i+=1)for(s=(c=p[i]).length,this.globalData.canvasContext.moveTo(c[0],c[1]),n=2;n<s;n+=6)this.globalData.canvasContext.bezierCurveTo(c[n],c[n+1],c[n+2],c[n+3],c[n+4],c[n+5]);this.globalData.canvasContext.closePath(),this.globalData.canvasContext.fill()}if(this.stroke){for(o&&o.sw?u!==o.sw&&(u=o.sw,a.lineWidth=o.sw):u!==this.values.sWidth&&(u=this.values.sWidth,a.lineWidth=this.values.sWidth),o&&o.sc?f!==o.sc&&(f=o.sc,a.strokeStyle=o.sc):f!==this.values.stroke&&(f=this.values.stroke,a.strokeStyle=this.values.stroke),r=(p=this.textSpans[t].elem).length,this.globalData.canvasContext.beginPath(),i=0;i<r;i+=1)for(s=(c=p[i]).length,this.globalData.canvasContext.moveTo(c[0],c[1]),n=2;n<s;n+=6)this.globalData.canvasContext.bezierCurveTo(c[n],c[n+1],c[n+2],c[n+3],c[n+4],c[n+5]);this.globalData.canvasContext.closePath(),this.globalData.canvasContext.stroke()}o&&this.globalData.renderer.restore()}},extendPrototype([BaseElement,TransformElement,CVBaseElement,HierarchyElement,FrameElement,RenderableElement],CVImageElement),CVImageElement.prototype.initElement=SVGShapeElement.prototype.initElement,CVImageElement.prototype.prepareFrame=IImageElement.prototype.prepareFrame,CVImageElement.prototype.createContent=function(){if(this.img.width&&(this.assetData.w!==this.img.width||this.assetData.h!==this.img.height)){var t=createTag("canvas");t.width=this.assetData.w,t.height=this.assetData.h;var e,i,r=t.getContext("2d"),n=this.img.width,s=this.img.height,a=n/s,o=this.assetData.w/this.assetData.h,h=this.assetData.pr||this.globalData.renderConfig.imagePreserveAspectRatio;a>o&&"xMidYMid slice"===h||a<o&&"xMidYMid slice"!==h?e=(i=s)*o:i=(e=n)/o,r.drawImage(this.img,(n-e)/2,(s-i)/2,e,i,0,0,this.assetData.w,this.assetData.h),this.img=t}},CVImageElement.prototype.renderInnerContent=function(){this.canvasContext.drawImage(this.img,0,0)},CVImageElement.prototype.destroy=function(){this.img=null},extendPrototype([BaseElement,TransformElement,CVBaseElement,HierarchyElement,FrameElement,RenderableElement],CVSolidElement),CVSolidElement.prototype.initElement=SVGShapeElement.prototype.initElement,CVSolidElement.prototype.prepareFrame=IImageElement.prototype.prepareFrame,CVSolidElement.prototype.renderInnerContent=function(){var t=this.canvasContext;t.fillStyle=this.data.sc,t.fillRect(0,0,this.data.sw,this.data.sh)},extendPrototype([BaseRenderer],CanvasRendererBase),CanvasRendererBase.prototype.createShape=function(t){return new CVShapeElement(t,this.globalData,this)},CanvasRendererBase.prototype.createText=function(t){return new CVTextElement(t,this.globalData,this)},CanvasRendererBase.prototype.createImage=function(t){return new CVImageElement(t,this.globalData,this)},CanvasRendererBase.prototype.createSolid=function(t){return new CVSolidElement(t,this.globalData,this)},CanvasRendererBase.prototype.createNull=SVGRenderer.prototype.createNull,CanvasRendererBase.prototype.ctxTransform=function(t){if(1!==t[0]||0!==t[1]||0!==t[4]||1!==t[5]||0!==t[12]||0!==t[13])if(this.renderConfig.clearCanvas){this.transformMat.cloneFromProps(t);var e=this.contextData.cTr.props;this.transformMat.transform(e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8],e[9],e[10],e[11],e[12],e[13],e[14],e[15]),this.contextData.cTr.cloneFromProps(this.transformMat.props);var i=this.contextData.cTr.props;this.canvasContext.setTransform(i[0],i[1],i[4],i[5],i[12],i[13])}else this.canvasContext.transform(t[0],t[1],t[4],t[5],t[12],t[13])},CanvasRendererBase.prototype.ctxOpacity=function(t){if(!this.renderConfig.clearCanvas)return this.canvasContext.globalAlpha*=t<0?0:t,void(this.globalData.currentGlobalAlpha=this.contextData.cO);this.contextData.cO*=t<0?0:t,this.globalData.currentGlobalAlpha!==this.contextData.cO&&(this.canvasContext.globalAlpha=this.contextData.cO,this.globalData.currentGlobalAlpha=this.contextData.cO)},CanvasRendererBase.prototype.reset=function(){this.renderConfig.clearCanvas?this.contextData.reset():this.canvasContext.restore()},CanvasRendererBase.prototype.save=function(t){if(this.renderConfig.clearCanvas){t&&this.canvasContext.save();var e,i=this.contextData.cTr.props;this.contextData._length<=this.contextData.cArrPos&&this.contextData.duplicate();var r=this.contextData.saved[this.contextData.cArrPos];for(e=0;e<16;e+=1)r[e]=i[e];this.contextData.savedOp[this.contextData.cArrPos]=this.contextData.cO,this.contextData.cArrPos+=1}else this.canvasContext.save()},CanvasRendererBase.prototype.restore=function(t){if(this.renderConfig.clearCanvas){t&&(this.canvasContext.restore(),this.globalData.blendMode="source-over"),this.contextData.cArrPos-=1;var e,i=this.contextData.saved[this.contextData.cArrPos],r=this.contextData.cTr.props;for(e=0;e<16;e+=1)r[e]=i[e];this.canvasContext.setTransform(i[0],i[1],i[4],i[5],i[12],i[13]),i=this.contextData.savedOp[this.contextData.cArrPos],this.contextData.cO=i,this.globalData.currentGlobalAlpha!==i&&(this.canvasContext.globalAlpha=i,this.globalData.currentGlobalAlpha=i)}else this.canvasContext.restore()},CanvasRendererBase.prototype.configAnimation=function(t){if(this.animationItem.wrapper){this.animationItem.container=createTag("canvas");var e=this.animationItem.container.style;e.width="100%",e.height="100%";var i="0px 0px 0px";e.transformOrigin=i,e.mozTransformOrigin=i,e.webkitTransformOrigin=i,e["-webkit-transform"]=i,e.contentVisibility=this.renderConfig.contentVisibility,this.animationItem.wrapper.appendChild(this.animationItem.container),this.canvasContext=this.animationItem.container.getContext("2d"),this.renderConfig.className&&this.animationItem.container.setAttribute("class",this.renderConfig.className),this.renderConfig.id&&this.animationItem.container.setAttribute("id",this.renderConfig.id)}else this.canvasContext=this.renderConfig.context;this.data=t,this.layers=t.layers,this.transformCanvas={w:t.w,h:t.h,sx:0,sy:0,tx:0,ty:0},this.setupGlobalData(t,document.body),this.globalData.canvasContext=this.canvasContext,this.globalData.renderer=this,this.globalData.isDashed=!1,this.globalData.progressiveLoad=this.renderConfig.progressiveLoad,this.globalData.transformCanvas=this.transformCanvas,this.elements=createSizedArray(t.layers.length),this.updateContainerSize()},CanvasRendererBase.prototype.updateContainerSize=function(t,e){var i,r,n,s;if(this.reset(),t?(i=t,r=e,this.canvasContext.canvas.width=i,this.canvasContext.canvas.height=r):(this.animationItem.wrapper&&this.animationItem.container?(i=this.animationItem.wrapper.offsetWidth,r=this.animationItem.wrapper.offsetHeight):(i=this.canvasContext.canvas.width,r=this.canvasContext.canvas.height),this.canvasContext.canvas.width=i*this.renderConfig.dpr,this.canvasContext.canvas.height=r*this.renderConfig.dpr),-1!==this.renderConfig.preserveAspectRatio.indexOf("meet")||-1!==this.renderConfig.preserveAspectRatio.indexOf("slice")){var a=this.renderConfig.preserveAspectRatio.split(" "),o=a[1]||"meet",h=a[0]||"xMidYMid",l=h.substr(0,4),p=h.substr(4);n=i/r,(s=this.transformCanvas.w/this.transformCanvas.h)>n&&"meet"===o||s<n&&"slice"===o?(this.transformCanvas.sx=i/(this.transformCanvas.w/this.renderConfig.dpr),this.transformCanvas.sy=i/(this.transformCanvas.w/this.renderConfig.dpr)):(this.transformCanvas.sx=r/(this.transformCanvas.h/this.renderConfig.dpr),this.transformCanvas.sy=r/(this.transformCanvas.h/this.renderConfig.dpr)),this.transformCanvas.tx="xMid"===l&&(s<n&&"meet"===o||s>n&&"slice"===o)?(i-this.transformCanvas.w*(r/this.transformCanvas.h))/2*this.renderConfig.dpr:"xMax"===l&&(s<n&&"meet"===o||s>n&&"slice"===o)?(i-this.transformCanvas.w*(r/this.transformCanvas.h))*this.renderConfig.dpr:0,this.transformCanvas.ty="YMid"===p&&(s>n&&"meet"===o||s<n&&"slice"===o)?(r-this.transformCanvas.h*(i/this.transformCanvas.w))/2*this.renderConfig.dpr:"YMax"===p&&(s>n&&"meet"===o||s<n&&"slice"===o)?(r-this.transformCanvas.h*(i/this.transformCanvas.w))*this.renderConfig.dpr:0}else"none"===this.renderConfig.preserveAspectRatio?(this.transformCanvas.sx=i/(this.transformCanvas.w/this.renderConfig.dpr),this.transformCanvas.sy=r/(this.transformCanvas.h/this.renderConfig.dpr),this.transformCanvas.tx=0,this.transformCanvas.ty=0):(this.transformCanvas.sx=this.renderConfig.dpr,this.transformCanvas.sy=this.renderConfig.dpr,this.transformCanvas.tx=0,this.transformCanvas.ty=0);this.transformCanvas.props=[this.transformCanvas.sx,0,0,0,0,this.transformCanvas.sy,0,0,0,0,1,0,this.transformCanvas.tx,this.transformCanvas.ty,0,1],this.ctxTransform(this.transformCanvas.props),this.canvasContext.beginPath(),this.canvasContext.rect(0,0,this.transformCanvas.w,this.transformCanvas.h),this.canvasContext.closePath(),this.canvasContext.clip(),this.renderFrame(this.renderedFrame,!0)},CanvasRendererBase.prototype.destroy=function(){var t;for(this.renderConfig.clearCanvas&&this.animationItem.wrapper&&(this.animationItem.wrapper.innerText=""),t=(this.layers?this.layers.length:0)-1;t>=0;t-=1)this.elements[t]&&this.elements[t].destroy();this.elements.length=0,this.globalData.canvasContext=null,this.animationItem.container=null,this.destroyed=!0},CanvasRendererBase.prototype.renderFrame=function(t,e){if((this.renderedFrame!==t||!0!==this.renderConfig.clearCanvas||e)&&!this.destroyed&&-1!==t){var i;this.renderedFrame=t,this.globalData.frameNum=t-this.animationItem._isFirstFrame,this.globalData.frameId+=1,this.globalData._mdf=!this.renderConfig.clearCanvas||e,this.globalData.projectInterface.currentFrame=t;var r=this.layers.length;for(this.completeLayers||this.checkLayers(t),i=0;i<r;i+=1)(this.completeLayers||this.elements[i])&&this.elements[i].prepareFrame(t-this.layers[i].st);if(this.globalData._mdf){for(!0===this.renderConfig.clearCanvas?this.canvasContext.clearRect(0,0,this.transformCanvas.w,this.transformCanvas.h):this.save(),i=r-1;i>=0;i-=1)(this.completeLayers||this.elements[i])&&this.elements[i].renderFrame();!0!==this.renderConfig.clearCanvas&&this.restore()}}},CanvasRendererBase.prototype.buildItem=function(t){var e=this.elements;if(!e[t]&&99!==this.layers[t].ty){var i=this.createItem(this.layers[t],this,this.globalData);e[t]=i,i.initExpressions()}},CanvasRendererBase.prototype.checkPendingElements=function(){for(;this.pendingElements.length;)this.pendingElements.pop().checkParenting()},CanvasRendererBase.prototype.hide=function(){this.animationItem.container.style.display="none"},CanvasRendererBase.prototype.show=function(){this.animationItem.container.style.display="block"},extendPrototype([CanvasRendererBase,ICompElement,CVBaseElement],CVCompElement),CVCompElement.prototype.renderInnerContent=function(){var t,e=this.canvasContext;for(e.beginPath(),e.moveTo(0,0),e.lineTo(this.data.w,0),e.lineTo(this.data.w,this.data.h),e.lineTo(0,this.data.h),e.lineTo(0,0),e.clip(),t=this.layers.length-1;t>=0;t-=1)(this.completeLayers||this.elements[t])&&this.elements[t].renderFrame()},CVCompElement.prototype.destroy=function(){var t;for(t=this.layers.length-1;t>=0;t-=1)this.elements[t]&&this.elements[t].destroy();this.layers=null,this.elements=null},CVCompElement.prototype.createComp=function(t){return new CVCompElement(t,this.globalData,this)},extendPrototype([CanvasRendererBase],CanvasRenderer),CanvasRenderer.prototype.createComp=function(t){return new CVCompElement(t,this.globalData,this)},HBaseElement.prototype={checkBlendMode:function(){},initRendererElement:function(){this.baseElement=createTag(this.data.tg||"div"),this.data.hasMask?(this.svgElement=createNS("svg"),this.layerElement=createNS("g"),this.maskedElement=this.layerElement,this.svgElement.appendChild(this.layerElement),this.baseElement.appendChild(this.svgElement)):this.layerElement=this.baseElement,styleDiv(this.baseElement)},createContainerElements:function(){this.renderableEffectsManager=new CVEffects(this),this.transformedElement=this.baseElement,this.maskedElement=this.layerElement,this.data.ln&&this.layerElement.setAttribute("id",this.data.ln),this.data.cl&&this.layerElement.setAttribute("class",this.data.cl),0!==this.data.bm&&this.setBlendMode()},renderElement:function(){var t=this.transformedElement?this.transformedElement.style:{};if(this.finalTransform._matMdf){var e=this.finalTransform.mat.toCSS();t.transform=e,t.webkitTransform=e}this.finalTransform._opMdf&&(t.opacity=this.finalTransform.mProp.o.v)},renderFrame:function(){this.data.hd||this.hidden||(this.renderTransform(),this.renderRenderable(),this.renderElement(),this.renderInnerContent(),this._isFirstFrame&&(this._isFirstFrame=!1))},destroy:function(){this.layerElement=null,this.transformedElement=null,this.matteElement&&(this.matteElement=null),this.maskManager&&(this.maskManager.destroy(),this.maskManager=null)},createRenderableComponents:function(){this.maskManager=new MaskElement(this.data,this,this.globalData)},addEffects:function(){},setMatte:function(){}},HBaseElement.prototype.getBaseElement=SVGBaseElement.prototype.getBaseElement,HBaseElement.prototype.destroyBaseElement=HBaseElement.prototype.destroy,HBaseElement.prototype.buildElementParenting=BaseRenderer.prototype.buildElementParenting,extendPrototype([BaseElement,TransformElement,HBaseElement,HierarchyElement,FrameElement,RenderableDOMElement],HSolidElement),HSolidElement.prototype.createContent=function(){var t;this.data.hasMask?((t=createNS("rect")).setAttribute("width",this.data.sw),t.setAttribute("height",this.data.sh),t.setAttribute("fill",this.data.sc),this.svgElement.setAttribute("width",this.data.sw),this.svgElement.setAttribute("height",this.data.sh)):((t=createTag("div")).style.width=this.data.sw+"px",t.style.height=this.data.sh+"px",t.style.backgroundColor=this.data.sc),this.layerElement.appendChild(t)},extendPrototype([BaseElement,TransformElement,HSolidElement,SVGShapeElement,HBaseElement,HierarchyElement,FrameElement,RenderableElement],HShapeElement),HShapeElement.prototype._renderShapeFrame=HShapeElement.prototype.renderInnerContent,HShapeElement.prototype.createContent=function(){var t;if(this.baseElement.style.fontSize=0,this.data.hasMask)this.layerElement.appendChild(this.shapesContainer),t=this.svgElement;else{t=createNS("svg");var e=this.comp.data?this.comp.data:this.globalData.compSize;t.setAttribute("width",e.w),t.setAttribute("height",e.h),t.appendChild(this.shapesContainer),this.layerElement.appendChild(t)}this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,this.shapesContainer,0,[],!0),this.filterUniqueShapes(),this.shapeCont=t},HShapeElement.prototype.getTransformedPoint=function(t,e){var i,r=t.length;for(i=0;i<r;i+=1)e=t[i].mProps.v.applyToPointArray(e[0],e[1],0);return e},HShapeElement.prototype.calculateShapeBoundingBox=function(t,e){var i,r,n,s,a,o=t.sh.v,h=t.transformers,l=o._length;if(!(l<=1)){for(i=0;i<l-1;i+=1)r=this.getTransformedPoint(h,o.v[i]),n=this.getTransformedPoint(h,o.o[i]),s=this.getTransformedPoint(h,o.i[i+1]),a=this.getTransformedPoint(h,o.v[i+1]),this.checkBounds(r,n,s,a,e);o.c&&(r=this.getTransformedPoint(h,o.v[i]),n=this.getTransformedPoint(h,o.o[i]),s=this.getTransformedPoint(h,o.i[0]),a=this.getTransformedPoint(h,o.v[0]),this.checkBounds(r,n,s,a,e))}},HShapeElement.prototype.checkBounds=function(t,e,i,r,n){this.getBoundsOfCurve(t,e,i,r);var s=this.shapeBoundingBox;n.x=bmMin(s.left,n.x),n.xMax=bmMax(s.right,n.xMax),n.y=bmMin(s.top,n.y),n.yMax=bmMax(s.bottom,n.yMax)},HShapeElement.prototype.shapeBoundingBox={left:0,right:0,top:0,bottom:0},HShapeElement.prototype.tempBoundingBox={x:0,xMax:0,y:0,yMax:0,width:0,height:0},HShapeElement.prototype.getBoundsOfCurve=function(t,e,i,r){for(var n,s,a,o,h,l,p,c=[[t[0],r[0]],[t[1],r[1]]],d=0;d<2;++d)s=6*t[d]-12*e[d]+6*i[d],n=-3*t[d]+9*e[d]-9*i[d]+3*r[d],a=3*e[d]-3*t[d],s|=0,a|=0,0==(n|=0)&&0===s||(0===n?(o=-a/s)>0&&o<1&&c[d].push(this.calculateF(o,t,e,i,r,d)):(h=s*s-4*a*n)>=0&&((l=(-s+bmSqrt(h))/(2*n))>0&&l<1&&c[d].push(this.calculateF(l,t,e,i,r,d)),(p=(-s-bmSqrt(h))/(2*n))>0&&p<1&&c[d].push(this.calculateF(p,t,e,i,r,d))));this.shapeBoundingBox.left=bmMin.apply(null,c[0]),this.shapeBoundingBox.top=bmMin.apply(null,c[1]),this.shapeBoundingBox.right=bmMax.apply(null,c[0]),this.shapeBoundingBox.bottom=bmMax.apply(null,c[1])},HShapeElement.prototype.calculateF=function(t,e,i,r,n,s){return bmPow(1-t,3)*e[s]+3*bmPow(1-t,2)*t*i[s]+3*(1-t)*bmPow(t,2)*r[s]+bmPow(t,3)*n[s]},HShapeElement.prototype.calculateBoundingBox=function(t,e){var i,r=t.length;for(i=0;i<r;i+=1)t[i]&&t[i].sh?this.calculateShapeBoundingBox(t[i],e):t[i]&&t[i].it?this.calculateBoundingBox(t[i].it,e):t[i]&&t[i].style&&t[i].w&&this.expandStrokeBoundingBox(t[i].w,e)},HShapeElement.prototype.expandStrokeBoundingBox=function(t,e){var i=0;if(t.keyframes){for(var r=0;r<t.keyframes.length;r+=1){var n=t.keyframes[r].s;n>i&&(i=n)}i*=t.mult}else i=t.v*t.mult;e.x-=i,e.xMax+=i,e.y-=i,e.yMax+=i},HShapeElement.prototype.currentBoxContains=function(t){return this.currentBBox.x<=t.x&&this.currentBBox.y<=t.y&&this.currentBBox.width+this.currentBBox.x>=t.x+t.width&&this.currentBBox.height+this.currentBBox.y>=t.y+t.height},HShapeElement.prototype.renderInnerContent=function(){if(this._renderShapeFrame(),!this.hidden&&(this._isFirstFrame||this._mdf)){var t=this.tempBoundingBox,e=999999;if(t.x=e,t.xMax=-e,t.y=e,t.yMax=-e,this.calculateBoundingBox(this.itemsData,t),t.width=t.xMax<t.x?0:t.xMax-t.x,t.height=t.yMax<t.y?0:t.yMax-t.y,this.currentBoxContains(t))return;var i=!1;if(this.currentBBox.w!==t.width&&(this.currentBBox.w=t.width,this.shapeCont.setAttribute("width",t.width),i=!0),this.currentBBox.h!==t.height&&(this.currentBBox.h=t.height,this.shapeCont.setAttribute("height",t.height),i=!0),i||this.currentBBox.x!==t.x||this.currentBBox.y!==t.y){this.currentBBox.w=t.width,this.currentBBox.h=t.height,this.currentBBox.x=t.x,this.currentBBox.y=t.y,this.shapeCont.setAttribute("viewBox",this.currentBBox.x+" "+this.currentBBox.y+" "+this.currentBBox.w+" "+this.currentBBox.h);var r=this.shapeCont.style,n="translate("+this.currentBBox.x+"px,"+this.currentBBox.y+"px)";r.transform=n,r.webkitTransform=n}}},extendPrototype([BaseElement,TransformElement,HBaseElement,HierarchyElement,FrameElement,RenderableDOMElement,ITextElement],HTextElement),HTextElement.prototype.createContent=function(){if(this.isMasked=this.checkMasks(),this.isMasked){this.renderType="svg",this.compW=this.comp.data.w,this.compH=this.comp.data.h,this.svgElement.setAttribute("width",this.compW),this.svgElement.setAttribute("height",this.compH);var t=createNS("g");this.maskedElement.appendChild(t),this.innerElem=t}else this.renderType="html",this.innerElem=this.layerElement;this.checkParenting()},HTextElement.prototype.buildNewText=function(){var t=this.textProperty.currentData;this.renderedLetters=createSizedArray(t.l?t.l.length:0);var e=this.innerElem.style,i=t.fc?this.buildColor(t.fc):"rgba(0,0,0,0)";e.fill=i,e.color=i,t.sc&&(e.stroke=this.buildColor(t.sc),e.strokeWidth=t.sw+"px");var r,n,s=this.globalData.fontManager.getFontByName(t.f);if(!this.globalData.fontManager.chars)if(e.fontSize=t.finalSize+"px",e.lineHeight=t.finalSize+"px",s.fClass)this.innerElem.className=s.fClass;else{e.fontFamily=s.fFamily;var a=t.fWeight,o=t.fStyle;e.fontStyle=o,e.fontWeight=a}var h,l,p,c=t.l;n=c.length;var d,f=this.mHelper,u="",m=0;for(r=0;r<n;r+=1){if(this.globalData.fontManager.chars?(this.textPaths[m]?h=this.textPaths[m]:((h=createNS("path")).setAttribute("stroke-linecap",lineCapEnum[1]),h.setAttribute("stroke-linejoin",lineJoinEnum[2]),h.setAttribute("stroke-miterlimit","4")),this.isMasked||(this.textSpans[m]?p=(l=this.textSpans[m]).children[0]:((l=createTag("div")).style.lineHeight=0,(p=createNS("svg")).appendChild(h),styleDiv(l)))):this.isMasked?h=this.textPaths[m]?this.textPaths[m]:createNS("text"):this.textSpans[m]?(l=this.textSpans[m],h=this.textPaths[m]):(styleDiv(l=createTag("span")),styleDiv(h=createTag("span")),l.appendChild(h)),this.globalData.fontManager.chars){var y,g=this.globalData.fontManager.getCharData(t.finalText[r],s.fStyle,this.globalData.fontManager.getFontByName(t.f).fFamily);if(y=g?g.data:null,f.reset(),y&&y.shapes&&y.shapes.length&&(d=y.shapes[0].it,f.scale(t.finalSize/100,t.finalSize/100),u=this.createPathShape(f,d),h.setAttribute("d",u)),this.isMasked)this.innerElem.appendChild(h);else{if(this.innerElem.appendChild(l),y&&y.shapes){document.body.appendChild(p);var v=p.getBBox();p.setAttribute("width",v.width+2),p.setAttribute("height",v.height+2),p.setAttribute("viewBox",v.x-1+" "+(v.y-1)+" "+(v.width+2)+" "+(v.height+2));var b=p.style,_="translate("+(v.x-1)+"px,"+(v.y-1)+"px)";b.transform=_,b.webkitTransform=_,c[r].yOffset=v.y-1}else p.setAttribute("width",1),p.setAttribute("height",1);l.appendChild(p)}}else if(h.textContent=c[r].val,h.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),this.isMasked)this.innerElem.appendChild(h);else{this.innerElem.appendChild(l);var E=h.style,S="translate3d(0,"+-t.finalSize/1.2+"px,0)";E.transform=S,E.webkitTransform=S}this.isMasked?this.textSpans[m]=h:this.textSpans[m]=l,this.textSpans[m].style.display="block",this.textPaths[m]=h,m+=1}for(;m<this.textSpans.length;)this.textSpans[m].style.display="none",m+=1},HTextElement.prototype.renderInnerContent=function(){var t;if(this.data.singleShape){if(!this._isFirstFrame&&!this.lettersChangedFlag)return;if(this.isMasked&&this.finalTransform._matMdf){this.svgElement.setAttribute("viewBox",-this.finalTransform.mProp.p.v[0]+" "+-this.finalTransform.mProp.p.v[1]+" "+this.compW+" "+this.compH),t=this.svgElement.style;var e="translate("+-this.finalTransform.mProp.p.v[0]+"px,"+-this.finalTransform.mProp.p.v[1]+"px)";t.transform=e,t.webkitTransform=e}}if(this.textAnimator.getMeasures(this.textProperty.currentData,this.lettersChangedFlag),this.lettersChangedFlag||this.textAnimator.lettersChangedFlag){var i,r,n,s,a,o=0,h=this.textAnimator.renderedLetters,l=this.textProperty.currentData.l;for(r=l.length,i=0;i<r;i+=1)l[i].n?o+=1:(s=this.textSpans[i],a=this.textPaths[i],n=h[o],o+=1,n._mdf.m&&(this.isMasked?s.setAttribute("transform",n.m):(s.style.webkitTransform=n.m,s.style.transform=n.m)),s.style.opacity=n.o,n.sw&&n._mdf.sw&&a.setAttribute("stroke-width",n.sw),n.sc&&n._mdf.sc&&a.setAttribute("stroke",n.sc),n.fc&&n._mdf.fc&&(a.setAttribute("fill",n.fc),a.style.color=n.fc));if(this.innerElem.getBBox&&!this.hidden&&(this._isFirstFrame||this._mdf)){var p=this.innerElem.getBBox();if(this.currentBBox.w!==p.width&&(this.currentBBox.w=p.width,this.svgElement.setAttribute("width",p.width)),this.currentBBox.h!==p.height&&(this.currentBBox.h=p.height,this.svgElement.setAttribute("height",p.height)),this.currentBBox.w!==p.width+2||this.currentBBox.h!==p.height+2||this.currentBBox.x!==p.x-1||this.currentBBox.y!==p.y-1){this.currentBBox.w=p.width+2,this.currentBBox.h=p.height+2,this.currentBBox.x=p.x-1,this.currentBBox.y=p.y-1,this.svgElement.setAttribute("viewBox",this.currentBBox.x+" "+this.currentBBox.y+" "+this.currentBBox.w+" "+this.currentBBox.h),t=this.svgElement.style;var c="translate("+this.currentBBox.x+"px,"+this.currentBBox.y+"px)";t.transform=c,t.webkitTransform=c}}}},extendPrototype([BaseElement,FrameElement,HierarchyElement],HCameraElement),HCameraElement.prototype.setup=function(){var t,e,i,r,n=this.comp.threeDElements.length;for(t=0;t<n;t+=1)if("3d"===(e=this.comp.threeDElements[t]).type){i=e.perspectiveElem.style,r=e.container.style;var s=this.pe.v+"px",a="0px 0px 0px",o="matrix3d(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1)";i.perspective=s,i.webkitPerspective=s,r.transformOrigin=a,r.mozTransformOrigin=a,r.webkitTransformOrigin=a,i.transform=o,i.webkitTransform=o}},HCameraElement.prototype.createElements=function(){},HCameraElement.prototype.hide=function(){},HCameraElement.prototype.renderFrame=function(){var t,e,i=this._isFirstFrame;if(this.hierarchy)for(e=this.hierarchy.length,t=0;t<e;t+=1)i=this.hierarchy[t].finalTransform.mProp._mdf||i;if(i||this.pe._mdf||this.p&&this.p._mdf||this.px&&(this.px._mdf||this.py._mdf||this.pz._mdf)||this.rx._mdf||this.ry._mdf||this.rz._mdf||this.or._mdf||this.a&&this.a._mdf){if(this.mat.reset(),this.hierarchy)for(t=e=this.hierarchy.length-1;t>=0;t-=1){var r=this.hierarchy[t].finalTransform.mProp;this.mat.translate(-r.p.v[0],-r.p.v[1],r.p.v[2]),this.mat.rotateX(-r.or.v[0]).rotateY(-r.or.v[1]).rotateZ(r.or.v[2]),this.mat.rotateX(-r.rx.v).rotateY(-r.ry.v).rotateZ(r.rz.v),this.mat.scale(1/r.s.v[0],1/r.s.v[1],1/r.s.v[2]),this.mat.translate(r.a.v[0],r.a.v[1],r.a.v[2])}if(this.p?this.mat.translate(-this.p.v[0],-this.p.v[1],this.p.v[2]):this.mat.translate(-this.px.v,-this.py.v,this.pz.v),this.a){var n;n=this.p?[this.p.v[0]-this.a.v[0],this.p.v[1]-this.a.v[1],this.p.v[2]-this.a.v[2]]:[this.px.v-this.a.v[0],this.py.v-this.a.v[1],this.pz.v-this.a.v[2]];var s=Math.sqrt(Math.pow(n[0],2)+Math.pow(n[1],2)+Math.pow(n[2],2)),a=[n[0]/s,n[1]/s,n[2]/s],o=Math.sqrt(a[2]*a[2]+a[0]*a[0]),h=Math.atan2(a[1],o),l=Math.atan2(a[0],-a[2]);this.mat.rotateY(l).rotateX(-h)}this.mat.rotateX(-this.rx.v).rotateY(-this.ry.v).rotateZ(this.rz.v),this.mat.rotateX(-this.or.v[0]).rotateY(-this.or.v[1]).rotateZ(this.or.v[2]),this.mat.translate(this.globalData.compSize.w/2,this.globalData.compSize.h/2,0),this.mat.translate(0,0,this.pe.v);var p=!this._prevMat.equals(this.mat);if((p||this.pe._mdf)&&this.comp.threeDElements){var c,d,f;for(e=this.comp.threeDElements.length,t=0;t<e;t+=1)if("3d"===(c=this.comp.threeDElements[t]).type){if(p){var u=this.mat.toCSS();(f=c.container.style).transform=u,f.webkitTransform=u}this.pe._mdf&&((d=c.perspectiveElem.style).perspective=this.pe.v+"px",d.webkitPerspective=this.pe.v+"px")}this.mat.clone(this._prevMat)}}this._isFirstFrame=!1},HCameraElement.prototype.prepareFrame=function(t){this.prepareProperties(t,!0)},HCameraElement.prototype.destroy=function(){},HCameraElement.prototype.getBaseElement=function(){return null},extendPrototype([BaseElement,TransformElement,HBaseElement,HSolidElement,HierarchyElement,FrameElement,RenderableElement],HImageElement),HImageElement.prototype.createContent=function(){var t=this.globalData.getAssetsPath(this.assetData),e=new Image;this.data.hasMask?(this.imageElem=createNS("image"),this.imageElem.setAttribute("width",this.assetData.w+"px"),this.imageElem.setAttribute("height",this.assetData.h+"px"),this.imageElem.setAttributeNS("http://www.w3.org/1999/xlink","href",t),this.layerElement.appendChild(this.imageElem),this.baseElement.setAttribute("width",this.assetData.w),this.baseElement.setAttribute("height",this.assetData.h)):this.layerElement.appendChild(e),e.crossOrigin="anonymous",e.src=t,this.data.ln&&this.baseElement.setAttribute("id",this.data.ln)},extendPrototype([BaseRenderer],HybridRendererBase),HybridRendererBase.prototype.buildItem=SVGRenderer.prototype.buildItem,HybridRendererBase.prototype.checkPendingElements=function(){for(;this.pendingElements.length;)this.pendingElements.pop().checkParenting()},HybridRendererBase.prototype.appendElementInPos=function(t,e){var i=t.getBaseElement();if(i){var r=this.layers[e];if(r.ddd&&this.supports3d)this.addTo3dContainer(i,e);else if(this.threeDElements)this.addTo3dContainer(i,e);else{for(var n,s,a=0;a<e;)this.elements[a]&&!0!==this.elements[a]&&this.elements[a].getBaseElement&&(s=this.elements[a],n=(this.layers[a].ddd?this.getThreeDContainerByPos(a):s.getBaseElement())||n),a+=1;n?r.ddd&&this.supports3d||this.layerElement.insertBefore(i,n):r.ddd&&this.supports3d||this.layerElement.appendChild(i)}}},HybridRendererBase.prototype.createShape=function(t){return this.supports3d?new HShapeElement(t,this.globalData,this):new SVGShapeElement(t,this.globalData,this)},HybridRendererBase.prototype.createText=function(t){return this.supports3d?new HTextElement(t,this.globalData,this):new SVGTextLottieElement(t,this.globalData,this)},HybridRendererBase.prototype.createCamera=function(t){return this.camera=new HCameraElement(t,this.globalData,this),this.camera},HybridRendererBase.prototype.createImage=function(t){return this.supports3d?new HImageElement(t,this.globalData,this):new IImageElement(t,this.globalData,this)},HybridRendererBase.prototype.createSolid=function(t){return this.supports3d?new HSolidElement(t,this.globalData,this):new ISolidElement(t,this.globalData,this)},HybridRendererBase.prototype.createNull=SVGRenderer.prototype.createNull,HybridRendererBase.prototype.getThreeDContainerByPos=function(t){for(var e=0,i=this.threeDElements.length;e<i;){if(this.threeDElements[e].startPos<=t&&this.threeDElements[e].endPos>=t)return this.threeDElements[e].perspectiveElem;e+=1}return null},HybridRendererBase.prototype.createThreeDContainer=function(t,e){var i,r,n=createTag("div");styleDiv(n);var s=createTag("div");if(styleDiv(s),"3d"===e){(i=n.style).width=this.globalData.compSize.w+"px",i.height=this.globalData.compSize.h+"px";var a="50% 50%";i.webkitTransformOrigin=a,i.mozTransformOrigin=a,i.transformOrigin=a;var o="matrix3d(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1)";(r=s.style).transform=o,r.webkitTransform=o}n.appendChild(s);var h={container:s,perspectiveElem:n,startPos:t,endPos:t,type:e};return this.threeDElements.push(h),h},HybridRendererBase.prototype.build3dContainers=function(){var t,e,i=this.layers.length,r="";for(t=0;t<i;t+=1)this.layers[t].ddd&&3!==this.layers[t].ty?("3d"!==r&&(r="3d",e=this.createThreeDContainer(t,"3d")),e.endPos=Math.max(e.endPos,t)):("2d"!==r&&(r="2d",e=this.createThreeDContainer(t,"2d")),e.endPos=Math.max(e.endPos,t));for(t=(i=this.threeDElements.length)-1;t>=0;t-=1)this.resizerElem.appendChild(this.threeDElements[t].perspectiveElem)},HybridRendererBase.prototype.addTo3dContainer=function(t,e){for(var i=0,r=this.threeDElements.length;i<r;){if(e<=this.threeDElements[i].endPos){for(var n,s=this.threeDElements[i].startPos;s<e;)this.elements[s]&&this.elements[s].getBaseElement&&(n=this.elements[s].getBaseElement()),s+=1;n?this.threeDElements[i].container.insertBefore(t,n):this.threeDElements[i].container.appendChild(t);break}i+=1}},HybridRendererBase.prototype.configAnimation=function(t){var e=createTag("div"),i=this.animationItem.wrapper,r=e.style;r.width=t.w+"px",r.height=t.h+"px",this.resizerElem=e,styleDiv(e),r.transformStyle="flat",r.mozTransformStyle="flat",r.webkitTransformStyle="flat",this.renderConfig.className&&e.setAttribute("class",this.renderConfig.className),i.appendChild(e),r.overflow="hidden";var n=createNS("svg");n.setAttribute("width","1"),n.setAttribute("height","1"),styleDiv(n),this.resizerElem.appendChild(n);var s=createNS("defs");n.appendChild(s),this.data=t,this.setupGlobalData(t,n),this.globalData.defs=s,this.layers=t.layers,this.layerElement=this.resizerElem,this.build3dContainers(),this.updateContainerSize()},HybridRendererBase.prototype.destroy=function(){var t;this.animationItem.wrapper&&(this.animationItem.wrapper.innerText=""),this.animationItem.container=null,this.globalData.defs=null;var e=this.layers?this.layers.length:0;for(t=0;t<e;t+=1)this.elements[t].destroy();this.elements.length=0,this.destroyed=!0,this.animationItem=null},HybridRendererBase.prototype.updateContainerSize=function(){var t,e,i,r,n=this.animationItem.wrapper.offsetWidth,s=this.animationItem.wrapper.offsetHeight,a=n/s;this.globalData.compSize.w/this.globalData.compSize.h>a?(t=n/this.globalData.compSize.w,e=n/this.globalData.compSize.w,i=0,r=(s-this.globalData.compSize.h*(n/this.globalData.compSize.w))/2):(t=s/this.globalData.compSize.h,e=s/this.globalData.compSize.h,i=(n-this.globalData.compSize.w*(s/this.globalData.compSize.h))/2,r=0);var o=this.resizerElem.style;o.webkitTransform="matrix3d("+t+",0,0,0,0,"+e+",0,0,0,0,1,0,"+i+","+r+",0,1)",o.transform=o.webkitTransform},HybridRendererBase.prototype.renderFrame=SVGRenderer.prototype.renderFrame,HybridRendererBase.prototype.hide=function(){this.resizerElem.style.display="none"},HybridRendererBase.prototype.show=function(){this.resizerElem.style.display="block"},HybridRendererBase.prototype.initItems=function(){if(this.buildAllItems(),this.camera)this.camera.setup();else{var t,e=this.globalData.compSize.w,i=this.globalData.compSize.h,r=this.threeDElements.length;for(t=0;t<r;t+=1){var n=this.threeDElements[t].perspectiveElem.style;n.webkitPerspective=Math.sqrt(Math.pow(e,2)+Math.pow(i,2))+"px",n.perspective=n.webkitPerspective}}},HybridRendererBase.prototype.searchExtraCompositions=function(t){var e,i=t.length,r=createTag("div");for(e=0;e<i;e+=1)if(t[e].xt){var n=this.createComp(t[e],r,this.globalData.comp,null);n.initExpressions(),this.globalData.projectInterface.registerComposition(n)}},extendPrototype([HybridRendererBase,ICompElement,HBaseElement],HCompElement),HCompElement.prototype._createBaseContainerElements=HCompElement.prototype.createContainerElements,HCompElement.prototype.createContainerElements=function(){this._createBaseContainerElements(),this.data.hasMask?(this.svgElement.setAttribute("width",this.data.w),this.svgElement.setAttribute("height",this.data.h),this.transformedElement=this.baseElement):this.transformedElement=this.layerElement},HCompElement.prototype.addTo3dContainer=function(t,e){for(var i,r=0;r<e;)this.elements[r]&&this.elements[r].getBaseElement&&(i=this.elements[r].getBaseElement()),r+=1;i?this.layerElement.insertBefore(t,i):this.layerElement.appendChild(t)},HCompElement.prototype.createComp=function(t){return this.supports3d?new HCompElement(t,this.globalData,this):new SVGCompElement(t,this.globalData,this)},extendPrototype([HybridRendererBase],HybridRenderer),HybridRenderer.prototype.createComp=function(t){return this.supports3d?new HCompElement(t,this.globalData,this):new SVGCompElement(t,this.globalData,this)};var CompExpressionInterface=function(t){function e(e){for(var i=0,r=t.layers.length;i<r;){if(t.layers[i].nm===e||t.layers[i].ind===e)return t.elements[i].layerInterface;i+=1}return null}return Object.defineProperty(e,"_name",{value:t.data.nm}),e.layer=e,e.pixelAspect=1,e.height=t.data.h||t.globalData.compSize.h,e.width=t.data.w||t.globalData.compSize.w,e.pixelAspect=1,e.frameDuration=1/t.globalData.frameRate,e.displayStartTime=0,e.numLayers=t.layers.length,e},Expressions=function(){var t={initExpressions:function(t){var e=0,i=[];t.renderer.compInterface=CompExpressionInterface(t.renderer),t.renderer.globalData.projectInterface.registerComposition(t.renderer),t.renderer.globalData.pushExpression=function(){e+=1},t.renderer.globalData.popExpression=function(){0==(e-=1)&&function(){var t,e=i.length;for(t=0;t<e;t+=1)i[t].release();i.length=0}()},t.renderer.globalData.registerExpressionProperty=function(t){-1===i.indexOf(t)&&i.push(t)}}};return t}(),MaskManagerInterface=function(){function t(t,e){this._mask=t,this._data=e}return Object.defineProperty(t.prototype,"maskPath",{get:function(){return this._mask.prop.k&&this._mask.prop.getValue(),this._mask.prop}}),Object.defineProperty(t.prototype,"maskOpacity",{get:function(){return this._mask.op.k&&this._mask.op.getValue(),100*this._mask.op.v}}),function(e){var i,r=createSizedArray(e.viewData.length),n=e.viewData.length;for(i=0;i<n;i+=1)r[i]=new t(e.viewData[i],e.masksProperties[i]);return function(t){for(i=0;i<n;){if(e.masksProperties[i].nm===t)return r[i];i+=1}return null}}}(),ExpressionPropertyInterface=function(){var t={pv:0,v:0,mult:1},e={pv:[0,0,0],v:[0,0,0],mult:1};function i(t,e,i){Object.defineProperty(t,"velocity",{get:function(){return e.getVelocityAtTime(e.comp.currentFrame)}}),t.numKeys=e.keyframes?e.keyframes.length:0,t.key=function(r){if(!t.numKeys)return 0;var n;n="s"in e.keyframes[r-1]?e.keyframes[r-1].s:"e"in e.keyframes[r-2]?e.keyframes[r-2].e:e.keyframes[r-2].s;var s="unidimensional"===i?new Number(n):Object.assign({},n);return s.time=e.keyframes[r-1].t/e.elem.comp.globalData.frameRate,s.value="unidimensional"===i?n[0]:n,s},t.valueAtTime=e.getValueAtTime,t.speedAtTime=e.getSpeedAtTime,t.velocityAtTime=e.getVelocityAtTime,t.propertyGroup=e.propertyGroup}function r(){return t}return function(n){return n?"unidimensional"===n.propType?function(e){e&&"pv"in e||(e=t);var r=1/e.mult,n=e.pv*r,s=new Number(n);return s.value=n,i(s,e,"unidimensional"),function(){return e.k&&e.getValue(),n=e.v*r,s.value!==n&&((s=new Number(n)).value=n,i(s,e,"unidimensional")),s}}(n):function(t){t&&"pv"in t||(t=e);var r=1/t.mult,n=t.data&&t.data.l||t.pv.length,s=createTypedArray("float32",n),a=createTypedArray("float32",n);return s.value=a,i(s,t,"multidimensional"),function(){t.k&&t.getValue();for(var e=0;e<n;e+=1)a[e]=t.v[e]*r,s[e]=a[e];return s}}(n):r}}(),TransformExpressionInterface=function(t){function e(t){switch(t){case"scale":case"Scale":case"ADBE Scale":case 6:return e.scale;case"rotation":case"Rotation":case"ADBE Rotation":case"ADBE Rotate Z":case 10:return e.rotation;case"ADBE Rotate X":return e.xRotation;case"ADBE Rotate Y":return e.yRotation;case"position":case"Position":case"ADBE Position":case 2:return e.position;case"ADBE Position_0":return e.xPosition;case"ADBE Position_1":return e.yPosition;case"ADBE Position_2":return e.zPosition;case"anchorPoint":case"AnchorPoint":case"Anchor Point":case"ADBE AnchorPoint":case 1:return e.anchorPoint;case"opacity":case"Opacity":case 11:return e.opacity;default:return null}}var i,r,n,s;return Object.defineProperty(e,"rotation",{get:ExpressionPropertyInterface(t.r||t.rz)}),Object.defineProperty(e,"zRotation",{get:ExpressionPropertyInterface(t.rz||t.r)}),Object.defineProperty(e,"xRotation",{get:ExpressionPropertyInterface(t.rx)}),Object.defineProperty(e,"yRotation",{get:ExpressionPropertyInterface(t.ry)}),Object.defineProperty(e,"scale",{get:ExpressionPropertyInterface(t.s)}),t.p?s=ExpressionPropertyInterface(t.p):(i=ExpressionPropertyInterface(t.px),r=ExpressionPropertyInterface(t.py),t.pz&&(n=ExpressionPropertyInterface(t.pz))),Object.defineProperty(e,"position",{get:function(){return t.p?s():[i(),r(),n?n():0]}}),Object.defineProperty(e,"xPosition",{get:ExpressionPropertyInterface(t.px)}),Object.defineProperty(e,"yPosition",{get:ExpressionPropertyInterface(t.py)}),Object.defineProperty(e,"zPosition",{get:ExpressionPropertyInterface(t.pz)}),Object.defineProperty(e,"anchorPoint",{get:ExpressionPropertyInterface(t.a)}),Object.defineProperty(e,"opacity",{get:ExpressionPropertyInterface(t.o)}),Object.defineProperty(e,"skew",{get:ExpressionPropertyInterface(t.sk)}),Object.defineProperty(e,"skewAxis",{get:ExpressionPropertyInterface(t.sa)}),Object.defineProperty(e,"orientation",{get:ExpressionPropertyInterface(t.or)}),e},LayerExpressionInterface=function(){function t(t){var e=new Matrix;return void 0!==t?this._elem.finalTransform.mProp.getValueAtTime(t).clone(e):this._elem.finalTransform.mProp.applyToMatrix(e),e}function e(t,e){var i=this.getMatrix(e);return i.props[12]=0,i.props[13]=0,i.props[14]=0,this.applyPoint(i,t)}function i(t,e){var i=this.getMatrix(e);return this.applyPoint(i,t)}function r(t,e){var i=this.getMatrix(e);return i.props[12]=0,i.props[13]=0,i.props[14]=0,this.invertPoint(i,t)}function n(t,e){var i=this.getMatrix(e);return this.invertPoint(i,t)}function s(t,e){if(this._elem.hierarchy&&this._elem.hierarchy.length){var i,r=this._elem.hierarchy.length;for(i=0;i<r;i+=1)this._elem.hierarchy[i].finalTransform.mProp.applyToMatrix(t)}return t.applyToPointArray(e[0],e[1],e[2]||0)}function a(t,e){if(this._elem.hierarchy&&this._elem.hierarchy.length){var i,r=this._elem.hierarchy.length;for(i=0;i<r;i+=1)this._elem.hierarchy[i].finalTransform.mProp.applyToMatrix(t)}return t.inversePoint(e)}function o(t){var e=new Matrix;if(e.reset(),this._elem.finalTransform.mProp.applyToMatrix(e),this._elem.hierarchy&&this._elem.hierarchy.length){var i,r=this._elem.hierarchy.length;for(i=0;i<r;i+=1)this._elem.hierarchy[i].finalTransform.mProp.applyToMatrix(e);return e.inversePoint(t)}return e.inversePoint(t)}function h(){return[1,1,1,1]}return function(l){var p;function c(t){switch(t){case"ADBE Root Vectors Group":case"Contents":case 2:return c.shapeInterface;case 1:case 6:case"Transform":case"transform":case"ADBE Transform Group":return p;case 4:case"ADBE Effect Parade":case"effects":case"Effects":return c.effect;case"ADBE Text Properties":return c.textInterface;default:return null}}c.getMatrix=t,c.invertPoint=a,c.applyPoint=s,c.toWorld=i,c.toWorldVec=e,c.fromWorld=n,c.fromWorldVec=r,c.toComp=i,c.fromComp=o,c.sampleImage=h,c.sourceRectAtTime=l.sourceRectAtTime.bind(l),c._elem=l;var d=getDescriptor(p=TransformExpressionInterface(l.finalTransform.mProp),"anchorPoint");return Object.defineProperties(c,{hasParent:{get:function(){return l.hierarchy.length}},parent:{get:function(){return l.hierarchy[0].layerInterface}},rotation:getDescriptor(p,"rotation"),scale:getDescriptor(p,"scale"),position:getDescriptor(p,"position"),opacity:getDescriptor(p,"opacity"),anchorPoint:d,anchor_point:d,transform:{get:function(){return p}},active:{get:function(){return l.isInRange}}}),c.startTime=l.data.st,c.index=l.data.ind,c.source=l.data.refId,c.height=0===l.data.ty?l.data.h:100,c.width=0===l.data.ty?l.data.w:100,c.inPoint=l.data.ip/l.comp.globalData.frameRate,c.outPoint=l.data.op/l.comp.globalData.frameRate,c._name=l.data.nm,c.registerMaskInterface=function(t){c.mask=new MaskManagerInterface(t,l)},c.registerEffectsInterface=function(t){c.effect=t},c}}(),propertyGroupFactory=function(t,e){return function(i){return(i=void 0===i?1:i)<=0?t:e(i-1)}},PropertyInterface=function(t,e){var i={_name:t};return function(t){return(t=void 0===t?1:t)<=0?i:e(t-1)}},EffectsExpressionInterface=function(){var t={createEffectsInterface:function(t,i){if(t.effectsManager){var r,n=[],s=t.data.ef,a=t.effectsManager.effectElements.length;for(r=0;r<a;r+=1)n.push(e(s[r],t.effectsManager.effectElements[r],i,t));var o=t.data.ef||[],h=function(t){for(r=0,a=o.length;r<a;){if(t===o[r].nm||t===o[r].mn||t===o[r].ix)return n[r];r+=1}return null};return Object.defineProperty(h,"numProperties",{get:function(){return o.length}}),h}return null}};function e(t,r,n,s){function a(e){for(var i=t.ef,r=0,n=i.length;r<n;){if(e===i[r].nm||e===i[r].mn||e===i[r].ix)return 5===i[r].ty?l[r]:l[r]();r+=1}throw new Error}var o,h=propertyGroupFactory(a,n),l=[],p=t.ef.length;for(o=0;o<p;o+=1)5===t.ef[o].ty?l.push(e(t.ef[o],r.effectElements[o],r.effectElements[o].propertyGroup,s)):l.push(i(r.effectElements[o],t.ef[o].ty,s,h));return"ADBE Color Control"===t.mn&&Object.defineProperty(a,"color",{get:function(){return l[0]()}}),Object.defineProperties(a,{numProperties:{get:function(){return t.np}},_name:{value:t.nm},propertyGroup:{value:h}}),a.enabled=0!==t.en,a.active=a.enabled,a}function i(t,e,i,r){var n=ExpressionPropertyInterface(t.p);return t.p.setGroupProperty&&t.p.setGroupProperty(PropertyInterface("",r)),function(){return 10===e?i.comp.compInterface(t.p.v):n()}}return t}(),ShapePathInterface=function(t,e,i){var r=e.sh;function n(t){return"Shape"===t||"shape"===t||"Path"===t||"path"===t||"ADBE Vector Shape"===t||2===t?n.path:null}var s=propertyGroupFactory(n,i);return r.setGroupProperty(PropertyInterface("Path",s)),Object.defineProperties(n,{path:{get:function(){return r.k&&r.getValue(),r}},shape:{get:function(){return r.k&&r.getValue(),r}},_name:{value:t.nm},ix:{value:t.ix},propertyIndex:{value:t.ix},mn:{value:t.mn},propertyGroup:{value:i}}),n},ShapeExpressionInterface=function(){function t(t,a,d){var f,u=[],m=t?t.length:0;for(f=0;f<m;f+=1)"gr"===t[f].ty?u.push(e(t[f],a[f],d)):"fl"===t[f].ty?u.push(i(t[f],a[f],d)):"st"===t[f].ty?u.push(n(t[f],a[f],d)):"tm"===t[f].ty?u.push(s(t[f],a[f],d)):"tr"===t[f].ty||("el"===t[f].ty?u.push(o(t[f],a[f],d)):"sr"===t[f].ty?u.push(h(t[f],a[f],d)):"sh"===t[f].ty?u.push(ShapePathInterface(t[f],a[f],d)):"rc"===t[f].ty?u.push(l(t[f],a[f],d)):"rd"===t[f].ty?u.push(p(t[f],a[f],d)):"rp"===t[f].ty?u.push(c(t[f],a[f],d)):"gf"===t[f].ty?u.push(r(t[f],a[f],d)):u.push((t[f],a[f],function(){return null})));return u}function e(e,i,r){var n=function(t){switch(t){case"ADBE Vectors Group":case"Contents":case 2:return n.content;default:return n.transform}};n.propertyGroup=propertyGroupFactory(n,r);var s=function(e,i,r){var n,s=function(t){for(var e=0,i=n.length;e<i;){if(n[e]._name===t||n[e].mn===t||n[e].propertyIndex===t||n[e].ix===t||n[e].ind===t)return n[e];e+=1}return"number"==typeof t?n[t-1]:null};s.propertyGroup=propertyGroupFactory(s,r),n=t(e.it,i.it,s.propertyGroup),s.numProperties=n.length;var o=a(e.it[e.it.length-1],i.it[i.it.length-1],s.propertyGroup);return s.transform=o,s.propertyIndex=e.cix,s._name=e.nm,s}(e,i,n.propertyGroup),o=a(e.it[e.it.length-1],i.it[i.it.length-1],n.propertyGroup);return n.content=s,n.transform=o,Object.defineProperty(n,"_name",{get:function(){return e.nm}}),n.numProperties=e.np,n.propertyIndex=e.ix,n.nm=e.nm,n.mn=e.mn,n}function i(t,e,i){function r(t){return"Color"===t||"color"===t?r.color:"Opacity"===t||"opacity"===t?r.opacity:null}return Object.defineProperties(r,{color:{get:ExpressionPropertyInterface(e.c)},opacity:{get:ExpressionPropertyInterface(e.o)},_name:{value:t.nm},mn:{value:t.mn}}),e.c.setGroupProperty(PropertyInterface("Color",i)),e.o.setGroupProperty(PropertyInterface("Opacity",i)),r}function r(t,e,i){function r(t){return"Start Point"===t||"start point"===t?r.startPoint:"End Point"===t||"end point"===t?r.endPoint:"Opacity"===t||"opacity"===t?r.opacity:null}return Object.defineProperties(r,{startPoint:{get:ExpressionPropertyInterface(e.s)},endPoint:{get:ExpressionPropertyInterface(e.e)},opacity:{get:ExpressionPropertyInterface(e.o)},type:{get:function(){return"a"}},_name:{value:t.nm},mn:{value:t.mn}}),e.s.setGroupProperty(PropertyInterface("Start Point",i)),e.e.setGroupProperty(PropertyInterface("End Point",i)),e.o.setGroupProperty(PropertyInterface("Opacity",i)),r}function n(t,e,i){var r,n=propertyGroupFactory(l,i),s=propertyGroupFactory(h,n);function a(i){Object.defineProperty(h,t.d[i].nm,{get:ExpressionPropertyInterface(e.d.dataProps[i].p)})}var o=t.d?t.d.length:0,h={};for(r=0;r<o;r+=1)a(r),e.d.dataProps[r].p.setGroupProperty(s);function l(t){return"Color"===t||"color"===t?l.color:"Opacity"===t||"opacity"===t?l.opacity:"Stroke Width"===t||"stroke width"===t?l.strokeWidth:null}return Object.defineProperties(l,{color:{get:ExpressionPropertyInterface(e.c)},opacity:{get:ExpressionPropertyInterface(e.o)},strokeWidth:{get:ExpressionPropertyInterface(e.w)},dash:{get:function(){return h}},_name:{value:t.nm},mn:{value:t.mn}}),e.c.setGroupProperty(PropertyInterface("Color",n)),e.o.setGroupProperty(PropertyInterface("Opacity",n)),e.w.setGroupProperty(PropertyInterface("Stroke Width",n)),l}function s(t,e,i){function r(e){return e===t.e.ix||"End"===e||"end"===e?r.end:e===t.s.ix?r.start:e===t.o.ix?r.offset:null}var n=propertyGroupFactory(r,i);return r.propertyIndex=t.ix,e.s.setGroupProperty(PropertyInterface("Start",n)),e.e.setGroupProperty(PropertyInterface("End",n)),e.o.setGroupProperty(PropertyInterface("Offset",n)),r.propertyIndex=t.ix,r.propertyGroup=i,Object.defineProperties(r,{start:{get:ExpressionPropertyInterface(e.s)},end:{get:ExpressionPropertyInterface(e.e)},offset:{get:ExpressionPropertyInterface(e.o)},_name:{value:t.nm}}),r.mn=t.mn,r}function a(t,e,i){function r(e){return t.a.ix===e||"Anchor Point"===e?r.anchorPoint:t.o.ix===e||"Opacity"===e?r.opacity:t.p.ix===e||"Position"===e?r.position:t.r.ix===e||"Rotation"===e||"ADBE Vector Rotation"===e?r.rotation:t.s.ix===e||"Scale"===e?r.scale:t.sk&&t.sk.ix===e||"Skew"===e?r.skew:t.sa&&t.sa.ix===e||"Skew Axis"===e?r.skewAxis:null}var n=propertyGroupFactory(r,i);return e.transform.mProps.o.setGroupProperty(PropertyInterface("Opacity",n)),e.transform.mProps.p.setGroupProperty(PropertyInterface("Position",n)),e.transform.mProps.a.setGroupProperty(PropertyInterface("Anchor Point",n)),e.transform.mProps.s.setGroupProperty(PropertyInterface("Scale",n)),e.transform.mProps.r.setGroupProperty(PropertyInterface("Rotation",n)),e.transform.mProps.sk&&(e.transform.mProps.sk.setGroupProperty(PropertyInterface("Skew",n)),e.transform.mProps.sa.setGroupProperty(PropertyInterface("Skew Angle",n))),e.transform.op.setGroupProperty(PropertyInterface("Opacity",n)),Object.defineProperties(r,{opacity:{get:ExpressionPropertyInterface(e.transform.mProps.o)},position:{get:ExpressionPropertyInterface(e.transform.mProps.p)},anchorPoint:{get:ExpressionPropertyInterface(e.transform.mProps.a)},scale:{get:ExpressionPropertyInterface(e.transform.mProps.s)},rotation:{get:ExpressionPropertyInterface(e.transform.mProps.r)},skew:{get:ExpressionPropertyInterface(e.transform.mProps.sk)},skewAxis:{get:ExpressionPropertyInterface(e.transform.mProps.sa)},_name:{value:t.nm}}),r.ty="tr",r.mn=t.mn,r.propertyGroup=i,r}function o(t,e,i){function r(e){return t.p.ix===e?r.position:t.s.ix===e?r.size:null}var n=propertyGroupFactory(r,i);r.propertyIndex=t.ix;var s="tm"===e.sh.ty?e.sh.prop:e.sh;return s.s.setGroupProperty(PropertyInterface("Size",n)),s.p.setGroupProperty(PropertyInterface("Position",n)),Object.defineProperties(r,{size:{get:ExpressionPropertyInterface(s.s)},position:{get:ExpressionPropertyInterface(s.p)},_name:{value:t.nm}}),r.mn=t.mn,r}function h(t,e,i){function r(e){return t.p.ix===e?r.position:t.r.ix===e?r.rotation:t.pt.ix===e?r.points:t.or.ix===e||"ADBE Vector Star Outer Radius"===e?r.outerRadius:t.os.ix===e?r.outerRoundness:!t.ir||t.ir.ix!==e&&"ADBE Vector Star Inner Radius"!==e?t.is&&t.is.ix===e?r.innerRoundness:null:r.innerRadius}var n=propertyGroupFactory(r,i),s="tm"===e.sh.ty?e.sh.prop:e.sh;return r.propertyIndex=t.ix,s.or.setGroupProperty(PropertyInterface("Outer Radius",n)),s.os.setGroupProperty(PropertyInterface("Outer Roundness",n)),s.pt.setGroupProperty(PropertyInterface("Points",n)),s.p.setGroupProperty(PropertyInterface("Position",n)),s.r.setGroupProperty(PropertyInterface("Rotation",n)),t.ir&&(s.ir.setGroupProperty(PropertyInterface("Inner Radius",n)),s.is.setGroupProperty(PropertyInterface("Inner Roundness",n))),Object.defineProperties(r,{position:{get:ExpressionPropertyInterface(s.p)},rotation:{get:ExpressionPropertyInterface(s.r)},points:{get:ExpressionPropertyInterface(s.pt)},outerRadius:{get:ExpressionPropertyInterface(s.or)},outerRoundness:{get:ExpressionPropertyInterface(s.os)},innerRadius:{get:ExpressionPropertyInterface(s.ir)},innerRoundness:{get:ExpressionPropertyInterface(s.is)},_name:{value:t.nm}}),r.mn=t.mn,r}function l(t,e,i){function r(e){return t.p.ix===e?r.position:t.r.ix===e?r.roundness:t.s.ix===e||"Size"===e||"ADBE Vector Rect Size"===e?r.size:null}var n=propertyGroupFactory(r,i),s="tm"===e.sh.ty?e.sh.prop:e.sh;return r.propertyIndex=t.ix,s.p.setGroupProperty(PropertyInterface("Position",n)),s.s.setGroupProperty(PropertyInterface("Size",n)),s.r.setGroupProperty(PropertyInterface("Rotation",n)),Object.defineProperties(r,{position:{get:ExpressionPropertyInterface(s.p)},roundness:{get:ExpressionPropertyInterface(s.r)},size:{get:ExpressionPropertyInterface(s.s)},_name:{value:t.nm}}),r.mn=t.mn,r}function p(t,e,i){function r(e){return t.r.ix===e||"Round Corners 1"===e?r.radius:null}var n=propertyGroupFactory(r,i),s=e;return r.propertyIndex=t.ix,s.rd.setGroupProperty(PropertyInterface("Radius",n)),Object.defineProperties(r,{radius:{get:ExpressionPropertyInterface(s.rd)},_name:{value:t.nm}}),r.mn=t.mn,r}function c(t,e,i){function r(e){return t.c.ix===e||"Copies"===e?r.copies:t.o.ix===e||"Offset"===e?r.offset:null}var n=propertyGroupFactory(r,i),s=e;return r.propertyIndex=t.ix,s.c.setGroupProperty(PropertyInterface("Copies",n)),s.o.setGroupProperty(PropertyInterface("Offset",n)),Object.defineProperties(r,{copies:{get:ExpressionPropertyInterface(s.c)},offset:{get:ExpressionPropertyInterface(s.o)},_name:{value:t.nm}}),r.mn=t.mn,r}return function(e,i,r){var n;function s(t){if("number"==typeof t)return 0===(t=void 0===t?1:t)?r:n[t-1];for(var e=0,i=n.length;e<i;){if(n[e]._name===t)return n[e];e+=1}return null}return s.propertyGroup=propertyGroupFactory(s,(function(){return r})),n=t(e,i,s.propertyGroup),s.numProperties=n.length,s._name="Contents",s}}(),TextExpressionInterface=function(t){var e,i;function r(t){return"ADBE Text Document"===t?r.sourceText:null}return Object.defineProperty(r,"sourceText",{get:function(){t.textProperty.getValue();var r=t.textProperty.currentData.t;return r!==e&&(t.textProperty.currentData.t=e,(i=new String(r)).value=r||new String(r)),i}}),r};function _typeof$2(t){return _typeof$2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_typeof$2(t)}var FootageInterface=(dataInterfaceFactory=function(t){function e(t){return"Outline"===t?e.outlineInterface():null}return e._name="Outline",e.outlineInterface=function(t){var e="",i=t.getFootageData();function r(t){if(i[t])return e=t,"object"===_typeof$2(i=i[t])?r:i;var n=t.indexOf(e);if(-1!==n){var s=parseInt(t.substr(n+e.length),10);return"object"===_typeof$2(i=i[s])?r:i}return""}return function(){return e="",i=t.getFootageData(),r}}(t),e},function(t){function e(t){return"Data"===t?e.dataInterface:null}return e._name="Data",e.dataInterface=dataInterfaceFactory(t),e}),dataInterfaceFactory,interfaces={layer:LayerExpressionInterface,effects:EffectsExpressionInterface,comp:CompExpressionInterface,shape:ShapeExpressionInterface,text:TextExpressionInterface,footage:FootageInterface};function getInterface(t){return interfaces[t]||null}function _typeof$1(t){return _typeof$1="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_typeof$1(t)}function seedRandom(t,e){var i=this,r=256,n=e.pow(r,6),s=e.pow(2,52),a=2*s,o=255;function h(t){var e,i=t.length,n=this,s=0,a=n.i=n.j=0,h=n.S=[];for(i||(t=[i++]);s<r;)h[s]=s++;for(s=0;s<r;s++)h[s]=h[a=o&a+t[s%i]+(e=h[s])],h[a]=e;n.g=function(t){for(var e,i=0,s=n.i,a=n.j,h=n.S;t--;)e=h[s=o&s+1],i=i*r+h[o&(h[s]=h[a=o&a+e])+(h[a]=e)];return n.i=s,n.j=a,i}}function l(t,e){return e.i=t.i,e.j=t.j,e.S=t.S.slice(),e}function p(t,e){var i,r=[],n=_typeof$1(t);if(e&&"object"==n)for(i in t)try{r.push(p(t[i],e-1))}catch(t){}return r.length?r:"string"==n?t:t+"\0"}function c(t,e){for(var i,r=t+"",n=0;n<r.length;)e[o&n]=o&(i^=19*e[o&n])+r.charCodeAt(n++);return d(e)}function d(t){return String.fromCharCode.apply(0,t)}e.seedrandom=function(o,f,u){var m=[],y=c(p((f=!0===f?{entropy:!0}:f||{}).entropy?[o,d(t)]:null===o?function(){try{var e=new Uint8Array(r);return(i.crypto||i.msCrypto).getRandomValues(e),d(e)}catch(e){var n=i.navigator,s=n&&n.plugins;return[+new Date,i,s,i.screen,d(t)]}}():o,3),m),g=new h(m),v=function(){for(var t=g.g(6),e=n,i=0;t<s;)t=(t+i)*r,e*=r,i=g.g(1);for(;t>=a;)t/=2,e/=2,i>>>=1;return(t+i)/e};return v.int32=function(){return 0|g.g(4)},v.quick=function(){return g.g(4)/4294967296},v.double=v,c(d(g.S),t),(f.pass||u||function(t,i,r,n){return n&&(n.S&&l(n,g),t.state=function(){return l(g,{})}),r?(e.random=t,i):t})(v,y,"global"in f?f.global:this==e,f.state)},c(e.random(),t)}function initialize$2(t){seedRandom([],t)}var propTypes={SHAPE:"shape"};function _typeof(t){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_typeof(t)}var ExpressionManager=function(){var ob={},Math=BMMath,window=null,document=null,XMLHttpRequest=null,fetch=null,frames=null;function $bm_isInstanceOfArray(t){return t.constructor===Array||t.constructor===Float32Array}function isNumerable(t,e){return"number"===t||"boolean"===t||"string"===t||e instanceof Number}function $bm_neg(t){var e=_typeof(t);if("number"===e||"boolean"===e||t instanceof Number)return-t;if($bm_isInstanceOfArray(t)){var i,r=t.length,n=[];for(i=0;i<r;i+=1)n[i]=-t[i];return n}return t.propType?t.v:-t}initialize$2(BMMath);var easeInBez=BezierFactory.getBezierEasing(.333,0,.833,.833,"easeIn").get,easeOutBez=BezierFactory.getBezierEasing(.167,.167,.667,1,"easeOut").get,easeInOutBez=BezierFactory.getBezierEasing(.33,0,.667,1,"easeInOut").get;function sum(t,e){var i=_typeof(t),r=_typeof(e);if("string"===i||"string"===r)return t+e;if(isNumerable(i,t)&&isNumerable(r,e))return t+e;if($bm_isInstanceOfArray(t)&&isNumerable(r,e))return(t=t.slice(0))[0]+=e,t;if(isNumerable(i,t)&&$bm_isInstanceOfArray(e))return(e=e.slice(0))[0]=t+e[0],e;if($bm_isInstanceOfArray(t)&&$bm_isInstanceOfArray(e)){for(var n=0,s=t.length,a=e.length,o=[];n<s||n<a;)("number"==typeof t[n]||t[n]instanceof Number)&&("number"==typeof e[n]||e[n]instanceof Number)?o[n]=t[n]+e[n]:o[n]=void 0===e[n]?t[n]:t[n]||e[n],n+=1;return o}return 0}var add=sum;function sub(t,e){var i=_typeof(t),r=_typeof(e);if(isNumerable(i,t)&&isNumerable(r,e))return"string"===i&&(t=parseInt(t,10)),"string"===r&&(e=parseInt(e,10)),t-e;if($bm_isInstanceOfArray(t)&&isNumerable(r,e))return(t=t.slice(0))[0]-=e,t;if(isNumerable(i,t)&&$bm_isInstanceOfArray(e))return(e=e.slice(0))[0]=t-e[0],e;if($bm_isInstanceOfArray(t)&&$bm_isInstanceOfArray(e)){for(var n=0,s=t.length,a=e.length,o=[];n<s||n<a;)("number"==typeof t[n]||t[n]instanceof Number)&&("number"==typeof e[n]||e[n]instanceof Number)?o[n]=t[n]-e[n]:o[n]=void 0===e[n]?t[n]:t[n]||e[n],n+=1;return o}return 0}function mul(t,e){var i,r,n,s=_typeof(t),a=_typeof(e);if(isNumerable(s,t)&&isNumerable(a,e))return t*e;if($bm_isInstanceOfArray(t)&&isNumerable(a,e)){for(n=t.length,i=createTypedArray("float32",n),r=0;r<n;r+=1)i[r]=t[r]*e;return i}if(isNumerable(s,t)&&$bm_isInstanceOfArray(e)){for(n=e.length,i=createTypedArray("float32",n),r=0;r<n;r+=1)i[r]=t*e[r];return i}return 0}function div(t,e){var i,r,n,s=_typeof(t),a=_typeof(e);if(isNumerable(s,t)&&isNumerable(a,e))return t/e;if($bm_isInstanceOfArray(t)&&isNumerable(a,e)){for(n=t.length,i=createTypedArray("float32",n),r=0;r<n;r+=1)i[r]=t[r]/e;return i}if(isNumerable(s,t)&&$bm_isInstanceOfArray(e)){for(n=e.length,i=createTypedArray("float32",n),r=0;r<n;r+=1)i[r]=t/e[r];return i}return 0}function mod(t,e){return"string"==typeof t&&(t=parseInt(t,10)),"string"==typeof e&&(e=parseInt(e,10)),t%e}var $bm_sum=sum,$bm_sub=sub,$bm_mul=mul,$bm_div=div,$bm_mod=mod;function clamp(t,e,i){if(e>i){var r=i;i=e,e=r}return Math.min(Math.max(t,e),i)}function radiansToDegrees(t){return t/degToRads}var radians_to_degrees=radiansToDegrees;function degreesToRadians(t){return t*degToRads}var degrees_to_radians=radiansToDegrees,helperLengthArray=[0,0,0,0,0,0];function length(t,e){if("number"==typeof t||t instanceof Number)return e=e||0,Math.abs(t-e);var i;e||(e=helperLengthArray);var r=Math.min(t.length,e.length),n=0;for(i=0;i<r;i+=1)n+=Math.pow(e[i]-t[i],2);return Math.sqrt(n)}function normalize(t){return div(t,length(t))}function rgbToHsl(t){var e,i,r=t[0],n=t[1],s=t[2],a=Math.max(r,n,s),o=Math.min(r,n,s),h=(a+o)/2;if(a===o)e=0,i=0;else{var l=a-o;switch(i=h>.5?l/(2-a-o):l/(a+o),a){case r:e=(n-s)/l+(n<s?6:0);break;case n:e=(s-r)/l+2;break;case s:e=(r-n)/l+4}e/=6}return[e,i,h,t[3]]}function hue2rgb(t,e,i){return i<0&&(i+=1),i>1&&(i-=1),i<1/6?t+6*(e-t)*i:i<.5?e:i<2/3?t+(e-t)*(2/3-i)*6:t}function hslToRgb(t){var e,i,r,n=t[0],s=t[1],a=t[2];if(0===s)e=a,r=a,i=a;else{var o=a<.5?a*(1+s):a+s-a*s,h=2*a-o;e=hue2rgb(h,o,n+1/3),i=hue2rgb(h,o,n),r=hue2rgb(h,o,n-1/3)}return[e,i,r,t[3]]}function linear(t,e,i,r,n){if(void 0!==r&&void 0!==n||(r=e,n=i,e=0,i=1),i<e){var s=i;i=e,e=s}if(t<=e)return r;if(t>=i)return n;var a,o=i===e?0:(t-e)/(i-e);if(!r.length)return r+(n-r)*o;var h=r.length,l=createTypedArray("float32",h);for(a=0;a<h;a+=1)l[a]=r[a]+(n[a]-r[a])*o;return l}function random(t,e){if(void 0===e&&(void 0===t?(t=0,e=1):(e=t,t=void 0)),e.length){var i,r=e.length;t||(t=createTypedArray("float32",r));var n=createTypedArray("float32",r),s=BMMath.random();for(i=0;i<r;i+=1)n[i]=t[i]+s*(e[i]-t[i]);return n}return void 0===t&&(t=0),t+BMMath.random()*(e-t)}function createPath(t,e,i,r){var n,s=t.length,a=shapePool.newElement();a.setPathData(!!r,s);var o,h,l=[0,0];for(n=0;n<s;n+=1)o=e&&e[n]?e[n]:l,h=i&&i[n]?i[n]:l,a.setTripleAt(t[n][0],t[n][1],h[0]+t[n][0],h[1]+t[n][1],o[0]+t[n][0],o[1]+t[n][1],n,!0);return a}function initiateExpression(elem,data,property){function noOp(t){return t}if(!elem.globalData.renderConfig.runExpressions)return noOp;var val=data.x,needsVelocity=/velocity(?![\w\d])/.test(val),_needsRandom=-1!==val.indexOf("random"),elemType=elem.data.ty,transform,$bm_transform,content,effect,thisProperty=property;thisProperty.valueAtTime=thisProperty.getValueAtTime,Object.defineProperty(thisProperty,"value",{get:function(){return thisProperty.v}}),elem.comp.frameDuration=1/elem.comp.globalData.frameRate,elem.comp.displayStartTime=0;var inPoint=elem.data.ip/elem.comp.globalData.frameRate,outPoint=elem.data.op/elem.comp.globalData.frameRate,width=elem.data.sw?elem.data.sw:0,height=elem.data.sh?elem.data.sh:0,name=elem.data.nm,loopIn,loop_in,loopOut,loop_out,smooth,toWorld,fromWorld,fromComp,toComp,fromCompToSurface,position,rotation,anchorPoint,scale,thisLayer,thisComp,mask,valueAtTime,velocityAtTime,scoped_bm_rt,expression_function=eval("[function _expression_function(){"+val+";scoped_bm_rt=$bm_rt}]")[0],numKeys=property.kf?data.k.length:0,active=!this.data||!0!==this.data.hd,wiggle=function(t,e){var i,r,n=this.pv.length?this.pv.length:1,s=createTypedArray("float32",n),a=Math.floor(5*time);for(i=0,r=0;i<a;){for(r=0;r<n;r+=1)s[r]+=-e+2*e*BMMath.random();i+=1}var o=5*time,h=o-Math.floor(o),l=createTypedArray("float32",n);if(n>1){for(r=0;r<n;r+=1)l[r]=this.pv[r]+s[r]+(-e+2*e*BMMath.random())*h;return l}return this.pv+s[0]+(-e+2*e*BMMath.random())*h}.bind(this);function loopInDuration(t,e){return loopIn(t,e,!0)}function loopOutDuration(t,e){return loopOut(t,e,!0)}thisProperty.loopIn&&(loopIn=thisProperty.loopIn.bind(thisProperty),loop_in=loopIn),thisProperty.loopOut&&(loopOut=thisProperty.loopOut.bind(thisProperty),loop_out=loopOut),thisProperty.smooth&&(smooth=thisProperty.smooth.bind(thisProperty)),this.getValueAtTime&&(valueAtTime=this.getValueAtTime.bind(this)),this.getVelocityAtTime&&(velocityAtTime=this.getVelocityAtTime.bind(this));var comp=elem.comp.globalData.projectInterface.bind(elem.comp.globalData.projectInterface),time,velocity,value,text,textIndex,textTotal,selectorValue;function lookAt(t,e){var i=[e[0]-t[0],e[1]-t[1],e[2]-t[2]],r=Math.atan2(i[0],Math.sqrt(i[1]*i[1]+i[2]*i[2]))/degToRads;return[-Math.atan2(i[1],i[2])/degToRads,r,0]}function easeOut(t,e,i,r,n){return applyEase(easeOutBez,t,e,i,r,n)}function easeIn(t,e,i,r,n){return applyEase(easeInBez,t,e,i,r,n)}function ease(t,e,i,r,n){return applyEase(easeInOutBez,t,e,i,r,n)}function applyEase(t,e,i,r,n,s){void 0===n?(n=i,s=r):e=(e-i)/(r-i),e>1?e=1:e<0&&(e=0);var a=t(e);if($bm_isInstanceOfArray(n)){var o,h=n.length,l=createTypedArray("float32",h);for(o=0;o<h;o+=1)l[o]=(s[o]-n[o])*a+n[o];return l}return(s-n)*a+n}function nearestKey(t){var e,i,r,n=data.k.length;if(data.k.length&&"number"!=typeof data.k[0])if(i=-1,(t*=elem.comp.globalData.frameRate)<data.k[0].t)i=1,r=data.k[0].t;else{for(e=0;e<n-1;e+=1){if(t===data.k[e].t){i=e+1,r=data.k[e].t;break}if(t>data.k[e].t&&t<data.k[e+1].t){t-data.k[e].t>data.k[e+1].t-t?(i=e+2,r=data.k[e+1].t):(i=e+1,r=data.k[e].t);break}}-1===i&&(i=e+1,r=data.k[e].t)}else i=0,r=0;var s={};return s.index=i,s.time=r/elem.comp.globalData.frameRate,s}function key(t){var e,i,r;if(!data.k.length||"number"==typeof data.k[0])throw new Error("The property has no keyframe at index "+t);t-=1,e={time:data.k[t].t/elem.comp.globalData.frameRate,value:[]};var n=Object.prototype.hasOwnProperty.call(data.k[t],"s")?data.k[t].s:data.k[t-1].e;for(r=n.length,i=0;i<r;i+=1)e[i]=n[i],e.value[i]=n[i];return e}function framesToTime(t,e){return e||(e=elem.comp.globalData.frameRate),t/e}function timeToFrames(t,e){return t||0===t||(t=time),e||(e=elem.comp.globalData.frameRate),t*e}function seedRandom(t){BMMath.seedrandom(randSeed+t)}function sourceRectAtTime(){return elem.sourceRectAtTime()}function substring(t,e){return"string"==typeof value?void 0===e?value.substring(t):value.substring(t,e):""}function substr(t,e){return"string"==typeof value?void 0===e?value.substr(t):value.substr(t,e):""}function posterizeTime(t){time=0===t?0:Math.floor(time*t)/t,value=valueAtTime(time)}var index=elem.data.ind,hasParent=!(!elem.hierarchy||!elem.hierarchy.length),parent,randSeed=Math.floor(1e6*Math.random()),globalData=elem.globalData;function executeExpression(t){return value=t,this.frameExpressionId===elem.globalData.frameId&&"textSelector"!==this.propType?value:("textSelector"===this.propType&&(textIndex=this.textIndex,textTotal=this.textTotal,selectorValue=this.selectorValue),thisLayer||(text=elem.layerInterface.text,thisLayer=elem.layerInterface,thisComp=elem.comp.compInterface,toWorld=thisLayer.toWorld.bind(thisLayer),fromWorld=thisLayer.fromWorld.bind(thisLayer),fromComp=thisLayer.fromComp.bind(thisLayer),toComp=thisLayer.toComp.bind(thisLayer),mask=thisLayer.mask?thisLayer.mask.bind(thisLayer):null,fromCompToSurface=fromComp),transform||(transform=elem.layerInterface("ADBE Transform Group"),$bm_transform=transform,transform&&(anchorPoint=transform.anchorPoint)),4!==elemType||content||(content=thisLayer("ADBE Root Vectors Group")),effect||(effect=thisLayer(4)),(hasParent=!(!elem.hierarchy||!elem.hierarchy.length))&&!parent&&(parent=elem.hierarchy[0].layerInterface),time=this.comp.renderedFrame/this.comp.globalData.frameRate,_needsRandom&&seedRandom(randSeed+time),needsVelocity&&(velocity=velocityAtTime(time)),expression_function(),this.frameExpressionId=elem.globalData.frameId,scoped_bm_rt=scoped_bm_rt.propType===propTypes.SHAPE?scoped_bm_rt.v:scoped_bm_rt)}return executeExpression.__preventDeadCodeRemoval=[$bm_transform,anchorPoint,time,velocity,inPoint,outPoint,width,height,name,loop_in,loop_out,smooth,toComp,fromCompToSurface,toWorld,fromWorld,mask,position,rotation,scale,thisComp,numKeys,active,wiggle,loopInDuration,loopOutDuration,comp,lookAt,easeOut,easeIn,ease,nearestKey,key,text,textIndex,textTotal,selectorValue,framesToTime,timeToFrames,sourceRectAtTime,substring,substr,posterizeTime,index,globalData],executeExpression}return ob.initiateExpression=initiateExpression,ob.__preventDeadCodeRemoval=[window,document,XMLHttpRequest,fetch,frames,$bm_neg,add,$bm_sum,$bm_sub,$bm_mul,$bm_div,$bm_mod,clamp,radians_to_degrees,degreesToRadians,degrees_to_radians,normalize,rgbToHsl,hslToRgb,linear,random,createPath],ob}(),expressionHelpers={searchExpressions:function(t,e,i){e.x&&(i.k=!0,i.x=!0,i.initiateExpression=ExpressionManager.initiateExpression,i.effectsSequence.push(i.initiateExpression(t,e,i).bind(i)))},getSpeedAtTime:function(t){var e=this.getValueAtTime(t),i=this.getValueAtTime(t+-.01),r=0;if(e.length){var n;for(n=0;n<e.length;n+=1)r+=Math.pow(i[n]-e[n],2);r=100*Math.sqrt(r)}else r=0;return r},getVelocityAtTime:function(t){if(void 0!==this.vel)return this.vel;var e,i,r=-.001,n=this.getValueAtTime(t),s=this.getValueAtTime(t+r);if(n.length)for(e=createTypedArray("float32",n.length),i=0;i<n.length;i+=1)e[i]=(s[i]-n[i])/r;else e=(s-n)/r;return e},getValueAtTime:function(t){return t*=this.elem.globalData.frameRate,(t-=this.offsetTime)!==this._cachingAtTime.lastFrame&&(this._cachingAtTime.lastIndex=this._cachingAtTime.lastFrame<t?this._cachingAtTime.lastIndex:0,this._cachingAtTime.value=this.interpolateValue(t,this._cachingAtTime),this._cachingAtTime.lastFrame=t),this._cachingAtTime.value},getStaticValueAtTime:function(){return this.pv},setGroupProperty:function(t){this.propertyGroup=t}};function addPropertyDecorator(){function t(t,e,i){if(!this.k||!this.keyframes)return this.pv;t=t?t.toLowerCase():"";var r,n,s,a,o,h=this.comp.renderedFrame,l=this.keyframes,p=l[l.length-1].t;if(h<=p)return this.pv;if(i?n=p-(r=e?Math.abs(p-this.elem.comp.globalData.frameRate*e):Math.max(0,p-this.elem.data.ip)):((!e||e>l.length-1)&&(e=l.length-1),r=p-(n=l[l.length-1-e].t)),"pingpong"===t){if(Math.floor((h-n)/r)%2!=0)return this.getValueAtTime((r-(h-n)%r+n)/this.comp.globalData.frameRate,0)}else{if("offset"===t){var c=this.getValueAtTime(n/this.comp.globalData.frameRate,0),d=this.getValueAtTime(p/this.comp.globalData.frameRate,0),f=this.getValueAtTime(((h-n)%r+n)/this.comp.globalData.frameRate,0),u=Math.floor((h-n)/r);if(this.pv.length){for(a=(o=new Array(c.length)).length,s=0;s<a;s+=1)o[s]=(d[s]-c[s])*u+f[s];return o}return(d-c)*u+f}if("continue"===t){var m=this.getValueAtTime(p/this.comp.globalData.frameRate,0),y=this.getValueAtTime((p-.001)/this.comp.globalData.frameRate,0);if(this.pv.length){for(a=(o=new Array(m.length)).length,s=0;s<a;s+=1)o[s]=m[s]+(m[s]-y[s])*((h-p)/this.comp.globalData.frameRate)/5e-4;return o}return m+(h-p)/.001*(m-y)}}return this.getValueAtTime(((h-n)%r+n)/this.comp.globalData.frameRate,0)}function e(t,e,i){if(!this.k)return this.pv;t=t?t.toLowerCase():"";var r,n,s,a,o,h=this.comp.renderedFrame,l=this.keyframes,p=l[0].t;if(h>=p)return this.pv;if(i?n=p+(r=e?Math.abs(this.elem.comp.globalData.frameRate*e):Math.max(0,this.elem.data.op-p)):((!e||e>l.length-1)&&(e=l.length-1),r=(n=l[e].t)-p),"pingpong"===t){if(Math.floor((p-h)/r)%2==0)return this.getValueAtTime(((p-h)%r+p)/this.comp.globalData.frameRate,0)}else{if("offset"===t){var c=this.getValueAtTime(p/this.comp.globalData.frameRate,0),d=this.getValueAtTime(n/this.comp.globalData.frameRate,0),f=this.getValueAtTime((r-(p-h)%r+p)/this.comp.globalData.frameRate,0),u=Math.floor((p-h)/r)+1;if(this.pv.length){for(a=(o=new Array(c.length)).length,s=0;s<a;s+=1)o[s]=f[s]-(d[s]-c[s])*u;return o}return f-(d-c)*u}if("continue"===t){var m=this.getValueAtTime(p/this.comp.globalData.frameRate,0),y=this.getValueAtTime((p+.001)/this.comp.globalData.frameRate,0);if(this.pv.length){for(a=(o=new Array(m.length)).length,s=0;s<a;s+=1)o[s]=m[s]+(m[s]-y[s])*(p-h)/.001;return o}return m+(m-y)*(p-h)/.001}}return this.getValueAtTime((r-((p-h)%r+p))/this.comp.globalData.frameRate,0)}function i(t,e){if(!this.k)return this.pv;if(t=.5*(t||.4),(e=Math.floor(e||5))<=1)return this.pv;var i,r,n=this.comp.renderedFrame/this.comp.globalData.frameRate,s=n-t,a=e>1?(n+t-s)/(e-1):1,o=0,h=0;for(i=this.pv.length?createTypedArray("float32",this.pv.length):0;o<e;){if(r=this.getValueAtTime(s+o*a),this.pv.length)for(h=0;h<this.pv.length;h+=1)i[h]+=r[h];else i+=r;o+=1}if(this.pv.length)for(h=0;h<this.pv.length;h+=1)i[h]/=e;else i/=e;return i}function r(t){this._transformCachingAtTime||(this._transformCachingAtTime={v:new Matrix});var e=this._transformCachingAtTime.v;if(e.cloneFromProps(this.pre.props),this.appliedTransformations<1){var i=this.a.getValueAtTime(t);e.translate(-i[0]*this.a.mult,-i[1]*this.a.mult,i[2]*this.a.mult)}if(this.appliedTransformations<2){var r=this.s.getValueAtTime(t);e.scale(r[0]*this.s.mult,r[1]*this.s.mult,r[2]*this.s.mult)}if(this.sk&&this.appliedTransformations<3){var n=this.sk.getValueAtTime(t),s=this.sa.getValueAtTime(t);e.skewFromAxis(-n*this.sk.mult,s*this.sa.mult)}if(this.r&&this.appliedTransformations<4){var a=this.r.getValueAtTime(t);e.rotate(-a*this.r.mult)}else if(!this.r&&this.appliedTransformations<4){var o=this.rz.getValueAtTime(t),h=this.ry.getValueAtTime(t),l=this.rx.getValueAtTime(t),p=this.or.getValueAtTime(t);e.rotateZ(-o*this.rz.mult).rotateY(h*this.ry.mult).rotateX(l*this.rx.mult).rotateZ(-p[2]*this.or.mult).rotateY(p[1]*this.or.mult).rotateX(p[0]*this.or.mult)}if(this.data.p&&this.data.p.s){var c=this.px.getValueAtTime(t),d=this.py.getValueAtTime(t);if(this.data.p.z){var f=this.pz.getValueAtTime(t);e.translate(c*this.px.mult,d*this.py.mult,-f*this.pz.mult)}else e.translate(c*this.px.mult,d*this.py.mult,0)}else{var u=this.p.getValueAtTime(t);e.translate(u[0]*this.p.mult,u[1]*this.p.mult,-u[2]*this.p.mult)}return e}function n(){return this.v.clone(new Matrix)}var s=TransformPropertyFactory.getTransformProperty;TransformPropertyFactory.getTransformProperty=function(t,e,i){var a=s(t,e,i);return a.dynamicProperties.length?a.getValueAtTime=r.bind(a):a.getValueAtTime=n.bind(a),a.setGroupProperty=expressionHelpers.setGroupProperty,a};var a=PropertyFactory.getProp;PropertyFactory.getProp=function(r,n,s,o,h){var l=a(r,n,s,o,h);l.kf?l.getValueAtTime=expressionHelpers.getValueAtTime.bind(l):l.getValueAtTime=expressionHelpers.getStaticValueAtTime.bind(l),l.setGroupProperty=expressionHelpers.setGroupProperty,l.loopOut=t,l.loopIn=e,l.smooth=i,l.getVelocityAtTime=expressionHelpers.getVelocityAtTime.bind(l),l.getSpeedAtTime=expressionHelpers.getSpeedAtTime.bind(l),l.numKeys=1===n.a?n.k.length:0,l.propertyIndex=n.ix;var p=0;return 0!==s&&(p=createTypedArray("float32",1===n.a?n.k[0].s.length:n.k.length)),l._cachingAtTime={lastFrame:initialDefaultFrame,lastIndex:0,value:p},expressionHelpers.searchExpressions(r,n,l),l.k&&h.addDynamicProperty(l),l};var o=ShapePropertyFactory.getConstructorFunction(),h=ShapePropertyFactory.getKeyframedConstructorFunction();function l(){}l.prototype={vertices:function(t,e){this.k&&this.getValue();var i,r=this.v;void 0!==e&&(r=this.getValueAtTime(e,0));var n=r._length,s=r[t],a=r.v,o=createSizedArray(n);for(i=0;i<n;i+=1)o[i]="i"===t||"o"===t?[s[i][0]-a[i][0],s[i][1]-a[i][1]]:[s[i][0],s[i][1]];return o},points:function(t){return this.vertices("v",t)},inTangents:function(t){return this.vertices("i",t)},outTangents:function(t){return this.vertices("o",t)},isClosed:function(){return this.v.c},pointOnPath:function(t,e){var i=this.v;void 0!==e&&(i=this.getValueAtTime(e,0)),this._segmentsLength||(this._segmentsLength=bez.getSegmentsLength(i));for(var r,n=this._segmentsLength,s=n.lengths,a=n.totalLength*t,o=0,h=s.length,l=0;o<h;){if(l+s[o].addedLength>a){var p=o,c=i.c&&o===h-1?0:o+1,d=(a-l)/s[o].addedLength;r=bez.getPointInSegment(i.v[p],i.v[c],i.o[p],i.i[c],d,s[o]);break}l+=s[o].addedLength,o+=1}return r||(r=i.c?[i.v[0][0],i.v[0][1]]:[i.v[i._length-1][0],i.v[i._length-1][1]]),r},vectorOnPath:function(t,e,i){1==t?t=this.v.c:0==t&&(t=.999);var r=this.pointOnPath(t,e),n=this.pointOnPath(t+.001,e),s=n[0]-r[0],a=n[1]-r[1],o=Math.sqrt(Math.pow(s,2)+Math.pow(a,2));return 0===o?[0,0]:"tangent"===i?[s/o,a/o]:[-a/o,s/o]},tangentOnPath:function(t,e){return this.vectorOnPath(t,e,"tangent")},normalOnPath:function(t,e){return this.vectorOnPath(t,e,"normal")},setGroupProperty:expressionHelpers.setGroupProperty,getValueAtTime:expressionHelpers.getStaticValueAtTime},extendPrototype([l],o),extendPrototype([l],h),h.prototype.getValueAtTime=function(t){return this._cachingAtTime||(this._cachingAtTime={shapeValue:shapePool.clone(this.pv),lastIndex:0,lastTime:initialDefaultFrame}),t*=this.elem.globalData.frameRate,(t-=this.offsetTime)!==this._cachingAtTime.lastTime&&(this._cachingAtTime.lastIndex=this._cachingAtTime.lastTime<t?this._caching.lastIndex:0,this._cachingAtTime.lastTime=t,this.interpolateShape(t,this._cachingAtTime.shapeValue,this._cachingAtTime)),this._cachingAtTime.shapeValue},h.prototype.initiateExpression=ExpressionManager.initiateExpression;var p=ShapePropertyFactory.getShapeProp;ShapePropertyFactory.getShapeProp=function(t,e,i,r,n){var s=p(t,e,i,r,n);return s.propertyIndex=e.ix,s.lock=!1,3===i?expressionHelpers.searchExpressions(t,e.pt,s):4===i&&expressionHelpers.searchExpressions(t,e.ks,s),s.k&&t.addDynamicProperty(s),s}}function initialize$1(){addPropertyDecorator()}function addDecorator(){TextProperty.prototype.getExpressionValue=function(t,e){var i=this.calculateExpression(e);if(t.t!==i){var r={};return this.copyData(r,t),r.t=i.toString(),r.__complete=!1,r}return t},TextProperty.prototype.searchProperty=function(){var t=this.searchKeyframes(),e=this.searchExpressions();return this.kf=t||e,this.kf},TextProperty.prototype.searchExpressions=function(){return this.data.d.x?(this.calculateExpression=ExpressionManager.initiateExpression.bind(this)(this.elem,this.data.d,this),this.addEffect(this.getExpressionValue.bind(this)),!0):null}}function initialize(){addDecorator()}function SVGComposableEffect(){}function SVGTintFilter(t,e,i,r,n){this.filterManager=e;var s=createNS("feColorMatrix");s.setAttribute("type","matrix"),s.setAttribute("color-interpolation-filters","linearRGB"),s.setAttribute("values","0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0"),s.setAttribute("result",r+"_tint_1"),t.appendChild(s),(s=createNS("feColorMatrix")).setAttribute("type","matrix"),s.setAttribute("color-interpolation-filters","sRGB"),s.setAttribute("values","1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"),s.setAttribute("result",r+"_tint_2"),t.appendChild(s),this.matrixFilter=s;var a=this.createMergeNode(r,[n,r+"_tint_1",r+"_tint_2"]);t.appendChild(a)}function SVGFillFilter(t,e,i,r){this.filterManager=e;var n=createNS("feColorMatrix");n.setAttribute("type","matrix"),n.setAttribute("color-interpolation-filters","sRGB"),n.setAttribute("values","1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"),n.setAttribute("result",r),t.appendChild(n),this.matrixFilter=n}function SVGStrokeEffect(t,e,i){this.initialized=!1,this.filterManager=e,this.elem=i,this.paths=[]}function SVGTritoneFilter(t,e,i,r){this.filterManager=e;var n=createNS("feColorMatrix");n.setAttribute("type","matrix"),n.setAttribute("color-interpolation-filters","linearRGB"),n.setAttribute("values","0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0"),t.appendChild(n);var s=createNS("feComponentTransfer");s.setAttribute("color-interpolation-filters","sRGB"),s.setAttribute("result",r),this.matrixFilter=s;var a=createNS("feFuncR");a.setAttribute("type","table"),s.appendChild(a),this.feFuncR=a;var o=createNS("feFuncG");o.setAttribute("type","table"),s.appendChild(o),this.feFuncG=o;var h=createNS("feFuncB");h.setAttribute("type","table"),s.appendChild(h),this.feFuncB=h,t.appendChild(s)}function SVGProLevelsFilter(t,e,i,r){this.filterManager=e;var n=this.filterManager.effectElements,s=createNS("feComponentTransfer");(n[10].p.k||0!==n[10].p.v||n[11].p.k||1!==n[11].p.v||n[12].p.k||1!==n[12].p.v||n[13].p.k||0!==n[13].p.v||n[14].p.k||1!==n[14].p.v)&&(this.feFuncR=this.createFeFunc("feFuncR",s)),(n[17].p.k||0!==n[17].p.v||n[18].p.k||1!==n[18].p.v||n[19].p.k||1!==n[19].p.v||n[20].p.k||0!==n[20].p.v||n[21].p.k||1!==n[21].p.v)&&(this.feFuncG=this.createFeFunc("feFuncG",s)),(n[24].p.k||0!==n[24].p.v||n[25].p.k||1!==n[25].p.v||n[26].p.k||1!==n[26].p.v||n[27].p.k||0!==n[27].p.v||n[28].p.k||1!==n[28].p.v)&&(this.feFuncB=this.createFeFunc("feFuncB",s)),(n[31].p.k||0!==n[31].p.v||n[32].p.k||1!==n[32].p.v||n[33].p.k||1!==n[33].p.v||n[34].p.k||0!==n[34].p.v||n[35].p.k||1!==n[35].p.v)&&(this.feFuncA=this.createFeFunc("feFuncA",s)),(this.feFuncR||this.feFuncG||this.feFuncB||this.feFuncA)&&(s.setAttribute("color-interpolation-filters","sRGB"),t.appendChild(s)),(n[3].p.k||0!==n[3].p.v||n[4].p.k||1!==n[4].p.v||n[5].p.k||1!==n[5].p.v||n[6].p.k||0!==n[6].p.v||n[7].p.k||1!==n[7].p.v)&&((s=createNS("feComponentTransfer")).setAttribute("color-interpolation-filters","sRGB"),s.setAttribute("result",r),t.appendChild(s),this.feFuncRComposed=this.createFeFunc("feFuncR",s),this.feFuncGComposed=this.createFeFunc("feFuncG",s),this.feFuncBComposed=this.createFeFunc("feFuncB",s))}function SVGDropShadowEffect(t,e,i,r,n){var s=e.container.globalData.renderConfig.filterSize,a=e.data.fs||s;t.setAttribute("x",a.x||s.x),t.setAttribute("y",a.y||s.y),t.setAttribute("width",a.width||s.width),t.setAttribute("height",a.height||s.height),this.filterManager=e;var o=createNS("feGaussianBlur");o.setAttribute("in","SourceAlpha"),o.setAttribute("result",r+"_drop_shadow_1"),o.setAttribute("stdDeviation","0"),this.feGaussianBlur=o,t.appendChild(o);var h=createNS("feOffset");h.setAttribute("dx","25"),h.setAttribute("dy","0"),h.setAttribute("in",r+"_drop_shadow_1"),h.setAttribute("result",r+"_drop_shadow_2"),this.feOffset=h,t.appendChild(h);var l=createNS("feFlood");l.setAttribute("flood-color","#00ff00"),l.setAttribute("flood-opacity","1"),l.setAttribute("result",r+"_drop_shadow_3"),this.feFlood=l,t.appendChild(l);var p=createNS("feComposite");p.setAttribute("in",r+"_drop_shadow_3"),p.setAttribute("in2",r+"_drop_shadow_2"),p.setAttribute("operator","in"),p.setAttribute("result",r+"_drop_shadow_4"),t.appendChild(p);var c=this.createMergeNode(r,[r+"_drop_shadow_4",n]);t.appendChild(c)}SVGComposableEffect.prototype={createMergeNode:function(t,e){var i,r,n=createNS("feMerge");for(n.setAttribute("result",t),r=0;r<e.length;r+=1)(i=createNS("feMergeNode")).setAttribute("in",e[r]),n.appendChild(i),n.appendChild(i);return n}},extendPrototype([SVGComposableEffect],SVGTintFilter),SVGTintFilter.prototype.renderFrame=function(t){if(t||this.filterManager._mdf){var e=this.filterManager.effectElements[0].p.v,i=this.filterManager.effectElements[1].p.v,r=this.filterManager.effectElements[2].p.v/100;this.matrixFilter.setAttribute("values",i[0]-e[0]+" 0 0 0 "+e[0]+" "+(i[1]-e[1])+" 0 0 0 "+e[1]+" "+(i[2]-e[2])+" 0 0 0 "+e[2]+" 0 0 0 "+r+" 0")}},SVGFillFilter.prototype.renderFrame=function(t){if(t||this.filterManager._mdf){var e=this.filterManager.effectElements[2].p.v,i=this.filterManager.effectElements[6].p.v;this.matrixFilter.setAttribute("values","0 0 0 0 "+e[0]+" 0 0 0 0 "+e[1]+" 0 0 0 0 "+e[2]+" 0 0 0 "+i+" 0")}},SVGStrokeEffect.prototype.initialize=function(){var t,e,i,r,n=this.elem.layerElement.children||this.elem.layerElement.childNodes;for(1===this.filterManager.effectElements[1].p.v?(r=this.elem.maskManager.masksProperties.length,i=0):r=1+(i=this.filterManager.effectElements[0].p.v-1),(e=createNS("g")).setAttribute("fill","none"),e.setAttribute("stroke-linecap","round"),e.setAttribute("stroke-dashoffset",1);i<r;i+=1)t=createNS("path"),e.appendChild(t),this.paths.push({p:t,m:i});if(3===this.filterManager.effectElements[10].p.v){var s=createNS("mask"),a=createElementID();s.setAttribute("id",a),s.setAttribute("mask-type","alpha"),s.appendChild(e),this.elem.globalData.defs.appendChild(s);var o=createNS("g");for(o.setAttribute("mask","url("+getLocationHref()+"#"+a+")");n[0];)o.appendChild(n[0]);this.elem.layerElement.appendChild(o),this.masker=s,e.setAttribute("stroke","#fff")}else if(1===this.filterManager.effectElements[10].p.v||2===this.filterManager.effectElements[10].p.v){if(2===this.filterManager.effectElements[10].p.v)for(n=this.elem.layerElement.children||this.elem.layerElement.childNodes;n.length;)this.elem.layerElement.removeChild(n[0]);this.elem.layerElement.appendChild(e),this.elem.layerElement.removeAttribute("mask"),e.setAttribute("stroke","#fff")}this.initialized=!0,this.pathMasker=e},SVGStrokeEffect.prototype.renderFrame=function(t){var e;this.initialized||this.initialize();var i,r,n=this.paths.length;for(e=0;e<n;e+=1)if(-1!==this.paths[e].m&&(i=this.elem.maskManager.viewData[this.paths[e].m],r=this.paths[e].p,(t||this.filterManager._mdf||i.prop._mdf)&&r.setAttribute("d",i.lastPath),t||this.filterManager.effectElements[9].p._mdf||this.filterManager.effectElements[4].p._mdf||this.filterManager.effectElements[7].p._mdf||this.filterManager.effectElements[8].p._mdf||i.prop._mdf)){var s;if(0!==this.filterManager.effectElements[7].p.v||100!==this.filterManager.effectElements[8].p.v){var a=.01*Math.min(this.filterManager.effectElements[7].p.v,this.filterManager.effectElements[8].p.v),o=.01*Math.max(this.filterManager.effectElements[7].p.v,this.filterManager.effectElements[8].p.v),h=r.getTotalLength();s="0 0 0 "+h*a+" ";var l,p=h*(o-a),c=1+2*this.filterManager.effectElements[4].p.v*this.filterManager.effectElements[9].p.v*.01,d=Math.floor(p/c);for(l=0;l<d;l+=1)s+="1 "+2*this.filterManager.effectElements[4].p.v*this.filterManager.effectElements[9].p.v*.01+" ";s+="0 "+10*h+" 0 0"}else s="1 "+2*this.filterManager.effectElements[4].p.v*this.filterManager.effectElements[9].p.v*.01;r.setAttribute("stroke-dasharray",s)}if((t||this.filterManager.effectElements[4].p._mdf)&&this.pathMasker.setAttribute("stroke-width",2*this.filterManager.effectElements[4].p.v),(t||this.filterManager.effectElements[6].p._mdf)&&this.pathMasker.setAttribute("opacity",this.filterManager.effectElements[6].p.v),(1===this.filterManager.effectElements[10].p.v||2===this.filterManager.effectElements[10].p.v)&&(t||this.filterManager.effectElements[3].p._mdf)){var f=this.filterManager.effectElements[3].p.v;this.pathMasker.setAttribute("stroke","rgb("+bmFloor(255*f[0])+","+bmFloor(255*f[1])+","+bmFloor(255*f[2])+")")}},SVGTritoneFilter.prototype.renderFrame=function(t){if(t||this.filterManager._mdf){var e=this.filterManager.effectElements[0].p.v,i=this.filterManager.effectElements[1].p.v,r=this.filterManager.effectElements[2].p.v,n=r[0]+" "+i[0]+" "+e[0],s=r[1]+" "+i[1]+" "+e[1],a=r[2]+" "+i[2]+" "+e[2];this.feFuncR.setAttribute("tableValues",n),this.feFuncG.setAttribute("tableValues",s),this.feFuncB.setAttribute("tableValues",a)}},SVGProLevelsFilter.prototype.createFeFunc=function(t,e){var i=createNS(t);return i.setAttribute("type","table"),e.appendChild(i),i},SVGProLevelsFilter.prototype.getTableValue=function(t,e,i,r,n){for(var s,a,o=0,h=Math.min(t,e),l=Math.max(t,e),p=Array.call(null,{length:256}),c=0,d=n-r,f=e-t;o<=256;)a=(s=o/256)<=h?f<0?n:r:s>=l?f<0?r:n:r+d*Math.pow((s-t)/f,1/i),p[c]=a,c+=1,o+=256/255;return p.join(" ")},SVGProLevelsFilter.prototype.renderFrame=function(t){if(t||this.filterManager._mdf){var e,i=this.filterManager.effectElements;this.feFuncRComposed&&(t||i[3].p._mdf||i[4].p._mdf||i[5].p._mdf||i[6].p._mdf||i[7].p._mdf)&&(e=this.getTableValue(i[3].p.v,i[4].p.v,i[5].p.v,i[6].p.v,i[7].p.v),this.feFuncRComposed.setAttribute("tableValues",e),this.feFuncGComposed.setAttribute("tableValues",e),this.feFuncBComposed.setAttribute("tableValues",e)),this.feFuncR&&(t||i[10].p._mdf||i[11].p._mdf||i[12].p._mdf||i[13].p._mdf||i[14].p._mdf)&&(e=this.getTableValue(i[10].p.v,i[11].p.v,i[12].p.v,i[13].p.v,i[14].p.v),this.feFuncR.setAttribute("tableValues",e)),this.feFuncG&&(t||i[17].p._mdf||i[18].p._mdf||i[19].p._mdf||i[20].p._mdf||i[21].p._mdf)&&(e=this.getTableValue(i[17].p.v,i[18].p.v,i[19].p.v,i[20].p.v,i[21].p.v),this.feFuncG.setAttribute("tableValues",e)),this.feFuncB&&(t||i[24].p._mdf||i[25].p._mdf||i[26].p._mdf||i[27].p._mdf||i[28].p._mdf)&&(e=this.getTableValue(i[24].p.v,i[25].p.v,i[26].p.v,i[27].p.v,i[28].p.v),this.feFuncB.setAttribute("tableValues",e)),this.feFuncA&&(t||i[31].p._mdf||i[32].p._mdf||i[33].p._mdf||i[34].p._mdf||i[35].p._mdf)&&(e=this.getTableValue(i[31].p.v,i[32].p.v,i[33].p.v,i[34].p.v,i[35].p.v),this.feFuncA.setAttribute("tableValues",e))}},extendPrototype([SVGComposableEffect],SVGDropShadowEffect),SVGDropShadowEffect.prototype.renderFrame=function(t){if(t||this.filterManager._mdf){if((t||this.filterManager.effectElements[4].p._mdf)&&this.feGaussianBlur.setAttribute("stdDeviation",this.filterManager.effectElements[4].p.v/4),t||this.filterManager.effectElements[0].p._mdf){var e=this.filterManager.effectElements[0].p.v;this.feFlood.setAttribute("flood-color",rgbToHex(Math.round(255*e[0]),Math.round(255*e[1]),Math.round(255*e[2])))}if((t||this.filterManager.effectElements[1].p._mdf)&&this.feFlood.setAttribute("flood-opacity",this.filterManager.effectElements[1].p.v/255),t||this.filterManager.effectElements[2].p._mdf||this.filterManager.effectElements[3].p._mdf){var i=this.filterManager.effectElements[3].p.v,r=(this.filterManager.effectElements[2].p.v-90)*degToRads,n=i*Math.cos(r),s=i*Math.sin(r);this.feOffset.setAttribute("dx",n),this.feOffset.setAttribute("dy",s)}}};var _svgMatteSymbols=[];function SVGMatte3Effect(t,e,i){this.initialized=!1,this.filterManager=e,this.filterElem=t,this.elem=i,i.matteElement=createNS("g"),i.matteElement.appendChild(i.layerElement),i.matteElement.appendChild(i.transformedElement),i.baseElement=i.matteElement}function SVGGaussianBlurEffect(t,e,i,r){t.setAttribute("x","-100%"),t.setAttribute("y","-100%"),t.setAttribute("width","300%"),t.setAttribute("height","300%"),this.filterManager=e;var n=createNS("feGaussianBlur");n.setAttribute("result",r),t.appendChild(n),this.feGaussianBlur=n}return SVGMatte3Effect.prototype.findSymbol=function(t){for(var e=0,i=_svgMatteSymbols.length;e<i;){if(_svgMatteSymbols[e]===t)return _svgMatteSymbols[e];e+=1}return null},SVGMatte3Effect.prototype.replaceInParent=function(t,e){var i=t.layerElement.parentNode;if(i){for(var r,n=i.children,s=0,a=n.length;s<a&&n[s]!==t.layerElement;)s+=1;s<=a-2&&(r=n[s+1]);var o=createNS("use");o.setAttribute("href","#"+e),r?i.insertBefore(o,r):i.appendChild(o)}},SVGMatte3Effect.prototype.setElementAsMask=function(t,e){if(!this.findSymbol(e)){var i=createElementID(),r=createNS("mask");r.setAttribute("id",e.layerId),r.setAttribute("mask-type","alpha"),_svgMatteSymbols.push(e);var n=t.globalData.defs;n.appendChild(r);var s=createNS("symbol");s.setAttribute("id",i),this.replaceInParent(e,i),s.appendChild(e.layerElement),n.appendChild(s);var a=createNS("use");a.setAttribute("href","#"+i),r.appendChild(a),e.data.hd=!1,e.show()}t.setMatte(e.layerId)},SVGMatte3Effect.prototype.initialize=function(){for(var t=this.filterManager.effectElements[0].p.v,e=this.elem.comp.elements,i=0,r=e.length;i<r;)e[i]&&e[i].data.ind===t&&this.setElementAsMask(this.elem,e[i]),i+=1;this.initialized=!0},SVGMatte3Effect.prototype.renderFrame=function(){this.initialized||this.initialize()},SVGGaussianBlurEffect.prototype.renderFrame=function(t){if(t||this.filterManager._mdf){var e=.3*this.filterManager.effectElements[0].p.v,i=this.filterManager.effectElements[1].p.v,r=3==i?0:e,n=2==i?0:e;this.feGaussianBlur.setAttribute("stdDeviation",r+" "+n);var s=1==this.filterManager.effectElements[2].p.v?"wrap":"duplicate";this.feGaussianBlur.setAttribute("edgeMode",s)}},registerRenderer("canvas",CanvasRenderer),registerRenderer("html",HybridRenderer),registerRenderer("svg",SVGRenderer),ShapeModifiers.registerModifier("tm",TrimModifier),ShapeModifiers.registerModifier("pb",PuckerAndBloatModifier),ShapeModifiers.registerModifier("rp",RepeaterModifier),ShapeModifiers.registerModifier("rd",RoundCornersModifier),ShapeModifiers.registerModifier("zz",ZigZagModifier),ShapeModifiers.registerModifier("op",OffsetPathModifier),setExpressionsPlugin(Expressions),setExpressionInterfaces(getInterface),initialize$1(),initialize(),registerEffect(20,SVGTintFilter,!0),registerEffect(21,SVGFillFilter,!0),registerEffect(22,SVGStrokeEffect,!1),registerEffect(23,SVGTritoneFilter,!0),registerEffect(24,SVGProLevelsFilter,!0),registerEffect(25,SVGDropShadowEffect,!0),registerEffect(28,SVGMatte3Effect,!1),registerEffect(29,SVGGaussianBlurEffect,!0),lottie},module.exports=factory())})(lottie$1,lottie$1.exports);var lottie=lottie$1.exports,_templateObject$1,styles=r$3(_templateObject$1||(_templateObject$1=_taggedTemplateLiteral(["\n  * {\n    box-sizing: border-box;\n  }\n\n  :host {\n    --lottie-player-toolbar-height: 35px;\n    --lottie-player-toolbar-background-color: transparent;\n    --lottie-player-toolbar-icon-color: #999;\n    --lottie-player-toolbar-icon-hover-color: #222;\n    --lottie-player-toolbar-icon-active-color: #555;\n    --lottie-player-seeker-track-color: #ccc;\n    --lottie-player-seeker-thumb-color: rgba(0, 107, 120, 0.8);\n    --lottie-player-seeker-display: block;\n\n    display: block;\n    width: 100%;\n    height: 100%;\n  }\n\n  .main {\n    display: flex;\n    flex-direction: column;\n    height: 100%;\n    width: 100%;\n  }\n\n  .animation {\n    width: 100%;\n    height: 100%;\n    display: flex;\n  }\n  .animation.controls {\n    height: calc(100% - 35px);\n  }\n\n  .toolbar {\n    display: flex;\n    align-items: center;\n    justify-items: center;\n    background-color: var(--lottie-player-toolbar-background-color);\n    margin: 0 5px;\n    height: 35px;\n  }\n\n  .toolbar button {\n    cursor: pointer;\n    fill: var(--lottie-player-toolbar-icon-color);\n    display: flex;\n    background: none;\n    border: 0;\n    padding: 0;\n    outline: none;\n    height: 100%;\n  }\n\n  .toolbar button:hover {\n    fill: var(--lottie-player-toolbar-icon-hover-color);\n  }\n\n  .toolbar button.active {\n    fill: var(--lottie-player-toolbar-icon-active-color);\n  }\n\n  .toolbar button.active:hover {\n    fill: var(--lottie-player-toolbar-icon-hover-color);\n  }\n\n  .toolbar button:focus {\n    outline: 1px dotted var(--lottie-player-toolbar-icon-active-color);\n  }\n\n  .toolbar button svg {\n  }\n\n  .toolbar button.disabled svg {\n    display: none;\n  }\n\n  .seeker {\n    -webkit-appearance: none;\n    width: 95%;\n    outline: none;\n    background-color: var(--lottie-player-toolbar-background-color);\n    display: var(--lottie-player-seeker-display);\n  }\n\n  .seeker::-webkit-slider-runnable-track {\n    width: 100%;\n    height: 5px;\n    cursor: pointer;\n    background: var(--lottie-player-seeker-track-color);\n    border-radius: 3px;\n  }\n  .seeker::-webkit-slider-thumb {\n    height: 15px;\n    width: 15px;\n    border-radius: 50%;\n    background: var(--lottie-player-seeker-thumb-color);\n    cursor: pointer;\n    -webkit-appearance: none;\n    margin-top: -5px;\n  }\n  .seeker:focus::-webkit-slider-runnable-track {\n    background: #999;\n  }\n  .seeker::-moz-range-track {\n    width: 100%;\n    height: 5px;\n    cursor: pointer;\n    background: var(--lottie-player-seeker-track-color);\n    border-radius: 3px;\n  }\n  .seeker::-moz-range-thumb {\n    height: 15px;\n    width: 15px;\n    border-radius: 50%;\n    background: var(--lottie-player-seeker-thumb-color);\n    cursor: pointer;\n  }\n  .seeker::-ms-track {\n    width: 100%;\n    height: 5px;\n    cursor: pointer;\n    background: transparent;\n    border-color: transparent;\n    color: transparent;\n  }\n  .seeker::-ms-fill-lower {\n    background: var(--lottie-player-seeker-track-color);\n    border-radius: 3px;\n  }\n  .seeker::-ms-fill-upper {\n    background: var(--lottie-player-seeker-track-color);\n    border-radius: 3px;\n  }\n  .seeker::-ms-thumb {\n    border: 0;\n    height: 15px;\n    width: 15px;\n    border-radius: 50%;\n    background: var(--lottie-player-seeker-thumb-color);\n    cursor: pointer;\n  }\n  .seeker:focus::-ms-fill-lower {\n    background: var(--lottie-player-seeker-track-color);\n  }\n  .seeker:focus::-ms-fill-upper {\n    background: var(--lottie-player-seeker-track-color);\n  }\n\n  .error {\n    display: flex;\n    justify-content: center;\n    height: 100%;\n    align-items: center;\n  }\n"]))),LOTTIE_PLAYER_VERSION="1.7.1",LOTTIE_WEB_VERSION="^5.10.0",_templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5,PlayerState,PlayMode,PlayerEvents;function parseSrc(t){if("object"==typeof t)return t;try{return JSON.parse(t)}catch(e){return new URL(t,window.location.href).toString()}}function isLottie(t){return["v","ip","op","layers","fr","w","h"].every((e=>Object.prototype.hasOwnProperty.call(t,e)))}function fromURL(t){return _fromURL.apply(this,arguments)}function _fromURL(){return(_fromURL=_asyncToGenerator((function*(t){if("string"!=typeof t)throw new Error("The url value must be a string");var e;try{var i=new URL(t),r=yield fetch(i.toString());e=yield r.json()}catch(t){throw new Error("An error occurred while trying to load the Lottie file from URL")}return e}))).apply(this,arguments)}!function(t){t.Destroyed="destroyed",t.Error="error",t.Frozen="frozen",t.Loading="loading",t.Paused="paused",t.Playing="playing",t.Stopped="stopped"}(PlayerState||(PlayerState={})),function(t){t.Bounce="bounce",t.Normal="normal"}(PlayMode||(PlayMode={})),function(t){t.Complete="complete",t.Destroyed="destroyed",t.Error="error",t.Frame="frame",t.Freeze="freeze",t.Load="load",t.Loop="loop",t.Pause="pause",t.Play="play",t.Ready="ready",t.Rendered="rendered",t.Stop="stop"}(PlayerEvents||(PlayerEvents={}));var LottiePlayer=class extends s{constructor(){super(...arguments),this.autoplay=!1,this.background="transparent",this.controls=!1,this.currentState=PlayerState.Loading,this.description="Lottie animation",this.direction=1,this.disableCheck=!1,this.disableShadowDOM=!1,this.hover=!1,this.intermission=1,this.loop=!1,this.mode=PlayMode.Normal,this.preserveAspectRatio="xMidYMid meet",this.renderer="svg",this.speed=1,this._io=void 0,this._counter=1}load(t){var e=this;return _asyncToGenerator((function*(){var i={container:e.container,loop:!1,autoplay:!1,renderer:e.renderer,rendererSettings:Object.assign({preserveAspectRatio:e.preserveAspectRatio,clearCanvas:!1,progressiveLoad:!0,hideOnTransparent:!0},e.viewBoxSize&&{viewBoxSize:e.viewBoxSize})};try{var r=parseSrc(t),n={},s="string"==typeof r?"path":"animationData";e._lottie&&e._lottie.destroy(),e.webworkers&&lottie$1.exports.useWebWorker(!0),e._lottie=lottie$1.exports.loadAnimation(Object.assign(Object.assign({},i),{[s]:r})),e._attachEventListeners(),e.disableCheck||("path"===s?(n=yield fromURL(r),s="animationData"):n=r,isLottie(n)||(e.currentState=PlayerState.Error,e.dispatchEvent(new CustomEvent(PlayerEvents.Error))))}catch(t){e.currentState=PlayerState.Error,e.dispatchEvent(new CustomEvent(PlayerEvents.Error))}}))()}getLottie(){return this._lottie}getVersions(){return{lottieWebVersion:LOTTIE_WEB_VERSION,lottiePlayerVersion:LOTTIE_PLAYER_VERSION}}play(){this._lottie&&(this._lottie.play(),this.currentState=PlayerState.Playing,this.dispatchEvent(new CustomEvent(PlayerEvents.Play)))}pause(){this._lottie&&(this._lottie.pause(),this.currentState=PlayerState.Paused,this.dispatchEvent(new CustomEvent(PlayerEvents.Pause)))}stop(){this._lottie&&(this._counter=1,this._lottie.stop(),this.currentState=PlayerState.Stopped,this.dispatchEvent(new CustomEvent(PlayerEvents.Stop)))}destroy(){this._lottie&&(this._lottie.destroy(),this._lottie=null,this.currentState=PlayerState.Destroyed,this.dispatchEvent(new CustomEvent(PlayerEvents.Destroyed)),this.remove())}seek(t){if(this._lottie){var e=/^(\d+)(%?)$/.exec(t.toString());if(e){var i="%"===e[2]?this._lottie.totalFrames*Number(e[1])/100:Number(e[1]);this.seeker=i,this.currentState===PlayerState.Playing?this._lottie.goToAndPlay(i,!0):(this._lottie.goToAndStop(i,!0),this._lottie.pause())}}}snapshot(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];if(this.shadowRoot){var e=this.shadowRoot.querySelector(".animation svg"),i=(new XMLSerializer).serializeToString(e);if(t){var r=document.createElement("a");r.href="data:image/svg+xml;charset=utf-8,".concat(encodeURIComponent(i)),r.download="download_".concat(this.seeker,".svg"),document.body.appendChild(r),r.click(),document.body.removeChild(r)}return i}}setSpeed(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;this._lottie&&this._lottie.setSpeed(t)}setDirection(t){this._lottie&&this._lottie.setDirection(t)}setLooping(t){this._lottie&&(this.loop=t,this._lottie.loop=t)}togglePlay(){return this.currentState===PlayerState.Playing?this.pause():this.play()}toggleLooping(){this.setLooping(!this.loop)}resize(){this._lottie&&this._lottie.resize()}static get styles(){return styles}disconnectedCallback(){this.isConnected||(this._io&&(this._io.disconnect(),this._io=void 0),document.removeEventListener("visibilitychange",(()=>this._onVisibilityChange())),this.destroy())}render(){var t=this.controls?"main controls":"main",e=this.controls?"animation controls":"animation";return $(_templateObject||(_templateObject=_taggedTemplateLiteral([' <div\n      id="animation-container"\n      class=','\n      lang="en"\n      aria-label=','\n      role="img"\n    >\n      <div\n        id="animation"\n        class=','\n        style="background:',';"\n      >\n        ',"\n      </div>\n      ","\n    </div>"])),t,this.description,e,this.background,this.currentState===PlayerState.Error?$(_templateObject2||(_templateObject2=_taggedTemplateLiteral(['<div class="error">⚠️</div>']))):void 0,this.controls&&!this.disableShadowDOM?this.renderControls():void 0)}createRenderRoot(){return this.disableShadowDOM&&(this.style.display="block"),this.disableShadowDOM?this:super.createRenderRoot()}firstUpdated(){"IntersectionObserver"in window&&(this._io=new IntersectionObserver((t=>{t[0].isIntersecting?this.currentState===PlayerState.Frozen&&this.play():this.currentState===PlayerState.Playing&&this.freeze()})),this._io.observe(this.container)),void 0!==document.hidden&&document.addEventListener("visibilitychange",(()=>this._onVisibilityChange())),this.src&&this.load(this.src),this.dispatchEvent(new CustomEvent(PlayerEvents.Rendered))}renderControls(){var t=this.currentState===PlayerState.Playing,e=this.currentState===PlayerState.Paused,i=this.currentState===PlayerState.Stopped;return $(_templateObject3||(_templateObject3=_taggedTemplateLiteral(['\n      <div\n        id="lottie-controls"\n        aria-label="lottie-animation-controls"\n        class="toolbar"\n      >\n        <button\n          id="lottie-play-button"\n          @click=',"\n          class=",'\n          style="align-items:center;"\n          tabindex="0"\n          aria-label="play-pause"\n        >\n          ','\n        </button>\n        <button\n          id="lottie-stop-button"\n          @click=',"\n          class=",'\n          style="align-items:center;"\n          tabindex="0"\n          aria-label="stop"\n        >\n          <svg width="24" height="24" aria-hidden="true" focusable="false">\n            <path d="M6 6h12v12H6V6z" />\n          </svg>\n        </button>\n        <input\n          id="lottie-seeker-input"\n          class="seeker"\n          type="range"\n          min="0"\n          step="1"\n          max="100"\n          .value=',"\n          @input=","\n          @mousedown=","\n          @mouseup=",'\n          aria-valuemin="1"\n          aria-valuemax="100"\n          role="slider"\n          aria-valuenow=','\n          tabindex="0"\n          aria-label="lottie-seek-input"\n        />\n        <button\n          id="lottie-loop-toggle"\n          @click=',"\n          class=",'\n          style="align-items:center;"\n          tabindex="0"\n          aria-label="loop-toggle"\n        >\n          <svg width="24" height="24" aria-hidden="true" focusable="false">\n            <path\n              d="M17.016 17.016v-4.031h1.969v6h-12v3l-3.984-3.984 3.984-3.984v3h10.031zM6.984 6.984v4.031H5.015v-6h12v-3l3.984 3.984-3.984 3.984v-3H6.984z"\n            />\n          </svg>\n        </button>\n      </div>\n    '])),this.togglePlay,t||e?"active":"",$(t?_templateObject4||(_templateObject4=_taggedTemplateLiteral(['<svg\n                width="24"\n                height="24"\n                aria-hidden="true"\n                focusable="false"\n              >\n                <path\n                  d="M14.016 5.016H18v13.969h-3.984V5.016zM6 18.984V5.015h3.984v13.969H6z"\n                />\n              </svg>'])):_templateObject5||(_templateObject5=_taggedTemplateLiteral(['<svg\n                width="24"\n                height="24"\n                aria-hidden="true"\n                focusable="false"\n              >\n                <path d="M8.016 5.016L18.985 12 8.016 18.984V5.015z" />\n              </svg>']))),this.stop,i?"active":"",this.seeker,this._handleSeekChange,(()=>{this._prevState=this.currentState,this.freeze()}),(()=>{this._prevState===PlayerState.Playing&&this.play()}),this.seeker,this.toggleLooping,this.loop?"active":"")}_onVisibilityChange(){!0===document.hidden&&this.currentState===PlayerState.Playing?this.freeze():this.currentState===PlayerState.Frozen&&this.play()}_handleSeekChange(t){if(this._lottie&&!isNaN(t.target.value)){var e=t.target.value/100*this._lottie.totalFrames;this.seek(e)}}_attachEventListeners(){this._lottie.addEventListener("enterFrame",(()=>{this.seeker=this._lottie.currentFrame/this._lottie.totalFrames*100,this.dispatchEvent(new CustomEvent(PlayerEvents.Frame,{detail:{frame:this._lottie.currentFrame,seeker:this.seeker}}))})),this._lottie.addEventListener("complete",(()=>{if(this.currentState===PlayerState.Playing){if(!this.loop||this.count&&this._counter>=this.count){if(this.dispatchEvent(new CustomEvent(PlayerEvents.Complete)),this.mode!==PlayMode.Bounce)return;if(0===this._lottie.currentFrame)return}this.mode===PlayMode.Bounce?(this.count&&(this._counter+=.5),setTimeout((()=>{this.dispatchEvent(new CustomEvent(PlayerEvents.Loop)),this.currentState===PlayerState.Playing&&(this._lottie.setDirection(-1*this._lottie.playDirection),this._lottie.play())}),this.intermission)):(this.count&&(this._counter+=1),window.setTimeout((()=>{this.dispatchEvent(new CustomEvent(PlayerEvents.Loop)),this.currentState===PlayerState.Playing&&(-1===this.direction?(this.seek("99%"),this.play()):(this._lottie.stop(),this._lottie.play()))}),this.intermission))}else this.dispatchEvent(new CustomEvent(PlayerEvents.Complete))})),this._lottie.addEventListener("DOMLoaded",(()=>{this.setSpeed(this.speed),this.setDirection(this.direction),this.autoplay&&(-1===this.direction&&this.seek("100%"),this.play()),this.dispatchEvent(new CustomEvent(PlayerEvents.Ready))})),this._lottie.addEventListener("data_ready",(()=>{this.dispatchEvent(new CustomEvent(PlayerEvents.Load))})),this._lottie.addEventListener("data_failed",(()=>{this.currentState=PlayerState.Error,this.dispatchEvent(new CustomEvent(PlayerEvents.Error))})),this.container.addEventListener("mouseenter",(()=>{this.hover&&this.currentState!==PlayerState.Playing&&this.play()})),this.container.addEventListener("mouseleave",(()=>{this.hover&&this.currentState===PlayerState.Playing&&this.stop()}))}freeze(){this._lottie&&(this._lottie.pause(),this.currentState=PlayerState.Frozen,this.dispatchEvent(new CustomEvent(PlayerEvents.Freeze)))}};__decorate([e$5({type:Boolean})],LottiePlayer.prototype,"autoplay",void 0),__decorate([e$5({type:String,reflect:!0})],LottiePlayer.prototype,"background",void 0),__decorate([e$5({type:Boolean})],LottiePlayer.prototype,"controls",void 0),__decorate([e$5({type:Number})],LottiePlayer.prototype,"count",void 0),__decorate([e$5({type:String})],LottiePlayer.prototype,"currentState",void 0),__decorate([e$5({type:String})],LottiePlayer.prototype,"description",void 0),__decorate([e$5({type:Number})],LottiePlayer.prototype,"direction",void 0),__decorate([e$5({type:Boolean})],LottiePlayer.prototype,"disableCheck",void 0),__decorate([e$5({type:Boolean})],LottiePlayer.prototype,"disableShadowDOM",void 0),__decorate([e$5({type:Boolean})],LottiePlayer.prototype,"hover",void 0),__decorate([e$5()],LottiePlayer.prototype,"intermission",void 0),__decorate([e$5({type:Boolean,reflect:!0})],LottiePlayer.prototype,"loop",void 0),__decorate([e$5()],LottiePlayer.prototype,"mode",void 0),__decorate([e$5({type:String})],LottiePlayer.prototype,"preserveAspectRatio",void 0),__decorate([e$5({type:String})],LottiePlayer.prototype,"renderer",void 0),__decorate([e$5({type:String})],LottiePlayer.prototype,"viewBoxSize",void 0),__decorate([e$5()],LottiePlayer.prototype,"seeker",void 0),__decorate([e$5({type:Number})],LottiePlayer.prototype,"speed",void 0),__decorate([e$5({type:String})],LottiePlayer.prototype,"src",void 0),__decorate([e$5({type:Boolean})],LottiePlayer.prototype,"webworkers",void 0),__decorate([i(".animation")],LottiePlayer.prototype,"container",void 0),LottiePlayer=__decorate([n$1("lottie-player")],LottiePlayer)},4239:function(t,e,i){"use strict";i.d(e,{A:function(){return r}});class r{constructor(t,e,i,r,n,s,a,o){this.shortcode=e,this.$element=r,t("tools").addTool(e.$id,'<ux-banner-tool id="'+e.$id+'"/>',r.find(".banner-layers")),i.$on(a.START,(function(i,r){r.shortcode.isChildOf(e)&&(r.preventDefault(),r.setContainment(e.$element.find(".banner-layers")),t("tools").showTool(e.$id))})),i.$on(a.MOVE,(function(i,r){if(!r.shortcode.isChildOf(e))return;let n=function(i){var r=null,n=null,s=i.innerY,a=i.constrains.width-(i.innerX+i.virtual.width),o=i.constrains.height-(i.innerY+i.virtual.height),h=i.innerX,l=_.min([{name:"right",distance:a},{name:"left",distance:h}],(function(t){return t.distance})),p=_.min([{name:"top",distance:s},{name:"bottom",distance:o}],(function(t){return t.distance})),c=Math.abs(s-o)<i.constrains.height/100*5,d=Math.abs(h-a)<i.constrains.width/100*5;return t("tools").getTool(e.$id).scope().grid.highlightHorizontalCenter(c),t("tools").getTool(e.$id).scope().grid.highlightVerticalCenter(d),r=round(l.distance/i.constrains.width*100,5),n=round(p.distance/i.constrains.height*100,5),"right"===l.name&&(r=100-r),"bottom"===p.name&&(n=100-n),c&&(n=50),d&&(r=50),{x:r=(r=r>=100?100:r)<=0?0:r,y:n=(n=n>=100?100:n)<=0?0:n}}(r);r.shortcode.$element.addClass("text-box-dragging"),r.shortcode.options.positionX=n.x,r.shortcode.options.positionY=n.y})),i.$on(a.END,(function(i,r){r.shortcode.isChildOf(e)&&(r.shortcode.$element.removeClass("text-box-dragging"),t("tools").hideTool(e.$id))})),i.$on(o.ATTACHED,(function(t,i){i.isChildOf(e)})),i.$on("$destroy",(function(){t("tools").removeTool(e.$id)})),i.$watch((()=>e.options.height),(function(t,i){t!==i&&e.parent.is("ux_slider")&&e.parent.$scope.$customCtrl.setHeight()}))}onResizeBottomStart(){const t=this.$element.get(0),{height:e}=this.shortcode.options,[,i,r]=(e||"0px").match(/^([\d.]+)(.*)/)||[];this.resizeUnit=r||"px",this.resizeOffsetHeight=t?t.offsetHeight:0,this.resizeValue=parseInt(i,10)}onResizeBottomMove(t){const e=this.resizeValue*(t.deltaY/this.resizeOffsetHeight),i=Math.max(this.resizeValue+e,0);this.shortcode.options.height=`${Number(i.toFixed(2))}${this.resizeUnit}`}}r.$inject=["app","shortcode","$scope","$element","$timeout","$interpolate","DragEvent","ShortcodeEvent"]},4268:function(t,e,i){"use strict";i.d(e,{A:function(){return r}});class r{constructor(t,e){this.app=t,this.shortcode=e}$getShortcodeInfo(){return this.shortcode._blockId!==this.shortcode.options.id&&(this.shortcode._blockId=this.shortcode.options.id,jQuery.get(i.g.flatsomeVars.ajaxurl,{action:"flatsome_block_title",block_id:this.shortcode.options.id},(({data:t})=>{this.shortcode._blockTitle=t.block_title,this.app.apply()}))),this.shortcode._blockTitle}}r.$inject=["app","shortcode"]},9071:function(t,e,i){"use strict";i.d(e,{A:function(){return r}});class r{constructor(t,e,i){this.app=t,this.shortcode=e,this.$element=i,this.minColumns=1,this.maxColumns=12}onResizeStart(t){this.screenWidth=window.innerWidth,this.rowWidth=this.shortcode.parent.$element.width(),this.columnWidth=this.rowWidth/this.maxColumns,this.currentColumn=function(t,e,i){let r=t.$element.offset().left+i/2-t.parent.$element.offset().left;return Math.floor(r/i)}(this.shortcode,this.rowWidth,this.columnWidth),this.currentColumnSpans=this.shortcode.options.span,this.nextColumnSpans=this.shortcode.nextSibling?this.shortcode.nextSibling.options.span:this.maxColumns,this.shortcode.$element.addClass("uxb-is-resizing"),this.shortcode.nextSibling&&this.shortcode.nextSibling.$element.addClass("uxb-is-resizing")}onResizeMove(t){let e=Math.floor((t.deltaX+this.columnWidth/2)/this.columnWidth);this.currentColumnSpans+e<this.minColumns&&(e=-(this.currentColumnSpans-1)),this.currentColumnSpans+e>this.maxColumns&&(e=this.maxColumns-this.currentColumnSpans),this.screenWidth>600&&this.shortcode.nextSibling&&(this.nextColumnSpans-e<this.minColumns&&(e=this.nextColumnSpans-1),this.currentColumn+this.currentColumnSpans+this.nextColumnSpans<=this.maxColumns&&(this.shortcode.nextSibling.options.span=this.nextColumnSpans-e,this.shortcode.nextSibling.apply())),this.shortcode.options.span=this.currentColumnSpans+e}onResizeEnd(t){this.shortcode.$element.removeClass("uxb-is-resizing"),this.shortcode.nextSibling&&this.shortcode.nextSibling.$element.removeClass("uxb-is-resizing"),delete this.screenWidth,delete this.rowWidth,delete this.columnWidth,delete this.currentColumn,delete this.currentColumnSpans,delete this.nextColumnSpans}}r.$inject=["app","shortcode","$element"]},4082:function(t,e,i){"use strict";i.d(e,{A:function(){return r}});class r{constructor(t,e,i){this.app=t,this.shortcode=e}onResizeRightStart(t){this.initColumns=this.shortcode.options.span,this.maxColumns=12}onResizeRightMove(t){let e=this.shortcode.parent.$element.width()/12,i=Math.floor((t.deltaX+e/2)/e);i<=1-this.initColumns&&(i=1-this.initColumns),i>=this.maxColumns-1&&(i=this.maxColumns-1),this.shortcode.options.span=this.initColumns+i}onResizeRightEnd(t){delete this.currentColumnSpans,delete this.maxColumns}onResizeBottomStart(t){this.containerHeight=this.shortcode.parent.options.height,this.initElementHeight=this.shortcode.$element.height(),this.initOptionHeight=this.shortcode.options.height}onResizeBottomMove(t){let e=this.shortcode.parent.options.height/4,i=(this.initElementHeight+(t.deltaY+e/2))/this.containerHeight;i>=1?this.shortcode.options.height="1":i>=.75?this.shortcode.options.height="3-4":i>=.66?this.shortcode.options.height="2-3":i>=.5?this.shortcode.options.height="1-2":i>=.33?this.shortcode.options.height="1-3":i>=.25&&(this.shortcode.options.height="1-4")}onResizeBottomEnd(t){delete this.containerHeight,delete this.initElementHeight,delete this.initOptionHeight}}r.$inject=["app","shortcode","$element"]},483:function(t,e,i){"use strict";i.d(e,{A:function(){return r}});class r{constructor(t,e,i,r,n){this.app=t,this.shortcode=e,this.mapElement=r.find("#map_"+e.$id).get(0),this.map=null,this.marker=null,this.initializeMap(),i.$watchCollection((()=>this.shortcode.options),((t,e)=>{if(t===e)return;let i=this.getOptions(t);this.map.setOptions(i),this.marker.setPosition(i.center),this.map.mapTypes.set("flatsome",this.getStyle(t))})),i.$on("$destroy",(()=>{this.map=null,this.marker=null}))}initializeMap(){let t=i.g.google,e=this.getOptions(this.shortcode.options),r=this.getStyle(this.shortcode.options);this.map=new t.maps.Map(this.mapElement,e),this.map.mapTypes.set("flatsome",r),this.marker=new i.g.google.maps.Marker({position:e.center,map:this.map,title:""})}getOptions(t){let e=i.g.google,r="true"===t.controls;return{zoom:t.zoom,center:new e.maps.LatLng(t.lat,t.long),disableDefaultUI:!0,mapTypeId:"flatsome",draggable:!1,zoomControl:r&&"true"===t.zoomControl,zoomControlOptions:{position:e.maps.ControlPosition.TOP_LEFT},mapTypeControl:r&&"true"===t.mapTypeControl,mapTypeControlOptions:{position:e.maps.ControlPosition.TOP_LEFT},streetViewControl:r&&"true"===t.streetViewControl,streetViewControlOptions:{position:e.maps.ControlPosition.TOP_LEFT},scrollwheel:!1,disableDoubleClickZoom:!0}}getStyle(t){let e=i.g.google,r=t.color,n=t.saturation;return new e.maps.StyledMapType([{featureType:"administrative",stylers:[{visibility:"on"}]},{featureType:"road",stylers:[{visibility:"on"},{hue:r}]},{stylers:[{visibility:"on"},{hue:r},{saturation:n}]}],{name:"flatsome"})}}r.$inject=["app","shortcode","$scope","$element","$window"]},1838:function(t,e,i){"use strict";i.d(e,{A:function(){return r}});class r{constructor(t,e,i,r){this.shortcode=e,this.$element=i,this.$timeout=r,t.$watchCollection((()=>this.shortcode.options),((t,e)=>{}))}}r.$inject=["$scope","shortcode","$element","$timeout"]},2807:function(t,e,i){"use strict";i.d(e,{A:function(){return r}});class r{constructor(t,e,i,r,n,s){this.app=e,this.targets=i,this.shortcode=r,this.shortcode.states.activeTab=0,n((()=>{this.targets.disable(this.shortcode.children),this.targets.enable(this.shortcode.childAt(0))}),0,!1),t.$watch((()=>e.states.selectedShortcode),(t=>{t&&t.isChildOf(this.shortcode)&&t.index!==this.shortcode.states.activeTab&&this.setTab(t.index)})),t.$on(s.DETACHED,((t,e)=>{e.isChildOf(this.shortcode)&&this.setTab(e.index-1)}))}setTab(t){t=t<0?0:t,this.shortcode.states.activeTab=t,this.targets.disable(this.shortcode.children),this.targets.enable(this.shortcode.childAt(t)),this.app.selectShortcode(this.shortcode.childAt(t)),this.app.configureShortcode(this.shortcode.childAt(t))}}r.$inject=["$scope","app","targets","shortcode","$timeout","ShortcodeEvent"]},1482:function(t,e,i){"use strict";i.d(e,{A:function(){return r}});class r{constructor(t,e,i,r,n){function s(){t("wp-editor").open()}n.on("dblclick",s),r.$on("$destroy",(function(){n.off("dblclick",s)}))}}r.$inject=["app","shortcode","tools","$scope","$element"]},6772:function(t,e,i){"use strict";i.d(e,{A:function(){return r}});class r{constructor(t,e,i){this.app=t,this.shortcode=e,this.$element=i}onResizeRightStart(t){this.maxWidth=this.$element.closest(".banner-layers").width(),this.initWidth=this.shortcode.options.width,this.isCenterX=50===this.shortcode.options.positionX}onResizeRightMove(t){let e=t.deltaX*(this.isCenterX?2:1),i=this.initWidth+e/this.maxWidth*100;i>100&&(i=100),i<0&&(i=0),this.shortcode.options.width=parseInt(i,10)}onResizeBottomMove(t){delete this.maxWidth,delete this.initWidth}}r.$inject=["app","shortcode","$element"]},636:function(t,e,i){"use strict";i.d(e,{A:function(){return r}});class r{constructor(t,e,i,r,n,s,a){this.app=t,this.shortcode=e,this.$element=i,this.$iframe=r,this.$scope=n,this.$timeout=s,this.$row=i.find(".row"),this.options={gutter:0,percentPosition:!0,itemSelector:".col",columnWidth:".grid-size",transitionDuration:"250ms"},this.packery=new Packery(this.$row.get(0),this.options),n.$watchCollection("shortcode.children",this.onChildrenChange.bind(this)),n.$watchCollection("shortcode.options",this.onOptionsChange.bind(this)),n.$on(a.START,this.onDragStart.bind(this)),n.$on(a.MOVE,this.onDragMove.bind(this)),n.$on(a.END,this.onDragEnd.bind(this)),n.$on("$destroy",(()=>this.packery.destroy()))}onChildrenChange(t,e){var i=t!==e?_.difference(t,e):t;i.length&&t!==e&&this.$timeout((()=>{this.$iframe().contents().find("body").scrollToElement(i[0].$element)}),250,!1),_.each(i,(t=>{this.$scope.$watchCollection((()=>t.options),this.onChildOptionsChange.bind(this))})),this.$timeout((()=>{this.packery.reloadItems(),this.packery.layout()}),0,!1)}onChildOptionsChange(t,e){var i=!1;t.span!==e.span&&(i=!0),t.height!==e.height&&(i=!0),t.spacing!==e.spacing&&(i=!0),i&&this.$timeout((()=>this.packery.layout()),0,!1)}onOptionsChange(t,e){this.$timeout((()=>this.packery.layout()),0,!1)}onDragStart(t,e){e.shortcode.isChildOf(this.shortcode)&&(e.preventDefault(),e.setContainment(this.$row),this.packery.itemDragStart(e.shortcode.$element.get(0)))}onDragMove(t,e){e.shortcode.isChildOf(this.shortcode)&&(e.shortcode.$element.css({left:e.innerX,top:e.innerY}),this.packery.itemDragMove(e.shortcode.$element.get(0),e.innerX,e.innerY))}onDragEnd(t,e){e.shortcode.isChildOf(this.shortcode)&&(this.packery.itemDragEnd(e.shortcode.$element.get(0)),this.reorderChildren())}reorderChildren(){var t=this.shortcode.children,e=this.packery.getItemElements();_.each(e,(function(t,e){angular.element(t).shortcode().$$order=e})),this.shortcode.children=_.sortBy(t,"$$order"),_.each(this.shortcode.children,(function(t){delete t.$$order}))}}r.$inject=["app","shortcode","$element","$iframe","$scope","$timeout","DragEvent"]},7752:function(t,e,i){"use strict";i.d(e,{A:function(){return r}});class r{constructor(t,e,i,r){this.shortcode=e,this.$element=i,this.$timeout=r,t.$watchCollection((()=>this.shortcode.options),((t,e)=>{t!==e&&"text"===t.type&&this.$timeout((()=>{jQuery(i).find(".tooltip").lazyTooltipster("content",t.text)}),100,!1)}))}}r.$inject=["$scope","shortcode","$element","$timeout"]},8728:function(t,e,i){"use strict";function r(t){return(r="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function n(t,e,i){return e in t?Object.defineProperty(t,e,{value:i,enumerable:!0,configurable:!0,writable:!0}):t[e]=i,t}function s(t,e){if(null==t)return{};var i,r,n=function(t,e){if(null==t)return{};var i,r,n={},s=Object.keys(t);for(r=0;r<s.length;r++)i=s[r],e.indexOf(i)>=0||(n[i]=t[i]);return n}(t,e);if(Object.getOwnPropertySymbols){var s=Object.getOwnPropertySymbols(t);for(r=0;r<s.length;r++)i=s[r],e.indexOf(i)>=0||Object.prototype.propertyIsEnumerable.call(t,i)&&(n[i]=t[i])}return n}function a(t,e){var i=e.get(t);if(!i)throw new TypeError("attempted to get private field on non-instance");return i.get?i.get.call(t):i.value}i.d(e,{A:function(){return D}}),i(9371);var o={player:"lottie-player"},h="[lottieInteractivity]:",l=function(){function t(){var e=this,i=arguments.length>0&&void 0!==arguments[0]?arguments[0]:o,l=i.actions,D=i.container,T=i.mode,M=i.player,I=s(i,["actions","container","mode","player"]);if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),p.set(this,{writable:!0,value:function(){if(e.player){var t=function(){e.player.addEventListener("enterFrame",a(e,_)),e.container.addEventListener("mouseenter",a(e,E)),e.container.addEventListener("mouseleave",a(e,S)),e.container.addEventListener("touchstart",a(e,E),{passive:!0}),e.container.addEventListener("touchend",a(e,S),{passive:!0})},i=function(){e.container.addEventListener("mouseenter",a(e,E)),e.container.addEventListener("mouseleave",a(e,S)),e.container.addEventListener("touchstart",a(e,E),{passive:!0}),e.container.addEventListener("touchend",a(e,S),{passive:!0})};e.stateHandler.set("loop",(function(){e.actions[e.interactionIdx].loop?e.player.loop=parseInt(e.actions[e.interactionIdx].loop)-1:e.player.loop=!0,e.player.autoplay=!0})),e.stateHandler.set("autoplay",(function(){e.player.loop=!1,e.player.autoplay=!0})),e.stateHandler.set("click",(function(){e.player.loop=!1,e.player.autoplay=!1,e.container.addEventListener("click",a(e,c))})),e.stateHandler.set("hover",(function(){e.player.loop=!1,e.player.autoplay=!1,e.container.addEventListener("mouseenter",a(e,c)),e.container.addEventListener("touchstart",a(e,c),{passive:!0})})),e.stateHandler.set("hold",i),e.stateHandler.set("pauseHold",i),e.transitionHandler.set("click",(function(){e.container.addEventListener("click",a(e,f))})),e.transitionHandler.set("hover",(function(){e.container.addEventListener("mouseenter",a(e,f)),e.container.addEventListener("touchstart",a(e,f),{passive:!0})})),e.transitionHandler.set("hold",t),e.transitionHandler.set("pauseHold",t),e.transitionHandler.set("repeat",(function(){e.player.loop=!0,e.player.autoplay=!0,e.player.addEventListener("loopComplete",(function t(){a(e,v).call(e,{handler:t})}))})),e.transitionHandler.set("onComplete",(function(){"loop"===e.actions[e.interactionIdx].state?e.player.addEventListener("loopComplete",a(e,g)):e.player.addEventListener("complete",a(e,g))})),e.transitionHandler.set("seek",(function(){e.player.stop(),e.player.addEventListener("enterFrame",a(e,b)),e.container.addEventListener("mousemove",a(e,u)),e.container.addEventListener("touchmove",a(e,m),{passive:!1}),e.container.addEventListener("mouseout",a(e,y))}))}}}),c.set(this,{writable:!0,value:function(){var t=e.actions[e.interactionIdx].forceFlag;t||!0!==e.player.isPaused?t&&a(e,x).call(e,!0):a(e,x).call(e,!0)}}),d.set(this,{writable:!0,value:function(){0===e.clickCounter?(e.player.play(),e.clickCounter++):(e.clickCounter++,e.player.setDirection(-1*e.player.playDirection),e.player.play())}}),f.set(this,{writable:!0,value:function(){var t=e.actions[e.interactionIdx].forceFlag,i=e.actions[e.interactionIdx].state,r=e.actions[e.interactionIdx].transition;if("chain"===e.mode){if(e.actions[e.interactionIdx].count){var n=parseInt(e.actions[e.interactionIdx].count);if(e.clickCounter<n-1)return void(e.clickCounter+=1)}return e.clickCounter=0,!t&&"click"===r&&"click"===i||"hover"===r&&"hover"===i?e.transitionHandler.get("onComplete").call():e.nextInteraction(),e.container.removeEventListener("click",a(e,f)),void e.container.removeEventListener("mouseenter",a(e,f))}t||!0!==e.player.isPaused?t&&e.player.goToAndPlay(0,!0):e.player.goToAndPlay(0,!0)}}),u.set(this,{writable:!0,value:function(t){a(e,A).call(e,t.clientX,t.clientY)}}),m.set(this,{writable:!0,value:function(t){t.cancelable&&t.preventDefault(),a(e,A).call(e,t.touches[0].clientX,t.touches[0].clientY)}}),y.set(this,{writable:!0,value:function(){a(e,A).call(e,-1,-1)}}),g.set(this,{writable:!0,value:function(){"loop"===e.actions[e.interactionIdx].state?e.player.removeEventListener("loopComplete",a(e,g)):e.player.removeEventListener("complete",a(e,g)),e.nextInteraction()}}),v.set(this,{writable:!0,value:function(t){var i=t.handler,r=1;e.actions[e.interactionIdx].repeat&&(r=e.actions[e.interactionIdx].repeat),e.playCounter>=r-1?(e.playCounter=0,e.player.removeEventListener("loopComplete",i),e.player.loop=!1,e.player.autoplay=!1,e.nextInteraction()):e.playCounter+=1}}),b.set(this,{writable:!0,value:function(){var t=e.actions[e.interactionIdx].frames;t&&e.player.currentFrame>=parseInt(t[1])-1&&(e.player.removeEventListener("enterFrame",a(e,b)),e.container.removeEventListener("mousemove",a(e,u)),e.container.removeEventListener("mouseout",a(e,y)),setTimeout(e.nextInteraction,0))}}),_.set(this,{writable:!0,value:function(){var t=e.actions[e.interactionIdx].frames;(t&&e.player.currentFrame>=t[1]||e.player.currentFrame>=e.player.totalFrames-1)&&(e.player.removeEventListener("enterFrame",a(e,_)),e.container.removeEventListener("mouseenter",a(e,E)),e.container.removeEventListener("mouseleave",a(e,S)),e.container.removeEventListener("touchstart",a(e,E),{passive:!0}),e.container.removeEventListener("touchend",a(e,S),{passive:!0}),e.player.pause(),e.holdStatus=!1,e.nextInteraction()),-1===e.player.playDirection&&t&&e.player.currentFrame<t[0]&&e.player.pause()}}),E.set(this,{writable:!0,value:function(){-1!==e.player.playDirection&&null!==e.holdStatus&&e.holdStatus||(e.player.setDirection(1),e.player.play(),e.holdStatus=!0)}}),S.set(this,{writable:!0,value:function(){"hold"===e.actions[e.interactionIdx].transition||"hold"===e.actions[e.interactionIdx].state||"hold"===e.actions[0].type?(e.player.setDirection(-1),e.player.play()):"pauseHold"!==e.actions[e.interactionIdx].transition&&"pauseHold"!==e.actions[e.interactionIdx].state&&"pauseHold"!==e.actions[0].type||e.player.pause(),e.holdStatus=!1}}),P.set(this,{writable:!0,value:function(){if(e.container.removeEventListener("click",a(e,f)),e.container.removeEventListener("click",a(e,c)),e.container.removeEventListener("mouseenter",a(e,f)),e.container.removeEventListener("touchstart",a(e,f)),e.container.removeEventListener("touchmove",a(e,m)),e.container.removeEventListener("mouseenter",a(e,c)),e.container.removeEventListener("touchstart",a(e,c)),e.container.removeEventListener("mouseenter",a(e,E)),e.container.removeEventListener("touchstart",a(e,E)),e.container.removeEventListener("mouseleave",a(e,S)),e.container.removeEventListener("mousemove",a(e,u)),e.container.removeEventListener("mouseout",a(e,y)),e.container.removeEventListener("touchend",a(e,S)),e.player)try{e.player.removeEventListener("loopComplete",a(e,g)),e.player.removeEventListener("complete",a(e,g)),e.player.removeEventListener("enterFrame",a(e,b)),e.player.removeEventListener("enterFrame",a(e,_))}catch(t){}}}),n(this,"jumpToInteraction",(function(t){a(e,P).call(e),e.interactionIdx=t,e.interactionIdx<0?e.interactionIdx=0:e.interactionIdx,e.nextInteraction(!1)})),n(this,"nextInteraction",(function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];e.oldInterctionIdx=e.interactionIdx,a(e,P).call(e),e.player.loop=!1;var i=e.actions[e.interactionIdx].jumpTo;i?i>=0&&i<e.actions.length?(e.interactionIdx=i,a(e,C).call(e,{ignorePath:!1})):(e.interactionIdx=0,e.player.goToAndStop(0,!0),a(e,C).call(e,{ignorePath:!1})):(t&&e.interactionIdx++,e.interactionIdx>=e.actions.length?e.actions[e.actions.length-1].reset?(e.interactionIdx=0,e.player.resetSegments(!0),e.actions[e.interactionIdx].frames?e.player.goToAndStop(e.actions[e.interactionIdx].frames,!0):e.player.goToAndStop(0,!0),a(e,C).call(e,{ignorePath:!1})):(e.interactionIdx=e.actions.length-1,a(e,C).call(e,{ignorePath:!1})):a(e,C).call(e,{ignorePath:!1})),e.container.dispatchEvent(new CustomEvent("transition",{bubbles:!0,composed:!0,detail:{oldIndex:e.oldInterctionIdx,newIndex:e.interactionIdx}}))})),x.set(this,{writable:!0,value:function(t){var i=e.actions[e.interactionIdx].frames;if(!i)return e.player.resetSegments(!0),void e.player.goToAndPlay(0,!0);"string"==typeof i?e.player.goToAndPlay(i,t):e.player.playSegments(i,t)}}),w.set(this,{writable:!0,value:function(){var t=e.actions[e.interactionIdx].path;if(!t)if("object"===r(e.enteredPlayer)&&"AnimationItem"===e.enteredPlayer.constructor.name){if(t=e.enteredPlayer,e.player===t)return void a(e,C).call(e,{ignorePath:!0})}else{var i=(t=e.loadedAnimation).substr(t.lastIndexOf("/")+1);if(i=i.substr(0,i.lastIndexOf(".json")),e.player.fileName===i)return void a(e,C).call(e,{ignorePath:!0})}var n=e.container.getBoundingClientRect(),s="width: "+n.width+"px !important; height: "+n.height+"px !important; background: "+e.container.style.background;if(e.container.setAttribute("style",s),"object"!==r(e.enteredPlayer)||"AnimationItem"!==e.enteredPlayer.constructor.name){if("string"==typeof e.enteredPlayer){var o=document.querySelector(e.enteredPlayer);o&&"LOTTIE-PLAYER"===o.nodeName&&(e.attachedListeners||(o.addEventListener("ready",(function(){e.container.style.width="",e.container.style.height=""})),o.addEventListener("load",(function(){e.player=o.getLottie(),a(e,C).call(e,{ignorePath:!0})})),e.attachedListeners=!0),o.load(t))}else e.enteredPlayer instanceof HTMLElement&&"LOTTIE-PLAYER"===e.enteredPlayer.nodeName&&(e.attachedListeners||(e.enteredPlayer.addEventListener("ready",(function(){e.container.style.width="",e.container.style.height=""})),e.enteredPlayer.addEventListener("load",(function(){e.player=e.enteredPlayer.getLottie(),a(e,C).call(e,{ignorePath:!0})})),e.attachedListeners=!0),e.enteredPlayer.load(t));if(!e.player)throw new Error("".concat(h," Specified player is invalid."),e.enteredPlayer)}else{if(!window.lottie)throw new Error("".concat(h," A Lottie player is required."));e.stop(),e.container.innerHTML="","object"===r(t)&&"AnimationItem"===t.constructor.name?e.player=window.lottie.loadAnimation({loop:!1,autoplay:!1,animationData:t.animationData,container:e.container}):e.player=window.lottie.loadAnimation({loop:!1,autoplay:!1,path:t,container:e.container}),e.player.addEventListener("DOMLoaded",(function(){e.container.style.width="",e.container.style.height="",a(e,C).call(e,{ignorePath:!0})}))}e.clickCounter=0,e.playCounter=0}}),C.set(this,{writable:!0,value:function(t){var i=t.ignorePath,r=e.actions[e.interactionIdx].frames,n=e.actions[e.interactionIdx].state,s=e.actions[e.interactionIdx].transition,o=e.actions[e.interactionIdx].path,h=e.stateHandler.get(n),l=e.transitionHandler.get(s),p=e.actions[e.interactionIdx].speed?e.actions[e.interactionIdx].speed:1,c=e.actions[e.interactionIdx].delay?e.actions[e.interactionIdx].delay:0;i||!(o||e.actions[e.actions.length-1].reset&&0===e.interactionIdx)?setTimeout((function(){r&&(e.player.autoplay=!1,e.player.resetSegments(!0),e.player.goToAndStop(r[0],!0)),h?h.call():"none"===n&&(e.player.loop=!1,e.player.autoplay=!1),l&&l.call(),e.player.autoplay&&(e.player.resetSegments(!0),a(e,x).call(e,!0)),e.player.setSpeed(p)}),c):a(e,w).call(e)}}),A.set(this,{writable:!0,value:function(t,i){if(-1!==t&&-1!==i){var r=e.getContainerCursorPosition(t,i);t=r.x,i=r.y}var n=e.actions.find((function(e){var r=e.position;if(r){if(Array.isArray(r.x)&&Array.isArray(r.y))return t>=r.x[0]&&t<=r.x[1]&&i>=r.y[0]&&i<=r.y[1];if(!Number.isNaN(r.x)&&!Number.isNaN(r.y))return t===r.x&&i===r.y}return!1}));if(n)if("seek"===n.type||"seek"===n.transition){var s=(t-n.position.x[0])/(n.position.x[1]-n.position.x[0]),a=(i-n.position.y[0])/(n.position.y[1]-n.position.y[0]);e.player.playSegments(n.frames,!0),n.position.y[0]<0&&n.position.y[1]>1?e.player.goToAndStop(Math.floor(s*e.player.totalFrames),!0):e.player.goToAndStop(Math.ceil((s+a)/2*e.player.totalFrames),!0)}else"loop"===n.type?e.player.playSegments(n.frames,!0):"play"===n.type?(!0===e.player.isPaused&&e.player.resetSegments(),e.player.playSegments(n.frames)):"stop"===n.type&&(e.player.resetSegments(!0),e.player.goToAndStop(n.frames[0],!0))}}),k.set(this,{writable:!0,value:function(){var t=e.getContainerVisibility(),i=e.actions.find((function(e){var i=e.visibility;return t>=i[0]&&t<=i[1]}));if(i)if("seek"===i.type){var r=i.frames[0],n=2==i.frames.length?i.frames[1]:e.player.totalFrames-1;null!==e.assignedSegment&&(e.player.resetSegments(!0),e.assignedSegment=null),e.player.goToAndStop(r+Math.round((t-i.visibility[0])/(i.visibility[1]-i.visibility[0])*(n-r)),!0)}else if("loop"===i.type)e.player.loop=!0,(null===e.assignedSegment||e.assignedSegment!==i.frames||!0===e.player.isPaused)&&(e.player.playSegments(i.frames,!0),e.assignedSegment=i.frames);else if("play"===i.type||"playOnce"===i.type){if("playOnce"===i.type&&!e.scrolledAndPlayed)return e.scrolledAndPlayed=!0,e.player.resetSegments(!0),void(i.frames?e.player.playSegments(i.frames,!0):e.player.play());"play"===i.type&&e.player.isPaused&&(e.player.resetSegments(!0),i.frames?e.player.playSegments(i.frames,!0):e.player.play())}else"stop"===i.type&&e.player.goToAndStop(i.frames[0],!0)}}),this.enteredPlayer=M,"object"!==r(M)||"AnimationItem"!==M.constructor.name){if("string"==typeof M){var F=document.querySelector(M);F&&"LOTTIE-PLAYER"===F.nodeName&&(M=F.getLottie())}else M instanceof HTMLElement&&"LOTTIE-PLAYER"===M.nodeName&&(M=M.getLottie());if(!M)throw new Error(h+"Specified player:"+M+" is invalid.")}"string"==typeof D&&(D=document.querySelector(D)),D||(D=M.wrapper),this.player=M,this.loadedAnimation=this.player.path+this.player.fileName+".json",this.attachedListeners=!1,this.container=D,this.mode=T,this.actions=l,this.options=I,this.assignedSegment=null,this.scrolledAndPlayed=!1,this.interactionIdx=0,this.oldInterctionIdx=0,this.clickCounter=0,this.playCounter=0,this.stateHandler=new Map,this.transitionHandler=new Map}var e;return(e=[{key:"getContainerVisibility",value:function(){var t=this.container.getBoundingClientRect(),e=t.top,i=t.height;return(window.innerHeight-e)/(window.innerHeight+i)}},{key:"getContainerCursorPosition",value:function(t,e){var i=this.container.getBoundingClientRect(),r=i.top;return{x:(t-i.left)/i.width,y:(e-r)/i.height}}},{key:"initScrollMode",value:function(){this.player.stop(),window.addEventListener("scroll",a(this,k),!0)}},{key:"initCursorMode",value:function(){this.actions&&1===this.actions.length?"click"===this.actions[0].type?(this.player.loop=!1,this.player.stop(),this.container.addEventListener("click",a(this,f))):"hover"===this.actions[0].type?(this.player.loop=!1,this.player.stop(),this.container.addEventListener("mouseenter",a(this,f)),this.container.addEventListener("touchstart",a(this,f),{passive:!0})):"toggle"===this.actions[0].type?(this.player.loop=!1,this.player.stop(),this.container.addEventListener("click",a(this,d))):"hold"===this.actions[0].type||"pauseHold"===this.actions[0].type?(this.container.addEventListener("mouseenter",a(this,E)),this.container.addEventListener("mouseleave",a(this,S)),this.container.addEventListener("touchstart",a(this,E),{passive:!0}),this.container.addEventListener("touchend",a(this,S),{passive:!0})):"seek"===this.actions[0].type&&(this.player.loop=!0,this.player.stop(),this.container.addEventListener("mousemove",a(this,u)),this.container.addEventListener("touchmove",a(this,m),{passive:!1}),this.container.addEventListener("mouseout",a(this,y))):(this.player.loop=!0,this.player.stop(),this.container.addEventListener("mousemove",a(this,u)),this.container.addEventListener("mouseleave",a(this,y)),a(this,A).call(this,-1,-1))}},{key:"initChainMode",value:function(){a(this,p).call(this),this.player.loop=!1,this.player.stop(),a(this,C).call(this,{ignorePath:!1})}},{key:"start",value:function(){var t=this;"scroll"===this.mode?this.player.isLoaded?this.initScrollMode():this.player.addEventListener("DOMLoaded",(function(){t.initScrollMode()})):"cursor"===this.mode?this.player.isLoaded?this.initCursorMode():this.player.addEventListener("DOMLoaded",(function(){t.initCursorMode()})):"chain"===this.mode&&(this.player.isLoaded?this.initChainMode():this.player.addEventListener("DOMLoaded",(function(){t.initChainMode()})))}},{key:"redefineOptions",value:function(t){var e=t.actions,i=t.container,n=t.mode,a=t.player,o=s(t,["actions","container","mode","player"]);if(this.stop(),this.enteredPlayer=a,"object"!==r(a)||"AnimationItem"!==a.constructor.name){if("string"==typeof a){var l=document.querySelector(a);l&&"LOTTIE-PLAYER"===l.nodeName&&(a=l.getLottie())}else a instanceof HTMLElement&&"LOTTIE-PLAYER"===a.nodeName&&(a=a.getLottie());if(!a)throw new Error(h+"Specified player:"+a+" is invalid.",a)}"string"==typeof i&&(i=document.querySelector(i)),i||(i=a.wrapper),this.player=a,this.loadedAnimation=this.player.path+this.player.fileName+".json",this.attachedListeners=!1,this.container=i,this.mode=n,this.actions=e,this.options=o,this.assignedSegment=null,this.scrolledAndPlayed=!1,this.interactionIdx=0,this.clickCounter=0,this.playCounter=0,this.holdStatus=null,this.stateHandler=new Map,this.transitionHandler=new Map,this.start()}},{key:"stop",value:function(){if("scroll"===this.mode&&window.removeEventListener("scroll",a(this,k),!0),"cursor"===this.mode&&(this.container.removeEventListener("click",a(this,f)),this.container.removeEventListener("click",a(this,d)),this.container.removeEventListener("mouseenter",a(this,f)),this.container.removeEventListener("touchstart",a(this,f)),this.container.removeEventListener("touchmove",a(this,m)),this.container.removeEventListener("mousemove",a(this,u)),this.container.removeEventListener("mouseleave",a(this,y)),this.container.removeEventListener("touchstart",a(this,E)),this.container.removeEventListener("touchend",a(this,S))),"chain"===this.mode&&(this.container.removeEventListener("click",a(this,f)),this.container.removeEventListener("click",a(this,c)),this.container.removeEventListener("mouseenter",a(this,f)),this.container.removeEventListener("touchstart",a(this,f)),this.container.removeEventListener("touchmove",a(this,m)),this.container.removeEventListener("mouseenter",a(this,c)),this.container.removeEventListener("touchstart",a(this,c)),this.container.removeEventListener("mouseenter",a(this,E)),this.container.removeEventListener("touchstart",a(this,E)),this.container.removeEventListener("mouseleave",a(this,S)),this.container.removeEventListener("mousemove",a(this,u)),this.container.removeEventListener("mouseout",a(this,y)),this.container.removeEventListener("touchend",a(this,S)),this.player))try{this.player.removeEventListener("loopComplete",a(this,g)),this.player.removeEventListener("complete",a(this,g)),this.player.removeEventListener("enterFrame",a(this,b)),this.player.removeEventListener("enterFrame",a(this,_))}catch(t){}this.player&&(this.player.destroy(),this.player=null)}}])&&function(t,e){for(var i=0;i<e.length;i++){var r=e[i];r.enumerable=r.enumerable||!1,r.configurable=!0,"value"in r&&(r.writable=!0),Object.defineProperty(t,r.key,r)}}(t.prototype,e),t}(),p=new WeakMap,c=new WeakMap,d=new WeakMap,f=new WeakMap,u=new WeakMap,m=new WeakMap,y=new WeakMap,g=new WeakMap,v=new WeakMap,b=new WeakMap,_=new WeakMap,E=new WeakMap,S=new WeakMap,P=new WeakMap,x=new WeakMap,w=new WeakMap,C=new WeakMap,A=new WeakMap,k=new WeakMap;class D{constructor(t,e,i,r,n){this.app=t,this.shortcode=e,this.$element=r,this.$timeout=n,this.player=null,this.lottie=null,this.interactivity=null,this.animationData=null,this.isInitializing=!1,this.isPlayingOnClick=!1,this.onMouseEnterHandler=this.onMouseEnter.bind(this),this.onMouseLeaveHandler=this.onMouseLeave.bind(this),this.onClickHandler=this.onClick.bind(this),this.onReadyHandler=this.onReady.bind(this),this.onErrorHandler=this.onError.bind(this),this.initialize(),i.$watchCollection((()=>this.shortcode.options),((t,e)=>{let i=!1;t.path!==e.path&&(i=!0),t.loop!==e.loop&&(i=!0),t.autoplay!==e.autoplay&&(i=!0),t.trigger!==e.trigger&&(i=!0),t.mouseout!==e.mouseout&&(i=!0),t.controls!==e.controls&&(i=!0),t.speed!==e.speed&&this.player.setSpeed(parseFloat(t.speed)),t.reverse!==e.reverse&&this.player.setDirection(t.reverse?-1:1),t.start===e.start&&t.end===e.end||("scroll"!==t.trigger?(this.lottie.playSegments([this.percentToFrame(t.start),this.percentToFrame(t.end)],!0),"true"!==t.autoplay&&this.player.pause()):i=!0),t.scrollActionType!==e.scrollActionType&&(i=!0),t.visibilityStart===e.visibilityStart&&t.visibilityEnd===e.visibilityEnd||(i=!0),i&&this.initialize()})),i.$on("$destroy",(()=>{this.destroy()}))}$onBeforeMove(){this.player&&document.body.appendChild(this.player)}$onAfterMove(){this.player&&this.$element.get(0).appendChild(this.player)}initialize(){this.isInitializing||(this.isInitializing=!0,this.player&&this.destroy(),this.$timeout((()=>{this.player=this.$element.find(".ux-lottie__player").get(0),this.player.load(this.getSrc()),this.player.addEventListener("ready",this.onReadyHandler),this.player.addEventListener("error",this.onErrorHandler),this.isInitializing=!1}),0,!1))}onReady(){this.lottie=this.player.getLottie(),this.animationData=this.lottie.animationData;const{autoplay:t,controls:e,end:i,loop:r,reverse:n,speed:s,start:a,trigger:o,scrollActionType:h,visibilityEnd:p,visibilityStart:c}=this.shortcode.options;this.player.__controls="true"===e,this.player.setLooping("true"===r),this.player.setSpeed(parseFloat(s)),this.player.setDirection("true"===n?-1:1),this.player.resize(),"true"!==t&&"scroll"===o||this.lottie.playSegments([this.percentToFrame(a),this.percentToFrame(i)],!0),"true"!==t&&(this.player.pause(),"scroll"===o&&(this.interactivity=function(t){var e=new l(t);return e.start(),e}({player:`#${this.shortcode.$id} .ux-lottie__player`,mode:"scroll",actions:[{visibility:[c/100,p/100],type:h,frames:[this.percentToFrame(a),this.percentToFrame(i)]}]}),document.dispatchEvent(new CustomEvent("scroll"))),"hover"===o&&(this.player.addEventListener("mouseenter",this.onMouseEnterHandler),this.player.addEventListener("mouseleave",this.onMouseLeaveHandler)),"click"===o&&this.player.addEventListener("click",this.onClickHandler))}onError(){console.error(`Flatsome: Animation source ${this.getSrc()} cannot be parsed, fails to load or has format errors.`)}destroy(){this.player&&(this.player.removeEventListener("mouseenter",this.onMouseEnterHandler),this.player.removeEventListener("mouseleave",this.onMouseLeaveHandler),this.player.removeEventListener("click",this.onClickHandler),this.player.removeEventListener("ready",this.onReadyHandler),this.player.removeEventListener("error",this.onErrorHandler),this.player=null,this.lottie=null,this.interactivity=null,this.animationData=null)}onMouseEnter(){"reverse"===this.shortcode.options.mouseout?(this.player.setDirection("true"===this.shortcode.options.reverse?-1:1),this.player.play()):this.player.play()}onMouseLeave(){"reverse"===this.shortcode.options.mouseout?(this.player.setDirection("true"===this.shortcode.options.reverse?1:-1),this.player.play()):this.player.pause()}onClick(){if(this.isPlayingOnClick)return this.player.pause(),void(this.isPlayingOnClick=!1);this.player.play(),this.isPlayingOnClick=!0}getSrc(){return this.shortcode.options.path||"https://assets7.lottiefiles.com/packages/lf20_wcq4npki.json"}percentToFrame(t){if(0===parseInt(t))return this.animationData.ip;if(100===parseInt(t))return this.animationData.op;const e=parseInt(this.animationData.ip),i=t*(parseInt(this.animationData.op)-e)/100+e;return Math.ceil(i)}}D.$inject=["app","shortcode","$scope","$element","$timeout"]},8814:function(t,e,i){"use strict";i.d(e,{A:function(){return r}}),i(8026);class r{constructor(t,e,i,r,n,s,a,o){this.app=t,this.shortcode=e,this.targets=i,this.$element=n,this.$timeout=s,this.$slider=n.find(".slider"),this.isInitializing=!1,this.flickity=null,this.options={initialIndex:0,cellAlign:"center",imagesLoaded:!0,freeScroll:"true"===this.shortcode.options.freescroll,wrapAround:"true"===this.shortcode.options.infinitive,prevNextButtons:"true"===this.shortcode.options.arrows,contain:!0,percentPosition:!0,pageDots:"true"===this.shortcode.options.bullets,selectedAttraction:.1,friction:.6,rightToLeft:!1,draggable:!1,on:{ready:()=>{this.handleVideo()}}},r.$watch((()=>t.states.selectedShortcode),(t=>{if(t){for(let e=0;e<this.shortcode.children.length;e++)if(t.isSelfOrDescendantOf(this.shortcode.children[e])){this.flickity.select(e);break}t.isAncestorOf(this.shortcode)&&this.$timeout((()=>this.setHeight()),0,!1)}})),r.$watchCollection((()=>this.shortcode.children),((t,e)=>{if(t.length&&t.length===e.length){let t=this.flickity?this.flickity.selectedIndex:0;this.options.initialIndex=e[t].index,this.initialize()}})),r.$watchCollection((()=>this.shortcode.options),((t,e)=>{let i=!1,r=!1;t!==e&&(t.slideAlign!==e.slideAlign&&(this.options.cellAlign=t.slideAlign,i=!0),t.visibility!==e.visibility&&(i=!0),t.arrows!==e.arrows&&(this.options.prevNextButtons="true"===t.arrows,i=!0),t.bullets!==e.bullets&&(this.options.pageDots="true"===t.bullets,i=!0),t.parallax!==e.parallax&&(this.options.parallax=t.parallax,i=!0),t.freescroll!==e.freescroll&&(this.options.freeScroll="true"===t.freescroll,i=!0),t.infinitive!==e.infinitive&&(this.options.wrapAround="true"===t.infinitive,i=!0),t.slideWidth!==e.slideWidth&&(r=!0),t.style!==e.style&&(r=!0),i&&this.initialize(),!i&&r&&this.$timeout((()=>this.$slider.flickity("resize")),100,!1))})),r.$on(o.REMOVED,((t,e)=>{e.isChildOf(this.shortcode)&&this.initialize()})),r.$on(o.ADDED,((t,e)=>{e.isChildOf(this.shortcode)&&(this.options.initialIndex=e.index,this.initialize())})),r.$on(a.ATTACHED,((t,e)=>{e.isChildOf(this.shortcode)&&(e.data.template||this.initialize())})),r.$on("$destroy",(()=>{this.destroy(),this.$slider=null}))}initialize(){this.isInitializing||(this.isInitializing=!0,this.flickity&&this.destroy(),this.$timeout((()=>{if(this.flickity=this.$slider.flickity(this.options).data("flickity"),this.flickity.on("select",this.onSelect.bind(this)),this.flickity.on("settle",this.onSettle.bind(this)),this.flickity.on("change",this.onChange.bind(this)),this.options.parallax){var t=jQuery(this.$slider).find(".bg, .flickity-slider > .img img"),e=this.flickity,i=this.options.parallax;jQuery(this.$slider).addClass("slider-has-parallax"),this.flickity.on("scroll",(function(r,n){e.slides.forEach((function(r,n){var s=t[n],a=-1*(r.target+e.x)/i;s&&(s.style.transform=`translateX(${a}px)`)}))}))}this.$slider.on("click.uxSlider",".flickity-prev-next-button.previous",this.previous.bind(this)),this.$slider.on("click.uxSlider",".flickity-prev-next-button.next",this.next.bind(this)),this.$slider.on("click",this.onClick.bind(this)),this.enableCurrentSlideTargets(),this.isInitializing=!1}),0,!1))}destroy(){this.flickity&&(this.flickity.off("select",this.onSelect),this.flickity.off("settle",this.onSettle),this.flickity.off("change",this.onChange),this.$slider.off("click",this.onClick),this.$slider.off("click.uxSlider",".flickity-prev-next-button.previous"),this.$slider.off("click.uxSlider",".flickity-prev-next-button.next"),this.flickity.destroy(),this.flickity=null)}onClick(t){let e=this.flickity.selectedIndex;this.app.configureShortcode(null),this.app.selectShortcode(this.shortcode.childAt(e)),this.shortcode.apply(),t.stopPropagation()}onSelect(){this.$slider.hasClass("slider-auto-height")&&this.$slider.find(".flickity-viewport").css({height:this.$slider.find(".is-selected").outerHeight()})}onSettle(){this.options.initialIndex=this.flickity.selectedIndex,this.enableCurrentSlideTargets()}onChange(){this.handleVideo()}setHeight(t){this.$element.find(".flickity-viewport").css({height:t||this.flickity?this.flickity.selectedElement.offsetHeight:null})}enableCurrentSlideTargets(){this.shortcode.children.forEach(((t,e)=>{e===this.flickity.selectedIndex?this.targets.enableElement(t.$element.get(0)):this.targets.disableElement(t.$element.get(0))}))}handleVideo(){this.$slider.find(".flickity-slider > :not(.is-selected) .video-bg").trigger("pause"),this.$slider.find(".is-selected .video-bg").trigger("play")}next(){this.$slider.off("click.uxSlider",".flickity-prev-next-button.next"),this.flickity.next()}previous(){this.$slider.off("click.uxSlider",".flickity-prev-next-button.previous"),this.flickity.previous()}}r.$inject=["app","shortcode","targets","$scope","$element","$timeout","ShortcodeEvent","ChildEvent"]},8460:function(){angular.module("uxBuilder").component("uxBannerTool",{controller:["app","$element",function(t,e){this.highlightHorizontalCenter=function(t){e.find(".grid-h-center").toggleClass("active",t)},this.highlightVerticalCenter=function(t){e.find(".grid-v-center").toggleClass("active",t)}}],controllerAs:"grid",template:'\n      <div class="grid-v-center"></div>\n      <div class="grid-h-center"></div>\n    '})},8026:function(t,e,i){var r,n,s,a,o,h,l,p,c,d,f,u,m,y,g,v,b,_,E,S,P,x,w,C,A,k,D,T,M,I,F,L,R,B,O,$,V,z,N,G,j,H,W,q;!function(r,n){W=[i(428)],q=function(t){return function(t,e){"use strict";var i=Array.prototype.slice,r=t.console,n=void 0===r?function(){}:function(t){r.error(t)};function s(r,s,o){(o=o||e||t.jQuery)&&(s.prototype.option||(s.prototype.option=function(t){o.isPlainObject(t)&&(this.options=o.extend(!0,this.options,t))}),o.fn[r]=function(t){return"string"==typeof t?(e=this,a=t,h=i.call(arguments,1),p="$()."+r+'("'+a+'")',e.each((function(t,e){var i=o.data(e,r);if(i){var s=i[a];if(s&&"_"!=a.charAt(0)){var c=s.apply(i,h);l=void 0===l?c:l}else n(p+" is not a valid method")}else n(r+" not initialized. Cannot call methods, i.e. "+p)})),void 0!==l?l:e):(function(t,e){t.each((function(t,i){var n=o.data(i,r);n?(n.option(e),n._init()):(n=new s(i,e),o.data(i,r,n))}))}(this,t),this);var e,a,h,l,p},a(o))}function a(t){!t||t&&t.bridget||(t.bridget=s)}return a(e||t.jQuery),s}(r,t)}.apply(e,W),void 0===q||(t.exports=q)}(window),"undefined"!=typeof window&&window,"function"==typeof(n=function(){function t(){}var e=t.prototype;return e.on=function(t,e){if(t&&e){var i=this._events=this._events||{},r=i[t]=i[t]||[];return-1==r.indexOf(e)&&r.push(e),this}},e.once=function(t,e){if(t&&e){this.on(t,e);var i=this._onceEvents=this._onceEvents||{};return(i[t]=i[t]||{})[e]=!0,this}},e.off=function(t,e){var i=this._events&&this._events[t];if(i&&i.length){var r=i.indexOf(e);return-1!=r&&i.splice(r,1),this}},e.emitEvent=function(t,e){var i=this._events&&this._events[t];if(i&&i.length){i=i.slice(0),e=e||[];for(var r=this._onceEvents&&this._onceEvents[t],n=0;n<i.length;n++){var s=i[n];r&&r[s]&&(this.off(t,s),delete r[s]),s.apply(this,e)}return this}},e.allOff=function(){delete this._events,delete this._onceEvents},t})?(r=n.call((s={id:"ev-emitter/ev-emitter",exports:{},loaded:!1}).exports,i,s.exports,s),s.loaded=!0,void 0===r&&(r=s.exports)):r=n,window,"function"==typeof(o=function(){"use strict";function t(t){var e=parseFloat(t);return-1==t.indexOf("%")&&!isNaN(e)&&e}var e="undefined"==typeof console?function(){}:function(t){console.error(t)},i=["paddingLeft","paddingRight","paddingTop","paddingBottom","marginLeft","marginRight","marginTop","marginBottom","borderLeftWidth","borderRightWidth","borderTopWidth","borderBottomWidth"],r=i.length;function n(t){var i=getComputedStyle(t);return i||e("Style returned "+i+". Are you running this code in a hidden iframe on Firefox? See https://bit.ly/getsizebug1"),i}var s,a=!1;function o(e){if(function(){if(!a){a=!0;var e=document.createElement("div");e.style.width="200px",e.style.padding="1px 2px 3px 4px",e.style.borderStyle="solid",e.style.borderWidth="1px 2px 3px 4px",e.style.boxSizing="border-box";var i=document.body||document.documentElement;i.appendChild(e);var r=n(e);s=200==Math.round(t(r.width)),o.isBoxSizeOuter=s,i.removeChild(e)}}(),"string"==typeof e&&(e=document.querySelector(e)),e&&"object"==typeof e&&e.nodeType){var h=n(e);if("none"==h.display)return function(){for(var t={width:0,height:0,innerWidth:0,innerHeight:0,outerWidth:0,outerHeight:0},e=0;e<r;e++)t[i[e]]=0;return t}();var l={};l.width=e.offsetWidth,l.height=e.offsetHeight;for(var p=l.isBorderBox="border-box"==h.boxSizing,c=0;c<r;c++){var d=i[c],f=h[d],u=parseFloat(f);l[d]=isNaN(u)?0:u}var m=l.paddingLeft+l.paddingRight,y=l.paddingTop+l.paddingBottom,g=l.marginLeft+l.marginRight,v=l.marginTop+l.marginBottom,b=l.borderLeftWidth+l.borderRightWidth,_=l.borderTopWidth+l.borderBottomWidth,E=p&&s,S=t(h.width);!1!==S&&(l.width=S+(E?0:m+b));var P=t(h.height);return!1!==P&&(l.height=P+(E?0:y+_)),l.innerWidth=l.width-(m+b),l.innerHeight=l.height-(y+_),l.outerWidth=l.width+g,l.outerHeight=l.height+v,l}}return o})?(a=o.call((h={id:"get-size/get-size",exports:{},loaded:!1}).exports,i,h.exports,h),h.loaded=!0,void 0===a&&(a=h.exports)):a=o,function(t,e){"use strict";"function"==typeof(p=e)?(c={id:"desandro-matches-selector/matches-selector",exports:{},loaded:!1},l=p.call(c.exports,i,c.exports,c),c.loaded=!0,void 0===l&&(l=c.exports)):l=p}(window,(function(){"use strict";var t=function(){var t=window.Element.prototype;if(t.matches)return"matches";if(t.matchesSelector)return"matchesSelector";for(var e=["webkit","moz","ms","o"],i=0;i<e.length;i++){var r=e[i]+"MatchesSelector";if(t[r])return r}}();return function(e,i){return e[t](i)}})),function(t,e){d=function(e){return function(t,e){var i={extend:function(t,e){for(var i in e)t[i]=e[i];return t},modulo:function(t,e){return(t%e+e)%e}},r=Array.prototype.slice;i.makeArray=function(t){return Array.isArray(t)?t:null==t?[]:"object"==typeof t&&"number"==typeof t.length?r.call(t):[t]},i.removeFrom=function(t,e){var i=t.indexOf(e);-1!=i&&t.splice(i,1)},i.getParent=function(t,i){for(;t.parentNode&&t!=document.body;)if(t=t.parentNode,e(t,i))return t},i.getQueryElement=function(t){return"string"==typeof t?document.querySelector(t):t},i.handleEvent=function(t){var e="on"+t.type;this[e]&&this[e](t)},i.filterFindElements=function(t,r){t=i.makeArray(t);var n=[];return t.forEach((function(t){if(t instanceof HTMLElement)if(r){e(t,r)&&n.push(t);for(var i=t.querySelectorAll(r),s=0;s<i.length;s++)n.push(i[s])}else n.push(t)})),n},i.debounceMethod=function(t,e,i){i=i||100;var r=t.prototype[e],n=e+"Timeout";t.prototype[e]=function(){var t=this[n];clearTimeout(t);var e=arguments,s=this;this[n]=setTimeout((function(){r.apply(s,e),delete s[n]}),i)}},i.docReady=function(t){var e=document.readyState;"complete"==e||"interactive"==e?setTimeout(t):document.addEventListener("DOMContentLoaded",t)},i.toDashed=function(t){return t.replace(/(.)([A-Z])/g,(function(t,e,i){return e+"-"+i})).toLowerCase()};var n=t.console;return i.htmlInit=function(e,r){i.docReady((function(){var s=i.toDashed(r),a="data-"+s,o=document.querySelectorAll("["+a+"]"),h=document.querySelectorAll(".js-"+s),l=i.makeArray(o).concat(i.makeArray(h)),p=a+"-options",c=t.jQuery;l.forEach((function(t){var i,s=t.getAttribute(a)||t.getAttribute(p);try{i=s&&JSON.parse(s)}catch(e){return void(n&&n.error("Error parsing "+a+" on "+t.className+": "+e))}var o=new e(t,i);c&&c.data(t,r,o)}))}))},i}(t,e)}.apply(f={},W=[l]),void 0!==d||(d=f)}(window),window,void 0!==(u=function(t){return function(t,e){function i(t,e){this.element=t,this.parent=e,this.create()}var r=i.prototype;return r.create=function(){this.element.style.position="absolute",this.element.setAttribute("aria-hidden","true"),this.x=0,this.shift=0},r.destroy=function(){this.unselect(),this.element.style.position="";var t=this.parent.originSide;this.element.style[t]="",this.element.removeAttribute("aria-hidden")},r.getSize=function(){this.size=e(this.element)},r.setPosition=function(t){this.x=t,this.updateTarget(),this.renderPosition(t)},r.updateTarget=r.setDefaultTarget=function(){var t="left"==this.parent.originSide?"marginLeft":"marginRight";this.target=this.x+this.size[t]+this.size.width*this.parent.cellAlign},r.renderPosition=function(t){var e=this.parent.originSide;this.element.style[e]=this.parent.getPositionValue(t)},r.select=function(){this.element.classList.add("is-selected"),this.element.removeAttribute("aria-hidden")},r.unselect=function(){this.element.classList.remove("is-selected"),this.element.setAttribute("aria-hidden","true")},r.wrapShift=function(t){this.shift=t,this.renderPosition(this.x+this.parent.slideableWidth*t)},r.remove=function(){this.element.parentNode.removeChild(this.element)},i}(0,t)}.apply(m={},W=[a]))||(u=m),window,"function"==typeof(g=function(){"use strict";function t(t){this.parent=t,this.isOriginLeft="left"==t.originSide,this.cells=[],this.outerWidth=0,this.height=0}var e=t.prototype;return e.addCell=function(t){if(this.cells.push(t),this.outerWidth+=t.size.outerWidth,this.height=Math.max(t.size.outerHeight,this.height),1==this.cells.length){this.x=t.x;var e=this.isOriginLeft?"marginLeft":"marginRight";this.firstMargin=t.size[e]}},e.updateTarget=function(){var t=this.isOriginLeft?"marginRight":"marginLeft",e=this.getLastCell(),i=e?e.size[t]:0,r=this.outerWidth-(this.firstMargin+i);this.target=this.x+this.firstMargin+r*this.parent.cellAlign},e.getLastCell=function(){return this.cells[this.cells.length-1]},e.select=function(){this.cells.forEach((function(t){t.select()}))},e.unselect=function(){this.cells.forEach((function(t){t.unselect()}))},e.getCellElements=function(){return this.cells.map((function(t){return t.element}))},t})?(y=g.call((v={id:"flickity/js/slide",exports:{},loaded:!1}).exports,i,v.exports,v),v.loaded=!0,void 0===y&&(y=v.exports)):y=g,window,void 0!==(b=function(t){return function(t,e){return{startAnimation:function(){this.isAnimating||(this.isAnimating=!0,this.restingFrames=0,this.animate())},animate:function(){this.applyDragForce(),this.applySelectedAttraction();var t=this.x;if(this.integratePhysics(),this.positionSlider(),this.settle(t),this.isAnimating){var e=this;requestAnimationFrame((function(){e.animate()}))}},positionSlider:function(){var t=this.x;this.options.wrapAround&&this.cells.length>1&&(t=e.modulo(t,this.slideableWidth),t-=this.slideableWidth,this.shiftWrapCells(t)),this.setTranslateX(t,this.isAnimating),this.dispatchScrollEvent()},setTranslateX:function(t,e){t+=this.cursorPosition,t=this.options.rightToLeft?-t:t;var i=this.getPositionValue(t);this.slider.style.transform=e?"translate3d("+i+",0,0)":"translateX("+i+")"},dispatchScrollEvent:function(){var t=this.slides[0];if(t){var e=-this.x-t.target,i=e/this.slidesWidth;this.dispatchEvent("scroll",null,[i,e])}},positionSliderAtSelected:function(){this.cells.length&&(this.x=-this.selectedSlide.target,this.velocity=0,this.positionSlider())},getPositionValue:function(t){return this.options.percentPosition?.01*Math.round(t/this.size.innerWidth*1e4)+"%":Math.round(t)+"px"},settle:function(t){!this.isPointerDown&&Math.round(100*this.x)==Math.round(100*t)&&this.restingFrames++,this.restingFrames>2&&(this.isAnimating=!1,delete this.isFreeScrolling,this.positionSlider(),this.dispatchEvent("settle",null,[this.selectedIndex]))},shiftWrapCells:function(t){var e=this.cursorPosition+t;this._shiftCells(this.beforeShiftCells,e,-1);var i=this.size.innerWidth-(t+this.slideableWidth+this.cursorPosition);this._shiftCells(this.afterShiftCells,i,1)},_shiftCells:function(t,e,i){for(var r=0;r<t.length;r++){var n=t[r],s=e>0?i:0;n.wrapShift(s),e-=n.size.outerWidth}},_unshiftCells:function(t){if(t&&t.length)for(var e=0;e<t.length;e++)t[e].wrapShift(0)},integratePhysics:function(){this.x+=this.velocity,this.velocity*=this.getFrictionFactor()},applyForce:function(t){this.velocity+=t},getFrictionFactor:function(){return 1-this.options[this.isFreeScrolling?"freeScrollFriction":"friction"]},getRestingPosition:function(){return this.x+this.velocity/(1-this.getFrictionFactor())},applyDragForce:function(){if(this.isDraggable&&this.isPointerDown){var t=this.dragX-this.x-this.velocity;this.applyForce(t)}},applySelectedAttraction:function(){if((!this.isDraggable||!this.isPointerDown)&&!this.isFreeScrolling&&this.slides.length){var t=(-1*this.selectedSlide.target-this.x)*this.options.selectedAttraction;this.applyForce(t)}}}}(0,t)}.apply(_={},W=[d]))||(b=_),function(t,e){E=function(e,i,r,n,s,a){return function(t,e,i,r,n,s,a){var o=t.jQuery,h=t.getComputedStyle,l=t.console;function p(t,e){for(t=r.makeArray(t);t.length;)e.appendChild(t.shift())}var c=0,d={};function f(t,e){var i=r.getQueryElement(t);if(i){if(this.element=i,this.element.flickityGUID){var n=d[this.element.flickityGUID];return n&&n.option(e),n}o&&(this.$element=o(this.element)),this.options=r.extend({},this.constructor.defaults),this.option(e),this._create()}else l&&l.error("Bad element for Flickity: "+(i||t))}f.defaults={accessibility:!0,cellAlign:"center",freeScrollFriction:.075,friction:.28,namespaceJQueryEvents:!0,percentPosition:!0,resize:!0,selectedAttraction:.025,setGallerySize:!0},f.createMethods=[];var u=f.prototype;r.extend(u,e.prototype),u._create=function(){var e=this.guid=++c;for(var i in this.element.flickityGUID=e,d[e]=this,this.selectedIndex=0,this.restingFrames=0,this.x=0,this.velocity=0,this.originSide=this.options.rightToLeft?"right":"left",this.viewport=document.createElement("div"),this.viewport.className="flickity-viewport",this._createSlider(),(this.options.resize||this.options.watchCSS)&&t.addEventListener("resize",this),this.options.on){var r=this.options.on[i];this.on(i,r)}f.createMethods.forEach((function(t){this[t]()}),this),this.options.watchCSS?this.watchCSS():this.activate()},u.option=function(t){r.extend(this.options,t)},u.activate=function(){this.isActive||(this.isActive=!0,this.element.classList.add("flickity-enabled"),this.options.rightToLeft&&this.element.classList.add("flickity-rtl"),this.getSize(),p(this._filterFindCellElements(this.element.children),this.slider),this.viewport.appendChild(this.slider),this.element.appendChild(this.viewport),this.reloadCells(),this.options.accessibility&&(this.element.tabIndex=0,this.element.addEventListener("keydown",this)),this.emitEvent("activate"),this.selectInitialIndex(),this.isInitActivated=!0,this.dispatchEvent("ready"))},u._createSlider=function(){var t=document.createElement("div");t.className="flickity-slider",t.style[this.originSide]=0,this.slider=t},u._filterFindCellElements=function(t){return r.filterFindElements(t,this.options.cellSelector)},u.reloadCells=function(){this.cells=this._makeCells(this.slider.children),this.positionCells(),this._getWrapShiftCells(),this.setGallerySize()},u._makeCells=function(t){return this._filterFindCellElements(t).map((function(t){return new n(t,this)}),this)},u.getLastCell=function(){return this.cells[this.cells.length-1]},u.getLastSlide=function(){return this.slides[this.slides.length-1]},u.positionCells=function(){this._sizeCells(this.cells),this._positionCells(0)},u._positionCells=function(t){t=t||0,this.maxCellHeight=t&&this.maxCellHeight||0;var e=0;if(t>0){var i=this.cells[t-1];e=i.x+i.size.outerWidth}for(var r=this.cells.length,n=t;n<r;n++){var s=this.cells[n];s.setPosition(e),e+=s.size.outerWidth,this.maxCellHeight=Math.max(s.size.outerHeight,this.maxCellHeight)}this.slideableWidth=e,this.updateSlides(),this._containSlides(),this.slidesWidth=r?this.getLastSlide().target-this.slides[0].target:0},u._sizeCells=function(t){t.forEach((function(t){t.getSize()}))},u.updateSlides=function(){if(this.slides=[],this.cells.length){var t=new s(this);this.slides.push(t);var e="left"==this.originSide?"marginRight":"marginLeft",i=this._getCanCellFit();this.cells.forEach((function(r,n){if(t.cells.length){var a=t.outerWidth-t.firstMargin+(r.size.outerWidth-r.size[e]);i.call(this,n,a)||(t.updateTarget(),t=new s(this),this.slides.push(t)),t.addCell(r)}else t.addCell(r)}),this),t.updateTarget(),this.updateSelectedSlide()}},u._getCanCellFit=function(){var t=this.options.groupCells;if(!t)return function(){return!1};if("number"==typeof t){var e=parseInt(t,10);return function(t){return t%e!=0}}var i="string"==typeof t&&t.match(/^(\d+)%$/),r=i?parseInt(i[1],10)/100:1;return function(t,e){return e<=(this.size.innerWidth+1)*r}},u._init=u.reposition=function(){this.positionCells(),this.positionSliderAtSelected()},u.getSize=function(){this.size=i(this.element),this.setCellAlign(),this.cursorPosition=this.size.innerWidth*this.cellAlign};var m={center:{left:.5,right:.5},left:{left:0,right:1},right:{right:0,left:1}};return u.setCellAlign=function(){var t=m[this.options.cellAlign];this.cellAlign=t?t[this.originSide]:this.options.cellAlign},u.setGallerySize=function(){if(this.options.setGallerySize){var t=this.options.adaptiveHeight&&this.selectedSlide?this.selectedSlide.height:this.maxCellHeight;this.viewport.style.height=t+"px"}},u._getWrapShiftCells=function(){if(this.options.wrapAround){this._unshiftCells(this.beforeShiftCells),this._unshiftCells(this.afterShiftCells);var t=this.cursorPosition,e=this.cells.length-1;this.beforeShiftCells=this._getGapCells(t,e,-1),t=this.size.innerWidth-this.cursorPosition,this.afterShiftCells=this._getGapCells(t,0,1)}},u._getGapCells=function(t,e,i){for(var r=[];t>0;){var n=this.cells[e];if(!n)break;r.push(n),e+=i,t-=n.size.outerWidth}return r},u._containSlides=function(){if(this.options.contain&&!this.options.wrapAround&&this.cells.length){var t=this.options.rightToLeft,e=t?"marginRight":"marginLeft",i=t?"marginLeft":"marginRight",r=this.slideableWidth-this.getLastCell().size[i],n=r<this.size.innerWidth,s=this.cursorPosition+this.cells[0].size[e],a=r-this.size.innerWidth*(1-this.cellAlign);this.slides.forEach((function(t){n?t.target=r*this.cellAlign:(t.target=Math.max(t.target,s),t.target=Math.min(t.target,a))}),this)}},u.dispatchEvent=function(t,e,i){var r=e?[e].concat(i):i;if(this.emitEvent(t,r),o&&this.$element){var n=t+=this.options.namespaceJQueryEvents?".flickity":"";if(e){var s=new o.Event(e);s.type=t,n=s}this.$element.trigger(n,i)}},u.select=function(t,e,i){if(this.isActive&&(t=parseInt(t,10),this._wrapSelect(t),(this.options.wrapAround||e)&&(t=r.modulo(t,this.slides.length)),this.slides[t])){var n=this.selectedIndex;this.selectedIndex=t,this.updateSelectedSlide(),i?this.positionSliderAtSelected():this.startAnimation(),this.options.adaptiveHeight&&this.setGallerySize(),this.dispatchEvent("select",null,[t]),t!=n&&this.dispatchEvent("change",null,[t]),this.dispatchEvent("cellSelect")}},u._wrapSelect=function(t){var e=this.slides.length;if(!(this.options.wrapAround&&e>1))return t;var i=r.modulo(t,e),n=Math.abs(i-this.selectedIndex),s=Math.abs(i+e-this.selectedIndex),a=Math.abs(i-e-this.selectedIndex);!this.isDragSelect&&s<n?t+=e:!this.isDragSelect&&a<n&&(t-=e),t<0?this.x-=this.slideableWidth:t>=e&&(this.x+=this.slideableWidth)},u.previous=function(t,e){this.select(this.selectedIndex-1,t,e)},u.next=function(t,e){this.select(this.selectedIndex+1,t,e)},u.updateSelectedSlide=function(){var t=this.slides[this.selectedIndex];t&&(this.unselectSelectedSlide(),this.selectedSlide=t,t.select(),this.selectedCells=t.cells,this.selectedElements=t.getCellElements(),this.selectedCell=t.cells[0],this.selectedElement=this.selectedElements[0])},u.unselectSelectedSlide=function(){this.selectedSlide&&this.selectedSlide.unselect()},u.selectInitialIndex=function(){var t=this.options.initialIndex;if(this.isInitActivated)this.select(this.selectedIndex,!1,!0);else{if(t&&"string"==typeof t&&this.queryCell(t))return void this.selectCell(t,!1,!0);var e=0;t&&this.slides[t]&&(e=t),this.select(e,!1,!0)}},u.selectCell=function(t,e,i){var r=this.queryCell(t);if(r){var n=this.getCellSlideIndex(r);this.select(n,e,i)}},u.getCellSlideIndex=function(t){for(var e=0;e<this.slides.length;e++)if(-1!=this.slides[e].cells.indexOf(t))return e},u.getCell=function(t){for(var e=0;e<this.cells.length;e++){var i=this.cells[e];if(i.element==t)return i}},u.getCells=function(t){t=r.makeArray(t);var e=[];return t.forEach((function(t){var i=this.getCell(t);i&&e.push(i)}),this),e},u.getCellElements=function(){return this.cells.map((function(t){return t.element}))},u.getParentCell=function(t){return this.getCell(t)||(t=r.getParent(t,".flickity-slider > *"),this.getCell(t))},u.getAdjacentCellElements=function(t,e){if(!t)return this.selectedSlide.getCellElements();e=void 0===e?this.selectedIndex:e;var i=this.slides.length;if(1+2*t>=i)return this.getCellElements();for(var n=[],s=e-t;s<=e+t;s++){var a=this.options.wrapAround?r.modulo(s,i):s,o=this.slides[a];o&&(n=n.concat(o.getCellElements()))}return n},u.queryCell=function(t){if("number"==typeof t)return this.cells[t];if("string"==typeof t){if(t.match(/^[#.]?[\d/]/))return;t=this.element.querySelector(t)}return this.getCell(t)},u.uiChange=function(){this.emitEvent("uiChange")},u.childUIPointerDown=function(t){"touchstart"!=t.type&&t.preventDefault(),this.focus()},u.onresize=function(){this.watchCSS(),this.resize()},r.debounceMethod(f,"onresize",150),u.resize=function(){if(this.isActive&&!this.isAnimating&&!this.isDragging){this.getSize(),this.options.wrapAround&&(this.x=r.modulo(this.x,this.slideableWidth)),this.positionCells(),this._getWrapShiftCells(),this.setGallerySize(),this.emitEvent("resize");var t=this.selectedElements&&this.selectedElements[0];this.selectCell(t,!1,!0)}},u.watchCSS=function(){this.options.watchCSS&&(-1!=h(this.element,":after").content.indexOf("flickity")?this.activate():this.deactivate())},u.onkeydown=function(t){var e=document.activeElement&&document.activeElement!=this.element;if(this.options.accessibility&&!e){var i=f.keyboardHandlers[t.keyCode];i&&i.call(this)}},f.keyboardHandlers={37:function(){var t=this.options.rightToLeft?"next":"previous";this.uiChange(),this[t]()},39:function(){var t=this.options.rightToLeft?"previous":"next";this.uiChange(),this[t]()}},u.focus=function(){var e=t.pageYOffset;this.element.focus({preventScroll:!0}),t.pageYOffset!=e&&t.scrollTo(t.pageXOffset,e)},u.deactivate=function(){this.isActive&&(this.element.classList.remove("flickity-enabled"),this.element.classList.remove("flickity-rtl"),this.unselectSelectedSlide(),this.cells.forEach((function(t){t.destroy()})),this.element.removeChild(this.viewport),p(this.slider.children,this.element),this.options.accessibility&&(this.element.removeAttribute("tabIndex"),this.element.removeEventListener("keydown",this)),this.isActive=!1,this.emitEvent("deactivate"))},u.destroy=function(){this.deactivate(),t.removeEventListener("resize",this),this.allOff(),this.emitEvent("destroy"),o&&this.$element&&o.removeData(this.element,"flickity"),delete this.element.flickityGUID,delete d[this.guid]},r.extend(u,a),f.data=function(t){var e=(t=r.getQueryElement(t))&&t.flickityGUID;return e&&d[e]},r.htmlInit(f,"flickity"),o&&o.bridget&&o.bridget("flickity",f),f.setJQuery=function(t){o=t},f.Cell=n,f.Slide=s,f}(t,e,i,r,n,s,a)}.apply(S={},W=[r,a,d,u,y,b]),void 0!==E||(E=S)}(window),function(t,e){P=function(e){return function(t,e){function i(){}var r=i.prototype=Object.create(e.prototype);r.bindStartEvent=function(t){this._bindStartEvent(t,!0)},r.unbindStartEvent=function(t){this._bindStartEvent(t,!1)},r._bindStartEvent=function(e,i){var r=(i=void 0===i||i)?"addEventListener":"removeEventListener",n="mousedown";"ontouchstart"in t?n="touchstart":t.PointerEvent&&(n="pointerdown"),e[r](n,this)},r.handleEvent=function(t){var e="on"+t.type;this[e]&&this[e](t)},r.getTouch=function(t){for(var e=0;e<t.length;e++){var i=t[e];if(i.identifier==this.pointerIdentifier)return i}},r.onmousedown=function(t){var e=t.button;e&&0!==e&&1!==e||this._pointerDown(t,t)},r.ontouchstart=function(t){this._pointerDown(t,t.changedTouches[0])},r.onpointerdown=function(t){this._pointerDown(t,t)},r._pointerDown=function(t,e){t.button||this.isPointerDown||(this.isPointerDown=!0,this.pointerIdentifier=void 0!==e.pointerId?e.pointerId:e.identifier,this.pointerDown(t,e))},r.pointerDown=function(t,e){this._bindPostStartEvents(t),this.emitEvent("pointerDown",[t,e])};var n={mousedown:["mousemove","mouseup"],touchstart:["touchmove","touchend","touchcancel"],pointerdown:["pointermove","pointerup","pointercancel"]};return r._bindPostStartEvents=function(e){if(e){var i=n[e.type];i.forEach((function(e){t.addEventListener(e,this)}),this),this._boundPointerEvents=i}},r._unbindPostStartEvents=function(){this._boundPointerEvents&&(this._boundPointerEvents.forEach((function(e){t.removeEventListener(e,this)}),this),delete this._boundPointerEvents)},r.onmousemove=function(t){this._pointerMove(t,t)},r.onpointermove=function(t){t.pointerId==this.pointerIdentifier&&this._pointerMove(t,t)},r.ontouchmove=function(t){var e=this.getTouch(t.changedTouches);e&&this._pointerMove(t,e)},r._pointerMove=function(t,e){this.pointerMove(t,e)},r.pointerMove=function(t,e){this.emitEvent("pointerMove",[t,e])},r.onmouseup=function(t){this._pointerUp(t,t)},r.onpointerup=function(t){t.pointerId==this.pointerIdentifier&&this._pointerUp(t,t)},r.ontouchend=function(t){var e=this.getTouch(t.changedTouches);e&&this._pointerUp(t,e)},r._pointerUp=function(t,e){this._pointerDone(),this.pointerUp(t,e)},r.pointerUp=function(t,e){this.emitEvent("pointerUp",[t,e])},r._pointerDone=function(){this._pointerReset(),this._unbindPostStartEvents(),this.pointerDone()},r._pointerReset=function(){this.isPointerDown=!1,delete this.pointerIdentifier},r.pointerDone=function(){},r.onpointercancel=function(t){t.pointerId==this.pointerIdentifier&&this._pointerCancel(t,t)},r.ontouchcancel=function(t){var e=this.getTouch(t.changedTouches);e&&this._pointerCancel(t,e)},r._pointerCancel=function(t,e){this._pointerDone(),this.pointerCancel(t,e)},r.pointerCancel=function(t,e){this.emitEvent("pointerCancel",[t,e])},i.getPointerPoint=function(t){return{x:t.pageX,y:t.pageY}},i}(t,e)}.apply(x={},W=[r]),void 0!==P||(P=x)}(window),function(t,e){w=function(e){return function(t,e){function i(){}var r=i.prototype=Object.create(e.prototype);r.bindHandles=function(){this._bindHandles(!0)},r.unbindHandles=function(){this._bindHandles(!1)},r._bindHandles=function(e){for(var i=(e=void 0===e||e)?"addEventListener":"removeEventListener",r=e?this._touchActionValue:"",n=0;n<this.handles.length;n++){var s=this.handles[n];this._bindStartEvent(s,e),s[i]("click",this),t.PointerEvent&&(s.style.touchAction=r)}},r._touchActionValue="none",r.pointerDown=function(t,e){this.okayPointerDown(t)&&(this.pointerDownPointer={pageX:e.pageX,pageY:e.pageY},t.preventDefault(),this.pointerDownBlur(),this._bindPostStartEvents(t),this.emitEvent("pointerDown",[t,e]))};var n={TEXTAREA:!0,INPUT:!0,SELECT:!0,OPTION:!0},s={radio:!0,checkbox:!0,button:!0,submit:!0,image:!0,file:!0};return r.okayPointerDown=function(t){var e=n[t.target.nodeName],i=s[t.target.type],r=!e||i;return r||this._pointerReset(),r},r.pointerDownBlur=function(){var t=document.activeElement;t&&t.blur&&t!=document.body&&t.blur()},r.pointerMove=function(t,e){var i=this._dragPointerMove(t,e);this.emitEvent("pointerMove",[t,e,i]),this._dragMove(t,e,i)},r._dragPointerMove=function(t,e){var i={x:e.pageX-this.pointerDownPointer.pageX,y:e.pageY-this.pointerDownPointer.pageY};return!this.isDragging&&this.hasDragStarted(i)&&this._dragStart(t,e),i},r.hasDragStarted=function(t){return Math.abs(t.x)>3||Math.abs(t.y)>3},r.pointerUp=function(t,e){this.emitEvent("pointerUp",[t,e]),this._dragPointerUp(t,e)},r._dragPointerUp=function(t,e){this.isDragging?this._dragEnd(t,e):this._staticClick(t,e)},r._dragStart=function(t,e){this.isDragging=!0,this.isPreventingClicks=!0,this.dragStart(t,e)},r.dragStart=function(t,e){this.emitEvent("dragStart",[t,e])},r._dragMove=function(t,e,i){this.isDragging&&this.dragMove(t,e,i)},r.dragMove=function(t,e,i){t.preventDefault(),this.emitEvent("dragMove",[t,e,i])},r._dragEnd=function(t,e){this.isDragging=!1,setTimeout(function(){delete this.isPreventingClicks}.bind(this)),this.dragEnd(t,e)},r.dragEnd=function(t,e){this.emitEvent("dragEnd",[t,e])},r.onclick=function(t){this.isPreventingClicks&&t.preventDefault()},r._staticClick=function(t,e){this.isIgnoringMouseUp&&"mouseup"==t.type||(this.staticClick(t,e),"mouseup"!=t.type&&(this.isIgnoringMouseUp=!0,setTimeout(function(){delete this.isIgnoringMouseUp}.bind(this),400)))},r.staticClick=function(t,e){this.emitEvent("staticClick",[t,e])},i.getPointerPoint=e.getPointerPoint,i}(t,e)}.apply(C={},W=[P]),void 0!==w||(w=C)}(window),function(t,e){A=function(e,i,r){return function(t,e,i,r){r.extend(e.defaults,{draggable:">1",dragThreshold:3}),e.createMethods.push("_createDrag");var n=e.prototype;r.extend(n,i.prototype),n._touchActionValue="pan-y",n._createDrag=function(){this.on("activate",this.onActivateDrag),this.on("uiChange",this._uiChangeDrag),this.on("deactivate",this.onDeactivateDrag),this.on("cellChange",this.updateDraggable)},n.onActivateDrag=function(){this.handles=[this.viewport],this.bindHandles(),this.updateDraggable()},n.onDeactivateDrag=function(){this.unbindHandles(),this.element.classList.remove("is-draggable")},n.updateDraggable=function(){">1"==this.options.draggable?this.isDraggable=this.slides.length>1:this.isDraggable=this.options.draggable,this.isDraggable?this.element.classList.add("is-draggable"):this.element.classList.remove("is-draggable")},n.bindDrag=function(){this.options.draggable=!0,this.updateDraggable()},n.unbindDrag=function(){this.options.draggable=!1,this.updateDraggable()},n._uiChangeDrag=function(){delete this.isFreeScrolling},n.pointerDown=function(e,i){this.isDraggable?this.okayPointerDown(e)&&(this._pointerDownPreventDefault(e),this.pointerDownFocus(e),document.activeElement!=this.element&&this.pointerDownBlur(),this.dragX=this.x,this.viewport.classList.add("is-pointer-down"),this.pointerDownScroll=a(),t.addEventListener("scroll",this),this._pointerDownDefault(e,i)):this._pointerDownDefault(e,i)},n._pointerDownDefault=function(t,e){this.pointerDownPointer={pageX:e.pageX,pageY:e.pageY},this._bindPostStartEvents(t),this.dispatchEvent("pointerDown",t,[e])};var s={INPUT:!0,TEXTAREA:!0,SELECT:!0};function a(){return{x:t.pageXOffset,y:t.pageYOffset}}return n.pointerDownFocus=function(t){s[t.target.nodeName]||this.focus()},n._pointerDownPreventDefault=function(t){var e="touchstart"==t.type,i="touch"==t.pointerType,r=s[t.target.nodeName];e||i||r||t.preventDefault()},n.hasDragStarted=function(t){return Math.abs(t.x)>this.options.dragThreshold},n.pointerUp=function(t,e){delete this.isTouchScrolling,this.viewport.classList.remove("is-pointer-down"),this.dispatchEvent("pointerUp",t,[e]),this._dragPointerUp(t,e)},n.pointerDone=function(){t.removeEventListener("scroll",this),delete this.pointerDownScroll},n.dragStart=function(e,i){this.isDraggable&&(this.dragStartPosition=this.x,this.startAnimation(),t.removeEventListener("scroll",this),this.dispatchEvent("dragStart",e,[i]))},n.pointerMove=function(t,e){var i=this._dragPointerMove(t,e);this.dispatchEvent("pointerMove",t,[e,i]),this._dragMove(t,e,i)},n.dragMove=function(t,e,i){if(this.isDraggable){t.preventDefault(),this.previousDragX=this.dragX;var r=this.options.rightToLeft?-1:1;this.options.wrapAround&&(i.x%=this.slideableWidth);var n=this.dragStartPosition+i.x*r;if(!this.options.wrapAround&&this.slides.length){var s=Math.max(-this.slides[0].target,this.dragStartPosition);n=n>s?.5*(n+s):n;var a=Math.min(-this.getLastSlide().target,this.dragStartPosition);n=n<a?.5*(n+a):n}this.dragX=n,this.dragMoveTime=new Date,this.dispatchEvent("dragMove",t,[e,i])}},n.dragEnd=function(t,e){if(this.isDraggable){this.options.freeScroll&&(this.isFreeScrolling=!0);var i=this.dragEndRestingSelect();if(this.options.freeScroll&&!this.options.wrapAround){var r=this.getRestingPosition();this.isFreeScrolling=-r>this.slides[0].target&&-r<this.getLastSlide().target}else this.options.freeScroll||i!=this.selectedIndex||(i+=this.dragEndBoostSelect());delete this.previousDragX,this.isDragSelect=this.options.wrapAround,this.select(i),delete this.isDragSelect,this.dispatchEvent("dragEnd",t,[e])}},n.dragEndRestingSelect=function(){var t=this.getRestingPosition(),e=Math.abs(this.getSlideDistance(-t,this.selectedIndex)),i=this._getClosestResting(t,e,1),r=this._getClosestResting(t,e,-1);return i.distance<r.distance?i.index:r.index},n._getClosestResting=function(t,e,i){for(var r=this.selectedIndex,n=1/0,s=this.options.contain&&!this.options.wrapAround?function(t,e){return t<=e}:function(t,e){return t<e};s(e,n)&&(r+=i,n=e,null!==(e=this.getSlideDistance(-t,r)));)e=Math.abs(e);return{distance:n,index:r-i}},n.getSlideDistance=function(t,e){var i=this.slides.length,n=this.options.wrapAround&&i>1,s=n?r.modulo(e,i):e,a=this.slides[s];if(!a)return null;var o=n?this.slideableWidth*Math.floor(e/i):0;return t-(a.target+o)},n.dragEndBoostSelect=function(){if(void 0===this.previousDragX||!this.dragMoveTime||new Date-this.dragMoveTime>100)return 0;var t=this.getSlideDistance(-this.dragX,this.selectedIndex),e=this.previousDragX-this.dragX;return t>0&&e>0?1:t<0&&e<0?-1:0},n.staticClick=function(t,e){var i=this.getParentCell(t.target),r=i&&i.element,n=i&&this.cells.indexOf(i);this.dispatchEvent("staticClick",t,[e,r,n])},n.onscroll=function(){var t=a(),e=this.pointerDownScroll.x-t.x,i=this.pointerDownScroll.y-t.y;(Math.abs(e)>3||Math.abs(i)>3)&&this._pointerDone()},e}(t,e,i,r)}.apply(k={},W=[E,w,d]),void 0!==A||(A=k)}(window),window,void 0!==(D=function(t,e,i){return function(t,e,i,r){"use strict";var n="http://www.w3.org/2000/svg";function s(t,e){this.direction=t,this.parent=e,this._create()}s.prototype=Object.create(i.prototype),s.prototype._create=function(){this.isEnabled=!0,this.isPrevious=-1==this.direction;var t=this.parent.options.rightToLeft?1:-1;this.isLeft=this.direction==t;var e=this.element=document.createElement("button");e.className="flickity-button flickity-prev-next-button",e.className+=this.isPrevious?" previous":" next",e.setAttribute("type","button"),this.disable(),e.setAttribute("aria-label",this.isPrevious?"Previous":"Next");var i=this.createSVG();e.appendChild(i),this.parent.on("select",this.update.bind(this)),this.on("pointerDown",this.parent.childUIPointerDown.bind(this.parent))},s.prototype.activate=function(){this.bindStartEvent(this.element),this.element.addEventListener("click",this),this.parent.element.appendChild(this.element)},s.prototype.deactivate=function(){this.parent.element.removeChild(this.element),this.unbindStartEvent(this.element),this.element.removeEventListener("click",this)},s.prototype.createSVG=function(){var t=document.createElementNS(n,"svg");t.setAttribute("class","flickity-button-icon"),t.setAttribute("viewBox","0 0 100 100");var e,i=document.createElementNS(n,"path"),r="string"==typeof(e=this.parent.options.arrowShape)?e:"M "+e.x0+",50 L "+e.x1+","+(e.y1+50)+" L "+e.x2+","+(e.y2+50)+" L "+e.x3+",50  L "+e.x2+","+(50-e.y2)+" L "+e.x1+","+(50-e.y1)+" Z";return i.setAttribute("d",r),i.setAttribute("class","arrow"),this.isLeft||i.setAttribute("transform","translate(100, 100) rotate(180) "),t.appendChild(i),t},s.prototype.handleEvent=r.handleEvent,s.prototype.onclick=function(){if(this.isEnabled){this.parent.uiChange();var t=this.isPrevious?"previous":"next";this.parent[t]()}},s.prototype.enable=function(){this.isEnabled||(this.element.disabled=!1,this.isEnabled=!0)},s.prototype.disable=function(){this.isEnabled&&(this.element.disabled=!0,this.isEnabled=!1)},s.prototype.update=function(){var t=this.parent.slides;if(this.parent.options.wrapAround&&t.length>1)this.enable();else{var e=t.length?t.length-1:0,i=this.isPrevious?0:e;this[this.parent.selectedIndex==i?"disable":"enable"]()}},s.prototype.destroy=function(){this.deactivate(),this.allOff()},r.extend(e.defaults,{prevNextButtons:!0,arrowShape:{x0:10,x1:60,y1:50,x2:70,y2:40,x3:30}}),e.createMethods.push("_createPrevNextButtons");var a=e.prototype;return a._createPrevNextButtons=function(){this.options.prevNextButtons&&(this.prevButton=new s(-1,this),this.nextButton=new s(1,this),this.on("activate",this.activatePrevNextButtons))},a.activatePrevNextButtons=function(){this.prevButton.activate(),this.nextButton.activate(),this.on("deactivate",this.deactivatePrevNextButtons)},a.deactivatePrevNextButtons=function(){this.prevButton.deactivate(),this.nextButton.deactivate(),this.off("deactivate",this.deactivatePrevNextButtons)},e.PrevNextButton=s,e}(0,t,e,i)}.apply(T={},W=[E,P,d]))||(D=T),window,void 0!==(M=function(t,e,i){return function(t,e,i,r){function n(t){this.parent=t,this._create()}n.prototype=Object.create(i.prototype),n.prototype._create=function(){this.holder=document.createElement("ol"),this.holder.className="flickity-page-dots",this.dots=[],this.handleClick=this.onClick.bind(this),this.on("pointerDown",this.parent.childUIPointerDown.bind(this.parent))},n.prototype.activate=function(){this.setDots(),this.holder.addEventListener("click",this.handleClick),this.bindStartEvent(this.holder),this.parent.element.appendChild(this.holder)},n.prototype.deactivate=function(){this.holder.removeEventListener("click",this.handleClick),this.unbindStartEvent(this.holder),this.parent.element.removeChild(this.holder)},n.prototype.setDots=function(){var t=this.parent.slides.length-this.dots.length;t>0?this.addDots(t):t<0&&this.removeDots(-t)},n.prototype.addDots=function(t){for(var e=document.createDocumentFragment(),i=[],r=this.dots.length,n=r+t,s=r;s<n;s++){var a=document.createElement("li");a.className="dot",a.setAttribute("aria-label","Page dot "+(s+1)),e.appendChild(a),i.push(a)}this.holder.appendChild(e),this.dots=this.dots.concat(i)},n.prototype.removeDots=function(t){this.dots.splice(this.dots.length-t,t).forEach((function(t){this.holder.removeChild(t)}),this)},n.prototype.updateSelected=function(){this.selectedDot&&(this.selectedDot.className="dot",this.selectedDot.removeAttribute("aria-current")),this.dots.length&&(this.selectedDot=this.dots[this.parent.selectedIndex],this.selectedDot.className="dot is-selected",this.selectedDot.setAttribute("aria-current","step"))},n.prototype.onTap=n.prototype.onClick=function(t){var e=t.target;if("LI"==e.nodeName){this.parent.uiChange();var i=this.dots.indexOf(e);this.parent.select(i)}},n.prototype.destroy=function(){this.deactivate(),this.allOff()},e.PageDots=n,r.extend(e.defaults,{pageDots:!0}),e.createMethods.push("_createPageDots");var s=e.prototype;return s._createPageDots=function(){this.options.pageDots&&(this.pageDots=new n(this),this.on("activate",this.activatePageDots),this.on("select",this.updateSelectedPageDots),this.on("cellChange",this.updatePageDots),this.on("resize",this.updatePageDots),this.on("deactivate",this.deactivatePageDots))},s.activatePageDots=function(){this.pageDots.activate()},s.updateSelectedPageDots=function(){this.pageDots.updateSelected()},s.updatePageDots=function(){this.pageDots.setDots()},s.deactivatePageDots=function(){this.pageDots.deactivate()},e.PageDots=n,e}(0,t,e,i)}.apply(I={},W=[E,P,d]))||(M=I),window,void 0!==(F=function(t,e,i){return function(t,e,i){function r(t){this.parent=t,this.state="stopped",this.onVisibilityChange=this.visibilityChange.bind(this),this.onVisibilityPlay=this.visibilityPlay.bind(this)}r.prototype=Object.create(t.prototype),r.prototype.play=function(){"playing"!=this.state&&(document.hidden?document.addEventListener("visibilitychange",this.onVisibilityPlay):(this.state="playing",document.addEventListener("visibilitychange",this.onVisibilityChange),this.tick()))},r.prototype.tick=function(){if("playing"==this.state){var t=this.parent.options.autoPlay;t="number"==typeof t?t:3e3;var e=this;this.clear(),this.timeout=setTimeout((function(){e.parent.next(!0),e.tick()}),t)}},r.prototype.stop=function(){this.state="stopped",this.clear(),document.removeEventListener("visibilitychange",this.onVisibilityChange)},r.prototype.clear=function(){clearTimeout(this.timeout)},r.prototype.pause=function(){"playing"==this.state&&(this.state="paused",this.clear())},r.prototype.unpause=function(){"paused"==this.state&&this.play()},r.prototype.visibilityChange=function(){this[document.hidden?"pause":"unpause"]()},r.prototype.visibilityPlay=function(){this.play(),document.removeEventListener("visibilitychange",this.onVisibilityPlay)},e.extend(i.defaults,{pauseAutoPlayOnHover:!0}),i.createMethods.push("_createPlayer");var n=i.prototype;return n._createPlayer=function(){this.player=new r(this),this.on("activate",this.activatePlayer),this.on("uiChange",this.stopPlayer),this.on("pointerDown",this.stopPlayer),this.on("deactivate",this.deactivatePlayer)},n.activatePlayer=function(){this.options.autoPlay&&(this.player.play(),this.element.addEventListener("mouseenter",this))},n.playPlayer=function(){this.player.play()},n.stopPlayer=function(){this.player.stop()},n.pausePlayer=function(){this.player.pause()},n.unpausePlayer=function(){this.player.unpause()},n.deactivatePlayer=function(){this.player.stop(),this.element.removeEventListener("mouseenter",this)},n.onmouseenter=function(){this.options.pauseAutoPlayOnHover&&(this.player.pause(),this.element.addEventListener("mouseleave",this))},n.onmouseleave=function(){this.player.unpause(),this.element.removeEventListener("mouseleave",this)},i.Player=r,i}(t,e,i)}.apply(L={},W=[r,d,E]))||(F=L),window,void 0!==(R=function(t,e){return function(t,e,i){var r=e.prototype;return r.insert=function(t,e){var i=this._makeCells(t);if(i&&i.length){var r=this.cells.length;e=void 0===e?r:e;var n=function(t){var e=document.createDocumentFragment();return t.forEach((function(t){e.appendChild(t.element)})),e}(i),s=e==r;if(s)this.slider.appendChild(n);else{var a=this.cells[e].element;this.slider.insertBefore(n,a)}if(0===e)this.cells=i.concat(this.cells);else if(s)this.cells=this.cells.concat(i);else{var o=this.cells.splice(e,r-e);this.cells=this.cells.concat(i).concat(o)}this._sizeCells(i),this.cellChange(e,!0)}},r.append=function(t){this.insert(t,this.cells.length)},r.prepend=function(t){this.insert(t,0)},r.remove=function(t){var e=this.getCells(t);if(e&&e.length){var r=this.cells.length-1;e.forEach((function(t){t.remove();var e=this.cells.indexOf(t);r=Math.min(e,r),i.removeFrom(this.cells,t)}),this),this.cellChange(r,!0)}},r.cellSizeChange=function(t){var e=this.getCell(t);if(e){e.getSize();var i=this.cells.indexOf(e);this.cellChange(i)}},r.cellChange=function(t,e){var i=this.selectedElement;this._positionCells(t),this._getWrapShiftCells(),this.setGallerySize();var r=this.getCell(i);r&&(this.selectedIndex=this.getCellSlideIndex(r)),this.selectedIndex=Math.min(this.slides.length-1,this.selectedIndex),this.emitEvent("cellChange",[t]),this.select(this.selectedIndex),e&&this.positionSliderAtSelected()},e}(0,t,e)}.apply(B={},W=[E,d]))||(R=B),window,void 0!==(O=function(t,e){return function(t,e,i){"use strict";e.createMethods.push("_createLazyload");var r=e.prototype;function n(t,e){this.img=t,this.flickity=e,this.load()}return r._createLazyload=function(){this.on("select",this.lazyLoad)},r.lazyLoad=function(){var t=this.options.lazyLoad;if(t){var e="number"==typeof t?t:0,r=this.getAdjacentCellElements(e),s=[];r.forEach((function(t){var e=function(t){if("IMG"==t.nodeName){var e=t.getAttribute("data-flickity-lazyload"),r=t.getAttribute("data-flickity-lazyload-src"),n=t.getAttribute("data-flickity-lazyload-srcset");if(e||r||n)return[t]}var s=t.querySelectorAll("img[data-flickity-lazyload], img[data-flickity-lazyload-src], img[data-flickity-lazyload-srcset]");return i.makeArray(s)}(t);s=s.concat(e)})),s.forEach((function(t){new n(t,this)}),this)}},n.prototype.handleEvent=i.handleEvent,n.prototype.load=function(){this.img.addEventListener("load",this),this.img.addEventListener("error",this);var t=this.img.getAttribute("data-flickity-lazyload")||this.img.getAttribute("data-flickity-lazyload-src"),e=this.img.getAttribute("data-flickity-lazyload-srcset");this.img.src=t,e&&this.img.setAttribute("srcset",e),this.img.removeAttribute("data-flickity-lazyload"),this.img.removeAttribute("data-flickity-lazyload-src"),this.img.removeAttribute("data-flickity-lazyload-srcset")},n.prototype.onload=function(t){this.complete(t,"flickity-lazyloaded")},n.prototype.onerror=function(t){this.complete(t,"flickity-lazyerror")},n.prototype.complete=function(t,e){this.img.removeEventListener("load",this),this.img.removeEventListener("error",this);var i=this.flickity.getParentCell(this.img),r=i&&i.element;this.flickity.cellSizeChange(r),this.img.classList.add(e),this.flickity.dispatchEvent("lazyLoad",t,r)},e.LazyLoader=n,e}(0,t,e)}.apply($={},W=[E,d]))||(O=$),window,"function"==typeof(V=function(t){return t})?void 0===(N=V.apply(z={},[E,A,D,M,F,R,O]))&&(N=z):N=V,window,W=[N,d],void 0===(q="function"==typeof(G=function(t,e){t.createMethods.push("_createAsNavFor");var i=t.prototype;return i._createAsNavFor=function(){this.on("activate",this.activateAsNavFor),this.on("deactivate",this.deactivateAsNavFor),this.on("destroy",this.destroyAsNavFor);var t=this.options.asNavFor;if(t){var e=this;setTimeout((function(){e.setNavCompanion(t)}))}},i.setNavCompanion=function(i){i=e.getQueryElement(i);var r=t.data(i);if(r&&r!=this){this.navCompanion=r;var n=this;this.onNavCompanionSelect=function(){n.navCompanionSelect()},r.on("select",this.onNavCompanionSelect),this.on("staticClick",this.onNavStaticClick),this.navCompanionSelect(!0)}},i.navCompanionSelect=function(t){var e,i,r,n=this.navCompanion&&this.navCompanion.selectedCells;if(n){var s=n[0],a=this.navCompanion.cells.indexOf(s),o=a+n.length-1,h=Math.floor((e=a,i=o,r=this.navCompanion.cellAlign,(i-e)*r+e));if(this.selectCell(h,!1,t),this.removeNavSelectedElements(),!(h>=this.cells.length)){var l=this.cells.slice(a,o+1);this.navSelectedElements=l.map((function(t){return t.element})),this.changeNavSelectedClass("add")}}},i.changeNavSelectedClass=function(t){this.navSelectedElements.forEach((function(e){e.classList[t]("is-nav-selected")}))},i.activateAsNavFor=function(){this.navCompanionSelect(!0)},i.removeNavSelectedElements=function(){this.navSelectedElements&&(this.changeNavSelectedClass("remove"),delete this.navSelectedElements)},i.onNavStaticClick=function(t,e,i,r){"number"==typeof r&&this.navCompanion.selectCell(r)},i.deactivateAsNavFor=function(){this.removeNavSelectedElements()},i.destroyAsNavFor=function(){this.navCompanion&&(this.navCompanion.off("select",this.onNavCompanionSelect),this.off("staticClick",this.onNavStaticClick),delete this.navCompanion)},t})?G.apply(e,W):G)||(t.exports=q),function(t,e){"use strict";j=function(e){return function(t,e){var i=t.jQuery,r=t.console;function n(t,e){for(var i in e)t[i]=e[i];return t}var s=Array.prototype.slice;function a(t,e,o){if(!(this instanceof a))return new a(t,e,o);var h,l=t;"string"==typeof t&&(l=document.querySelectorAll(t)),l?(this.elements=(h=l,Array.isArray(h)?h:"object"==typeof h&&"number"==typeof h.length?s.call(h):[h]),this.options=n({},this.options),"function"==typeof e?o=e:n(this.options,e),o&&this.on("always",o),this.getImages(),i&&(this.jqDeferred=new i.Deferred),setTimeout(this.check.bind(this))):r.error("Bad element for imagesLoaded "+(l||t))}a.prototype=Object.create(e.prototype),a.prototype.options={},a.prototype.getImages=function(){this.images=[],this.elements.forEach(this.addElementImages,this)},a.prototype.addElementImages=function(t){"IMG"==t.nodeName&&this.addImage(t),!0===this.options.background&&this.addElementBackgroundImages(t);var e=t.nodeType;if(e&&o[e]){for(var i=t.querySelectorAll("img"),r=0;r<i.length;r++){var n=i[r];this.addImage(n)}if("string"==typeof this.options.background){var s=t.querySelectorAll(this.options.background);for(r=0;r<s.length;r++){var a=s[r];this.addElementBackgroundImages(a)}}}};var o={1:!0,9:!0,11:!0};function h(t){this.img=t}function l(t,e){this.url=t,this.element=e,this.img=new Image}return a.prototype.addElementBackgroundImages=function(t){var e=getComputedStyle(t);if(e)for(var i=/url\((['"])?(.*?)\1\)/gi,r=i.exec(e.backgroundImage);null!==r;){var n=r&&r[2];n&&this.addBackground(n,t),r=i.exec(e.backgroundImage)}},a.prototype.addImage=function(t){var e=new h(t);this.images.push(e)},a.prototype.addBackground=function(t,e){var i=new l(t,e);this.images.push(i)},a.prototype.check=function(){var t=this;function e(e,i,r){setTimeout((function(){t.progress(e,i,r)}))}this.progressedCount=0,this.hasAnyBroken=!1,this.images.length?this.images.forEach((function(t){t.once("progress",e),t.check()})):this.complete()},a.prototype.progress=function(t,e,i){this.progressedCount++,this.hasAnyBroken=this.hasAnyBroken||!t.isLoaded,this.emitEvent("progress",[this,t,e]),this.jqDeferred&&this.jqDeferred.notify&&this.jqDeferred.notify(this,t),this.progressedCount==this.images.length&&this.complete(),this.options.debug&&r&&r.log("progress: "+i,t,e)},a.prototype.complete=function(){var t=this.hasAnyBroken?"fail":"done";if(this.isComplete=!0,this.emitEvent(t,[this]),this.emitEvent("always",[this]),this.jqDeferred){var e=this.hasAnyBroken?"reject":"resolve";this.jqDeferred[e](this)}},h.prototype=Object.create(e.prototype),h.prototype.check=function(){this.getIsImageComplete()?this.confirm(0!==this.img.naturalWidth,"naturalWidth"):(this.proxyImage=new Image,this.proxyImage.addEventListener("load",this),this.proxyImage.addEventListener("error",this),this.img.addEventListener("load",this),this.img.addEventListener("error",this),this.proxyImage.src=this.img.src)},h.prototype.getIsImageComplete=function(){return this.img.complete&&this.img.naturalWidth},h.prototype.confirm=function(t,e){this.isLoaded=t,this.emitEvent("progress",[this,this.img,e])},h.prototype.handleEvent=function(t){var e="on"+t.type;this[e]&&this[e](t)},h.prototype.onload=function(){this.confirm(!0,"onload"),this.unbindEvents()},h.prototype.onerror=function(){this.confirm(!1,"onerror"),this.unbindEvents()},h.prototype.unbindEvents=function(){this.proxyImage.removeEventListener("load",this),this.proxyImage.removeEventListener("error",this),this.img.removeEventListener("load",this),this.img.removeEventListener("error",this)},l.prototype=Object.create(h.prototype),l.prototype.check=function(){this.img.addEventListener("load",this),this.img.addEventListener("error",this),this.img.src=this.url,this.getIsImageComplete()&&(this.confirm(0!==this.img.naturalWidth,"naturalWidth"),this.unbindEvents())},l.prototype.unbindEvents=function(){this.img.removeEventListener("load",this),this.img.removeEventListener("error",this)},l.prototype.confirm=function(t,e){this.isLoaded=t,this.emitEvent("progress",[this,this.element,e])},a.makeJQueryPlugin=function(e){(e=e||t.jQuery)&&((i=e).fn.imagesLoaded=function(t,e){return new a(this,t,e).jqDeferred.promise(i(this))})},a.makeJQueryPlugin(),a}(t,e)}.apply(H={},W=[r]),void 0!==j||(j=H)}("undefined"!=typeof window?window:this),window,void 0===(q=function(t,e){return function(t,e,i){"use strict";e.createMethods.push("_createImagesLoaded");var r=e.prototype;return r._createImagesLoaded=function(){this.on("activate",this.imagesLoaded)},r.imagesLoaded=function(){if(this.options.imagesLoaded){var t=this;i(this.slider).on("progress",(function(e,i){var r=t.getParentCell(i.img);t.cellSizeChange(r&&r.element),t.options.freeScroll||t.positionSliderAtSelected()}))}},e}(0,t,e)}.apply(e,W=[N,j]))||(t.exports=q)},428:function(t){"use strict";t.exports=window.jQuery}},__webpack_module_cache__={};function __webpack_require__(t){var e=__webpack_module_cache__[t];if(void 0!==e)return e.exports;var i=__webpack_module_cache__[t]={exports:{}};return __webpack_modules__[t].call(i.exports,i,i.exports,__webpack_require__),i.exports}__webpack_require__.n=function(t){var e=t&&t.__esModule?function(){return t.default}:function(){return t};return __webpack_require__.d(e,{a:e}),e},__webpack_require__.d=function(t,e){for(var i in e)__webpack_require__.o(e,i)&&!__webpack_require__.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:e[i]})},__webpack_require__.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(t){if("object"==typeof window)return window}}(),__webpack_require__.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)};var __webpack_exports__={};!function(){"use strict";__webpack_require__(8460),UxBuilder.controller("block",__webpack_require__(4268).A),UxBuilder.controller("col",__webpack_require__(9071).A),UxBuilder.controller("col_grid",__webpack_require__(4082).A),UxBuilder.controller("tabgroup",__webpack_require__(2807).A),UxBuilder.controller("text",__webpack_require__(1482).A),UxBuilder.controller("ux_banner",__webpack_require__(4239).A),UxBuilder.controller("ux_banner_grid",__webpack_require__(636).A),UxBuilder.controller("ux_slider",__webpack_require__(8814).A),UxBuilder.controller("ux_hotspot",__webpack_require__(7752).A),UxBuilder.controller("scroll_to",__webpack_require__(1838).A),UxBuilder.controller("map",__webpack_require__(483).A),UxBuilder.controller("text_box",__webpack_require__(6772).A),UxBuilder.controller("ux_lottie",__webpack_require__(8728).A),UxBuilder.on("shortcode-attached",(function(t){console.debug("+ shortcode-attached",t.tag),Flatsome.attach(t.$element)})),UxBuilder.on("shortcode-moved",(function(t){console.debug("⬍ shortcode-moved",t.tag),"scroll_to"===t.tag&&Flatsome.attach("scroll-to",t.$element)})),UxBuilder.on("shortcode-detached",(function(t){console.debug("- shortcode-detached",t.tag),Flatsome.detach(t.$element)})),UxBuilder.addfilter("shortcode-content",(function(t){return t.replace(/data-animate="(.*?)"/g,'data-animate="$1" data-animated="true"')})),UxBuilder.on("shortcode-content-change",(function(t){console.debug("~ shortcode-content-change",t.tag)})),UxBuilder.on("shortcode-content-mcetoggleformat",(function(t,e,i){console.debug("~ shortcode-content-mcetoggleformat",t.tag,e,i),Flatsome.detach(t.$element),Flatsome.attach(t.$element)}))}()})();