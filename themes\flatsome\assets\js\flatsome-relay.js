!function(){"use strict";const e="flatsome",t=".ux-relay",a=".ux-relay__pagination",r=".ux-relay__load-more-button",o=".ux-relay__nav-button";class n{constructor(){this.$body=jQuery(document.body),jQuery(`${r}`,this.$body).length>0&&this.loadMore(),jQuery(`${a}`,this.$body).length>0&&this.pagination(),jQuery(`${o}`,this.$body).length>0&&this.nextPrev()}nextPrev(){this.$body.on(`click.${e}${o}`,`${o}`,(e=>{e.preventDefault();const a=jQuery(e.currentTarget),r=a.closest(`${t}`),o=r.data("flatsome-relay");if(!o)return void this.logError("data attribute empty or missing");const{tag:n,atts:s}=o,l=a.data("flatsome-dir");"next"===l?o.currentPage++:"prev"===l&&o.currentPage--,s.page_number=o.currentPage,this.sendAjaxRequest(r,n,s,o,null,(function(e,t){t.find(".ux-relay__nav-button--next").prop("disabled",o.currentPage>=o.totalPages),t.find(".ux-relay__nav-button--prev").prop("disabled",o.currentPage<=1)}))}))}loadMore(){this.$body.on(`click.${e}${r}`,`${r}`,(e=>{e.preventDefault();const a=jQuery(e.currentTarget),r=a.closest(`${t}`),o=r.data("flatsome-relay");if(!o)return void this.logError("data attribute empty or missing");const{tag:n,atts:s}=o;o.currentPage++,s.page_number=o.currentPage,r.attr("data-flatsome-relay",JSON.stringify(o)),this.sendAjaxRequest(r,n,s,o,a)}))}pagination(){this.$body.on(`click.${e}${a}`,".ux-relay__pagination.page-numbers a",(e=>{e.preventDefault();const a=jQuery(e.currentTarget),r=a.closest(`${t}`),o=r.data("flatsome-relay");if(!o)return void this.logError("data attribute empty or missing");const{tag:n,atts:s}=o,l=a.attr("href").split("#/page/")[1];o.currentPage=l,s.page_number=o.currentPage,this.sendAjaxRequest(r,n,s,o)}))}sendAjaxRequest(e,t,a,r,o=null,n=null){o?o.addClass("loading"):function(e,t={}){"boolean"==typeof t&&(t={processing:t});const{style:a="normal",position:r="",processing:o=!0}=t;let n;if("string"==typeof e?n=document.querySelector(e):e instanceof Element?n=e:e instanceof jQuery&&(n=e.get(0)),!n)return;if(!o){const e=n.querySelector(".ux-loader");return e&&e.remove(),void Array.from(n.children).forEach((e=>{e.style.opacity=""}))}"static"===window.getComputedStyle(n).position&&(n.style.position="relative");const s=function(e,t){const a=["ux-loader"];["normal","spotlight"].includes(e)&&a.push(`ux-loader--style-${e}`),["sticky"].includes(t)&&a.push(`ux-loader--position-${t}`);const r=document.createElement("div");r.className=a.join(" ");const o=document.createElement("div");o.className="ux-loader__inner";const n=document.createElement("div");return n.className="loading-spin centered",o.appendChild(n),r.appendChild(o),r}(a,r);n.insertAdjacentElement("afterbegin",s),Array.from(n.children).forEach((e=>{e!==s&&(e.style.opacity=".2")}))}(e.find(".row"));const s=e.data("flatsome-relay-request");s&&"function"==typeof s.abort&&s.abort();const l=jQuery.ajax({url:flatsomeVars.ajaxurl,data:{action:"flatsome_ajax_apply_shortcode",tag:t,atts:a}}).done((t=>{o&&o.toggleClass("loading"),e.data("flatsome-relay-request",null),this.handleAjaxSuccess(t,e,r,o,n),this.$body.trigger("flatsome-relay-request-done",[t,e,r,o])})).fail((t=>{"abort"!==t.statusText&&this.showError(t,e)}));e.data("flatsome-relay-request",l)}handleAjaxSuccess(e,t,a,r=null,o=null){if(!e.success)return this.showError(e.data,t);if("string"!=typeof e.data)return console.error("Element ajax error:",e.data);const n=jQuery(e.data),s=r?t.find("[data-packery-options]"):n.find("[data-packery-options]"),l=s.length,i=r?t.find(".row"):n.find(".row");n.find("[data-animate]").removeAttr("data-animate");"product"!==a.postType&&"post"!==a.postType||(()=>{const e=n.find(".col");if(r){let t=parseInt(r.find(".ux-relay__current-count").text(),10);t+=parseInt(a.postCount,10),r.find(".ux-relay__current-count").text(t),l?s.imagesLoaded((()=>{s.packery().append(e).packery("appended",e).packery("layout").on("layoutComplete",(function(){i.hasClass("equalize-box")&&jQuery(document).trigger("flatsome-equalize-box")}))})):i.append(e),Flatsome.attach(e)}else this.$body.trigger("flatsome-relay-before-replace-element",[t,n]),i.fadeOut(0),Flatsome.detach(t),t.replaceWith(n),i.fadeIn(),s.length&&s.imagesLoaded((()=>{s.packery()})),Flatsome.attach(n);!l&&i.hasClass("equalize-box")&&jQuery(document).trigger("flatsome-equalize-box"),r&&a.currentPage>=a.totalPages&&r.hide(),o&&"function"==typeof o&&o(t,n)})()}showError(e,t){this.$body.hasClass("admin-bar")&&t.before('<div class="container error"><p>Element ajax error: '+e+"</p></div>"),this.logError(e)}logError(e){console.error("Element ajax error:",e)}}jQuery((()=>new n))}();