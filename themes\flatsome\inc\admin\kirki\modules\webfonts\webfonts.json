{"items": {"ABeeZee": {"family": "ABeeZee", "category": "sans-serif", "variants": ["italic", "regular"]}, "ADLaM Display": {"family": "ADLaM Display", "category": "display", "variants": ["regular"]}, "AR One Sans": {"family": "AR One Sans", "category": "sans-serif", "variants": ["500", "600", "700", "regular"]}, "Abel": {"family": "<PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Abhaya Libre": {"family": "<PERSON><PERSON><PERSON>", "category": "serif", "variants": ["500", "600", "700", "800", "regular"]}, "Aboreto": {"family": "Aboreto", "category": "display", "variants": ["regular"]}, "Abril Fatface": {"family": "Abril Fatface", "category": "display", "variants": ["regular"]}, "Abyssinica SIL": {"family": "Abyssinica SIL", "category": "serif", "variants": ["regular"]}, "Aclonica": {"family": "Aclonica", "category": "sans-serif", "variants": ["regular"]}, "Acme": {"family": "Acme", "category": "sans-serif", "variants": ["regular"]}, "Actor": {"family": "Actor", "category": "sans-serif", "variants": ["regular"]}, "Adamina": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Advent Pro": {"family": "Advent Pro", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Afacad": {"family": "Afacad", "category": "sans-serif", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Afacad Flux": {"family": "Afacad Flux", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Agbalumo": {"family": "Agbalumo", "category": "display", "variants": ["regular"]}, "Agdasima": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["700", "regular"]}, "Agu Display": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Aguafina Script": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Akatab": {"family": "Akatab", "category": "sans-serif", "variants": ["500", "600", "700", "800", "900", "regular"]}, "Akaya Kanadaka": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Akaya Telivigala": {"family": "Akaya Telivigala", "category": "display", "variants": ["regular"]}, "Akronim": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Akshar": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["300", "500", "600", "700", "regular"]}, "Aladin": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Alata": {"family": "Alata", "category": "sans-serif", "variants": ["regular"]}, "Alatsi": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Albert Sans": {"family": "<PERSON>", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Aldrich": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Alef": {"family": "Alef", "category": "sans-serif", "variants": ["700", "regular"]}, "Alegreya": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "serif", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Alegreya SC": {"family": "Alegreya SC", "category": "serif", "variants": ["500", "500italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Alegreya Sans": {"family": "Alegreya Sans", "category": "sans-serif", "variants": ["100", "100italic", "300", "300italic", "500", "500italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Alegreya Sans SC": {"family": "Alegreya Sans SC", "category": "sans-serif", "variants": ["100", "100italic", "300", "300italic", "500", "500italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Aleo": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Alex Brush": {"family": "<PERSON>", "category": "handwriting", "variants": ["regular"]}, "Alexandria": {"family": "Alexandria", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Alfa Slab One": {"family": "Alfa Slab One", "category": "display", "variants": ["regular"]}, "Alice": {"family": "<PERSON>", "category": "serif", "variants": ["regular"]}, "Alike": {"family": "Alike", "category": "serif", "variants": ["regular"]}, "Alike Angular": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Alkalami": {"family": "Alkalami", "category": "serif", "variants": ["regular"]}, "Alkatra": {"family": "Alkatra", "category": "display", "variants": ["500", "600", "700", "regular"]}, "Allan": {"family": "<PERSON>", "category": "display", "variants": ["700", "regular"]}, "Allerta": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Allerta Stencil": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Allison": {"family": "<PERSON>", "category": "handwriting", "variants": ["regular"]}, "Allura": {"family": "<PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Almarai": {"family": "Almarai", "category": "sans-serif", "variants": ["300", "700", "800", "regular"]}, "Almendra": {"family": "<PERSON><PERSON><PERSON>", "category": "serif", "variants": ["700", "700italic", "italic", "regular"]}, "Almendra Display": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Almendra SC": {"family": "Almendra SC", "category": "serif", "variants": ["regular"]}, "Alumni Sans": {"family": "Alumni Sans", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Alumni Sans Collegiate One": {"family": "Alumni Sans Collegiate One", "category": "sans-serif", "variants": ["italic", "regular"]}, "Alumni Sans Inline One": {"family": "Alumni Sans Inline One", "category": "display", "variants": ["italic", "regular"]}, "Alumni Sans Pinstripe": {"family": "Alumni Sans Pinstripe", "category": "sans-serif", "variants": ["italic", "regular"]}, "Amarante": {"family": "Amarante", "category": "display", "variants": ["regular"]}, "Amaranth": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["700", "700italic", "italic", "regular"]}, "Amatic SC": {"family": "Amatic SC", "category": "handwriting", "variants": ["700", "regular"]}, "Amethysta": {"family": "Amethysta", "category": "serif", "variants": ["regular"]}, "Amiko": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["600", "700", "regular"]}, "Amiri": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["700", "700italic", "italic", "regular"]}, "Amiri Quran": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Amita": {"family": "<PERSON><PERSON>", "category": "handwriting", "variants": ["700", "regular"]}, "Anaheim": {"family": "Anaheim", "category": "sans-serif", "variants": ["500", "600", "700", "800", "regular"]}, "Andada Pro": {"family": "Andada Pro", "category": "serif", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "italic", "regular"]}, "Andika": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["700", "700italic", "italic", "regular"]}, "Anek Bangla": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "regular"]}, "Anek Devanagari": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "regular"]}, "Anek Gujarati": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "regular"]}, "Anek Gurmukhi": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "regular"]}, "Anek Kannada": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "regular"]}, "Anek Latin": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "regular"]}, "Anek Malayalam": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "regular"]}, "Anek Odia": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "regular"]}, "Anek Tamil": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "regular"]}, "Anek Telugu": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "regular"]}, "Angkor": {"family": "Angkor", "category": "display", "variants": ["regular"]}, "Annapurna SIL": {"family": "Annapurna SIL", "category": "serif", "variants": ["700", "regular"]}, "Annie Use Your Telescope": {"family": "Annie Use Your Telescope", "category": "handwriting", "variants": ["regular"]}, "Anonymous Pro": {"family": "Anonymous Pro", "category": "monospace", "variants": ["700", "700italic", "italic", "regular"]}, "Anta": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Antic": {"family": "Antic", "category": "sans-serif", "variants": ["regular"]}, "Antic Didone": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Antic Slab": {"family": "Antic Slab", "category": "serif", "variants": ["regular"]}, "Anton": {"family": "<PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Anton SC": {"family": "Anton SC", "category": "sans-serif", "variants": ["regular"]}, "Antonio": {"family": "Antonio", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "regular"]}, "Anuphan": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "regular"]}, "Anybody": {"family": "Anybody", "category": "display", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Aoboshi One": {"family": "<PERSON><PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Arapey": {"family": "Ara<PERSON><PERSON>", "category": "serif", "variants": ["italic", "regular"]}, "Arbutus": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Arbutus Slab": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Architects Daughter": {"family": "Architects Daughter", "category": "handwriting", "variants": ["regular"]}, "Archivo": {"family": "Archivo", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Archivo Black": {"family": "Archivo Black", "category": "sans-serif", "variants": ["regular"]}, "Archivo Narrow": {"family": "Archivo Narrow", "category": "sans-serif", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Are You Serious": {"family": "Are You Serious", "category": "handwriting", "variants": ["regular"]}, "Aref Ruqaa": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["700", "regular"]}, "Aref Ruqaa Ink": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["700", "regular"]}, "Arima": {"family": "Ari<PERSON>", "category": "display", "variants": ["100", "200", "300", "500", "600", "700", "regular"]}, "Arimo": {"family": "Ari<PERSON>", "category": "sans-serif", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Arizonia": {"family": "Arizonia", "category": "handwriting", "variants": ["regular"]}, "Armata": {"family": "Armata", "category": "sans-serif", "variants": ["regular"]}, "Arsenal": {"family": "Arsenal", "category": "sans-serif", "variants": ["700", "700italic", "italic", "regular"]}, "Arsenal SC": {"family": "Arsenal SC", "category": "sans-serif", "variants": ["700", "700italic", "italic", "regular"]}, "Artifika": {"family": "Artifika", "category": "serif", "variants": ["regular"]}, "Arvo": {"family": "Arvo", "category": "serif", "variants": ["700", "700italic", "italic", "regular"]}, "Arya": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["700", "regular"]}, "Asap": {"family": "Asap", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Asap Condensed": {"family": "Asap Condensed", "category": "sans-serif", "variants": ["200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Asar": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Asset": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Assistant": {"family": "Assistant", "category": "sans-serif", "variants": ["200", "300", "500", "600", "700", "800", "regular"]}, "Astloch": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["700", "regular"]}, "Asul": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["700", "regular"]}, "Athiti": {"family": "Athiti", "category": "sans-serif", "variants": ["200", "300", "500", "600", "700", "regular"]}, "Atkinson Hyperlegible": {"family": "Atkinson Hyperlegible", "category": "sans-serif", "variants": ["700", "700italic", "italic", "regular"]}, "Atma": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["300", "500", "600", "700", "regular"]}, "Atomic Age": {"family": "Atomic Age", "category": "display", "variants": ["regular"]}, "Aubrey": {"family": "<PERSON>", "category": "display", "variants": ["regular"]}, "Audiowide": {"family": "Audiowide", "category": "display", "variants": ["regular"]}, "Autour One": {"family": "Autour One", "category": "display", "variants": ["regular"]}, "Average": {"family": "Average", "category": "serif", "variants": ["regular"]}, "Average Sans": {"family": "Average Sans", "category": "sans-serif", "variants": ["regular"]}, "Averia Gruesa Libre": {"family": "Averia Gruesa Libre", "category": "display", "variants": ["regular"]}, "Averia Libre": {"family": "Averia Libre", "category": "display", "variants": ["300", "300italic", "700", "700italic", "italic", "regular"]}, "Averia Sans Libre": {"family": "Averia Sans Libre", "category": "display", "variants": ["300", "300italic", "700", "700italic", "italic", "regular"]}, "Averia Serif Libre": {"family": "Averia Serif <PERSON>", "category": "display", "variants": ["300", "300italic", "700", "700italic", "italic", "regular"]}, "Azeret Mono": {"family": "<PERSON><PERSON><PERSON>", "category": "monospace", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "B612": {"family": "B612", "category": "sans-serif", "variants": ["700", "700italic", "italic", "regular"]}, "B612 Mono": {"family": "B612 Mono", "category": "monospace", "variants": ["700", "700italic", "italic", "regular"]}, "BIZ UDGothic": {"family": "BIZ UDGothic", "category": "sans-serif", "variants": ["700", "regular"]}, "BIZ UDMincho": {"family": "BIZ UDMincho", "category": "serif", "variants": ["700", "regular"]}, "BIZ UDPGothic": {"family": "BIZ UDPGothic", "category": "sans-serif", "variants": ["700", "regular"]}, "BIZ UDPMincho": {"family": "BIZ UDPMincho", "category": "serif", "variants": ["700", "regular"]}, "Babylonica": {"family": "Babylonica", "category": "handwriting", "variants": ["regular"]}, "Bacasime Antique": {"family": "Bacasime Antique", "category": "serif", "variants": ["regular"]}, "Bad Script": {"family": "<PERSON>", "category": "handwriting", "variants": ["regular"]}, "Badeen Display": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Bagel Fat One": {"family": "Bagel <PERSON> One", "category": "display", "variants": ["regular"]}, "Bahiana": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Bahianita": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Bai Jamjuree": {"family": "<PERSON>", "category": "sans-serif", "variants": ["200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Bakbak One": {"family": "Bakbak One", "category": "display", "variants": ["regular"]}, "Ballet": {"family": "Ballet", "category": "handwriting", "variants": ["regular"]}, "Baloo 2": {"family": "Baloo 2", "category": "display", "variants": ["500", "600", "700", "800", "regular"]}, "Baloo Bhai 2": {"family": "Baloo Bhai 2", "category": "display", "variants": ["500", "600", "700", "800", "regular"]}, "Baloo Bhaijaan 2": {"family": "Baloo Bhaijaan 2", "category": "display", "variants": ["500", "600", "700", "800", "regular"]}, "Baloo Bhaina 2": {"family": "Baloo Bhaina 2", "category": "display", "variants": ["500", "600", "700", "800", "regular"]}, "Baloo Chettan 2": {"family": "Baloo Chettan 2", "category": "display", "variants": ["500", "600", "700", "800", "regular"]}, "Baloo Da 2": {"family": "Baloo Da 2", "category": "display", "variants": ["500", "600", "700", "800", "regular"]}, "Baloo Paaji 2": {"family": "Baloo Paaji 2", "category": "display", "variants": ["500", "600", "700", "800", "regular"]}, "Baloo Tamma 2": {"family": "Baloo Tamma 2", "category": "display", "variants": ["500", "600", "700", "800", "regular"]}, "Baloo Tammudu 2": {"family": "Baloo Tammudu 2", "category": "display", "variants": ["500", "600", "700", "800", "regular"]}, "Baloo Thambi 2": {"family": "Baloo Thambi 2", "category": "display", "variants": ["500", "600", "700", "800", "regular"]}, "Balsamiq Sans": {"family": "Balsamiq Sans", "category": "display", "variants": ["700", "700italic", "italic", "regular"]}, "Balthazar": {"family": "Balthazar", "category": "serif", "variants": ["regular"]}, "Bangers": {"family": "Bangers", "category": "display", "variants": ["regular"]}, "Barlow": {"family": "<PERSON>", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Barlow Condensed": {"family": "<PERSON> Condensed", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Barlow Semi Condensed": {"family": "<PERSON> Semi Condensed", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Barriecito": {"family": "Barr<PERSON>ci<PERSON>", "category": "display", "variants": ["regular"]}, "Barrio": {"family": "Barrio", "category": "display", "variants": ["regular"]}, "Basic": {"family": "Basic", "category": "sans-serif", "variants": ["regular"]}, "Baskervville": {"family": "Baskervville", "category": "serif", "variants": ["italic", "regular"]}, "Baskervville SC": {"family": "Baskervville SC", "category": "serif", "variants": ["regular"]}, "Battambang": {"family": "Battambang", "category": "display", "variants": ["100", "300", "700", "900", "regular"]}, "Baumans": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Bayon": {"family": "Bayon", "category": "sans-serif", "variants": ["regular"]}, "Be Vietnam Pro": {"family": "Be Vietnam Pro", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Beau Rivage": {"family": "Beau Rivage", "category": "handwriting", "variants": ["regular"]}, "Bebas Neue": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Beiruti": {"family": "Beiruti", "category": "sans-serif", "variants": ["200", "300", "500", "600", "700", "800", "900", "regular"]}, "Belanosima": {"family": "Belanosima", "category": "sans-serif", "variants": ["600", "700", "regular"]}, "Belgrano": {"family": "Belgrano", "category": "serif", "variants": ["regular"]}, "Bellefair": {"family": "Bellefair", "category": "serif", "variants": ["regular"]}, "Belleza": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Bellota": {"family": "Bellota", "category": "display", "variants": ["300", "300italic", "700", "700italic", "italic", "regular"]}, "Bellota Text": {"family": "Bellota Text", "category": "display", "variants": ["300", "300italic", "700", "700italic", "italic", "regular"]}, "BenchNine": {"family": "BenchNine", "category": "sans-serif", "variants": ["300", "700", "regular"]}, "Benne": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Bentham": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Berkshire Swash": {"family": "Berkshire Swash", "category": "handwriting", "variants": ["regular"]}, "Besley": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Beth Ellen": {"family": "<PERSON>", "category": "handwriting", "variants": ["regular"]}, "Bevan": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["italic", "regular"]}, "BhuTuka Expanded One": {"family": "BhuTuka Expanded One", "category": "serif", "variants": ["regular"]}, "Big Shoulders Display": {"family": "Big Shoulders Display", "category": "display", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Big Shoulders Inline Display": {"family": "Big Shoulders Inline Display", "category": "display", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Big Shoulders Inline Text": {"family": "Big Shoulders Inline Text", "category": "display", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Big Shoulders Stencil Display": {"family": "Big Shoulders Sten<PERSON>l Display", "category": "display", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Big Shoulders Stencil Text": {"family": "Big Shoulders Stencil Text", "category": "display", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Big Shoulders Text": {"family": "Big Shoulders Text", "category": "display", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Bigelow Rules": {"family": "Bigelow Rules", "category": "display", "variants": ["regular"]}, "Bigshot One": {"family": "Bigshot One", "category": "display", "variants": ["regular"]}, "Bilbo": {"family": "Bilbo", "category": "handwriting", "variants": ["regular"]}, "Bilbo Swash Caps": {"family": "Bilbo Swash Caps", "category": "handwriting", "variants": ["regular"]}, "BioRhyme": {"family": "BioRhyme", "category": "serif", "variants": ["200", "300", "500", "600", "700", "800", "regular"]}, "BioRhyme Expanded": {"family": "BioRhyme Expanded", "category": "serif", "variants": ["200", "300", "700", "800", "regular"]}, "Birthstone": {"family": "Birthstone", "category": "handwriting", "variants": ["regular"]}, "Birthstone Bounce": {"family": "Birthstone Bounce", "category": "handwriting", "variants": ["500", "regular"]}, "Biryani": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["200", "300", "600", "700", "800", "900", "regular"]}, "Bitter": {"family": "Bitter", "category": "serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Black And White Picture": {"family": "Black And White Picture", "category": "display", "variants": ["regular"]}, "Black Han Sans": {"family": "Black Han Sans", "category": "sans-serif", "variants": ["regular"]}, "Black Ops One": {"family": "Black Ops One", "category": "display", "variants": ["regular"]}, "Blaka": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Blaka Hollow": {"family": "Blaka Hollow", "category": "display", "variants": ["regular"]}, "Blaka Ink": {"family": "Blaka Ink", "category": "display", "variants": ["regular"]}, "Blinker": {"family": "Blinker", "category": "sans-serif", "variants": ["100", "200", "300", "600", "700", "800", "900", "regular"]}, "Bodoni Moda": {"family": "<PERSON><PERSON><PERSON>", "category": "serif", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Bodoni Moda SC": {"family": "Bodoni Moda SC", "category": "serif", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Bokor": {"family": "Bokor", "category": "display", "variants": ["regular"]}, "Bona Nova": {"family": "Bona Nova", "category": "serif", "variants": ["700", "italic", "regular"]}, "Bona Nova SC": {"family": "Bona Nova SC", "category": "serif", "variants": ["700", "italic", "regular"]}, "Bonbon": {"family": "Bonbon", "category": "handwriting", "variants": ["regular"]}, "Bonheur Royale": {"family": "Bonheur Royale", "category": "handwriting", "variants": ["regular"]}, "Boogaloo": {"family": "Boogaloo", "category": "display", "variants": ["regular"]}, "Borel": {"family": "<PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Bowlby One": {"family": "Bowlby One", "category": "display", "variants": ["regular"]}, "Bowlby One SC": {"family": "Bowlby One SC", "category": "display", "variants": ["regular"]}, "Braah One": {"family": "Braah One", "category": "sans-serif", "variants": ["regular"]}, "Brawler": {"family": "Brawler", "category": "serif", "variants": ["700", "regular"]}, "Bree Serif": {"family": "<PERSON>", "category": "serif", "variants": ["regular"]}, "Bricolage Grotesque": {"family": "Bricolage Grotesque", "category": "sans-serif", "variants": ["200", "300", "500", "600", "700", "800", "regular"]}, "Bruno Ace": {"family": "<PERSON>", "category": "display", "variants": ["regular"]}, "Bruno Ace SC": {"family": "<PERSON>", "category": "display", "variants": ["regular"]}, "Brygada 1918": {"family": "Brygada 1918", "category": "serif", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Bubblegum Sans": {"family": "Bubblegum Sans", "category": "display", "variants": ["regular"]}, "Bubbler One": {"family": "Bubbler One", "category": "sans-serif", "variants": ["regular"]}, "Buda": {"family": "Buda", "category": "display", "variants": ["300"]}, "Buenard": {"family": "<PERSON><PERSON><PERSON>", "category": "serif", "variants": ["700", "regular"]}, "Bungee": {"family": "Bungee", "category": "display", "variants": ["regular"]}, "Bungee Hairline": {"family": "Bungee Hairline", "category": "display", "variants": ["regular"]}, "Bungee Inline": {"family": "Bungee Inline", "category": "display", "variants": ["regular"]}, "Bungee Outline": {"family": "Bungee Outline", "category": "display", "variants": ["regular"]}, "Bungee Shade": {"family": "Bungee Shade", "category": "display", "variants": ["regular"]}, "Bungee Spice": {"family": "Bungee Spice", "category": "display", "variants": ["regular"]}, "Bungee Tint": {"family": "Bungee Tint", "category": "display", "variants": ["regular"]}, "Butcherman": {"family": "<PERSON>man", "category": "display", "variants": ["regular"]}, "Butterfly Kids": {"family": "Butterfly Kids", "category": "handwriting", "variants": ["regular"]}, "Cabin": {"family": "Cabin", "category": "sans-serif", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Cabin Condensed": {"family": "<PERSON><PERSON><PERSON> Condensed", "category": "sans-serif", "variants": ["500", "600", "700", "regular"]}, "Cabin Sketch": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["700", "regular"]}, "Cactus Classical Serif": {"family": "Cactus Classical Serif", "category": "serif", "variants": ["regular"]}, "Caesar Dressing": {"family": "<PERSON> Dressing", "category": "display", "variants": ["regular"]}, "Cagliostro": {"family": "Cag<PERSON>stro", "category": "sans-serif", "variants": ["regular"]}, "Cairo": {"family": "Cairo", "category": "sans-serif", "variants": ["200", "300", "500", "600", "700", "800", "900", "regular"]}, "Cairo Play": {"family": "Cairo Play", "category": "sans-serif", "variants": ["200", "300", "500", "600", "700", "800", "900", "regular"]}, "Caladea": {"family": "Caladea", "category": "serif", "variants": ["700", "700italic", "italic", "regular"]}, "Calistoga": {"family": "Calistoga", "category": "display", "variants": ["regular"]}, "Calligraffitti": {"family": "Call<PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Cambay": {"family": "Cambay", "category": "sans-serif", "variants": ["700", "700italic", "italic", "regular"]}, "Cambo": {"family": "Cambo", "category": "serif", "variants": ["regular"]}, "Candal": {"family": "Candal", "category": "sans-serif", "variants": ["regular"]}, "Cantarell": {"family": "Cantarell", "category": "sans-serif", "variants": ["700", "700italic", "italic", "regular"]}, "Cantata One": {"family": "Cantata One", "category": "serif", "variants": ["regular"]}, "Cantora One": {"family": "Cantora One", "category": "sans-serif", "variants": ["regular"]}, "Caprasimo": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Capriola": {"family": "Capriola", "category": "sans-serif", "variants": ["regular"]}, "Caramel": {"family": "Caramel", "category": "handwriting", "variants": ["regular"]}, "Carattere": {"family": "<PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Cardo": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["700", "italic", "regular"]}, "Carlito": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["700", "700italic", "italic", "regular"]}, "Carme": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Carrois Gothic": {"family": "Carrois Gothic", "category": "sans-serif", "variants": ["regular"]}, "Carrois Gothic SC": {"family": "Carrois Gothic SC", "category": "sans-serif", "variants": ["regular"]}, "Carter One": {"family": "<PERSON>", "category": "display", "variants": ["regular"]}, "Castoro": {"family": "Castoro", "category": "serif", "variants": ["italic", "regular"]}, "Castoro Titling": {"family": "Castoro Titling", "category": "display", "variants": ["regular"]}, "Catamaran": {"family": "Catamaran", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Caudex": {"family": "Caudex", "category": "serif", "variants": ["700", "700italic", "italic", "regular"]}, "Caveat": {"family": "Caveat", "category": "handwriting", "variants": ["500", "600", "700", "regular"]}, "Caveat Brush": {"family": "Caveat Brush", "category": "handwriting", "variants": ["regular"]}, "Cedarville Cursive": {"family": "Cedarville Cursive", "category": "handwriting", "variants": ["regular"]}, "Ceviche One": {"family": "Ceviche One", "category": "display", "variants": ["regular"]}, "Chakra Petch": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Changa": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["200", "300", "500", "600", "700", "800", "regular"]}, "Changa One": {"family": "Changa One", "category": "display", "variants": ["italic", "regular"]}, "Chango": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Charis SIL": {"family": "Charis SIL", "category": "serif", "variants": ["700", "700italic", "italic", "regular"]}, "Charm": {"family": "Charm", "category": "handwriting", "variants": ["700", "regular"]}, "Charmonman": {"family": "<PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["700", "regular"]}, "Chathura": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "300", "700", "800", "regular"]}, "Chau Philomene One": {"family": "Chau Philomene One", "category": "sans-serif", "variants": ["italic", "regular"]}, "Chela One": {"family": "Chela One", "category": "display", "variants": ["regular"]}, "Chelsea Market": {"family": "Chelsea Market", "category": "display", "variants": ["regular"]}, "Chenla": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Cherish": {"family": "Cherish", "category": "handwriting", "variants": ["regular"]}, "Cherry Bomb One": {"family": "Cherry Bomb One", "category": "display", "variants": ["regular"]}, "Cherry Cream Soda": {"family": "Cherry Cream Soda", "category": "display", "variants": ["regular"]}, "Cherry Swash": {"family": "Cherry Swash", "category": "display", "variants": ["700", "regular"]}, "Chewy": {"family": "Chewy", "category": "display", "variants": ["regular"]}, "Chicle": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Chilanka": {"family": "<PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Chivo": {"family": "Chivo", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Chivo Mono": {"family": "Chivo Mono", "category": "monospace", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Chocolate Classical Sans": {"family": "Chocolate Classical Sans", "category": "sans-serif", "variants": ["regular"]}, "Chokokutai": {"family": "Chokokutai", "category": "display", "variants": ["regular"]}, "Chonburi": {"family": "Chonburi", "category": "display", "variants": ["regular"]}, "Cinzel": {"family": "<PERSON><PERSON><PERSON>", "category": "serif", "variants": ["500", "600", "700", "800", "900", "regular"]}, "Cinzel Decorative": {"family": "Cinzel Decorative", "category": "display", "variants": ["700", "900", "regular"]}, "Clicker Script": {"family": "<PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Climate Crisis": {"family": "Climate Crisis", "category": "display", "variants": ["regular"]}, "Coda": {"family": "Coda", "category": "display", "variants": ["800", "regular"]}, "Codystar": {"family": "Codystar", "category": "display", "variants": ["300", "regular"]}, "Coiny": {"family": "Coiny", "category": "display", "variants": ["regular"]}, "Combo": {"family": "Combo", "category": "display", "variants": ["regular"]}, "Comfortaa": {"family": "Comfortaa", "category": "display", "variants": ["300", "500", "600", "700", "regular"]}, "Comforter": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Comforter Brush": {"family": "Comforter Brush", "category": "handwriting", "variants": ["regular"]}, "Comic Neue": {"family": "Comic Neue", "category": "handwriting", "variants": ["300", "300italic", "700", "700italic", "italic", "regular"]}, "Coming Soon": {"family": "Coming Soon", "category": "handwriting", "variants": ["regular"]}, "Comme": {"family": "Comme", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Commissioner": {"family": "Commissioner", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Concert One": {"family": "Concert One", "category": "display", "variants": ["regular"]}, "Condiment": {"family": "Condiment", "category": "handwriting", "variants": ["regular"]}, "Content": {"family": "Content", "category": "display", "variants": ["700", "regular"]}, "Contrail One": {"family": "Contrail One", "category": "display", "variants": ["regular"]}, "Convergence": {"family": "Convergence", "category": "sans-serif", "variants": ["regular"]}, "Cookie": {"family": "<PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Copse": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Corben": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["700", "regular"]}, "Corinthia": {"family": "Corinthia", "category": "handwriting", "variants": ["700", "regular"]}, "Cormorant": {"family": "Cormorant", "category": "serif", "variants": ["300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Cormorant Garamond": {"family": "<PERSON><PERSON><PERSON><PERSON> G<PERSON>mond", "category": "serif", "variants": ["300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Cormorant Infant": {"family": "Cormorant Infant", "category": "serif", "variants": ["300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Cormorant SC": {"family": "Cormorant SC", "category": "serif", "variants": ["300", "500", "600", "700", "regular"]}, "Cormorant Unicase": {"family": "Cormorant Unicase", "category": "serif", "variants": ["300", "500", "600", "700", "regular"]}, "Cormorant Upright": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "serif", "variants": ["300", "500", "600", "700", "regular"]}, "Courgette": {"family": "<PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Courier Prime": {"family": "Courier Prime", "category": "monospace", "variants": ["700", "700italic", "italic", "regular"]}, "Cousine": {"family": "Cousine", "category": "monospace", "variants": ["700", "700italic", "italic", "regular"]}, "Coustard": {"family": "<PERSON><PERSON><PERSON>", "category": "serif", "variants": ["900", "regular"]}, "Covered By Your Grace": {"family": "Covered By Your Grace", "category": "handwriting", "variants": ["regular"]}, "Crafty Girls": {"family": "Crafty Girls", "category": "handwriting", "variants": ["regular"]}, "Creepster": {"family": "Creepster", "category": "display", "variants": ["regular"]}, "Crete Round": {"family": "Crete Round", "category": "serif", "variants": ["italic", "regular"]}, "Crimson Pro": {"family": "Crimson Pro", "category": "serif", "variants": ["200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Crimson Text": {"family": "Crimson Text", "category": "serif", "variants": ["600", "600italic", "700", "700italic", "italic", "regular"]}, "Croissant One": {"family": "Croissant One", "category": "display", "variants": ["regular"]}, "Crushed": {"family": "Crushed", "category": "display", "variants": ["regular"]}, "Cuprum": {"family": "Cuprum", "category": "sans-serif", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Cute Font": {"family": "<PERSON>e Font", "category": "display", "variants": ["regular"]}, "Cutive": {"family": "Cutive", "category": "serif", "variants": ["regular"]}, "Cutive Mono": {"family": "Cutive Mono", "category": "monospace", "variants": ["regular"]}, "DM Mono": {"family": "DM Mono", "category": "monospace", "variants": ["300", "300italic", "500", "500italic", "italic", "regular"]}, "DM Sans": {"family": "DM Sans", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "DM Serif Display": {"family": "DM Serif <PERSON>lay", "category": "serif", "variants": ["italic", "regular"]}, "DM Serif Text": {"family": "DM Serif Text", "category": "serif", "variants": ["italic", "regular"]}, "Dai Banna SIL": {"family": "Dai Banna SIL", "category": "serif", "variants": ["300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Damion": {"family": "Damion", "category": "handwriting", "variants": ["regular"]}, "Dancing Script": {"family": "<PERSON>ript", "category": "handwriting", "variants": ["500", "600", "700", "regular"]}, "Danfo": {"family": "Dan<PERSON>", "category": "serif", "variants": ["regular"]}, "Dangrek": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Darker Grotesque": {"family": "Darker Grotesque", "category": "sans-serif", "variants": ["300", "500", "600", "700", "800", "900", "regular"]}, "Darumadrop One": {"family": "Darumadrop One", "category": "display", "variants": ["regular"]}, "David Libre": {"family": "<PERSON>", "category": "serif", "variants": ["500", "700", "regular"]}, "Dawning of a New Day": {"family": "Dawning of a New Day", "category": "handwriting", "variants": ["regular"]}, "Days One": {"family": "Days One", "category": "sans-serif", "variants": ["regular"]}, "Dekko": {"family": "<PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Dela Gothic One": {"family": "Dela Gothic One", "category": "display", "variants": ["regular"]}, "Delicious Handrawn": {"family": "Delicious Handrawn", "category": "handwriting", "variants": ["regular"]}, "Delius": {"family": "<PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Delius Swash Caps": {"family": "Delius Swash Caps", "category": "handwriting", "variants": ["regular"]}, "Delius Unicase": {"family": "<PERSON><PERSON>", "category": "handwriting", "variants": ["700", "regular"]}, "Della Respira": {"family": "<PERSON>", "category": "serif", "variants": ["regular"]}, "Denk One": {"family": "Denk One", "category": "sans-serif", "variants": ["regular"]}, "Devonshire": {"family": "Devonshire", "category": "handwriting", "variants": ["regular"]}, "Dhurjati": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Didact Gothic": {"family": "Didact Gothic", "category": "sans-serif", "variants": ["regular"]}, "Diphylleia": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Diplomata": {"family": "Diplomata", "category": "display", "variants": ["regular"]}, "Diplomata SC": {"family": "Diplomata SC", "category": "display", "variants": ["regular"]}, "Do Hyeon": {"family": "<PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Dokdo": {"family": "Dokdo", "category": "display", "variants": ["regular"]}, "Domine": {"family": "Domine", "category": "serif", "variants": ["500", "600", "700", "regular"]}, "Donegal One": {"family": "Donegal One", "category": "serif", "variants": ["regular"]}, "Dongle": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["300", "700", "regular"]}, "Doppio One": {"family": "Doppio One", "category": "sans-serif", "variants": ["regular"]}, "Dorsa": {"family": "Dorsa", "category": "sans-serif", "variants": ["regular"]}, "Dosis": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["200", "300", "500", "600", "700", "800", "regular"]}, "DotGothic16": {"family": "DotGothic16", "category": "sans-serif", "variants": ["regular"]}, "Doto": {"family": "Dot<PERSON>", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Dr Sugiyama": {"family": "<PERSON>", "category": "handwriting", "variants": ["regular"]}, "Duru Sans": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "DynaPuff": {"family": "DynaPuff", "category": "display", "variants": ["500", "600", "700", "regular"]}, "Dynalight": {"family": "Dynalight", "category": "display", "variants": ["regular"]}, "EB Garamond": {"family": "EB Garamond", "category": "serif", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "italic", "regular"]}, "Eagle Lake": {"family": "Eagle Lake", "category": "handwriting", "variants": ["regular"]}, "East Sea Dokdo": {"family": "East Sea Dokdo", "category": "handwriting", "variants": ["regular"]}, "Eater": {"family": "Eater", "category": "display", "variants": ["regular"]}, "Economica": {"family": "Economica", "category": "sans-serif", "variants": ["700", "700italic", "italic", "regular"]}, "Eczar": {"family": "<PERSON><PERSON><PERSON>", "category": "serif", "variants": ["500", "600", "700", "800", "regular"]}, "Edu AU VIC WA NT Arrows": {"family": "Edu AU VIC WA NT Arrows", "category": "handwriting", "variants": ["500", "600", "700", "regular"]}, "Edu AU VIC WA NT Dots": {"family": "Edu AU VIC WA NT Dots", "category": "handwriting", "variants": ["500", "600", "700", "regular"]}, "Edu AU VIC WA NT Guides": {"family": "Edu AU VIC WA NT Guides", "category": "handwriting", "variants": ["500", "600", "700", "regular"]}, "Edu AU VIC WA NT Hand": {"family": "Edu AU VIC WA NT Hand", "category": "handwriting", "variants": ["500", "600", "700", "regular"]}, "Edu AU VIC WA NT Pre": {"family": "Edu AU VIC WA NT Pre", "category": "handwriting", "variants": ["500", "600", "700", "regular"]}, "Edu NSW ACT Foundation": {"family": "Edu NSW ACT Foundation", "category": "handwriting", "variants": ["500", "600", "700", "regular"]}, "Edu QLD Beginner": {"family": "Edu QLD Beginner", "category": "handwriting", "variants": ["500", "600", "700", "regular"]}, "Edu SA Beginner": {"family": "Edu SA Beginner", "category": "handwriting", "variants": ["500", "600", "700", "regular"]}, "Edu TAS Beginner": {"family": "Edu TAS Beginner", "category": "handwriting", "variants": ["500", "600", "700", "regular"]}, "Edu VIC WA NT Beginner": {"family": "Edu VIC WA NT Beginner", "category": "handwriting", "variants": ["500", "600", "700", "regular"]}, "El Messiri": {"family": "El Messiri", "category": "sans-serif", "variants": ["500", "600", "700", "regular"]}, "Electrolize": {"family": "Electrolize", "category": "sans-serif", "variants": ["regular"]}, "Elsie": {"family": "<PERSON>", "category": "display", "variants": ["900", "regular"]}, "Elsie Swash Caps": {"family": "<PERSON>s", "category": "display", "variants": ["900", "regular"]}, "Emblema One": {"family": "Emblema One", "category": "display", "variants": ["regular"]}, "Emilys Candy": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Encode Sans": {"family": "Encode Sans", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Encode Sans Condensed": {"family": "Encode Sans Condensed", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Encode Sans Expanded": {"family": "Encode Sans Expanded", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Encode Sans SC": {"family": "Encode Sans SC", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Encode Sans Semi Condensed": {"family": "Encode Sans Semi Condensed", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Encode Sans Semi Expanded": {"family": "Encode Sans Semi Expanded", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Engagement": {"family": "Engagement", "category": "handwriting", "variants": ["regular"]}, "Englebert": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Enriqueta": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["500", "600", "700", "regular"]}, "Ephesis": {"family": "Ephesis", "category": "handwriting", "variants": ["regular"]}, "Epilogue": {"family": "Epilogue", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Erica One": {"family": "<PERSON>", "category": "display", "variants": ["regular"]}, "Esteban": {"family": "Esteban", "category": "serif", "variants": ["regular"]}, "Estonia": {"family": "Estonia", "category": "handwriting", "variants": ["regular"]}, "Euphoria Script": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Ewert": {"family": "Ewert", "category": "display", "variants": ["regular"]}, "Exo": {"family": "Exo", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Exo 2": {"family": "Exo 2", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Expletus Sans": {"family": "Expletus <PERSON>", "category": "display", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Explora": {"family": "Explora", "category": "handwriting", "variants": ["regular"]}, "Faculty Glyphic": {"family": "Faculty Glyphic", "category": "sans-serif", "variants": ["regular"]}, "Fahkwang": {"family": "Fahkwang", "category": "sans-serif", "variants": ["200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Familjen Grotesk": {"family": "Familjen Grotesk", "category": "sans-serif", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Fanwood Text": {"family": "Fanwood Text", "category": "serif", "variants": ["italic", "regular"]}, "Farro": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["300", "500", "700", "regular"]}, "Farsan": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Fascinate": {"family": "Fascinate", "category": "display", "variants": ["regular"]}, "Fascinate Inline": {"family": "Fascinate Inline", "category": "display", "variants": ["regular"]}, "Faster One": {"family": "Faster One", "category": "display", "variants": ["regular"]}, "Fasthand": {"family": "Fasthand", "category": "display", "variants": ["regular"]}, "Fauna One": {"family": "Fauna One", "category": "serif", "variants": ["regular"]}, "Faustina": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "italic", "regular"]}, "Federant": {"family": "Federant", "category": "display", "variants": ["regular"]}, "Federo": {"family": "Federo", "category": "sans-serif", "variants": ["regular"]}, "Felipa": {"family": "<PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Fenix": {"family": "Fenix", "category": "serif", "variants": ["regular"]}, "Festive": {"family": "Festive", "category": "handwriting", "variants": ["regular"]}, "Figtree": {"family": "Figtree", "category": "sans-serif", "variants": ["300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Finger Paint": {"family": "<PERSON><PERSON>t", "category": "display", "variants": ["regular"]}, "Finlandica": {"family": "Finlandica", "category": "sans-serif", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Fira Code": {"family": "Fira Code", "category": "monospace", "variants": ["300", "500", "600", "700", "regular"]}, "Fira Mono": {"family": "Fira Mono", "category": "monospace", "variants": ["500", "700", "regular"]}, "Fira Sans": {"family": "Fira Sans", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Fira Sans Condensed": {"family": "Fira Sans Condensed", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Fira Sans Extra Condensed": {"family": "Fira Sans Extra Condensed", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Fjalla One": {"family": "Fjalla One", "category": "sans-serif", "variants": ["regular"]}, "Fjord One": {"family": "Fjord One", "category": "serif", "variants": ["regular"]}, "Flamenco": {"family": "Flamenco", "category": "display", "variants": ["300", "regular"]}, "Flavors": {"family": "Flavors", "category": "display", "variants": ["regular"]}, "Fleur De Leah": {"family": "<PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Flow Block": {"family": "Flow Block", "category": "display", "variants": ["regular"]}, "Flow Circular": {"family": "Flow Circular", "category": "display", "variants": ["regular"]}, "Flow Rounded": {"family": "Flow Rounded", "category": "display", "variants": ["regular"]}, "Foldit": {"family": "Foldit", "category": "display", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Fondamento": {"family": "Fondamento", "category": "handwriting", "variants": ["italic", "regular"]}, "Fontdiner Swanky": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Forum": {"family": "Forum", "category": "display", "variants": ["regular"]}, "Fragment Mono": {"family": "Fragment Mono", "category": "monospace", "variants": ["italic", "regular"]}, "Francois One": {"family": "<PERSON>is <PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Frank Ruhl Libre": {"family": "<PERSON>", "category": "serif", "variants": ["300", "500", "600", "700", "800", "900", "regular"]}, "Fraunces": {"family": "<PERSON><PERSON><PERSON>", "category": "serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Freckle Face": {"family": "<PERSON><PERSON><PERSON> Face", "category": "display", "variants": ["regular"]}, "Fredericka the Great": {"family": "<PERSON><PERSON> the <PERSON>", "category": "display", "variants": ["regular"]}, "Fredoka": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["300", "500", "600", "700", "regular"]}, "Freehand": {"family": "Freehand", "category": "display", "variants": ["regular"]}, "Freeman": {"family": "<PERSON>", "category": "display", "variants": ["regular"]}, "Fresca": {"family": "Fresca", "category": "sans-serif", "variants": ["regular"]}, "Frijole": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Fruktur": {"family": "Fruktur", "category": "display", "variants": ["italic", "regular"]}, "Fugaz One": {"family": "Fugaz One", "category": "display", "variants": ["regular"]}, "Fuggles": {"family": "Fuggles", "category": "handwriting", "variants": ["regular"]}, "Funnel Display": {"family": "Funnel Display", "category": "display", "variants": ["300", "500", "600", "700", "800", "regular"]}, "Funnel Sans": {"family": "Funnel Sans", "category": "sans-serif", "variants": ["300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "italic", "regular"]}, "Fustat": {"family": "Fustat", "category": "sans-serif", "variants": ["200", "300", "500", "600", "700", "800", "regular"]}, "Fuzzy Bubbles": {"family": "Fuzzy Bubbles", "category": "handwriting", "variants": ["700", "regular"]}, "GFS Didot": {"family": "GFS <PERSON>ot", "category": "serif", "variants": ["regular"]}, "GFS Neohellenic": {"family": "GFS Neohellenic", "category": "sans-serif", "variants": ["700", "700italic", "italic", "regular"]}, "Ga Maamli": {"family": "Ga <PERSON>", "category": "display", "variants": ["regular"]}, "Gabarito": {"family": "G<PERSON><PERSON>", "category": "display", "variants": ["500", "600", "700", "800", "900", "regular"]}, "Gabriela": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Gaegu": {"family": "<PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["300", "700", "regular"]}, "Gafata": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Gajraj One": {"family": "Gajraj One", "category": "display", "variants": ["regular"]}, "Galada": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Galdeano": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Galindo": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Gamja Flower": {"family": "<PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Gantari": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Gasoek One": {"family": "Gasoek One", "category": "sans-serif", "variants": ["regular"]}, "Gayathri": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "700", "regular"]}, "Geist": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Geist Mono": {"family": "<PERSON><PERSON><PERSON>", "category": "monospace", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Gelasio": {"family": "Gelasio", "category": "serif", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Gemunu Libre": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["200", "300", "500", "600", "700", "800", "regular"]}, "Genos": {"family": "Genos", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Gentium Book Plus": {"family": "Gentium Book Plus", "category": "serif", "variants": ["700", "700italic", "italic", "regular"]}, "Gentium Plus": {"family": "Gentium Plus", "category": "serif", "variants": ["700", "700italic", "italic", "regular"]}, "Geo": {"family": "Geo", "category": "sans-serif", "variants": ["italic", "regular"]}, "Geologica": {"family": "Geologica", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Georama": {"family": "Georama", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Geostar": {"family": "Geostar", "category": "display", "variants": ["regular"]}, "Geostar Fill": {"family": "Geostar Fill", "category": "display", "variants": ["regular"]}, "Germania One": {"family": "Germania One", "category": "display", "variants": ["regular"]}, "Gideon Roman": {"family": "<PERSON>", "category": "display", "variants": ["regular"]}, "Gidugu": {"family": "Gidugu", "category": "sans-serif", "variants": ["regular"]}, "Gilda Display": {"family": "<PERSON><PERSON> Display", "category": "serif", "variants": ["regular"]}, "Girassol": {"family": "Girassol", "category": "display", "variants": ["regular"]}, "Give You Glory": {"family": "Give You Glory", "category": "handwriting", "variants": ["regular"]}, "Glass Antiqua": {"family": "Glass Antiqua", "category": "display", "variants": ["regular"]}, "Glegoo": {"family": "<PERSON><PERSON><PERSON>", "category": "serif", "variants": ["700", "regular"]}, "Gloock": {"family": "Gloock", "category": "serif", "variants": ["regular"]}, "Gloria Hallelujah": {"family": "Gloria Hallelujah", "category": "handwriting", "variants": ["regular"]}, "Glory": {"family": "Glory", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "italic", "regular"]}, "Gluten": {"family": "Gluten", "category": "display", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Goblin One": {"family": "Goblin One", "category": "display", "variants": ["regular"]}, "Gochi Hand": {"family": "Go<PERSON> Hand", "category": "handwriting", "variants": ["regular"]}, "Goldman": {"family": "<PERSON>", "category": "display", "variants": ["700", "regular"]}, "Golos Text": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["500", "600", "700", "800", "900", "regular"]}, "Gorditas": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["700", "regular"]}, "Gothic A1": {"family": "Gothic A1", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Gotu": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Goudy Bookletter 1911": {"family": "Goudy Bookletter 1911", "category": "serif", "variants": ["regular"]}, "Gowun Batang": {"family": "<PERSON><PERSON><PERSON>", "category": "serif", "variants": ["700", "regular"]}, "Gowun Dodum": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Graduate": {"family": "Graduate", "category": "serif", "variants": ["regular"]}, "Grand Hotel": {"family": "Grand Hotel", "category": "handwriting", "variants": ["regular"]}, "Grandiflora One": {"family": "Grandiflora One", "category": "serif", "variants": ["regular"]}, "Grandstander": {"family": "Grandstander", "category": "display", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Grape Nuts": {"family": "Grape Nuts", "category": "handwriting", "variants": ["regular"]}, "Gravitas One": {"family": "Gravitas One", "category": "display", "variants": ["regular"]}, "Great Vibes": {"family": "Great Vibes", "category": "handwriting", "variants": ["regular"]}, "Grechen Fuemen": {"family": "G<PERSON>chen Fuemen", "category": "handwriting", "variants": ["regular"]}, "Grenze": {"family": "<PERSON><PERSON><PERSON>", "category": "serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Grenze Gotisch": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Grey Qo": {"family": "<PERSON>", "category": "handwriting", "variants": ["regular"]}, "Griffy": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Gruppo": {"family": "Gruppo", "category": "sans-serif", "variants": ["regular"]}, "Gudea": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["700", "italic", "regular"]}, "Gugi": {"family": "Gug<PERSON>", "category": "display", "variants": ["regular"]}, "Gulzar": {"family": "<PERSON><PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Gupter": {"family": "<PERSON><PERSON><PERSON>", "category": "serif", "variants": ["500", "700", "regular"]}, "Gurajada": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Gwendolyn": {"family": "<PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["700", "regular"]}, "Habibi": {"family": "Ha<PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Hachi Maru Pop": {"family": "Hachi Maru Pop", "category": "handwriting", "variants": ["regular"]}, "Hahmlet": {"family": "<PERSON><PERSON><PERSON>", "category": "serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Halant": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["300", "500", "600", "700", "regular"]}, "Hammersmith One": {"family": "Hammersmith One", "category": "sans-serif", "variants": ["regular"]}, "Hanalei": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Hanalei Fill": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Handjet": {"family": "Handjet", "category": "display", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Handlee": {"family": "Handlee", "category": "handwriting", "variants": ["regular"]}, "Hanken Grotesk": {"family": "Hanken Grotesk", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Hanuman": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["100", "300", "700", "900", "regular"]}, "Happy Monkey": {"family": "Happy Monkey", "category": "display", "variants": ["regular"]}, "Harmattan": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["500", "600", "700", "regular"]}, "Headland One": {"family": "Headland One", "category": "serif", "variants": ["regular"]}, "Hedvig Letters Sans": {"family": "Hedvig <PERSON> Sans", "category": "sans-serif", "variants": ["regular"]}, "Hedvig Letters Serif": {"family": "Hedvig <PERSON> Serif", "category": "serif", "variants": ["regular"]}, "Heebo": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Henny Penny": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Hepta Slab": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Herr Von Muellerhoff": {"family": "<PERSON>", "category": "handwriting", "variants": ["regular"]}, "Hi Melody": {"family": "<PERSON>", "category": "handwriting", "variants": ["regular"]}, "Hina Mincho": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Hind": {"family": "Hind", "category": "sans-serif", "variants": ["300", "500", "600", "700", "regular"]}, "Hind Guntur": {"family": "<PERSON>nd <PERSON>", "category": "sans-serif", "variants": ["300", "500", "600", "700", "regular"]}, "Hind Madurai": {"family": "Hind Madurai", "category": "sans-serif", "variants": ["300", "500", "600", "700", "regular"]}, "Hind Mysuru": {"family": "Hind <PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["300", "500", "600", "700", "regular"]}, "Hind Siliguri": {"family": "Hind Siliguri", "category": "sans-serif", "variants": ["300", "500", "600", "700", "regular"]}, "Hind Vadodara": {"family": "Hind Vadodara", "category": "sans-serif", "variants": ["300", "500", "600", "700", "regular"]}, "Holtwood One SC": {"family": "Holtwood One SC", "category": "serif", "variants": ["regular"]}, "Homemade Apple": {"family": "Homemade Apple", "category": "handwriting", "variants": ["regular"]}, "Homenaje": {"family": "Homenaje", "category": "sans-serif", "variants": ["regular"]}, "Honk": {"family": "Honk", "category": "display", "variants": ["regular"]}, "Host Grotesk": {"family": "Host Grotesk", "category": "sans-serif", "variants": ["300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "italic", "regular"]}, "Hubballi": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Hubot Sans": {"family": "<PERSON>bot Sans", "category": "sans-serif", "variants": ["200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Hurricane": {"family": "Hurricane", "category": "handwriting", "variants": ["regular"]}, "IBM Plex Mono": {"family": "IBM Plex Mono", "category": "monospace", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "IBM Plex Sans": {"family": "IBM Plex Sans", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "IBM Plex Sans Arabic": {"family": "IBM Plex Sans Arabic", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "regular"]}, "IBM Plex Sans Condensed": {"family": "IBM Plex Sans Condensed", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "IBM Plex Sans Devanagari": {"family": "IBM Plex Sans Devanagari", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "regular"]}, "IBM Plex Sans Hebrew": {"family": "IBM Plex Sans Hebrew", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "regular"]}, "IBM Plex Sans JP": {"family": "IBM Plex Sans JP", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "regular"]}, "IBM Plex Sans KR": {"family": "IBM Plex Sans KR", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "regular"]}, "IBM Plex Sans Thai": {"family": "IBM Plex Sans Thai", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "regular"]}, "IBM Plex Sans Thai Looped": {"family": "IBM Plex Sans Thai Looped", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "regular"]}, "IBM Plex Serif": {"family": "IBM Plex Serif", "category": "serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "IM Fell DW Pica": {"family": "IM Fell DW Pica", "category": "serif", "variants": ["italic", "regular"]}, "IM Fell DW Pica SC": {"family": "IM Fell DW Pica SC", "category": "serif", "variants": ["regular"]}, "IM Fell Double Pica": {"family": "IM Fell Double Pica", "category": "serif", "variants": ["italic", "regular"]}, "IM Fell Double Pica SC": {"family": "IM Fell Double Pica SC", "category": "serif", "variants": ["regular"]}, "IM Fell English": {"family": "IM Fell English", "category": "serif", "variants": ["italic", "regular"]}, "IM Fell English SC": {"family": "IM Fell English SC", "category": "serif", "variants": ["regular"]}, "IM Fell French Canon": {"family": "IM Fell French Canon", "category": "serif", "variants": ["italic", "regular"]}, "IM Fell French Canon SC": {"family": "IM Fell French Canon SC", "category": "serif", "variants": ["regular"]}, "IM Fell Great Primer": {"family": "IM Fell Great Primer", "category": "serif", "variants": ["italic", "regular"]}, "IM Fell Great Primer SC": {"family": "IM Fell Great Primer SC", "category": "serif", "variants": ["regular"]}, "Ibarra Real Nova": {"family": "Ibarra Real Nova", "category": "serif", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Iceberg": {"family": "Iceberg", "category": "display", "variants": ["regular"]}, "Iceland": {"family": "Iceland", "category": "display", "variants": ["regular"]}, "Imbue": {"family": "Imbue", "category": "serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Imperial Script": {"family": "Imperial Script", "category": "handwriting", "variants": ["regular"]}, "Imprima": {"family": "Imprima", "category": "sans-serif", "variants": ["regular"]}, "Inclusive Sans": {"family": "Inclusive Sans", "category": "sans-serif", "variants": ["italic", "regular"]}, "Inconsolata": {"family": "Inconsolata", "category": "monospace", "variants": ["200", "300", "500", "600", "700", "800", "900", "regular"]}, "Inder": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Indie Flower": {"family": "Indie Flower", "category": "handwriting", "variants": ["regular"]}, "Ingrid Darling": {"family": "<PERSON>", "category": "handwriting", "variants": ["regular"]}, "Inika": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["700", "regular"]}, "Inknut Antiqua": {"family": "Inknut Antiqua", "category": "serif", "variants": ["300", "500", "600", "700", "800", "900", "regular"]}, "Inria Sans": {"family": "Inria Sans", "category": "sans-serif", "variants": ["300", "300italic", "700", "700italic", "italic", "regular"]}, "Inria Serif": {"family": "Inria Serif", "category": "serif", "variants": ["300", "300italic", "700", "700italic", "italic", "regular"]}, "Inspiration": {"family": "Inspiration", "category": "handwriting", "variants": ["regular"]}, "Instrument Sans": {"family": "Instrument Sans", "category": "sans-serif", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Instrument Serif": {"family": "Instrument Serif", "category": "serif", "variants": ["italic", "regular"]}, "Inter": {"family": "Inter", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Inter Tight": {"family": "Inter Tight", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Irish Grover": {"family": "Irish Grover", "category": "display", "variants": ["regular"]}, "Island Moments": {"family": "Island Moments", "category": "handwriting", "variants": ["regular"]}, "Istok Web": {"family": "Istok Web", "category": "sans-serif", "variants": ["700", "700italic", "italic", "regular"]}, "Italiana": {"family": "Italiana", "category": "serif", "variants": ["regular"]}, "Italianno": {"family": "Italianno", "category": "handwriting", "variants": ["regular"]}, "Itim": {"family": "<PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Jacquard 12": {"family": "Jacquard 12", "category": "display", "variants": ["regular"]}, "Jacquard 12 Charted": {"family": "Jacquard 12 Charted", "category": "display", "variants": ["regular"]}, "Jacquard 24": {"family": "Jacquard 24", "category": "display", "variants": ["regular"]}, "Jacquard 24 Charted": {"family": "Jacquard 24 Charted", "category": "display", "variants": ["regular"]}, "Jacquarda Bastarda 9": {"family": "Jacquarda Bastarda 9", "category": "display", "variants": ["regular"]}, "Jacquarda Bastarda 9 Charted": {"family": "Jacquarda Bastarda 9 Charted", "category": "display", "variants": ["regular"]}, "Jacques Francois": {"family": "<PERSON>", "category": "serif", "variants": ["regular"]}, "Jacques Francois Shadow": {"family": "<PERSON>", "category": "display", "variants": ["regular"]}, "Jaini": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Jaini Purva": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Jaldi": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["700", "regular"]}, "Jaro": {"family": "J<PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Jersey 10": {"family": "Jersey 10", "category": "display", "variants": ["regular"]}, "Jersey 10 Charted": {"family": "Jersey 10 Charted", "category": "display", "variants": ["regular"]}, "Jersey 15": {"family": "Jersey 15", "category": "display", "variants": ["regular"]}, "Jersey 15 Charted": {"family": "Jersey 15 Charted", "category": "display", "variants": ["regular"]}, "Jersey 20": {"family": "Jersey 20", "category": "display", "variants": ["regular"]}, "Jersey 20 Charted": {"family": "Jersey 20 Charted", "category": "display", "variants": ["regular"]}, "Jersey 25": {"family": "Jersey 25", "category": "display", "variants": ["regular"]}, "Jersey 25 Charted": {"family": "Jersey 25 Charted", "category": "display", "variants": ["regular"]}, "JetBrains Mono": {"family": "JetBrains Mono", "category": "monospace", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "italic", "regular"]}, "Jim Nightshade": {"family": "<PERSON>", "category": "handwriting", "variants": ["regular"]}, "Joan": {"family": "<PERSON>", "category": "serif", "variants": ["regular"]}, "Jockey One": {"family": "Jockey One", "category": "sans-serif", "variants": ["regular"]}, "Jolly Lodger": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Jomhuria": {"family": "Jomhur<PERSON>", "category": "display", "variants": ["regular"]}, "Jomolhari": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Josefin Sans": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Josefin Slab": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Jost": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Joti One": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Jua": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Judson": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["700", "italic", "regular"]}, "Julee": {"family": "<PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Julius Sans One": {"family": "Julius Sans One", "category": "sans-serif", "variants": ["regular"]}, "Junge": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Jura": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["300", "500", "600", "700", "regular"]}, "Just Another Hand": {"family": "Just Another Hand", "category": "handwriting", "variants": ["regular"]}, "Just Me Again Down Here": {"family": "Just Me Again Down Here", "category": "handwriting", "variants": ["regular"]}, "K2D": {"family": "K2D", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "italic", "regular"]}, "Kablammo": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Kadwa": {"family": "Kadwa", "category": "serif", "variants": ["700", "regular"]}, "Kaisei Decol": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["500", "700", "regular"]}, "Kaisei HarunoUmi": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["500", "700", "regular"]}, "Kaisei Opti": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["500", "700", "regular"]}, "Kaisei Tokumin": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["500", "700", "800", "regular"]}, "Kalam": {"family": "Ka<PERSON>", "category": "handwriting", "variants": ["300", "700", "regular"]}, "Kalnia": {"family": "Kalnia", "category": "serif", "variants": ["100", "200", "300", "500", "600", "700", "regular"]}, "Kalnia Glaze": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["100", "200", "300", "500", "600", "700", "regular"]}, "Kameron": {"family": "<PERSON><PERSON><PERSON>", "category": "serif", "variants": ["500", "600", "700", "regular"]}, "Kanit": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Kantumruy Pro": {"family": "Kantumruy Pro", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Karantina": {"family": "Karantina", "category": "display", "variants": ["300", "700", "regular"]}, "Karla": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "italic", "regular"]}, "Karla Tamil Inclined": {"family": "Karla Tamil Inclined", "category": "sans-serif", "variants": ["700", "regular"]}, "Karla Tamil Upright": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["700", "regular"]}, "Karma": {"family": "<PERSON>rma", "category": "serif", "variants": ["300", "500", "600", "700", "regular"]}, "Katibeh": {"family": "Kat<PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Kaushan Script": {"family": "<PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Kavivanar": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Kavoon": {"family": "Kavoon", "category": "display", "variants": ["regular"]}, "Kay Pho Du": {"family": "<PERSON>", "category": "serif", "variants": ["500", "600", "700", "regular"]}, "Kdam Thmor Pro": {"family": "Kdam Thmor Pro", "category": "sans-serif", "variants": ["regular"]}, "Keania One": {"family": "Keania One", "category": "display", "variants": ["regular"]}, "Kelly Slab": {"family": "<PERSON>", "category": "display", "variants": ["regular"]}, "Kenia": {"family": "Ken<PERSON>", "category": "display", "variants": ["regular"]}, "Khand": {"family": "Khand", "category": "sans-serif", "variants": ["300", "500", "600", "700", "regular"]}, "Khmer": {"family": "Khmer", "category": "sans-serif", "variants": ["regular"]}, "Khula": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["300", "600", "700", "800", "regular"]}, "Kings": {"family": "Kings", "category": "handwriting", "variants": ["regular"]}, "Kirang Haerang": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Kite One": {"family": "Kite One", "category": "sans-serif", "variants": ["regular"]}, "Kiwi Maru": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["300", "500", "regular"]}, "Klee One": {"family": "Klee One", "category": "handwriting", "variants": ["600", "regular"]}, "Knewave": {"family": "Knewave", "category": "display", "variants": ["regular"]}, "KoHo": {"family": "KoHo", "category": "sans-serif", "variants": ["200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Kodchasan": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Kode Mono": {"family": "<PERSON><PERSON>", "category": "monospace", "variants": ["500", "600", "700", "regular"]}, "Koh Santepheap": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["100", "300", "700", "900", "regular"]}, "Kolker Brush": {"family": "<PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Konkhmer Sleokchher": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Kosugi": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Kosugi Maru": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Kotta One": {"family": "Kotta One", "category": "serif", "variants": ["regular"]}, "Koulen": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Kranky": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Kreon": {"family": "K<PERSON><PERSON>", "category": "serif", "variants": ["300", "500", "600", "700", "regular"]}, "Kristi": {"family": "<PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Krona One": {"family": "Krona One", "category": "sans-serif", "variants": ["regular"]}, "Krub": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Kufam": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Kulim Park": {"family": "Kulim Park", "category": "sans-serif", "variants": ["200", "200italic", "300", "300italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Kumar One": {"family": "<PERSON>", "category": "display", "variants": ["regular"]}, "Kumar One Outline": {"family": "Kumar One Outline", "category": "display", "variants": ["regular"]}, "Kumbh Sans": {"family": "Kumbh Sans", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Kurale": {"family": "<PERSON><PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "LXGW WenKai Mono TC": {"family": "LXGW WenKai Mono TC", "category": "monospace", "variants": ["300", "700", "regular"]}, "LXGW WenKai TC": {"family": "LXGW WenKai TC", "category": "handwriting", "variants": ["300", "700", "regular"]}, "La Belle Aurore": {"family": "La Belle Aurore", "category": "handwriting", "variants": ["regular"]}, "Labrada": {"family": "Labrada", "category": "serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Lacquer": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Laila": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["300", "500", "600", "700", "regular"]}, "Lakki Reddy": {"family": "<PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Lalezar": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Lancelot": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Langar": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Lateef": {"family": "<PERSON>ef", "category": "serif", "variants": ["200", "300", "500", "600", "700", "800", "regular"]}, "Lato": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "100italic", "300", "300italic", "700", "700italic", "900", "900italic", "italic", "regular"]}, "Lavishly Yours": {"family": "Lavishly Yours", "category": "handwriting", "variants": ["regular"]}, "League Gothic": {"family": "League Gothic", "category": "sans-serif", "variants": ["regular"]}, "League Script": {"family": "League Script", "category": "handwriting", "variants": ["regular"]}, "League Spartan": {"family": "League Spartan", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Leckerli One": {"family": "Leckerli One", "category": "handwriting", "variants": ["regular"]}, "Ledger": {"family": "Ledger", "category": "serif", "variants": ["regular"]}, "Lekton": {"family": "Lekton", "category": "monospace", "variants": ["700", "italic", "regular"]}, "Lemon": {"family": "Lemon", "category": "display", "variants": ["regular"]}, "Lemonada": {"family": "Lemonada", "category": "display", "variants": ["300", "500", "600", "700", "regular"]}, "Lexend": {"family": "Lexend", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Lexend Deca": {"family": "Lexend Deca", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Lexend Exa": {"family": "Lexend Exa", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Lexend Giga": {"family": "Lexend Giga", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Lexend Mega": {"family": "Lexend Mega", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Lexend Peta": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Lexend Tera": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Lexend Zetta": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Libre Barcode 128": {"family": "Libre Barcode 128", "category": "display", "variants": ["regular"]}, "Libre Barcode 128 Text": {"family": "Libre Barcode 128 Text", "category": "display", "variants": ["regular"]}, "Libre Barcode 39": {"family": "Libre Barcode 39", "category": "display", "variants": ["regular"]}, "Libre Barcode 39 Extended": {"family": "Libre Barcode 39 Extended", "category": "display", "variants": ["regular"]}, "Libre Barcode 39 Extended Text": {"family": "Libre Barcode 39 Extended Text", "category": "display", "variants": ["regular"]}, "Libre Barcode 39 Text": {"family": "Libre Barcode 39 Text", "category": "display", "variants": ["regular"]}, "Libre Barcode EAN13 Text": {"family": "Libre Barcode EAN13 Text", "category": "display", "variants": ["regular"]}, "Libre Baskerville": {"family": "Libre Baskerville", "category": "serif", "variants": ["700", "italic", "regular"]}, "Libre Bodoni": {"family": "Libre Bo<PERSON>i", "category": "serif", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Libre Caslon Display": {"family": "Libre Caslon Display", "category": "serif", "variants": ["regular"]}, "Libre Caslon Text": {"family": "Libre Caslon Text", "category": "serif", "variants": ["700", "italic", "regular"]}, "Libre Franklin": {"family": "Libre Franklin", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Licorice": {"family": "Licorice", "category": "handwriting", "variants": ["regular"]}, "Life Savers": {"family": "Life Savers", "category": "display", "variants": ["700", "800", "regular"]}, "Lilita One": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Lily Script One": {"family": "<PERSON>", "category": "display", "variants": ["regular"]}, "Limelight": {"family": "Limelight", "category": "display", "variants": ["regular"]}, "Linden Hill": {"family": "Linden Hill", "category": "serif", "variants": ["italic", "regular"]}, "Linefont": {"family": "Linefont", "category": "display", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Lisu Bosa": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Literata": {"family": "Literata", "category": "serif", "variants": ["200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Liu Jian Mao Cao": {"family": "<PERSON>", "category": "handwriting", "variants": ["regular"]}, "Livvic": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "900", "900italic", "italic", "regular"]}, "Lobster": {"family": "Lobster", "category": "display", "variants": ["regular"]}, "Lobster Two": {"family": "Lobster Two", "category": "display", "variants": ["700", "700italic", "italic", "regular"]}, "Londrina Outline": {"family": "Londrina Outline", "category": "display", "variants": ["regular"]}, "Londrina Shadow": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Londrina Sketch": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Londrina Solid": {"family": "<PERSON>nd<PERSON>", "category": "display", "variants": ["100", "300", "900", "regular"]}, "Long Cang": {"family": "<PERSON>", "category": "handwriting", "variants": ["regular"]}, "Lora": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Love Light": {"family": "Love Light", "category": "handwriting", "variants": ["regular"]}, "Love Ya Like A Sister": {"family": "Love Ya Like A Sister", "category": "display", "variants": ["regular"]}, "Loved by the King": {"family": "Loved by the King", "category": "handwriting", "variants": ["regular"]}, "Lovers Quarrel": {"family": "Lovers Quarrel", "category": "handwriting", "variants": ["regular"]}, "Luckiest Guy": {"family": "Luckiest Guy", "category": "display", "variants": ["regular"]}, "Lugrasimo": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Lumanosimo": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Lunasima": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["700", "regular"]}, "Lusitana": {"family": "Lusitana", "category": "serif", "variants": ["700", "regular"]}, "Lustria": {"family": "Lustria", "category": "serif", "variants": ["regular"]}, "Luxurious Roman": {"family": "Luxurious Roman", "category": "display", "variants": ["regular"]}, "Luxurious Script": {"family": "Luxuri<PERSON>", "category": "handwriting", "variants": ["regular"]}, "M PLUS 1": {"family": "M PLUS 1", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "M PLUS 1 Code": {"family": "M PLUS 1 Code", "category": "monospace", "variants": ["100", "200", "300", "500", "600", "700", "regular"]}, "M PLUS 1p": {"family": "M PLUS 1p", "category": "sans-serif", "variants": ["100", "300", "500", "700", "800", "900", "regular"]}, "M PLUS 2": {"family": "M PLUS 2", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "M PLUS Code Latin": {"family": "M PLUS Code Latin", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "regular"]}, "M PLUS Rounded 1c": {"family": "M PLUS Rounded 1c", "category": "sans-serif", "variants": ["100", "300", "500", "700", "800", "900", "regular"]}, "Ma Shan Zheng": {"family": "<PERSON>", "category": "handwriting", "variants": ["regular"]}, "Macondo": {"family": "Macondo", "category": "display", "variants": ["regular"]}, "Macondo Swash Caps": {"family": "Macondo Swash Caps", "category": "display", "variants": ["regular"]}, "Mada": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["200", "300", "500", "600", "700", "800", "900", "regular"]}, "Madimi One": {"family": "<PERSON><PERSON>i <PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Magra": {"family": "Magra", "category": "sans-serif", "variants": ["700", "regular"]}, "Maiden Orange": {"family": "<PERSON>", "category": "serif", "variants": ["regular"]}, "Maitree": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["200", "300", "500", "600", "700", "regular"]}, "Major Mono Display": {"family": "Major <PERSON><PERSON>", "category": "monospace", "variants": ["regular"]}, "Mako": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Mali": {"family": "Mali", "category": "handwriting", "variants": ["200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Mallanna": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Maname": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Mandali": {"family": "Mandali", "category": "sans-serif", "variants": ["regular"]}, "Manjari": {"family": "Man<PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "700", "regular"]}, "Manrope": {"family": "Manrope", "category": "sans-serif", "variants": ["200", "300", "500", "600", "700", "800", "regular"]}, "Mansalva": {"family": "<PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Manuale": {"family": "Manuale", "category": "serif", "variants": ["300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "italic", "regular"]}, "Marcellus": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Marcellus SC": {"family": "Marcellus SC", "category": "serif", "variants": ["regular"]}, "Marck Script": {"family": "<PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Margarine": {"family": "Margarine", "category": "display", "variants": ["regular"]}, "Marhey": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["300", "500", "600", "700", "regular"]}, "Markazi Text": {"family": "Markazi Text", "category": "serif", "variants": ["500", "600", "700", "regular"]}, "Marko One": {"family": "Marko One", "category": "serif", "variants": ["regular"]}, "Marmelad": {"family": "Marmelad", "category": "sans-serif", "variants": ["regular"]}, "Martel": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["200", "300", "600", "700", "800", "900", "regular"]}, "Martel Sans": {"family": "Martel Sans", "category": "sans-serif", "variants": ["200", "300", "600", "700", "800", "900", "regular"]}, "Martian Mono": {"family": "Martian Mono", "category": "monospace", "variants": ["100", "200", "300", "500", "600", "700", "800", "regular"]}, "Marvel": {"family": "Marvel", "category": "sans-serif", "variants": ["700", "700italic", "italic", "regular"]}, "Mate": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["italic", "regular"]}, "Mate SC": {"family": "Mate SC", "category": "serif", "variants": ["regular"]}, "Matemasie": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Material Icons": {"family": "Material Icons", "category": "monospace", "variants": ["regular"]}, "Material Icons Outlined": {"family": "Material Icons Outlined", "category": "monospace", "variants": ["regular"]}, "Material Icons Round": {"family": "Material Icons Round", "category": "monospace", "variants": ["regular"]}, "Material Icons Sharp": {"family": "Material Icons Sharp", "category": "monospace", "variants": ["regular"]}, "Material Icons Two Tone": {"family": "Material Icons Two Tone", "category": "monospace", "variants": ["regular"]}, "Material Symbols Outlined": {"family": "Material Symbols Outlined", "category": "monospace", "variants": ["100", "200", "300", "500", "600", "700", "regular"]}, "Material Symbols Rounded": {"family": "Material Symbols Rounded", "category": "monospace", "variants": ["100", "200", "300", "500", "600", "700", "regular"]}, "Material Symbols Sharp": {"family": "Material Symbols Sharp", "category": "monospace", "variants": ["100", "200", "300", "500", "600", "700", "regular"]}, "Maven Pro": {"family": "Maven <PERSON>", "category": "sans-serif", "variants": ["500", "600", "700", "800", "900", "regular"]}, "McLaren": {"family": "McLaren", "category": "display", "variants": ["regular"]}, "Mea Culpa": {"family": "<PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Meddon": {"family": "Meddon", "category": "handwriting", "variants": ["regular"]}, "MedievalSharp": {"family": "MedievalSharp", "category": "display", "variants": ["regular"]}, "Medula One": {"family": "Medula One", "category": "display", "variants": ["regular"]}, "Meera Inimai": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Megrim": {"family": "Megrim", "category": "display", "variants": ["regular"]}, "Meie Script": {"family": "<PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Meow Script": {"family": "<PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Merienda": {"family": "<PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["300", "500", "600", "700", "800", "900", "regular"]}, "Merriweather": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "serif", "variants": ["300", "300italic", "700", "700italic", "900", "900italic", "italic", "regular"]}, "Merriweather Sans": {"family": "Merriweather Sans", "category": "sans-serif", "variants": ["300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "italic", "regular"]}, "Metal": {"family": "Metal", "category": "display", "variants": ["regular"]}, "Metal Mania": {"family": "Metal Mania", "category": "display", "variants": ["regular"]}, "Metamorphous": {"family": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Metrophobic": {"family": "Metrophobic", "category": "sans-serif", "variants": ["regular"]}, "Michroma": {"family": "Michroma", "category": "sans-serif", "variants": ["regular"]}, "Micro 5": {"family": "Micro 5", "category": "display", "variants": ["regular"]}, "Micro 5 Charted": {"family": "Micro 5 Charted", "category": "display", "variants": ["regular"]}, "Milonga": {"family": "Milonga", "category": "display", "variants": ["regular"]}, "Miltonian": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Miltonian Tattoo": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Mina": {"family": "Mina", "category": "sans-serif", "variants": ["700", "regular"]}, "Mingzat": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Miniver": {"family": "<PERSON>ver", "category": "display", "variants": ["regular"]}, "Miriam Libre": {"family": "<PERSON>", "category": "sans-serif", "variants": ["500", "600", "700", "regular"]}, "Mirza": {"family": "<PERSON>", "category": "serif", "variants": ["500", "600", "700", "regular"]}, "Miss Fajardose": {"family": "Miss Fajardo<PERSON>", "category": "handwriting", "variants": ["regular"]}, "Mitr": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["200", "300", "500", "600", "700", "regular"]}, "Mochiy Pop One": {"family": "Mochiy Pop One", "category": "sans-serif", "variants": ["regular"]}, "Mochiy Pop P One": {"family": "Mochiy Pop P One", "category": "sans-serif", "variants": ["regular"]}, "Modak": {"family": "Modak", "category": "display", "variants": ["regular"]}, "Modern Antiqua": {"family": "Modern Antiqua", "category": "display", "variants": ["regular"]}, "Moderustic": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["300", "500", "600", "700", "800", "regular"]}, "Mogra": {"family": "Mogra", "category": "display", "variants": ["regular"]}, "Mohave": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Moirai One": {"family": "Moirai One", "category": "display", "variants": ["regular"]}, "Molengo": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Molle": {"family": "<PERSON><PERSON>", "category": "handwriting", "variants": ["italic"]}, "Mona Sans": {"family": "Mona Sans", "category": "sans-serif", "variants": ["200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Monda": {"family": "Monda", "category": "sans-serif", "variants": ["500", "600", "700", "regular"]}, "Monofett": {"family": "<PERSON><PERSON><PERSON>", "category": "monospace", "variants": ["regular"]}, "Monomaniac One": {"family": "Monomaniac One", "category": "sans-serif", "variants": ["regular"]}, "Monoton": {"family": "Monoton", "category": "display", "variants": ["regular"]}, "Monsieur La Doulaise": {"family": "<PERSON> Doulaise", "category": "handwriting", "variants": ["regular"]}, "Montaga": {"family": "Montaga", "category": "serif", "variants": ["regular"]}, "Montagu Slab": {"family": "Montagu Slab", "category": "serif", "variants": ["100", "200", "300", "500", "600", "700", "regular"]}, "MonteCarlo": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Montez": {"family": "Montez", "category": "handwriting", "variants": ["regular"]}, "Montserrat": {"family": "Montserrat", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Montserrat Alternates": {"family": "Montserrat Alternates", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Montserrat Subrayada": {"family": "Montserrat Subrayada", "category": "sans-serif", "variants": ["700", "regular"]}, "Montserrat Underline": {"family": "Montserrat Underline", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Moo Lah Lah": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Mooli": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Moon Dance": {"family": "Moon Dance", "category": "handwriting", "variants": ["regular"]}, "Moul": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Moulpali": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Mountains of Christmas": {"family": "Mountains of Christmas", "category": "display", "variants": ["700", "regular"]}, "Mouse Memoirs": {"family": "Mouse Me<PERSON>irs", "category": "sans-serif", "variants": ["regular"]}, "Mr Bedfort": {"family": "Mr <PERSON>", "category": "handwriting", "variants": ["regular"]}, "Mr Dafoe": {"family": "Mr <PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Mr De Haviland": {"family": "Mr <PERSON>", "category": "handwriting", "variants": ["regular"]}, "Mrs Saint Delafield": {"family": "Mrs <PERSON>", "category": "handwriting", "variants": ["regular"]}, "Mrs Sheppards": {"family": "Mrs <PERSON>", "category": "handwriting", "variants": ["regular"]}, "Ms Madi": {"family": "<PERSON> <PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Mukta": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["200", "300", "500", "600", "700", "800", "regular"]}, "Mukta Mahee": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["200", "300", "500", "600", "700", "800", "regular"]}, "Mukta Malar": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["200", "300", "500", "600", "700", "800", "regular"]}, "Mukta Vaani": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["200", "300", "500", "600", "700", "800", "regular"]}, "Mulish": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Murecho": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "MuseoModerno": {"family": "MuseoModerno", "category": "display", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "My Soul": {"family": "My Soul", "category": "handwriting", "variants": ["regular"]}, "Mynerve": {"family": "Mynerve", "category": "handwriting", "variants": ["regular"]}, "Mystery Quest": {"family": "Mystery Quest", "category": "display", "variants": ["regular"]}, "NTR": {"family": "NTR", "category": "sans-serif", "variants": ["regular"]}, "Nabla": {"family": "Nabla", "category": "display", "variants": ["regular"]}, "Namdhinggo": {"family": "Namdhinggo", "category": "serif", "variants": ["500", "600", "700", "800", "regular"]}, "Nanum Brush Script": {"family": "Nanum Brush Script", "category": "handwriting", "variants": ["regular"]}, "Nanum Gothic": {"family": "Nanum Gothic", "category": "sans-serif", "variants": ["700", "800", "regular"]}, "Nanum Gothic Coding": {"family": "Nanum Gothic Coding", "category": "handwriting", "variants": ["700", "regular"]}, "Nanum Myeongjo": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["700", "800", "regular"]}, "Nanum Pen Script": {"family": "<PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Narnoor": {"family": "<PERSON><PERSON><PERSON>", "category": "serif", "variants": ["500", "600", "700", "800", "regular"]}, "Neonderthaw": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Nerko One": {"family": "Nerko One", "category": "handwriting", "variants": ["regular"]}, "Neucha": {"family": "Neucha", "category": "handwriting", "variants": ["regular"]}, "Neuton": {"family": "Neuton", "category": "serif", "variants": ["200", "300", "700", "800", "italic", "regular"]}, "New Amsterdam": {"family": "New Amsterdam", "category": "sans-serif", "variants": ["regular"]}, "New Rocker": {"family": "<PERSON> Rocker", "category": "display", "variants": ["regular"]}, "New Tegomin": {"family": "New Tegomin", "category": "serif", "variants": ["regular"]}, "News Cycle": {"family": "News Cycle", "category": "sans-serif", "variants": ["700", "regular"]}, "Newsreader": {"family": "Newsreader", "category": "serif", "variants": ["200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "italic", "regular"]}, "Niconne": {"family": "<PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Niramit": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Nixie One": {"family": "Nixie One", "category": "display", "variants": ["regular"]}, "Nobile": {"family": "Nobile", "category": "sans-serif", "variants": ["500", "500italic", "700", "700italic", "italic", "regular"]}, "Nokora": {"family": "Nokor<PERSON>", "category": "sans-serif", "variants": ["100", "300", "700", "900", "regular"]}, "Norican": {"family": "Norican", "category": "handwriting", "variants": ["regular"]}, "Nosifer": {"family": "Nosifer", "category": "display", "variants": ["regular"]}, "Notable": {"family": "Notable", "category": "sans-serif", "variants": ["regular"]}, "Nothing You Could Do": {"family": "Nothing You Could Do", "category": "handwriting", "variants": ["regular"]}, "Noticia Text": {"family": "Noticia Text", "category": "serif", "variants": ["700", "700italic", "italic", "regular"]}, "Noto Color Emoji": {"family": "Noto Color Emoji", "category": "sans-serif", "variants": ["regular"]}, "Noto Emoji": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["300", "500", "600", "700", "regular"]}, "Noto Kufi Arabic": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Music": {"family": "Noto Music", "category": "sans-serif", "variants": ["regular"]}, "Noto Naskh Arabic": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["500", "600", "700", "regular"]}, "Noto Nastaliq Urdu": {"family": "Noto Nastaliq Urdu", "category": "serif", "variants": ["500", "600", "700", "regular"]}, "Noto Rashi Hebrew": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans": {"family": "Noto Sans", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Noto Sans Adlam": {"family": "Noto Sans Adlam", "category": "sans-serif", "variants": ["500", "600", "700", "regular"]}, "Noto Sans Adlam Unjoined": {"family": "Noto Sans Adlam Unjoined", "category": "sans-serif", "variants": ["500", "600", "700", "regular"]}, "Noto Sans Anatolian Hieroglyphs": {"family": "Noto Sans Anatolian Hieroglyphs", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Arabic": {"family": "Noto Sans Arabic", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Armenian": {"family": "Noto Sans Armenian", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Avestan": {"family": "Noto Sans Avestan", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Balinese": {"family": "Noto Sans Balinese", "category": "sans-serif", "variants": ["500", "600", "700", "regular"]}, "Noto Sans Bamum": {"family": "Noto Sans Bamum", "category": "sans-serif", "variants": ["500", "600", "700", "regular"]}, "Noto Sans Bassa Vah": {"family": "Noto Sans Bassa Vah", "category": "sans-serif", "variants": ["500", "600", "700", "regular"]}, "Noto Sans Batak": {"family": "Noto Sans Batak", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Bengali": {"family": "Noto Sans Bengali", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Bhaiksuki": {"family": "Noto Sans Bhaiksuki", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Brahmi": {"family": "Noto Sans Brahmi", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Buginese": {"family": "Noto Sans Buginese", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Buhid": {"family": "Noto Sans Buhid", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Canadian Aboriginal": {"family": "Noto Sans Canadian Aboriginal", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Carian": {"family": "Noto Sans Carian", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Caucasian Albanian": {"family": "Noto Sans Caucasian Albanian", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Chakma": {"family": "Noto Sans Chakma", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Cham": {"family": "Noto Sans Cham", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Cherokee": {"family": "Noto Sans Cherokee", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Chorasmian": {"family": "Noto Sans Chorasmian", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Coptic": {"family": "Noto Sans Coptic", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Cuneiform": {"family": "Noto Sans Cuneiform", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Cypriot": {"family": "Noto Sans Cypriot", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Cypro Minoan": {"family": "Noto Sans Cypro Minoan", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Deseret": {"family": "Noto Sans Deseret", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Devanagari": {"family": "Noto Sans Devanagari", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Display": {"family": "Noto Sans Display", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Noto Sans Duployan": {"family": "Noto Sans Duployan", "category": "sans-serif", "variants": ["700", "regular"]}, "Noto Sans Egyptian Hieroglyphs": {"family": "Noto Sans Egyptian Hieroglyphs", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Elbasan": {"family": "Noto Sans Elbasan", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Elymaic": {"family": "Noto Sans Elymaic", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Ethiopic": {"family": "Noto Sans Ethiopic", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Georgian": {"family": "Noto Sans Georgian", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Glagolitic": {"family": "Noto Sans Glagolitic", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Gothic": {"family": "Noto Sans Gothic", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Grantha": {"family": "Noto Sans Grantha", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Gujarati": {"family": "Noto Sans Gujarati", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Gunjala Gondi": {"family": "Noto Sans Gunjala Go<PERSON>", "category": "sans-serif", "variants": ["500", "600", "700", "regular"]}, "Noto Sans Gurmukhi": {"family": "Noto Sans Gurmukhi", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans HK": {"family": "Noto Sans HK", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Hanifi Rohingya": {"family": "Noto Sans Hanifi <PERSON>", "category": "sans-serif", "variants": ["500", "600", "700", "regular"]}, "Noto Sans Hanunoo": {"family": "Noto Sans Hanunoo", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Hatran": {"family": "Noto Sans Hatran", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Hebrew": {"family": "Noto Sans Hebrew", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Imperial Aramaic": {"family": "Noto Sans Imperial Aramaic", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Indic Siyaq Numbers": {"family": "Noto Sans Indic Siyaq Numbers", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Inscriptional Pahlavi": {"family": "Noto Sans Inscriptional Pahlavi", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Inscriptional Parthian": {"family": "Noto Sans Inscriptional Parthian", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans JP": {"family": "Noto Sans JP", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Javanese": {"family": "Noto Sans Javanese", "category": "sans-serif", "variants": ["500", "600", "700", "regular"]}, "Noto Sans KR": {"family": "Noto Sans KR", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Kaithi": {"family": "Noto Sans Kaithi", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Kannada": {"family": "Noto Sans Kannada", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Kawi": {"family": "Noto Sans Kawi", "category": "sans-serif", "variants": ["500", "600", "700", "regular"]}, "Noto Sans Kayah Li": {"family": "Noto Sans Kayah Li", "category": "sans-serif", "variants": ["500", "600", "700", "regular"]}, "Noto Sans Kharoshthi": {"family": "Noto Sans Kharoshthi", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Khmer": {"family": "Noto Sans Khmer", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Khojki": {"family": "Noto Sans Khojki", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Khudawadi": {"family": "Noto Sans Khudawadi", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Lao": {"family": "Noto Sans Lao", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Lao Looped": {"family": "Noto Sans Lao Looped", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Lepcha": {"family": "Noto Sans Lepcha", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Limbu": {"family": "Noto Sans Limbu", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Linear A": {"family": "Noto Sans Linear A", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Linear B": {"family": "Noto Sans Linear B", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Lisu": {"family": "Noto Sans Lisu", "category": "sans-serif", "variants": ["500", "600", "700", "regular"]}, "Noto Sans Lycian": {"family": "Noto Sans Lycian", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Lydian": {"family": "Noto Sans Lydian", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Mahajani": {"family": "Noto Sans Mahajani", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Malayalam": {"family": "Noto Sans Malayalam", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Mandaic": {"family": "Noto Sans Mandaic", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Manichaean": {"family": "Noto Sans Manichaean", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Marchen": {"family": "Noto Sans Marchen", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Masaram Gondi": {"family": "Noto Sans Masaram <PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Math": {"family": "Noto Sans Math", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Mayan Numerals": {"family": "Noto Sans Mayan Numerals", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Medefaidrin": {"family": "Noto Sans Medefaidrin", "category": "sans-serif", "variants": ["500", "600", "700", "regular"]}, "Noto Sans Meetei Mayek": {"family": "Noto Sans Meetei <PERSON>", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Mende Kikakui": {"family": "Noto Sans Mende Kikakui", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Meroitic": {"family": "Noto Sans Meroitic", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Miao": {"family": "Noto Sans Miao", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Modi": {"family": "Noto Sans Modi", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Mongolian": {"family": "Noto Sans Mongolian", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Mono": {"family": "Noto Sans Mono", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Mro": {"family": "Noto Sans Mro", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Multani": {"family": "Noto Sans Multani", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Myanmar": {"family": "Noto Sans Myanmar", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans NKo": {"family": "Noto Sans NKo", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans NKo Unjoined": {"family": "Noto Sans NKo Unjoined", "category": "sans-serif", "variants": ["500", "600", "700", "regular"]}, "Noto Sans Nabataean": {"family": "Noto Sans Nabataean", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Nag Mundari": {"family": "Noto Sans Nag Mundari", "category": "sans-serif", "variants": ["500", "600", "700", "regular"]}, "Noto Sans Nandinagari": {"family": "Noto Sans Nandinagari", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans New Tai Lue": {"family": "Noto Sans New Tai Lue", "category": "sans-serif", "variants": ["500", "600", "700", "regular"]}, "Noto Sans Newa": {"family": "Noto Sans Newa", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Nushu": {"family": "Noto Sans Nushu", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Ogham": {"family": "Noto Sans Ogham", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Ol Chiki": {"family": "Noto Sans Ol Chiki", "category": "sans-serif", "variants": ["500", "600", "700", "regular"]}, "Noto Sans Old Hungarian": {"family": "Noto Sans Old Hungarian", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Old Italic": {"family": "Noto Sans Old Italic", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Old North Arabian": {"family": "Noto Sans Old North Arabian", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Old Permic": {"family": "Noto Sans Old Permic", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Old Persian": {"family": "Noto Sans Old Persian", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Old Sogdian": {"family": "Noto Sans Old Sogdian", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Old South Arabian": {"family": "Noto Sans Old South Arabian", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Old Turkic": {"family": "Noto Sans Old Turkic", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Oriya": {"family": "Noto Sans Oriya", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Osage": {"family": "Noto Sans Osage", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Osmanya": {"family": "Noto Sans Osmanya", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Pahawh Hmong": {"family": "Noto Sans Pa<PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Palmyrene": {"family": "Noto Sans Palmyrene", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Pau Cin Hau": {"family": "Noto Sans Pau Cin Hau", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans PhagsPa": {"family": "Noto Sans PhagsPa", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Phoenician": {"family": "Noto Sans Phoenician", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Psalter Pahlavi": {"family": "Noto Sans Psalter <PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Rejang": {"family": "Noto Sans Rejang", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Runic": {"family": "Noto Sans Runic", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans SC": {"family": "Noto Sans SC", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Samaritan": {"family": "Noto Sans Samaritan", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Saurashtra": {"family": "Noto Sans Saurashtra", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Sharada": {"family": "Noto Sans Sharada", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Shavian": {"family": "Noto Sans Shavian", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Siddham": {"family": "Noto Sans Siddham", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans SignWriting": {"family": "Noto Sans SignWriting", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Sinhala": {"family": "Noto Sans Sinhala", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Sogdian": {"family": "Noto Sans Sogdian", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Sora Sompeng": {"family": "Noto Sans Sora Sompeng", "category": "sans-serif", "variants": ["500", "600", "700", "regular"]}, "Noto Sans Soyombo": {"family": "Noto Sans Soyombo", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Sundanese": {"family": "Noto Sans Sundanese", "category": "sans-serif", "variants": ["500", "600", "700", "regular"]}, "Noto Sans Syloti Nagri": {"family": "Noto Sans Syloti Nagri", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Symbols": {"family": "Noto Sans Symbols", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Symbols 2": {"family": "Noto Sans Symbols 2", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Syriac": {"family": "Noto Sans Syriac", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Syriac Eastern": {"family": "Noto Sans Syriac Eastern", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans TC": {"family": "Noto Sans TC", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Tagalog": {"family": "Noto Sans Tagalog", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Tagbanwa": {"family": "Noto Sans Tagbanwa", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Tai Le": {"family": "Noto Sans Tai Le", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Tai Tham": {"family": "Noto Sans Tai Tham", "category": "sans-serif", "variants": ["500", "600", "700", "regular"]}, "Noto Sans Tai Viet": {"family": "Noto Sans Tai Viet", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Takri": {"family": "Noto Sans Takri", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Tamil": {"family": "Noto Sans Tamil", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Tamil Supplement": {"family": "Noto Sans Tamil Supplement", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Tangsa": {"family": "Noto Sans Tangsa", "category": "sans-serif", "variants": ["500", "600", "700", "regular"]}, "Noto Sans Telugu": {"family": "Noto Sans Telugu", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Thaana": {"family": "Noto Sans Thaana", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Thai": {"family": "Noto Sans Thai", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Thai Looped": {"family": "Noto Sans Thai Looped", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Sans Tifinagh": {"family": "Noto Sans Tifinagh", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Tirhuta": {"family": "Noto Sans Tirhuta", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Ugaritic": {"family": "Noto Sans Ugaritic", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Vai": {"family": "Noto Sans Vai", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Vithkuqi": {"family": "Noto Sans Vithkuqi", "category": "sans-serif", "variants": ["500", "600", "700", "regular"]}, "Noto Sans Wancho": {"family": "Noto Sans Wancho", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Warang Citi": {"family": "Noto Sans Warang Citi", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Yi": {"family": "Noto Sans Yi", "category": "sans-serif", "variants": ["regular"]}, "Noto Sans Zanabazar Square": {"family": "Noto Sans Zanabazar Square", "category": "sans-serif", "variants": ["regular"]}, "Noto Serif": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Noto Serif Ahom": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Noto Serif Armenian": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Serif Balinese": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Noto Serif Bengali": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Serif Devanagari": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Serif Display": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Noto Serif Dogra": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Noto Serif Ethiopic": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Serif Georgian": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Serif Grantha": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Noto Serif Gujarati": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Serif Gurmukhi": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Serif HK": {"family": "Noto Serif HK", "category": "serif", "variants": ["200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Serif Hebrew": {"family": "Noto Serif <PERSON>", "category": "serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Serif JP": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Serif KR": {"family": "Noto Serif KR", "category": "serif", "variants": ["200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Serif Kannada": {"family": "Noto Serif Kannada", "category": "serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Serif Khitan Small Script": {"family": "Noto Serif <PERSON> Small Script", "category": "serif", "variants": ["regular"]}, "Noto Serif Khmer": {"family": "Noto Serif <PERSON>", "category": "serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Serif Khojki": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["500", "600", "700", "regular"]}, "Noto Serif Lao": {"family": "Noto Ser<PERSON>", "category": "serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Serif Makasar": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Noto Serif Malayalam": {"family": "Noto Serif Malayalam", "category": "serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Serif Myanmar": {"family": "Noto Serif Myanmar", "category": "serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Serif NP Hmong": {"family": "Noto Serif NP Hmong", "category": "serif", "variants": ["500", "600", "700", "regular"]}, "Noto Serif Old Uyghur": {"family": "Noto Serif Old Uyghur", "category": "serif", "variants": ["regular"]}, "Noto Serif Oriya": {"family": "Noto Serif <PERSON>", "category": "serif", "variants": ["500", "600", "700", "regular"]}, "Noto Serif Ottoman Siyaq": {"family": "Noto Serif Ottoman Siyaq", "category": "serif", "variants": ["regular"]}, "Noto Serif SC": {"family": "Noto Serif SC", "category": "serif", "variants": ["200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Serif Sinhala": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Serif TC": {"family": "Noto Serif TC", "category": "serif", "variants": ["200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Serif Tamil": {"family": "Noto <PERSON>", "category": "serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Noto Serif Tangut": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Noto Serif Telugu": {"family": "Noto Serif Telugu", "category": "serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Serif Thai": {"family": "Noto Serif <PERSON>", "category": "serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Serif Tibetan": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Noto Serif Toto": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["500", "600", "700", "regular"]}, "Noto Serif Vithkuqi": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["500", "600", "700", "regular"]}, "Noto Serif Yezidi": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["500", "600", "700", "regular"]}, "Noto Traditional Nushu": {"family": "Noto Traditional Nushu", "category": "sans-serif", "variants": ["300", "500", "600", "700", "regular"]}, "Noto Znamenny Musical Notation": {"family": "Noto Znamenny Musical Notation", "category": "sans-serif", "variants": ["regular"]}, "Nova Cut": {"family": "Nova Cut", "category": "display", "variants": ["regular"]}, "Nova Flat": {"family": "Nova Flat", "category": "display", "variants": ["regular"]}, "Nova Mono": {"family": "Nova Mono", "category": "monospace", "variants": ["regular"]}, "Nova Oval": {"family": "Nova Oval", "category": "display", "variants": ["regular"]}, "Nova Round": {"family": "Nova Round", "category": "display", "variants": ["regular"]}, "Nova Script": {"family": "Nova Script", "category": "display", "variants": ["regular"]}, "Nova Slim": {"family": "Nova Slim", "category": "display", "variants": ["regular"]}, "Nova Square": {"family": "Nova Square", "category": "display", "variants": ["regular"]}, "Numans": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Nunito": {"family": "Nunito", "category": "sans-serif", "variants": ["200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Nunito Sans": {"family": "Nunito Sans", "category": "sans-serif", "variants": ["200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Nuosu SIL": {"family": "Nuosu SIL", "category": "sans-serif", "variants": ["regular"]}, "Odibee Sans": {"family": "Odibee Sans", "category": "display", "variants": ["regular"]}, "Odor Mean Chey": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Offside": {"family": "Offside", "category": "display", "variants": ["regular"]}, "Oi": {"family": "Oi", "category": "display", "variants": ["regular"]}, "Ojuju": {"family": "Ojuju", "category": "sans-serif", "variants": ["200", "300", "500", "600", "700", "800", "regular"]}, "Old Standard TT": {"family": "Old Standard TT", "category": "serif", "variants": ["700", "italic", "regular"]}, "Oldenburg": {"family": "Oldenburg", "category": "display", "variants": ["regular"]}, "Ole": {"family": "Ole", "category": "handwriting", "variants": ["regular"]}, "Oleo Script": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["700", "regular"]}, "Oleo Script Swash Caps": {"family": "<PERSON><PERSON> Script Swash Caps", "category": "display", "variants": ["700", "regular"]}, "Onest": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Oooh Baby": {"family": "Oooh Baby", "category": "handwriting", "variants": ["regular"]}, "Open Sans": {"family": "Open Sans", "category": "sans-serif", "variants": ["300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "italic", "regular"]}, "Oranienbaum": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Orbit": {"family": "Orbit", "category": "sans-serif", "variants": ["regular"]}, "Orbitron": {"family": "Orbitron", "category": "sans-serif", "variants": ["500", "600", "700", "800", "900", "regular"]}, "Oregano": {"family": "Oregano", "category": "display", "variants": ["italic", "regular"]}, "Orelega One": {"family": "Orelega One", "category": "display", "variants": ["regular"]}, "Orienta": {"family": "Orienta", "category": "sans-serif", "variants": ["regular"]}, "Original Surfer": {"family": "Original Surfer", "category": "display", "variants": ["regular"]}, "Oswald": {"family": "<PERSON>", "category": "sans-serif", "variants": ["200", "300", "500", "600", "700", "regular"]}, "Outfit": {"family": "Outfit", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Over the Rainbow": {"family": "Over the Rainbow", "category": "handwriting", "variants": ["regular"]}, "Overlock": {"family": "Overlock", "category": "display", "variants": ["700", "700italic", "900", "900italic", "italic", "regular"]}, "Overlock SC": {"family": "Overlock SC", "category": "display", "variants": ["regular"]}, "Overpass": {"family": "Overpass", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Overpass Mono": {"family": "Overpass Mono", "category": "monospace", "variants": ["300", "500", "600", "700", "regular"]}, "Ovo": {"family": "Ovo", "category": "serif", "variants": ["regular"]}, "Oxanium": {"family": "Oxanium", "category": "display", "variants": ["200", "300", "500", "600", "700", "800", "regular"]}, "Oxygen": {"family": "Oxygen", "category": "sans-serif", "variants": ["300", "700", "regular"]}, "Oxygen Mono": {"family": "Oxygen Mono", "category": "monospace", "variants": ["regular"]}, "PT Mono": {"family": "PT Mono", "category": "monospace", "variants": ["regular"]}, "PT Sans": {"family": "PT Sans", "category": "sans-serif", "variants": ["700", "700italic", "italic", "regular"]}, "PT Sans Caption": {"family": "PT Sans Caption", "category": "sans-serif", "variants": ["700", "regular"]}, "PT Sans Narrow": {"family": "PT Sans Narrow", "category": "sans-serif", "variants": ["700", "regular"]}, "PT Serif": {"family": "PT Serif", "category": "serif", "variants": ["700", "700italic", "italic", "regular"]}, "PT Serif Caption": {"family": "PT Serif Caption", "category": "serif", "variants": ["italic", "regular"]}, "Pacifico": {"family": "Pacifico", "category": "handwriting", "variants": ["regular"]}, "Padauk": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["700", "regular"]}, "Padyakke Expanded One": {"family": "Padyakke Expanded One", "category": "serif", "variants": ["regular"]}, "Palanquin": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "regular"]}, "Palanquin Dark": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["500", "600", "700", "regular"]}, "Palette Mosaic": {"family": "Pa<PERSON>", "category": "display", "variants": ["regular"]}, "Pangolin": {"family": "Pangolin", "category": "handwriting", "variants": ["regular"]}, "Paprika": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Parisienne": {"family": "Parisienne", "category": "handwriting", "variants": ["regular"]}, "Parkinsans": {"family": "Parkinsans", "category": "sans-serif", "variants": ["300", "500", "600", "700", "800", "regular"]}, "Passero One": {"family": "Passero One", "category": "display", "variants": ["regular"]}, "Passion One": {"family": "Passion One", "category": "display", "variants": ["700", "900", "regular"]}, "Passions Conflict": {"family": "Passions Conflict", "category": "handwriting", "variants": ["regular"]}, "Pathway Extreme": {"family": "Pathway Extreme", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Pathway Gothic One": {"family": "Pathway Gothic One", "category": "sans-serif", "variants": ["regular"]}, "Patrick Hand": {"family": "<PERSON>", "category": "handwriting", "variants": ["regular"]}, "Patrick Hand SC": {"family": "<PERSON>", "category": "handwriting", "variants": ["regular"]}, "Pattaya": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Patua One": {"family": "Patua One", "category": "display", "variants": ["regular"]}, "Pavanam": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Paytone One": {"family": "Paytone One", "category": "sans-serif", "variants": ["regular"]}, "Peddana": {"family": "Peddana", "category": "serif", "variants": ["regular"]}, "Peralta": {"family": "<PERSON><PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Permanent Marker": {"family": "Permanent Marker", "category": "handwriting", "variants": ["regular"]}, "Petemoss": {"family": "<PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Petit Formal Script": {"family": "Petit Formal Script", "category": "handwriting", "variants": ["regular"]}, "Petrona": {"family": "Petrona", "category": "serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Phetsarath": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "serif", "variants": ["700", "regular"]}, "Philosopher": {"family": "Philosopher", "category": "sans-serif", "variants": ["700", "700italic", "italic", "regular"]}, "Phudu": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["300", "500", "600", "700", "800", "900", "regular"]}, "Piazzolla": {"family": "Piazzolla", "category": "serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Piedra": {"family": "Piedra", "category": "display", "variants": ["regular"]}, "Pinyon Script": {"family": "<PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Pirata One": {"family": "Pirata One", "category": "display", "variants": ["regular"]}, "Pixelify Sans": {"family": "Pixelify Sans", "category": "display", "variants": ["500", "600", "700", "regular"]}, "Plaster": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Platypi": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "serif", "variants": ["300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "italic", "regular"]}, "Play": {"family": "Play", "category": "sans-serif", "variants": ["700", "regular"]}, "Playball": {"family": "Playball", "category": "display", "variants": ["regular"]}, "Playfair": {"family": "Playfair", "category": "serif", "variants": ["300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Playfair Display": {"family": "Playfair Display", "category": "serif", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Playfair Display SC": {"family": "Playfair Display SC", "category": "serif", "variants": ["700", "700italic", "900", "900italic", "italic", "regular"]}, "Playpen Sans": {"family": "Playpen Sans", "category": "handwriting", "variants": ["100", "200", "300", "500", "600", "700", "800", "regular"]}, "Playwrite AR": {"family": "Playwrite AR", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite AR Guides": {"family": "Playwrite AR Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite AT": {"family": "Playwrite AT", "category": "handwriting", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "italic", "regular"]}, "Playwrite AT Guides": {"family": "Playwrite AT Guides", "category": "handwriting", "variants": ["italic", "regular"]}, "Playwrite AU NSW": {"family": "Playwrite AU NSW", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite AU NSW Guides": {"family": "Playwrite AU NSW Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite AU QLD": {"family": "Playwrite AU QLD", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite AU QLD Guides": {"family": "Playwrite AU QLD Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite AU SA": {"family": "Playwrite AU SA", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite AU SA Guides": {"family": "Playwrite AU SA Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite AU TAS": {"family": "Playwrite AU TAS", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite AU TAS Guides": {"family": "Playwrite AU TAS Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite AU VIC": {"family": "Playwrite AU VIC", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite AU VIC Guides": {"family": "Playwrite AU VIC Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite BE VLG": {"family": "Playwrite BE VLG", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite BE VLG Guides": {"family": "Playwrite BE VLG Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite BE WAL": {"family": "Playwrite BE WAL", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite BE WAL Guides": {"family": "Playwrite BE WAL Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite BR": {"family": "Playwrite BR", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite BR Guides": {"family": "Playwrite BR Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite CA": {"family": "Playwrite CA", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite CA Guides": {"family": "Playwrite CA Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite CL": {"family": "Playwrite CL", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite CL Guides": {"family": "Playwrite CL Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite CO": {"family": "Playwrite CO", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite CO Guides": {"family": "Playwrite CO Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite CU": {"family": "Playwrite CU", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite CU Guides": {"family": "Playwrite CU Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite CZ": {"family": "Playwrite CZ", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite CZ Guides": {"family": "Playwrite CZ Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite DE Grund": {"family": "Playwrite DE Grund", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite DE Grund Guides": {"family": "Playwrite DE Grund Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite DE LA": {"family": "Playwrite DE LA", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite DE LA Guides": {"family": "Playwrite DE LA Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite DE SAS": {"family": "Playwrite DE SAS", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite DE SAS Guides": {"family": "Playwrite DE SAS Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite DE VA": {"family": "Playwrite DE VA", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite DE VA Guides": {"family": "Playwrite DE VA Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite DK Loopet": {"family": "Playwrite DK Loopet", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite DK Loopet Guides": {"family": "Playwrite DK Loopet Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite DK Uloopet": {"family": "Playwrite DK Uloopet", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite DK Uloopet Guides": {"family": "Playwrite DK Uloopet Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite ES": {"family": "Playwrite ES", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite ES Deco": {"family": "Playwrite ES Deco", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite ES Deco Guides": {"family": "Playwrite ES Deco Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite ES Guides": {"family": "Playwrite ES Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite FR Moderne": {"family": "Playwrite FR Moderne", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite FR Moderne Guides": {"family": "Playwrite FR Moderne Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite FR Trad": {"family": "Playwrite FR Trad", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite FR Trad Guides": {"family": "Playwrite FR Trad Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite GB J": {"family": "Playwrite GB J", "category": "handwriting", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "italic", "regular"]}, "Playwrite GB J Guides": {"family": "Playwrite GB J Guides", "category": "handwriting", "variants": ["italic", "regular"]}, "Playwrite GB S": {"family": "Playwrite GB S", "category": "handwriting", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "italic", "regular"]}, "Playwrite GB S Guides": {"family": "Playwrite GB S Guides", "category": "handwriting", "variants": ["italic", "regular"]}, "Playwrite HR": {"family": "Playwrite HR", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite HR Guides": {"family": "Playwrite HR Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite HR Lijeva": {"family": "Playwrite HR Lijeva", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite HR Lijeva Guides": {"family": "Playwrite HR Lijeva Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite HU": {"family": "Playwrite HU", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite HU Guides": {"family": "Playwrite HU Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite ID": {"family": "Playwrite ID", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite ID Guides": {"family": "Playwrite ID Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite IE": {"family": "Playwrite IE", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite IE Guides": {"family": "Playwrite IE Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite IN": {"family": "Playwrite IN", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite IN Guides": {"family": "Playwrite IN Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite IS": {"family": "Playwrite IS", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite IS Guides": {"family": "Playwrite IS Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite IT Moderna": {"family": "Playwrite IT Moderna", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite IT Moderna Guides": {"family": "Playwrite IT Moderna Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite IT Trad": {"family": "Playwrite IT Trad", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite IT Trad Guides": {"family": "Playwrite IT Trad Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite MX": {"family": "Playwrite MX", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite MX Guides": {"family": "Playwrite MX Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite NG Modern": {"family": "Playwrite NG Modern", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite NG Modern Guides": {"family": "Playwrite NG Modern Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite NL": {"family": "Playwrite NL", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite NL Guides": {"family": "Playwrite NL Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite NO": {"family": "Playwrite NO", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite NO Guides": {"family": "Playwrite NO Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite NZ": {"family": "Playwrite NZ", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite NZ Guides": {"family": "Playwrite NZ Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite PE": {"family": "Playwrite PE", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite PE Guides": {"family": "Playwrite PE Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite PL": {"family": "Playwrite PL", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite PL Guides": {"family": "Playwrite PL Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite PT": {"family": "Playwrite PT", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite PT Guides": {"family": "Playwrite PT Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite RO": {"family": "Playwrite RO", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite RO Guides": {"family": "Playwrite RO Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite SK": {"family": "Playwrite SK", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite SK Guides": {"family": "Playwrite SK Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite TZ": {"family": "Playwrite TZ", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite TZ Guides": {"family": "Playwrite TZ Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite US Modern": {"family": "Playwrite US Modern", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite US Modern Guides": {"family": "Playwrite US Modern Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite US Trad": {"family": "Playwrite US Trad", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite US Trad Guides": {"family": "Playwrite US Trad Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite VN": {"family": "Playwrite VN", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite VN Guides": {"family": "Playwrite VN Guides", "category": "handwriting", "variants": ["regular"]}, "Playwrite ZA": {"family": "Playwrite ZA", "category": "handwriting", "variants": ["100", "200", "300", "regular"]}, "Playwrite ZA Guides": {"family": "Playwrite ZA Guides", "category": "handwriting", "variants": ["regular"]}, "Plus Jakarta Sans": {"family": "Plus Jakarta Sans", "category": "sans-serif", "variants": ["200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "italic", "regular"]}, "Podkova": {"family": "<PERSON><PERSON><PERSON>", "category": "serif", "variants": ["500", "600", "700", "800", "regular"]}, "Poetsen One": {"family": "<PERSON>en One", "category": "display", "variants": ["regular"]}, "Poiret One": {"family": "Poiret One", "category": "display", "variants": ["regular"]}, "Poller One": {"family": "Poller One", "category": "display", "variants": ["regular"]}, "Poltawski Nowy": {"family": "Poltawski Nowy", "category": "serif", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Poly": {"family": "Poly", "category": "serif", "variants": ["italic", "regular"]}, "Pompiere": {"family": "Pompiere", "category": "display", "variants": ["regular"]}, "Ponnala": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Pontano Sans": {"family": "Pontano Sans", "category": "sans-serif", "variants": ["300", "500", "600", "700", "regular"]}, "Poor Story": {"family": "Poor Story", "category": "display", "variants": ["regular"]}, "Poppins": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Port Lligat Sans": {"family": "Port Lligat Sans", "category": "sans-serif", "variants": ["regular"]}, "Port Lligat Slab": {"family": "Port Lligat Slab", "category": "serif", "variants": ["regular"]}, "Potta One": {"family": "Potta One", "category": "display", "variants": ["regular"]}, "Pragati Narrow": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["700", "regular"]}, "Praise": {"family": "<PERSON>raise", "category": "handwriting", "variants": ["regular"]}, "Prata": {"family": "Prata", "category": "serif", "variants": ["regular"]}, "Preahvihear": {"family": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Press Start 2P": {"family": "Press Start 2P", "category": "display", "variants": ["regular"]}, "Pridi": {"family": "<PERSON><PERSON><PERSON>", "category": "serif", "variants": ["200", "300", "500", "600", "700", "regular"]}, "Princess Sofia": {"family": "Princess <PERSON>", "category": "handwriting", "variants": ["regular"]}, "Prociono": {"family": "Prociono", "category": "serif", "variants": ["regular"]}, "Prompt": {"family": "Prompt", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Prosto One": {"family": "Prosto One", "category": "display", "variants": ["regular"]}, "Protest Guerrilla": {"family": "Protest Guerrilla", "category": "display", "variants": ["regular"]}, "Protest Revolution": {"family": "Protest Revolution", "category": "display", "variants": ["regular"]}, "Protest Riot": {"family": "Protest Riot", "category": "display", "variants": ["regular"]}, "Protest Strike": {"family": "Protest Strike", "category": "display", "variants": ["regular"]}, "Proza Libre": {"family": "Proza Libre", "category": "sans-serif", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "italic", "regular"]}, "Public Sans": {"family": "Public Sans", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Puppies Play": {"family": "Puppies Play", "category": "handwriting", "variants": ["regular"]}, "Puritan": {"family": "Puritan", "category": "sans-serif", "variants": ["700", "700italic", "italic", "regular"]}, "Purple Purse": {"family": "Purple Purse", "category": "display", "variants": ["regular"]}, "Qahiri": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Quando": {"family": "Quando", "category": "serif", "variants": ["regular"]}, "Quantico": {"family": "Quantico", "category": "sans-serif", "variants": ["700", "700italic", "italic", "regular"]}, "Quattrocento": {"family": "Quattrocento", "category": "serif", "variants": ["700", "regular"]}, "Quattrocento Sans": {"family": "Quattrocento Sans", "category": "sans-serif", "variants": ["700", "700italic", "italic", "regular"]}, "Questrial": {"family": "Quest<PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Quicksand": {"family": "Quicksand", "category": "sans-serif", "variants": ["300", "500", "600", "700", "regular"]}, "Quintessential": {"family": "Quintessential", "category": "handwriting", "variants": ["regular"]}, "Qwigley": {"family": "<PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Qwitcher Grypen": {"family": "Qwitcher Grypen", "category": "handwriting", "variants": ["700", "regular"]}, "REM": {"family": "REM", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Racing Sans One": {"family": "Racing Sans One", "category": "display", "variants": ["regular"]}, "Radio Canada": {"family": "Radio Canada", "category": "sans-serif", "variants": ["300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Radio Canada Big": {"family": "Radio Canada Big", "category": "sans-serif", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Radley": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["italic", "regular"]}, "Rajdhani": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["300", "500", "600", "700", "regular"]}, "Rakkas": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Raleway": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Raleway Dots": {"family": "Raleway Dots", "category": "display", "variants": ["regular"]}, "Ramabhadra": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Ramaraja": {"family": "<PERSON><PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Rambla": {"family": "Rambla", "category": "sans-serif", "variants": ["700", "700italic", "italic", "regular"]}, "Rammetto One": {"family": "Rammetto One", "category": "display", "variants": ["regular"]}, "Rampart One": {"family": "Rampart One", "category": "display", "variants": ["regular"]}, "Ranchers": {"family": "Ranchers", "category": "display", "variants": ["regular"]}, "Rancho": {"family": "Rancho", "category": "handwriting", "variants": ["regular"]}, "Ranga": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["700", "regular"]}, "Rasa": {"family": "Rasa", "category": "serif", "variants": ["300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Rationale": {"family": "Rationale", "category": "sans-serif", "variants": ["regular"]}, "Ravi Prakash": {"family": "<PERSON>", "category": "display", "variants": ["regular"]}, "Readex Pro": {"family": "Readex Pro", "category": "sans-serif", "variants": ["200", "300", "500", "600", "700", "regular"]}, "Recursive": {"family": "Recursive", "category": "sans-serif", "variants": ["300", "500", "600", "700", "800", "900", "regular"]}, "Red Hat Display": {"family": "Red Hat Display", "category": "sans-serif", "variants": ["300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Red Hat Mono": {"family": "Red Hat Mono", "category": "monospace", "variants": ["300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Red Hat Text": {"family": "Red Hat Text", "category": "sans-serif", "variants": ["300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Red Rose": {"family": "Red Rose", "category": "display", "variants": ["300", "500", "600", "700", "regular"]}, "Redacted": {"family": "Redacted", "category": "display", "variants": ["regular"]}, "Redacted Script": {"family": "Redacted <PERSON><PERSON><PERSON>", "category": "display", "variants": ["300", "700", "regular"]}, "Reddit Mono": {"family": "Reddit <PERSON>o", "category": "monospace", "variants": ["200", "300", "500", "600", "700", "800", "900", "regular"]}, "Reddit Sans": {"family": "Reddit Sans", "category": "sans-serif", "variants": ["200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Reddit Sans Condensed": {"family": "Reddit Sans Condensed", "category": "sans-serif", "variants": ["200", "300", "500", "600", "700", "800", "900", "regular"]}, "Redressed": {"family": "Redressed", "category": "handwriting", "variants": ["regular"]}, "Reem Kufi": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["500", "600", "700", "regular"]}, "Reem Kufi Fun": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["500", "600", "700", "regular"]}, "Reem Kufi Ink": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Reenie Beanie": {"family": "<PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Reggae One": {"family": "Reggae One", "category": "display", "variants": ["regular"]}, "Rethink Sans": {"family": "Rethink Sans", "category": "sans-serif", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "italic", "regular"]}, "Revalia": {"family": "Revalia", "category": "display", "variants": ["regular"]}, "Rhodium Libre": {"family": "Rhodium Libre", "category": "serif", "variants": ["regular"]}, "Ribeye": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Ribeye Marrow": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Righteous": {"family": "Righteous", "category": "display", "variants": ["regular"]}, "Risque": {"family": "Risque", "category": "display", "variants": ["regular"]}, "Road Rage": {"family": "Road Rage", "category": "display", "variants": ["regular"]}, "Roboto": {"family": "Roboto", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Roboto Condensed": {"family": "Roboto Condensed", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Roboto Flex": {"family": "Roboto Flex", "category": "sans-serif", "variants": ["regular"]}, "Roboto Mono": {"family": "Roboto Mono", "category": "monospace", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Roboto Serif": {"family": "Roboto Serif", "category": "serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Roboto Slab": {"family": "<PERSON><PERSON> Slab", "category": "serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Rochester": {"family": "Rochester", "category": "handwriting", "variants": ["regular"]}, "Rock 3D": {"family": "Rock 3D", "category": "display", "variants": ["regular"]}, "Rock Salt": {"family": "Rock Salt", "category": "handwriting", "variants": ["regular"]}, "RocknRoll One": {"family": "RocknRoll One", "category": "sans-serif", "variants": ["regular"]}, "Rokkitt": {"family": "Rokkitt", "category": "serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Romanesco": {"family": "Romanesco", "category": "handwriting", "variants": ["regular"]}, "Ropa Sans": {"family": "Ropa Sans", "category": "sans-serif", "variants": ["italic", "regular"]}, "Rosario": {"family": "Rosario", "category": "sans-serif", "variants": ["300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Rosarivo": {"family": "Rosarivo", "category": "serif", "variants": ["italic", "regular"]}, "Rouge Script": {"family": "<PERSON>", "category": "handwriting", "variants": ["regular"]}, "Rowdies": {"family": "Rowdies", "category": "display", "variants": ["300", "700", "regular"]}, "Rozha One": {"family": "Rozha One", "category": "serif", "variants": ["regular"]}, "Rubik": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Rubik 80s Fade": {"family": "Rubik 80s Fade", "category": "display", "variants": ["regular"]}, "Rubik Beastly": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Rubik Broken Fax": {"family": "Rubik Broken Fax", "category": "display", "variants": ["regular"]}, "Rubik Bubbles": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Rubik Burned": {"family": "Rubik Burned", "category": "display", "variants": ["regular"]}, "Rubik Dirt": {"family": "<PERSON><PERSON>k <PERSON>", "category": "display", "variants": ["regular"]}, "Rubik Distressed": {"family": "Rubik Distressed", "category": "display", "variants": ["regular"]}, "Rubik Doodle Shadow": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Rubik Doodle Triangles": {"family": "Rubik Doodle Triangles", "category": "display", "variants": ["regular"]}, "Rubik Gemstones": {"family": "Rubik Gemstones", "category": "display", "variants": ["regular"]}, "Rubik Glitch": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Rubik Glitch Pop": {"family": "Rubik Glitch Pop", "category": "display", "variants": ["regular"]}, "Rubik Iso": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Rubik Lines": {"family": "Rubik Lines", "category": "display", "variants": ["regular"]}, "Rubik Maps": {"family": "Rubik Maps", "category": "display", "variants": ["regular"]}, "Rubik Marker Hatch": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Rubik Maze": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Rubik Microbe": {"family": "Rubik Microbe", "category": "display", "variants": ["regular"]}, "Rubik Mono One": {"family": "Rubik Mono One", "category": "sans-serif", "variants": ["regular"]}, "Rubik Moonrocks": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Rubik Pixels": {"family": "Rubik <PERSON>", "category": "display", "variants": ["regular"]}, "Rubik Puddles": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Rubik Scribble": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Rubik Spray Paint": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Rubik Storm": {"family": "Rubik Storm", "category": "display", "variants": ["regular"]}, "Rubik Vinyl": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Rubik Wet Paint": {"family": "Rubik Wet Paint", "category": "display", "variants": ["regular"]}, "Ruda": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["500", "600", "700", "800", "900", "regular"]}, "Rufina": {"family": "<PERSON><PERSON><PERSON>", "category": "serif", "variants": ["700", "regular"]}, "Ruge Boogie": {"family": "Ruge Boogie", "category": "handwriting", "variants": ["regular"]}, "Ruluko": {"family": "R<PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Rum Raisin": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Ruslan Display": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Russo One": {"family": "Russo One", "category": "sans-serif", "variants": ["regular"]}, "Ruthie": {"family": "<PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Ruwudu": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "serif", "variants": ["500", "600", "700", "regular"]}, "Rye": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "STIX Two Text": {"family": "STIX Two Text", "category": "serif", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "SUSE": {"family": "SUSE", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "regular"]}, "Sacramento": {"family": "Sacramento", "category": "handwriting", "variants": ["regular"]}, "Sahitya": {"family": "Sahitya", "category": "serif", "variants": ["700", "regular"]}, "Sail": {"family": "Sail", "category": "display", "variants": ["regular"]}, "Saira": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Saira Condensed": {"family": "<PERSON><PERSON> Condensed", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Saira Extra Condensed": {"family": "Saira Extra Condensed", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Saira Semi Condensed": {"family": "Saira Semi Condensed", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Saira Stencil One": {"family": "Saira Stencil One", "category": "display", "variants": ["regular"]}, "Salsa": {"family": "Salsa", "category": "display", "variants": ["regular"]}, "Sanchez": {"family": "<PERSON>", "category": "serif", "variants": ["italic", "regular"]}, "Sancreek": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Sankofa Display": {"family": "Sankofa Display", "category": "sans-serif", "variants": ["regular"]}, "Sansita": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Sansita Swashed": {"family": "<PERSON><PERSON><PERSON> Swashed", "category": "display", "variants": ["300", "500", "600", "700", "800", "900", "regular"]}, "Sarabun": {"family": "Sarabun", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "italic", "regular"]}, "Sarala": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["700", "regular"]}, "Sarina": {"family": "Sarina", "category": "display", "variants": ["regular"]}, "Sarpanch": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["500", "600", "700", "800", "900", "regular"]}, "Sassy Frass": {"family": "<PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Satisfy": {"family": "<PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Sawarabi Gothic": {"family": "Sawarabi Gothic", "category": "sans-serif", "variants": ["regular"]}, "Sawarabi Mincho": {"family": "<PERSON><PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Scada": {"family": "Scada", "category": "sans-serif", "variants": ["700", "700italic", "italic", "regular"]}, "Scheherazade New": {"family": "Scheherazade New", "category": "serif", "variants": ["500", "600", "700", "regular"]}, "Schibsted Grotesk": {"family": "Schibsted Grotesk", "category": "sans-serif", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Schoolbell": {"family": "<PERSON>bell", "category": "handwriting", "variants": ["regular"]}, "Scope One": {"family": "Scope One", "category": "serif", "variants": ["regular"]}, "Seaweed Script": {"family": "Seaweed Sc<PERSON>t", "category": "display", "variants": ["regular"]}, "Secular One": {"family": "Secular One", "category": "sans-serif", "variants": ["regular"]}, "Sedan": {"family": "Sedan", "category": "serif", "variants": ["italic", "regular"]}, "Sedan SC": {"family": "Sedan SC", "category": "serif", "variants": ["regular"]}, "Sedgwick Ave": {"family": "Sedgwick Ave", "category": "handwriting", "variants": ["regular"]}, "Sedgwick Ave Display": {"family": "Sedgwick Ave Display", "category": "handwriting", "variants": ["regular"]}, "Sen": {"family": "<PERSON>", "category": "sans-serif", "variants": ["500", "600", "700", "800", "regular"]}, "Send Flowers": {"family": "Send Flowers", "category": "handwriting", "variants": ["regular"]}, "Sevillana": {"family": "Sevillana", "category": "display", "variants": ["regular"]}, "Seymour One": {"family": "<PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Shadows Into Light": {"family": "Shadows Into Light", "category": "handwriting", "variants": ["regular"]}, "Shadows Into Light Two": {"family": "Shadows Into Light Two", "category": "handwriting", "variants": ["regular"]}, "Shalimar": {"family": "<PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Shantell Sans": {"family": "Shantell Sans", "category": "display", "variants": ["300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "italic", "regular"]}, "Shanti": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Share": {"family": "Share", "category": "sans-serif", "variants": ["700", "700italic", "italic", "regular"]}, "Share Tech": {"family": "Share Tech", "category": "sans-serif", "variants": ["regular"]}, "Share Tech Mono": {"family": "Share Tech Mono", "category": "monospace", "variants": ["regular"]}, "Shippori Antique": {"family": "Shippori Antique", "category": "sans-serif", "variants": ["regular"]}, "Shippori Antique B1": {"family": "Shippori Antique B1", "category": "sans-serif", "variants": ["regular"]}, "Shippori Mincho": {"family": "Ship<PERSON><PERSON>", "category": "serif", "variants": ["500", "600", "700", "800", "regular"]}, "Shippori Mincho B1": {"family": "Shippori Mincho B1", "category": "serif", "variants": ["500", "600", "700", "800", "regular"]}, "Shizuru": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Shojumaru": {"family": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Short Stack": {"family": "<PERSON>", "category": "handwriting", "variants": ["regular"]}, "Shrikhand": {"family": "Shrikhand", "category": "display", "variants": ["regular"]}, "Siemreap": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Sigmar": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Sigmar One": {"family": "Sigmar One", "category": "display", "variants": ["regular"]}, "Signika": {"family": "Sign<PERSON>", "category": "sans-serif", "variants": ["300", "500", "600", "700", "regular"]}, "Signika Negative": {"family": "Signika Negative", "category": "sans-serif", "variants": ["300", "500", "600", "700", "regular"]}, "Silkscreen": {"family": "Silkscreen", "category": "display", "variants": ["700", "regular"]}, "Simonetta": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["900", "900italic", "italic", "regular"]}, "Single Day": {"family": "Single Day", "category": "display", "variants": ["regular"]}, "Sintony": {"family": "Sintony", "category": "sans-serif", "variants": ["700", "regular"]}, "Sirin Stencil": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Six Caps": {"family": "Six Caps", "category": "sans-serif", "variants": ["regular"]}, "Sixtyfour": {"family": "<PERSON><PERSON><PERSON>ur", "category": "monospace", "variants": ["regular"]}, "Sixtyfour Convergence": {"family": "Sixtyfour Convergence", "category": "monospace", "variants": ["regular"]}, "Skranji": {"family": "Skra<PERSON>", "category": "display", "variants": ["700", "regular"]}, "Slabo 13px": {"family": "Slabo 13px", "category": "serif", "variants": ["regular"]}, "Slabo 27px": {"family": "Slabo 27px", "category": "serif", "variants": ["regular"]}, "Slackey": {"family": "Slackey", "category": "display", "variants": ["regular"]}, "Slackside One": {"family": "Slackside One", "category": "handwriting", "variants": ["regular"]}, "Smokum": {"family": "Smokum", "category": "display", "variants": ["regular"]}, "Smooch": {"family": "<PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Smooch Sans": {"family": "Smooch Sans", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Smythe": {"family": "<PERSON><PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Sniglet": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "display", "variants": ["800", "regular"]}, "Snippet": {"family": "Snippet", "category": "sans-serif", "variants": ["regular"]}, "Snowburst One": {"family": "Snowburst One", "category": "display", "variants": ["regular"]}, "Sofadi One": {"family": "Sofadi One", "category": "display", "variants": ["regular"]}, "Sofia": {"family": "Sofia", "category": "handwriting", "variants": ["regular"]}, "Sofia Sans": {"family": "Sofia Sans", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Sofia Sans Condensed": {"family": "Sofia Sans Condensed", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Sofia Sans Extra Condensed": {"family": "Sofia Sans Extra Condensed", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Sofia Sans Semi Condensed": {"family": "Sofia Sans Semi Condensed", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Solitreo": {"family": "Solitreo", "category": "handwriting", "variants": ["regular"]}, "Solway": {"family": "Solway", "category": "serif", "variants": ["300", "500", "700", "800", "regular"]}, "Sometype Mono": {"family": "Sometype Mono", "category": "monospace", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Song Myung": {"family": "<PERSON>", "category": "serif", "variants": ["regular"]}, "Sono": {"family": "Sono", "category": "sans-serif", "variants": ["200", "300", "500", "600", "700", "800", "regular"]}, "Sonsie One": {"family": "<PERSON>ie One", "category": "display", "variants": ["regular"]}, "Sora": {"family": "<PERSON>ra", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "regular"]}, "Sorts Mill Goudy": {"family": "Sorts Mill Goudy", "category": "serif", "variants": ["italic", "regular"]}, "Sour Gummy": {"family": "Sour Gummy", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Source Code Pro": {"family": "Source Code Pro", "category": "monospace", "variants": ["200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Source Sans 3": {"family": "Source Sans 3", "category": "sans-serif", "variants": ["200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Source Serif 4": {"family": "Source Serif 4", "category": "serif", "variants": ["200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Space Grotesk": {"family": "Space Grotesk", "category": "sans-serif", "variants": ["300", "500", "600", "700", "regular"]}, "Space Mono": {"family": "Space Mono", "category": "monospace", "variants": ["700", "700italic", "italic", "regular"]}, "Special Elite": {"family": "Special Elite", "category": "display", "variants": ["regular"]}, "Spectral": {"family": "Spectral", "category": "serif", "variants": ["200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "italic", "regular"]}, "Spectral SC": {"family": "Spectral SC", "category": "serif", "variants": ["200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "italic", "regular"]}, "Spicy Rice": {"family": "Spicy Rice", "category": "display", "variants": ["regular"]}, "Spinnaker": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Spirax": {"family": "Spirax", "category": "display", "variants": ["regular"]}, "Splash": {"family": "Splash", "category": "handwriting", "variants": ["regular"]}, "Spline Sans": {"family": "Spline Sans", "category": "sans-serif", "variants": ["300", "500", "600", "700", "regular"]}, "Spline Sans Mono": {"family": "Spline Sans Mono", "category": "monospace", "variants": ["300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Squada One": {"family": "Squada One", "category": "display", "variants": ["regular"]}, "Square Peg": {"family": "Square Peg", "category": "handwriting", "variants": ["regular"]}, "Sree Krushnadevaraya": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Sriracha": {"family": "<PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Srisakdi": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "display", "variants": ["700", "regular"]}, "Staatliches": {"family": "Staatliches", "category": "display", "variants": ["regular"]}, "Stalemate": {"family": "Stalemate", "category": "handwriting", "variants": ["regular"]}, "Stalinist One": {"family": "Stalinist One", "category": "display", "variants": ["regular"]}, "Stardos Stencil": {"family": "Stardos Stencil", "category": "display", "variants": ["700", "regular"]}, "Stick": {"family": "Stick", "category": "sans-serif", "variants": ["regular"]}, "Stick No Bills": {"family": "Stick No Bills", "category": "sans-serif", "variants": ["200", "300", "500", "600", "700", "800", "regular"]}, "Stint Ultra Condensed": {"family": "Stint Ultra Condensed", "category": "serif", "variants": ["regular"]}, "Stint Ultra Expanded": {"family": "Stint Ultra Expanded", "category": "serif", "variants": ["regular"]}, "Stoke": {"family": "Stoke", "category": "serif", "variants": ["300", "regular"]}, "Strait": {"family": "Strait", "category": "sans-serif", "variants": ["regular"]}, "Style Script": {"family": "<PERSON> Script", "category": "handwriting", "variants": ["regular"]}, "Stylish": {"family": "Stylish", "category": "sans-serif", "variants": ["regular"]}, "Sue Ellen Francisco": {"family": "<PERSON>", "category": "handwriting", "variants": ["regular"]}, "Suez One": {"family": "Suez One", "category": "serif", "variants": ["regular"]}, "Sulphur Point": {"family": "Sulphur Point", "category": "sans-serif", "variants": ["300", "700", "regular"]}, "Sumana": {"family": "Sumana", "category": "serif", "variants": ["700", "regular"]}, "Sunflower": {"family": "Sunflower", "category": "sans-serif", "variants": ["300", "500", "700"]}, "Sunshiney": {"family": "<PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Supermercado One": {"family": "Supermercado One", "category": "display", "variants": ["regular"]}, "Sura": {"family": "Sura", "category": "serif", "variants": ["700", "regular"]}, "Suranna": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Suravaram": {"family": "<PERSON><PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Suwannaphum": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "serif", "variants": ["100", "300", "700", "900", "regular"]}, "Swanky and Moo Moo": {"family": "<PERSON><PERSON> and <PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Syncopate": {"family": "Syncopate", "category": "sans-serif", "variants": ["700", "regular"]}, "Syne": {"family": "Syne", "category": "sans-serif", "variants": ["500", "600", "700", "800", "regular"]}, "Syne Mono": {"family": "Syne Mono", "category": "monospace", "variants": ["regular"]}, "Syne Tactile": {"family": "Syne Tactile", "category": "display", "variants": ["regular"]}, "Tac One": {"family": "Tac One", "category": "sans-serif", "variants": ["regular"]}, "Tai Heritage Pro": {"family": "Tai Heritage Pro", "category": "serif", "variants": ["700", "regular"]}, "Tajawal": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["200", "300", "500", "700", "800", "900", "regular"]}, "Tangerine": {"family": "Tangerine", "category": "handwriting", "variants": ["700", "regular"]}, "Tapestry": {"family": "Tapestry", "category": "handwriting", "variants": ["regular"]}, "Taprom": {"family": "Taprom", "category": "display", "variants": ["regular"]}, "Tauri": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Taviraj": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Teachers": {"family": "Teachers", "category": "sans-serif", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "italic", "regular"]}, "Teko": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["300", "500", "600", "700", "regular"]}, "Tektur": {"family": "Tektur", "category": "display", "variants": ["500", "600", "700", "800", "900", "regular"]}, "Telex": {"family": "Telex", "category": "sans-serif", "variants": ["regular"]}, "Tenali Ramakrishna": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Tenor Sans": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Text Me One": {"family": "Text Me One", "category": "sans-serif", "variants": ["regular"]}, "Texturina": {"family": "<PERSON><PERSON><PERSON>", "category": "serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Thasadith": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["700", "700italic", "italic", "regular"]}, "The Girl Next Door": {"family": "The Girl Next Door", "category": "handwriting", "variants": ["regular"]}, "The Nautigal": {"family": "The Nautigal", "category": "handwriting", "variants": ["700", "regular"]}, "Tienne": {"family": "Tienne", "category": "serif", "variants": ["700", "900", "regular"]}, "Tillana": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["500", "600", "700", "800", "regular"]}, "Tilt Neon": {"family": "Tilt Neon", "category": "display", "variants": ["regular"]}, "Tilt Prism": {"family": "Tilt Prism", "category": "display", "variants": ["regular"]}, "Tilt Warp": {"family": "Tilt Warp", "category": "display", "variants": ["regular"]}, "Timmana": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Tinos": {"family": "Tin<PERSON>", "category": "serif", "variants": ["700", "700italic", "italic", "regular"]}, "Tiny5": {"family": "Tiny5", "category": "sans-serif", "variants": ["regular"]}, "Tiro Bangla": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["italic", "regular"]}, "Tiro Devanagari Hindi": {"family": "Tiro Devanagari Hindi", "category": "serif", "variants": ["italic", "regular"]}, "Tiro Devanagari Marathi": {"family": "Tiro Devanagari Marathi", "category": "serif", "variants": ["italic", "regular"]}, "Tiro Devanagari Sanskrit": {"family": "T<PERSON> Dev<PERSON>gari Sanskrit", "category": "serif", "variants": ["italic", "regular"]}, "Tiro Gurmukhi": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["italic", "regular"]}, "Tiro Kannada": {"family": "Tiro Kannada", "category": "serif", "variants": ["italic", "regular"]}, "Tiro Tamil": {"family": "Tiro Tamil", "category": "serif", "variants": ["italic", "regular"]}, "Tiro Telugu": {"family": "Tiro Telugu", "category": "serif", "variants": ["italic", "regular"]}, "Titan One": {"family": "Titan One", "category": "display", "variants": ["regular"]}, "Titillium Web": {"family": "Titillium Web", "category": "sans-serif", "variants": ["200", "200italic", "300", "300italic", "600", "600italic", "700", "700italic", "900", "italic", "regular"]}, "Tomorrow": {"family": "Tomorrow", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Tourney": {"family": "Tourney", "category": "display", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Trade Winds": {"family": "Trade Winds", "category": "display", "variants": ["regular"]}, "Train One": {"family": "Train One", "category": "display", "variants": ["regular"]}, "Trirong": {"family": "Trirong", "category": "serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Trispace": {"family": "Trispace", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "regular"]}, "Trocchi": {"family": "<PERSON><PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Trochut": {"family": "Trochut", "category": "display", "variants": ["700", "italic", "regular"]}, "Truculenta": {"family": "Truculenta", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Trykker": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Tsukimi Rounded": {"family": "<PERSON><PERSON><PERSON><PERSON> Rounded", "category": "sans-serif", "variants": ["300", "500", "600", "700", "regular"]}, "Tulpen One": {"family": "Tulpen One", "category": "display", "variants": ["regular"]}, "Turret Road": {"family": "Turret Road", "category": "display", "variants": ["200", "300", "500", "700", "800", "regular"]}, "Twinkle Star": {"family": "Twinkle Star", "category": "handwriting", "variants": ["regular"]}, "Ubuntu": {"family": "Ubuntu", "category": "sans-serif", "variants": ["300", "300italic", "500", "500italic", "700", "700italic", "italic", "regular"]}, "Ubuntu Condensed": {"family": "Ubuntu Condensed", "category": "sans-serif", "variants": ["regular"]}, "Ubuntu Mono": {"family": "Ubuntu Mono", "category": "monospace", "variants": ["700", "700italic", "italic", "regular"]}, "Ubuntu Sans": {"family": "Ubuntu Sans", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "italic", "regular"]}, "Ubuntu Sans Mono": {"family": "Ubuntu Sans Mono", "category": "monospace", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Uchen": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Ultra": {"family": "Ultra", "category": "serif", "variants": ["regular"]}, "Unbounded": {"family": "Unbounded", "category": "sans-serif", "variants": ["200", "300", "500", "600", "700", "800", "900", "regular"]}, "Uncial Antiqua": {"family": "Uncial Antiqua", "category": "display", "variants": ["regular"]}, "Underdog": {"family": "Underdog", "category": "display", "variants": ["regular"]}, "Unica One": {"family": "Unica One", "category": "display", "variants": ["regular"]}, "UnifrakturCook": {"family": "UnifrakturCook", "category": "display", "variants": ["700"]}, "UnifrakturMaguntia": {"family": "UnifrakturMaguntia", "category": "display", "variants": ["regular"]}, "Unkempt": {"family": "Unkempt", "category": "display", "variants": ["700", "regular"]}, "Unlock": {"family": "Unlock", "category": "display", "variants": ["regular"]}, "Unna": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["700", "700italic", "italic", "regular"]}, "Updock": {"family": "Updock", "category": "handwriting", "variants": ["regular"]}, "Urbanist": {"family": "Urbanist", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "VT323": {"family": "VT323", "category": "monospace", "variants": ["regular"]}, "Vampiro One": {"family": "Vampiro One", "category": "display", "variants": ["regular"]}, "Varela": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Varela Round": {"family": "Varela Round", "category": "sans-serif", "variants": ["regular"]}, "Varta": {"family": "Varta", "category": "sans-serif", "variants": ["300", "500", "600", "700", "regular"]}, "Vast Shadow": {"family": "Vast Shadow", "category": "serif", "variants": ["regular"]}, "Vazirmatn": {"family": "Vazirmatn", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Vesper Libre": {"family": "Vesper <PERSON>", "category": "serif", "variants": ["500", "700", "900", "regular"]}, "Viaoda Libre": {"family": "Viaoda Libre", "category": "display", "variants": ["regular"]}, "Vibes": {"family": "Vibes", "category": "display", "variants": ["regular"]}, "Vibur": {"family": "V<PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Victor Mono": {"family": "<PERSON>", "category": "monospace", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Vidaloka": {"family": "Vidaloka", "category": "serif", "variants": ["regular"]}, "Viga": {"family": "Viga", "category": "sans-serif", "variants": ["regular"]}, "Vina Sans": {"family": "Vina Sans", "category": "display", "variants": ["regular"]}, "Voces": {"family": "Voces", "category": "sans-serif", "variants": ["regular"]}, "Volkhov": {"family": "<PERSON><PERSON><PERSON>", "category": "serif", "variants": ["700", "700italic", "italic", "regular"]}, "Vollkorn": {"family": "<PERSON><PERSON><PERSON>", "category": "serif", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Vollkorn SC": {"family": "Vollkorn SC", "category": "serif", "variants": ["600", "700", "900", "regular"]}, "Voltaire": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Vujahday Script": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Waiting for the Sunrise": {"family": "Waiting for the Sunrise", "category": "handwriting", "variants": ["regular"]}, "Wallpoet": {"family": "Wallpoet", "category": "display", "variants": ["regular"]}, "Walter Turncoat": {"family": "<PERSON>", "category": "handwriting", "variants": ["regular"]}, "Warnes": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Water Brush": {"family": "Water Brush", "category": "handwriting", "variants": ["regular"]}, "Waterfall": {"family": "Waterfall", "category": "handwriting", "variants": ["regular"]}, "Wavefont": {"family": "Wavefont", "category": "display", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Wellfleet": {"family": "Wellfleet", "category": "serif", "variants": ["regular"]}, "Wendy One": {"family": "<PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Whisper": {"family": "Whisper", "category": "handwriting", "variants": ["regular"]}, "WindSong": {"family": "WindSong", "category": "handwriting", "variants": ["500", "regular"]}, "Wire One": {"family": "Wire One", "category": "sans-serif", "variants": ["regular"]}, "Wittgenstein": {"family": "Wittgenstein", "category": "serif", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Wix Madefor Display": {"family": "Wix Madefor Display", "category": "sans-serif", "variants": ["500", "600", "700", "800", "regular"]}, "Wix Madefor Text": {"family": "Wix Madefor Text", "category": "sans-serif", "variants": ["500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "italic", "regular"]}, "Work Sans": {"family": "Work Sans", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Workbench": {"family": "Workbench", "category": "monospace", "variants": ["regular"]}, "Xanh Mono": {"family": "<PERSON><PERSON><PERSON>", "category": "monospace", "variants": ["italic", "regular"]}, "Yaldevi": {"family": "<PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["200", "300", "500", "600", "700", "regular"]}, "Yanone Kaffeesatz": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["200", "300", "500", "600", "700", "regular"]}, "Yantramanav": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "300", "500", "700", "900", "regular"]}, "Yarndings 12": {"family": "Yarndings 12", "category": "display", "variants": ["regular"]}, "Yarndings 12 Charted": {"family": "Yarndings 12 Charted", "category": "display", "variants": ["regular"]}, "Yarndings 20": {"family": "Yarndings 20", "category": "display", "variants": ["regular"]}, "Yarndings 20 Charted": {"family": "Yarndings 20 Charted", "category": "display", "variants": ["regular"]}, "Yatra One": {"family": "Yatra One", "category": "display", "variants": ["regular"]}, "Yellowtail": {"family": "Yellowtail", "category": "handwriting", "variants": ["regular"]}, "Yeon Sung": {"family": "<PERSON><PERSON>", "category": "display", "variants": ["regular"]}, "Yeseva One": {"family": "Yeseva One", "category": "display", "variants": ["regular"]}, "Yesteryear": {"family": "Yesteryear", "category": "handwriting", "variants": ["regular"]}, "Yomogi": {"family": "<PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Young Serif": {"family": "<PERSON>", "category": "serif", "variants": ["regular"]}, "Yrsa": {"family": "Yrsa", "category": "serif", "variants": ["300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Ysabeau": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Ysabeau Infant": {"family": "<PERSON><PERSON><PERSON><PERSON>", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Ysabeau Office": {"family": "Ysabeau Office", "category": "sans-serif", "variants": ["100", "100italic", "200", "200italic", "300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "800", "800italic", "900", "900italic", "italic", "regular"]}, "Ysabeau SC": {"family": "Ysabeau SC", "category": "sans-serif", "variants": ["100", "200", "300", "500", "600", "700", "800", "900", "regular"]}, "Yuji Boku": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Yuji Hentaigana Akari": {"family": "<PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Yuji Hentaigana Akebono": {"family": "<PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Yuji Mai": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Yuji Syuku": {"family": "<PERSON><PERSON>", "category": "serif", "variants": ["regular"]}, "Yusei Magic": {"family": "Yusei Magic", "category": "sans-serif", "variants": ["regular"]}, "ZCOOL KuaiLe": {"family": "ZCOOL KuaiLe", "category": "sans-serif", "variants": ["regular"]}, "ZCOOL QingKe HuangYou": {"family": "ZCOOL QingKe HuangYou", "category": "sans-serif", "variants": ["regular"]}, "ZCOOL XiaoWei": {"family": "ZCOOL XiaoWei", "category": "sans-serif", "variants": ["regular"]}, "Zain": {"family": "<PERSON><PERSON>", "category": "sans-serif", "variants": ["200", "300", "300italic", "700", "800", "900", "italic", "regular"]}, "Zen Antique": {"family": "Zen Antique", "category": "serif", "variants": ["regular"]}, "Zen Antique Soft": {"family": "Zen Antique Soft", "category": "serif", "variants": ["regular"]}, "Zen Dots": {"family": "Zen Dots", "category": "display", "variants": ["regular"]}, "Zen Kaku Gothic Antique": {"family": "Zen Kaku Gothic Antique", "category": "sans-serif", "variants": ["300", "500", "700", "900", "regular"]}, "Zen Kaku Gothic New": {"family": "Zen Kaku Gothic New", "category": "sans-serif", "variants": ["300", "500", "700", "900", "regular"]}, "Zen Kurenaido": {"family": "<PERSON>", "category": "sans-serif", "variants": ["regular"]}, "Zen Loop": {"family": "Zen Loop", "category": "display", "variants": ["italic", "regular"]}, "Zen Maru Gothic": {"family": "Zen Maru Gothic", "category": "sans-serif", "variants": ["300", "500", "700", "900", "regular"]}, "Zen Old Mincho": {"family": "Zen Old Mincho", "category": "serif", "variants": ["500", "600", "700", "900", "regular"]}, "Zen Tokyo Zoo": {"family": "Zen Tokyo Zoo", "category": "display", "variants": ["regular"]}, "Zeyada": {"family": "<PERSON><PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Zhi Mang Xing": {"family": "<PERSON><PERSON>", "category": "handwriting", "variants": ["regular"]}, "Zilla Slab": {"family": "Zilla Slab", "category": "serif", "variants": ["300", "300italic", "500", "500italic", "600", "600italic", "700", "700italic", "italic", "regular"]}, "Zilla Slab Highlight": {"family": "Zilla Slab Highlight", "category": "serif", "variants": ["700", "regular"]}}, "order": {"popularity": ["Roboto", "Open Sans", "Noto Sans JP", "Montserrat", "<PERSON><PERSON><PERSON>", "Inter", "<PERSON><PERSON>", "Roboto Condensed", "Material Icons", "<PERSON>", "Roboto Mono", "Noto Sans", "<PERSON><PERSON><PERSON>", "Nunito Sans", "Nunito", "Playfair Display", "<PERSON><PERSON><PERSON>", "Ubuntu", "Noto Sans KR", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON> Slab", "<PERSON><PERSON><PERSON>", "PT Sans", "Work Sans", "<PERSON><PERSON>", "DM Sans", "<PERSON><PERSON>", "Noto Sans TC", "Fira Sans", "Manrope", "<PERSON>", "Quicksand", "<PERSON><PERSON><PERSON>", "Material Symbols Outlined", "IBM Plex Sans", "Titillium Web", "<PERSON><PERSON>", "PT Serif", "<PERSON><PERSON>", "<PERSON><PERSON>", "Outfit", "Material Icons Outlined", "Nanum Gothic", "Noto Color Emoji", "Libre Franklin", "Schibsted Grotesk", "<PERSON><PERSON>", "Hind Siliguri", "Inconsolata", "Libre Baskerville", "<PERSON><PERSON>", "Figtree", "<PERSON>", "IBM Plex Mono", "<PERSON><PERSON>", "<PERSON>ript", "<PERSON><PERSON>", "Space Grotesk", "<PERSON><PERSON><PERSON>", "Noto Sans SC", "Prompt", "Archivo", "Source Code Pro", "Ari<PERSON>", "EB Garamond", "<PERSON><PERSON>", "Source Sans 3", "Cabin", "<PERSON> Condensed", "Cairo", "Exo 2", "Crimson Text", "PT Sans Narrow", "Bitter", "Plus Jakarta Sans", "Assistant", "Oxanium", "Hind", "Oxygen", "Material Icons Round", "M PLUS Rounded 1c", "<PERSON>", "Overpass", "Pacifico", "Slabo 27px", "Signika Negative", "Nanum Gothic Coding", "Caveat", "Public Sans", "<PERSON><PERSON> Condensed", "Red Hat Display", "Fjalla One", "Arvo", "Lexend", "Archivo Black", "<PERSON><PERSON>", "Lobster", "<PERSON><PERSON><PERSON>", "Roboto Flex", "Comfortaa", "Sofia Sans", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON> G<PERSON>mond", "Varela Round", "Material Icons Sharp", "Noto Sans Arabic", "<PERSON>ra", "<PERSON><PERSON>", "Material Symbols Rounded", "Maven <PERSON>", "Asap", "<PERSON> Semi Condensed", "Rowdies", "Abril Fatface", "Play", "IBM Plex Sans Arabic", "Noto Sans HK", "Shadows Into Light", "Alfa Slab One", "Zilla Slab", "Urbanist", "Fira Sans Condensed", "Material Icons Two Tone", "IBM Plex Serif", "M PLUS 1p", "<PERSON><PERSON>", "DM Serif <PERSON>lay", "Almarai", "Merriweather Sans", "Permanent Marker", "Quest<PERSON>", "Indie Flower", "Sign<PERSON>", "Source Serif 4", "Be Vietnam Pro", "<PERSON>", "Noto Sans Thai", "Inter Tight", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Passion One", "Domine", "Righteous", "<PERSON><PERSON><PERSON>", "Cormorant", "Orbitron", "<PERSON><PERSON>", "Smooch Sans", "Archivo Narrow", "Catamaran", "Hind Madurai", "<PERSON><PERSON>", "Rubik Mono One", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Exo", "Lexend Deca", "Sarabun", "Acme", "Great Vibes", "Amatic SC", "<PERSON><PERSON>", "<PERSON>", "Noto Sans Display", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Noto Sans Bengali", "Concert One", "Libre Caslon Text", "ABeeZee", "Afacad Flux", "Spectral", "League Spartan", "Zen Kaku Gothic New", "Chivo", "Alegreya Sans", "Press Start 2P", "Patua One", "Montserrat Alternates", "Prata", "Asap Condensed", "Antic Slab", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Ka<PERSON>", "Noto Serif KR", "Bangers", "Alata", "Red Hat Text", "Tin<PERSON>", "Yatra One", "Russo One", "Silkscreen", "JetBrains Mono", "<PERSON><PERSON>", "Noto Serif TC", "Spicy Rice", "<PERSON><PERSON>", "Yellowtail", "<PERSON><PERSON><PERSON>", "PT Sans Caption", "Roboto Serif", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Lobster Two", "IBM Plex Sans Condensed", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Crimson Pro", "Readex Pro", "Crete Round", "Gruppo", "Sawarabi Gothic", "Gloria Hallelujah", "Noticia Text", "<PERSON>is <PERSON>", "<PERSON>", "Sacramento", "Philosopher", "Encode Sans", "Monda", "Vazirmatn", "Old Standard TT", "<PERSON><PERSON><PERSON>", "Gothic A1", "Ship<PERSON><PERSON>", "VT323", "DM Serif Text", "News Cycle", "Advent Pro", "Luckiest Guy", "Didact Gothic", "Cantarell", "<PERSON><PERSON>", "Space Mono", "Ubuntu Condensed", "Dela Gothic One", "Encode Sans Condensed", "Libre Barcode 39", "<PERSON><PERSON>", "Fira Sans Extra Condensed", "Zen Maru Gothic", "Rokkitt", "Neucha", "Noto Sans Tamil", "Sofia", "Khand", "Courier Prime", "<PERSON><PERSON>", "Hammersmith One", "<PERSON><PERSON>", "Staatliches", "Berkshire Swash", "El Messiri", "Special Elite", "Suez One", "<PERSON><PERSON>", "Inria Sans", "Noto Sans Mono", "Poiret One", "Kumbh Sans", "Neuton", "Quattrocento Sans", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Baskervville", "Commissioner", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Literata", "<PERSON><PERSON>", "Yeseva One", "Unbounded", "Tangerine", "<PERSON><PERSON>", "Geologica", "Bungee", "<PERSON><PERSON>", "DM Mono", "Atkinson Hyperlegible", "Pathway Gothic One", "Hanken Grotesk", "League Gothic", "Cuprum", "Baloo 2", "<PERSON>", "Parisienne", "Quattrocento", "Paytone One", "Ubuntu Mono", "Architects Daughter", "<PERSON><PERSON>", "PT Mono", "<PERSON><PERSON><PERSON>", "Instrument Sans", "Audiowide", "Sorts Mill Goudy", "Gelasio", "<PERSON><PERSON><PERSON>", "Libre Bo<PERSON>i", "Antonio", "<PERSON><PERSON>", "Playfair Display SC", "Baloo Bhaijaan 2", "BIZ UDPGothic", "Vidaloka", "Unica One", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Squada One", "Material Symbols Sharp", "<PERSON><PERSON><PERSON>", "Pirata One", "Comic Neue", "<PERSON>", "Blinker", "Homemade Apple", "<PERSON>", "Epilogue", "<PERSON><PERSON> Display", "Syne", "Bowlby One", "Arsenal", "Handlee", "<PERSON><PERSON><PERSON>", "Six Caps", "<PERSON><PERSON>", "Noto Sans Devanagari", "Saira Semi Condensed", "Sofia Sans Condensed", "<PERSON>", "Forum", "Wix Madefor Text", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Actor", "Ultra", "Radio Canada", "Quantico", "<PERSON><PERSON><PERSON>", "Creepster", "Newsreader", "Hind Vadodara", "<PERSON><PERSON>", "Bricolage Grotesque", "Playball", "Istok Web", "<PERSON><PERSON><PERSON>", "Share Tech Mono", "Ropa Sans", "Black Ops One", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Playfair", "Zen Old Mincho", "Rammetto One", "<PERSON><PERSON><PERSON>", "STIX Two Text", "<PERSON><PERSON><PERSON>", "Baloo Paaji 2", "Noto Serif SC", "Big Shoulders Display", "Titan One", "Fira Mono", "Cousine", "Alegreya Sans SC", "Changa One", "Noto Sans Malayalam", "Mandali", "Alexandria", "Monoton", "<PERSON><PERSON>", "Damion", "Cormorant Infant", "Rock Salt", "<PERSON><PERSON><PERSON> Condensed", "Viga", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Lexend Giga", "Pangolin", "<PERSON><PERSON><PERSON>", "Electrolize", "Black Han Sans", "<PERSON><PERSON><PERSON>", "GFS <PERSON>ot", "<PERSON><PERSON>", "Julius Sans One", "Calistoga", "<PERSON><PERSON><PERSON>", "Mr <PERSON><PERSON><PERSON>", "Ysabeau SC", "Alef", "Economica", "Anonymous Pro", "<PERSON><PERSON>", "Secular One", "Leckerli One", "Pontano Sans", "<PERSON><PERSON>", "<PERSON><PERSON>", "Italianno", "Fugaz One", "Anton SC", "<PERSON><PERSON>", "Noto Sans Hebrew", "Shrikhand", "<PERSON>nd<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Caveat Brush", "Ara<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Lusitana", "Aclonica", "<PERSON><PERSON><PERSON>", "Nothing You Could Do", "BenchNine", "G<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Holtwood One SC", "Basic", "Chewy", "<PERSON><PERSON>", "Cinzel Decorative", "<PERSON><PERSON>", "Noto Sans Symbols", "<PERSON><PERSON>", "<PERSON><PERSON>", "Fira Code", "Potta One", "Charm", "<PERSON><PERSON>", "Agbalumo", "Syncopate", "Martel Sans", "Averia Serif <PERSON>", "M PLUS 1", "<PERSON><PERSON><PERSON>", "Days One", "IBM Plex Sans Thai", "<PERSON>", "Lemonada", "Coda", "<PERSON><PERSON>", "<PERSON>", "Petrona", "<PERSON><PERSON>", "<PERSON><PERSON>", "Go<PERSON> Hand", "Shippori Mincho B1", "Seaweed Sc<PERSON>t", "Quintessential", "Hachi Maru Pop", "<PERSON>rma", "<PERSON><PERSON><PERSON>", "Covered By Your Grace", "Balsamiq Sans", "<PERSON><PERSON> the <PERSON>", "<PERSON><PERSON>", "<PERSON>", "Lemon", "<PERSON><PERSON><PERSON>", "Rochester", "Darker Grotesque", "Armata", "<PERSON>nd <PERSON>", "K<PERSON><PERSON>", "Racing Sans One", "Nanum Brush Script", "Athiti", "Yrsa", "<PERSON><PERSON><PERSON>", "Major <PERSON><PERSON>", "Just Another Hand", "<PERSON><PERSON><PERSON>", "Noto Sans Telugu", "Noto Nastaliq Urdu", "Boogaloo", "Mrs <PERSON>", "<PERSON><PERSON><PERSON>", "Saira Extra Condensed", "Eater", "<PERSON><PERSON>", "PT Serif Caption", "Candal", "<PERSON><PERSON><PERSON>", "Scada", "Marcellus SC", "Michroma", "<PERSON><PERSON><PERSON>", "Italiana", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "La Belle Aurore", "Graduate", "Caudex", "<PERSON><PERSON>", "Cutive Mono", "Averia Libre", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shadows Into Light Two", "Glory", "Zen Kaku Gothic Antique", "Rozha One", "K2D", "Grandstander", "Nixie One", "Ovo", "Georama", "<PERSON><PERSON>", "Sintony", "Krona One", "<PERSON>ef", "Salsa", "Brygada 1918", "<PERSON><PERSON><PERSON>", "Arizonia", "<PERSON>", "Overpass Mono", "Marmelad", "Antic", "Nobile", "Sofia Sans Extra Condensed", "Cantata One", "Cedarville Cursive", "<PERSON><PERSON>", "Ibarra Real Nova", "Big Shoulders Text", "Alumni Sans", "Annie Use Your Telescope", "Zilla Slab Highlight", "<PERSON><PERSON>", "Odibee Sans", "Bowlby One SC", "Noto Sans Kannada", "Limelight", "Bellefair", "Trirong", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "BioRhyme", "<PERSON> <PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Oooh Baby", "Overlock", "Black And White Picture", "Metrophobic", "<PERSON><PERSON><PERSON><PERSON>", "RocknRoll One", "Yesteryear", "<PERSON><PERSON>", "Belanosima", "Mali", "Castoro", "<PERSON><PERSON><PERSON>", "Klee One", "Telex", "Knewave", "M PLUS 2", "Lustria", "Chonburi", "<PERSON><PERSON><PERSON>", "Montserrat Subrayada", "Fahkwang", "Jomhur<PERSON>", "Contrail One", "<PERSON><PERSON>", "Poly", "Petit Formal Script", "Inria Serif", "Jersey 10", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Average Sans", "<PERSON><PERSON>", "<PERSON><PERSON>", "Cambay", "Love Ya Like A Sister", "Rambla", "Coming Soon", "Irish Grover", "Grand Hotel", "Fresca", "<PERSON>en One", "Aboreto", "Gravitas One", "Baloo Da 2", "Wallpoet", "Rancho", "Oxygen Mono", "Proza Libre", "Tomorrow", "Zen Dots", "<PERSON><PERSON>", "Sofia Sans Semi Condensed", "Mochiy Pop One", "Bungee Inline", "<PERSON><PERSON><PERSON>", "Man<PERSON><PERSON>", "Sigmar One", "<PERSON> Script", "Rethink Sans", "Caladea", "Cormorant SC", "Norican", "Fjord One", "DotGothic16", "Alegreya SC", "<PERSON><PERSON><PERSON><PERSON>", "UnifrakturMaguntia", "Markazi Text", "BIZ UDGothic", "Share", "Encode Sans Semi Condensed", "<PERSON><PERSON><PERSON>", "Meddon", "<PERSON><PERSON><PERSON>", "Yusei Magic", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Call<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON>", "<PERSON> Doulaise", "<PERSON><PERSON><PERSON>", "Lexend Exa", "<PERSON><PERSON><PERSON>", "Rosario", "MuseoModerno", "<PERSON>bell", "IM Fell English", "<PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "Protest Revolution", "<PERSON><PERSON>", "B612", "<PERSON><PERSON><PERSON><PERSON>", "Wix Madefor Display", "Waiting for the Sunrise", "Magra", "<PERSON><PERSON><PERSON><PERSON>", "Spectral SC", "B612 Mono", "Cormorant Unicase", "Nova Square", "<PERSON><PERSON><PERSON>", "Reddit Sans", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Turret Road", "Spline Sans", "Noto Sans Georgian", "Bellota Text", "Bungee Shade", "Fanwood Text", "Pathway Extreme", "<PERSON><PERSON>", "Average", "Noto Sans Lao Looped", "<PERSON><PERSON>", "<PERSON><PERSON>", "Bona Nova SC", "Dawning of a New Day", "Goudy Bookletter 1911", "Noto Sans Meetei <PERSON>", "Tilt Neon", "Jockey One", "IBM Plex Sans KR", "Noto Sans Khmer", "Rasa", "<PERSON>", "IBM Plex Sans JP", "Gloock", "Faster One", "Mouse Me<PERSON>irs", "<PERSON><PERSON><PERSON>", "Baloo Thambi 2", "<PERSON><PERSON>", "IM Fell English SC", "Syne Mono", "IM Fell DW Pica", "Vina Sans", "Tienne", "Alike", "Afacad", "Carrois Gothic", "Noto Serif HK", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Montagu Slab", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Quando", "REM", "<PERSON><PERSON>", "Nova Mono", "Prosto One", "<PERSON><PERSON><PERSON>", "Instrument Serif", "Familjen Grotesk", "Fuggles", "Sedgwick Ave", "<PERSON><PERSON>", "Stardos Stencil", "Capriola", "Inknut Antiqua", "Marvel", "Hurricane", "Bubblegum Sans", "Mountains of Christmas", "Kadwa", "<PERSON><PERSON><PERSON>", "Brawler", "Loved by the King", "ZCOOL XiaoWei", "<PERSON><PERSON>", "Ephesis", "Vesper <PERSON>", "Noto Sans Sinhala", "Macondo", "Rampart One", "Battambang", "Encode Sans Expanded", "<PERSON><PERSON>", "SUSE", "Anybody", "Chelsea Market", "<PERSON><PERSON><PERSON><PERSON>", "Expletus <PERSON>", "Sarina", "KoHo", "Goblin One", "<PERSON><PERSON><PERSON> Swashed", "Barr<PERSON>ci<PERSON>", "Fauna One", "Sunflower", "Baloo Tamma 2", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Esteban", "Megrim", "League Script", "Amethysta", "<PERSON>", "Baloo Chettan 2", "Mr <PERSON>", "Corinthia", "<PERSON>", "Cutive", "<PERSON>", "Croissant One", "Federo", "<PERSON><PERSON><PERSON>", "Qwitcher Grypen", "Geo", "<PERSON><PERSON>", "Fondamento", "McLaren", "Noto Sans Gujarati", "<PERSON><PERSON>", "Happy Monkey", "Solway", "Noto Sans Armenian", "<PERSON><PERSON>", "Skra<PERSON>", "Red Rose", "IM Fell Double Pica", "<PERSON><PERSON><PERSON><PERSON>", "Over the Rainbow", "Encode Sans Semi Expanded", "<PERSON>", "Jersey 15", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Tilt Warp", "Lekton", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Supermercado One", "<PERSON><PERSON>", "BIZ UDPMincho", "Andada Pro", "Notable", "<PERSON><PERSON> Script Swash Caps", "<PERSON>", "Coiny", "Pompiere", "Poller One", "Pixelify Sans", "ADLaM Display", "V<PERSON><PERSON>", "Libre Caslon Display", "<PERSON><PERSON><PERSON>", "ZCOOL QingKe HuangYou", "Imbue", "<PERSON><PERSON>", "<PERSON>", "Oregano", "Amarante", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>t", "<PERSON><PERSON><PERSON>", "Cambo", "Bellota", "<PERSON><PERSON>", "Host Grotesk", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Puritan", "Crafty Girls", "<PERSON>", "<PERSON><PERSON>", "Bokor", "<PERSON><PERSON>", "Iceberg", "NTR", "<PERSON><PERSON><PERSON><PERSON>", "Germania One", "Reddit <PERSON>o", "Flow Circular", "Give You Glory", "Noto Sans Myanmar", "Just Me Again Down Here", "Noto Sans Oriya", "Ubuntu Sans", "Averia Sans Libre", "Birthstone", "<PERSON><PERSON><PERSON>", "Vast Shadow", "Kalnia", "Cherry Cream Soda", "Libre Barcode 39 Text", "Slackey", "Fragment Mono", "Bakbak One", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Codystar", "Vollkorn SC", "Kdam Thmor Pro", "<PERSON><PERSON><PERSON>", "Bigshot One", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Road Rage", "<PERSON><PERSON><PERSON>", "Montez", "Libre Barcode 128", "Viaoda Libre", "Zen Antique Soft", "Charis SIL", "<PERSON>", "<PERSON><PERSON>", "Artifika", "<PERSON>", "Gluten", "Gug<PERSON>", "Wire One", "Slabo 13px", "Share Tech", "Original Surfer", "Orelega One", "Saira Stencil One", "Noto Sans Gurmukhi", "Noto Serif Malayalam", "Montaga", "Libre Barcode 39 Extended Text", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Recursive", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Square Peg", "Nova Round", "Bilbo Swash Caps", "<PERSON><PERSON><PERSON>", "ZCOOL KuaiLe", "BhuTuka Expanded One", "Unkempt", "<PERSON><PERSON><PERSON>", "Comforter Brush", "Redressed", "Baloo Bhai 2", "Macondo Swash Caps", "IBM Plex Sans Hebrew", "Bayon", "Sumana", "<PERSON><PERSON>", "Balthazar", "Tiro Devanagari Hindi", "<PERSON><PERSON><PERSON>", "Doppio One", "<PERSON><PERSON><PERSON>", "AR One Sans", "The Girl Next Door", "Reggae One", "Zen Antique", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Dokdo", "Prociono", "Radio Canada Big", "Shantell Sans", "Trade Winds", "Cairo Play", "Ari<PERSON>", "Modak", "The Nautigal", "<PERSON><PERSON>", "Mochiy Pop P One", "<PERSON><PERSON>", "Shippori Antique", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Headland One", "IM Fell French Canon", "<PERSON><PERSON>", "<PERSON>", "Rosarivo", "Homenaje", "<PERSON><PERSON><PERSON>", "Imprima", "Belgrano", "Convergence", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Nokor<PERSON>", "<PERSON><PERSON>", "Noto Serif <PERSON>", "MedievalSharp", "Anaheim", "Sulphur Point", "Kantumruy Pro", "Dynalight", "Cag<PERSON>stro", "Overlock SC", "<PERSON>ie One", "Piazzolla", "<PERSON><PERSON><PERSON>", "Alkalami", "<PERSON><PERSON><PERSON>", "Ledger", "Mate SC", "<PERSON><PERSON>", "Mogra", "Cantora One", "Modern Antiqua", "<PERSON><PERSON>", "Uncial Antiqua", "<PERSON><PERSON>", "Baloo Tammudu 2", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Raleway Dots", "Birthstone Bounce", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Crushed", "<PERSON><PERSON>k <PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Freehand", "Noto Sans Math", "Stick", "<PERSON><PERSON>", "<PERSON>", "Sofadi One", "Abyssinica SIL", "Libre Barcode 128 Text", "Noto Sans Buhid", "Sura", "Chau Philomene One", "Varta", "Life Savers", "Martian Mono", "<PERSON><PERSON>", "Eagle Lake", "Bona Nova", "Tektur", "Rhodium Libre", "Nosifer", "<PERSON><PERSON><PERSON>", "Red Hat Mono", "<PERSON><PERSON>", "Chivo Mono", "Sail", "Cherry Swash", "IM Fell DW Pica SC", "Manuale", "IM Fell Great Primer", "Delius Swash Caps", "Orienta", "<PERSON><PERSON> and <PERSON><PERSON>", "Flamenco", "<PERSON><PERSON>", "Nova Flat", "East Sea Dokdo", "Monomaniac One", "<PERSON>", "Scheherazade New", "Nova Slim", "<PERSON>", "WindSong", "<PERSON><PERSON>", "Strait", "DynaPuff", "Genos", "<PERSON><PERSON>", "Scope One", "<PERSON><PERSON><PERSON> Face", "Lexend Mega", "<PERSON><PERSON>", "Ceviche One", "<PERSON>ver", "<PERSON><PERSON><PERSON>", "Vampiro One", "Iceland", "J<PERSON>", "Edu SA Beginner", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Rationale", "Sevillana", "IM Fell Double Pica SC", "<PERSON><PERSON>", "Medula One", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Denk One", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Lovers Quarrel", "Redacted", "Delicious Handrawn", "<PERSON><PERSON><PERSON>", "Khmer", "Kat<PERSON><PERSON>", "Ken<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>i <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Stint Ultra Condensed", "Akatab", "<PERSON><PERSON><PERSON><PERSON>", "Bungee Spice", "<PERSON><PERSON>", "IM Fell Great Primer SC", "<PERSON><PERSON><PERSON>", "Noto Sans Wancho", "Grape Nuts", "<PERSON><PERSON><PERSON><PERSON>", "Licorice", "<PERSON><PERSON><PERSON>", "<PERSON>", "Mina", "Protest Strike", "Ysabeau Office", "<PERSON><PERSON><PERSON>", "IM Fell French Canon SC", "Voces", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Noto Sans Hatran", "Stalemate", "Akaya Telivigala", "Whisper", "Stylish", "<PERSON><PERSON><PERSON>", "Stick No Bills", "Ewert", "Playpen Sans", "Moon Dance", "<PERSON><PERSON><PERSON>", "Baloo Bhaina 2", "Nova Script", "Ha<PERSON><PERSON>", "Playwrite IN", "Playwrite VN", "Barrio", "Nova Oval", "<PERSON><PERSON>", "Metal Mania", "Passions Conflict", "Nova Cut", "<PERSON><PERSON>", "Tiny5", "Fustat", "Ranchers", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Kavoon", "<PERSON><PERSON>", "Fenix", "Alkatra", "<PERSON><PERSON>", "Keania One", "Kite One", "Underdog", "<PERSON><PERSON><PERSON><PERSON>", "Playwrite DE Grund", "<PERSON> Dressing", "UnifrakturCook", "Noto Sans Thai Looped", "Margarine", "Edu TAS Beginner", "Kulim Park", "Sedgwick Ave Display", "<PERSON><PERSON>", "<PERSON>", "Noto Sans Lao", "Libre Barcode 39 Extended", "Noto Sans Anatolian Hieroglyphs", "Braah One", "Chocolate Classical Sans", "Karantina", "<PERSON>e Font", "M PLUS 1 Code", "Train One", "Angkor", "Unlock", "<PERSON>", "<PERSON>", "<PERSON>", "Atomic Age", "Mona Sans", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Zen Tokyo Zoo", "Carrois Gothic SC", "Lavishly Yours", "Spline Sans Mono", "Shippori Antique B1", "Ga <PERSON>", "Beau Rivage", "<PERSON>", "<PERSON><PERSON><PERSON>", "Playwrite CU", "Autour One", "Noto Sans Newa", "Londrina Outline", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Fascinate Inline", "Girassol", "<PERSON><PERSON>", "Waterfall", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "BIZ UDMincho", "<PERSON><PERSON>", "Noto Sans Symbols 2", "<PERSON><PERSON><PERSON>", "Bodoni Moda SC", "Parkinsans", "Port Lligat Slab", "Stoke", "Fasthand", "<PERSON>", "Handjet", "<PERSON><PERSON><PERSON>", "<PERSON> Rocker", "Fuzzy Bubbles", "Mystery Quest", "Text Me One", "Sono", "<PERSON><PERSON><PERSON>", "Noto Sans Ethiopic", "Baskervville SC", "Gentium Plus", "<PERSON><PERSON><PERSON>", "Nerko One", "Honk", "<PERSON><PERSON>", "Spirax", "Poltawski Nowy", "Tilt Prism", "Offside", "Condiment", "<PERSON><PERSON>", "Sixtyfour Convergence", "Gentium Book Plus", "Inclusive Sans", "Engagement", "<PERSON><PERSON><PERSON>", "Almendra SC", "Dorsa", "Reddit Sans Condensed", "Playwrite IS", "Big Shoulders Stencil Text", "Imperial Script", "Federant", "Festive", "Risque", "Trispace", "Bungee Hairline", "<PERSON><PERSON>", "<PERSON><PERSON>", "Send Flowers", "<PERSON><PERSON>", "Tac One", "<PERSON><PERSON>", "Mynerve", "Port Lligat Sans", "<PERSON><PERSON><PERSON><PERSON>", "Donegal One", "Noto Serif Kannada", "Kumar One Outline", "<PERSON><PERSON>", "Protest Riot", "<PERSON><PERSON><PERSON>", "IBM Plex Sans Thai Looped", "Rubik Wet Paint", "Solitreo", "Bilbo", "Bubbler One", "<PERSON><PERSON><PERSON><PERSON>", "Metal", "<PERSON><PERSON><PERSON>", "Milonga", "Hind <PERSON><PERSON><PERSON>", "Edu VIC WA NT Beginner", "<PERSON><PERSON>", "New Tegomin", "<PERSON>", "R<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Tulpen One", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Finlandica", "Linden Hill", "Playwrite US Trad", "<PERSON><PERSON>", "Tourney", "<PERSON><PERSON><PERSON>", "Stint Ultra Expanded", "<PERSON><PERSON><PERSON>", "Noto Serif <PERSON>", "Content", "Edu NSW ACT Foundation", "Climate Crisis", "<PERSON><PERSON><PERSON><PERSON>", "Kotta One", "<PERSON>", "<PERSON>", "Bonheur Royale", "Peddana", "<PERSON><PERSON>", "Marko One", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Faculty Glyphic", "Fascinate", "<PERSON><PERSON><PERSON>", "Sour Gummy", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Smokum", "<PERSON><PERSON>", "<PERSON>s", "Romanesco", "Buda", "Glass Antiqua", "Luxuri<PERSON>", "<PERSON><PERSON><PERSON>", "Averia Gruesa Libre", "Mrs <PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Big Shoulders Inline Text", "Sahitya", "Cherry Bomb One", "Encode Sans SC", "Water Brush", "Poor Story", "<PERSON><PERSON>", "<PERSON>", "Alumni Sans Inline One", "<PERSON><PERSON>", "Noto Sans Gothic", "<PERSON><PERSON><PERSON>", "Flow Rounded", "<PERSON><PERSON><PERSON>", "Bigelow Rules", "Noto Serif Telugu", "Sedan SC", "Noto Sans Osmanya", "Castoro Titling", "Sometype Mono", "Beiruti", "<PERSON>raise", "Big Shoulders Sten<PERSON>l Display", "Devonshire", "Noto Sans Glagolitic", "Ballet", "<PERSON>", "Playwrite GB S", "Hedvig <PERSON> Sans", "Micro 5", "New Amsterdam", "Noto Sans Syriac Eastern", "Luxurious Roman", "<PERSON><PERSON><PERSON>", "Piedra", "<PERSON><PERSON><PERSON><PERSON>", "Gidugu", "Bagel <PERSON> One", "Wellfleet", "Single Day", "Bungee Outline", "Nabla", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Edu AU VIC WA NT Hand", "Truculenta", "<PERSON><PERSON><PERSON>", "<PERSON>", "Noto Sans Samaritan", "Diplomata SC", "<PERSON><PERSON><PERSON>", "Passero One", "Noto <PERSON>", "<PERSON><PERSON>", "IBM Plex Sans Devanagari", "<PERSON><PERSON>", "Playwrite AU SA", "Teachers", "Labrada", "<PERSON><PERSON>", "Princess <PERSON>", "Trochut", "Darumadrop One", "Snippet", "<PERSON>", "<PERSON>", "Noto Serif <PERSON>", "Wittgenstein", "Chela One", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Funnel Display", "Alumni Sans Pinstripe", "<PERSON><PERSON>", "<PERSON><PERSON>", "Tiro Tamil", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Stalinist One", "Butterfly Kids", "Bacasime Antique", "Flavors", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "T<PERSON> Dev<PERSON>gari Sanskrit", "Updock", "Revalia", "Tiro Telugu", "Fruktur", "Comme", "<PERSON>man", "Gasoek One", "<PERSON><PERSON><PERSON>", "Libre Barcode EAN13 Text", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Babylonica", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Noto Serif <PERSON>", "<PERSON><PERSON>", "Oldenburg", "<PERSON><PERSON><PERSON>", "Hedvig <PERSON> Serif", "Emblema One", "<PERSON><PERSON><PERSON>", "Tiro Devanagari Marathi", "Noto Sans Tangsa", "Bonbon", "Mr <PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Rubik Distressed", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Oi", "Foldit", "<PERSON>", "Caramel", "Funnel Sans", "LXGW WenKai TC", "Love Light", "Dot<PERSON>", "Diplomata", "Explora", "<PERSON><PERSON><PERSON><PERSON>", "Noto Sans Coptic", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Orbit", "BioRhyme Expanded", "Miss Fajardo<PERSON>", "<PERSON><PERSON>", "Alumni Sans Collegiate One", "Dan<PERSON>", "Edu AU VIC WA NT Pre", "Geostar", "<PERSON><PERSON><PERSON><PERSON> Rounded", "Rubik Gemstones", "<PERSON><PERSON>", "Combo", "<PERSON><PERSON>", "Jersey 20", "Inspiration", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Jacquard 12", "GFS Neohellenic", "<PERSON><PERSON>", "Ojuju", "Jersey 25", "<PERSON><PERSON>", "Playwrite HU", "<PERSON><PERSON>", "Arsenal SC", "<PERSON><PERSON>", "Noto Sans Javanese", "<PERSON><PERSON><PERSON>", "Grandiflora One", "<PERSON><PERSON>", "Workbench", "Purple Purse", "Twinkle Star", "Taprom", "<PERSON><PERSON><PERSON>", "My Soul", "Kings", "Rubik Glitch Pop", "Nuosu SIL", "Ruge Boogie", "Sedan", "Noto Sans Adlam", "Island Moments", "Cactus Classical Serif", "Noto Sans Canadian Aboriginal", "Noto Sans NKo Unjoined", "Noto Music", "Edu AU VIC WA NT Guides", "Pa<PERSON>", "Noto Sans Cypro Minoan", "<PERSON><PERSON>", "M PLUS Code Latin", "<PERSON><PERSON><PERSON>", "Big Shoulders Inline Display", "Dai Banna SIL", "Moirai One", "Redacted <PERSON><PERSON><PERSON>", "Noto Sans Syloti Nagri", "Edu AU VIC WA NT Dots", "<PERSON><PERSON>", "Noto Sans Sora Sompeng", "Playwrite AR", "Protest Guerrilla", "Noto Sans Mongolian", "<PERSON><PERSON><PERSON>", "Tai Heritage Pro", "Splash", "Noto Ser<PERSON>", "Slackside One", "Playwrite IT Moderna", "Noto Sans Cuneiform", "<PERSON><PERSON><PERSON>ur", "<PERSON><PERSON><PERSON>", "Jacquard 24", "Noto Sans Linear A", "Noto Sans Tagalog", "Geostar Fill", "Playwrite FR Moderne", "LXGW WenKai Mono TC", "Tiro Kannada", "Rock 3D", "Tapestry", "Zen Loop", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Bungee Tint", "<PERSON><PERSON><PERSON>", "Cherish", "Vibes", "Playwrite HR Lijeva", "Rubik 80s Fade", "Sankofa Display", "Snowburst One", "Noto Sans Tai Viet", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Playwrite AU NSW", "G<PERSON>chen Fuemen", "Noto Sans Carian", "Playwrite ES", "<PERSON><PERSON><PERSON>", "Flow Block", "Edu QLD Beginner", "Ubuntu Sans Mono", "Noto Sans Old Hungarian", "<PERSON><PERSON>", "Noto Serif NP Hmong", "Noto Sans Old Italic", "Estonia", "Noto Sans Nag Mundari", "Ole", "<PERSON><PERSON>", "Montserrat Underline", "Edu AU VIC WA NT Arrows", "Are You Serious", "Noto Sans Sharada", "Syne Tactile", "Jacquard 12 Charted", "Jacquarda Bastarda 9", "<PERSON><PERSON>", "Annapurna SIL", "Noto Serif <PERSON> Small Script", "<PERSON>bot Sans", "Noto Sans Indic Siyaq Numbers", "Noto Sans Nandinagari", "<PERSON><PERSON>", "Playwrite HR", "Rubik Maps", "Noto Serif Myanmar", "Rubik Burned", "Noto Sans New Tai Lue", "Playwrite NL", "Rubik <PERSON>", "Noto Traditional Nushu", "Noto Sans Warang Citi", "Playwrite MX", "Playwrite BE VLG", "Namdhinggo", "Blaka Ink", "Blaka Hollow", "Noto Sans Hanunoo", "Noto Sans Avestan", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Noto Sans Old North Arabian", "Noto Sans PhagsPa", "Jersey 20 Charted", "Rubik Doodle Triangles", "Playwrite CO Guides", "Noto Sans Thaana", "Playwrite AU VIC Guides", "Noto Sans Cherokee", "Puppies Play", "Noto Serif Ottoman Siyaq", "Playwrite CZ Guides", "Noto Sans Egyptian Hieroglyphs", "Noto Sans Batak", "Noto Sans Bamum", "Rubik Microbe", "Playwrite HR Lijeva Guides", "Playwrite ES Guides", "Playwrite RO Guides", "Playwrite ID Guides", "Noto Sans Khojki", "Noto Sans Imperial Aramaic", "Playwrite GB J", "Gajraj One", "<PERSON><PERSON>", "Noto Znamenny Musical Notation", "Playwrite MX Guides", "Playwrite DK Loopet Guides", "Playwrite CA Guides", "Playwrite PT", "Playwrite AT", "Noto Sans Miao", "Noto Serif Old Uyghur", "<PERSON><PERSON>", "Noto Sans Balinese", "Karla Tamil Inclined", "Playwrite ZA", "<PERSON>", "Playwrite SK", "<PERSON><PERSON>", "Playwrite NZ", "Playwrite BE WAL Guides", "Playwrite NL Guides", "Playwrite US Modern", "Noto Sans Adlam Unjoined", "Playwrite PE Guides", "Playwrite PT Guides", "Playwrite PL Guides", "Playwrite BE WAL", "Noto Sans Ol Chiki", "Noto Sans Tifinagh", "Noto Sans Deseret", "Noto Sans Vai", "Playwrite ES Deco Guides", "Playwrite US Modern Guides", "Playwrite IE Guides", "Playwrite DE Grund Guides", "Noto Sans Marchen", "Playwrite IS Guides", "Playwrite SK Guides", "<PERSON><PERSON>", "Playwrite AU QLD Guides", "Chokokutai", "<PERSON><PERSON>", "Playwrite NZ Guides", "Playwrite HR Guides", "Playwrite US Trad Guides", "Playwrite VN Guides", "Rubik Broken Fax", "Playwrite FR Trad Guides", "Noto Sans Old South Arabian", "Playwrite DE VA Guides", "Playwrite ZA Guides", "Noto Sans Elbasan", "Playwrite PE", "Playwrite GB J Guides", "Playwrite DE LA Guides", "Noto Sans Mro", "Playwrite IT Moderna Guides", "Playwrite IN Guides", "Playwrite IT Trad Guides", "Playwrite NG Modern Guides", "Noto Sans Sundanese", "Playwrite AU SA Guides", "Playwrite PL", "Playwrite NO Guides", "Playwrite TZ Guides", "Playwrite DK Loopet", "Playwrite DE SAS Guides", "Playwrite DK Uloopet Guides", "Playwrite AU TAS Guides", "Noto Sans Tirhuta", "Playwrite AR Guides", "Noto Sans Tai Le", "Playwrite GB S Guides", "Playwrite AU TAS", "Noto Sans Old Persian", "Playwrite AU NSW Guides", "Playwrite CL", "Playwrite AU VIC", "Noto Sans Cham", "Playwrite CO", "Noto Sans NKo", "Playwrite AU QLD", "<PERSON><PERSON>", "Noto Sans Lisu", "Noto Sans Yi", "Noto Sans Old Turkic", "Playwrite FR Moderne Guides", "Playwrite AT Guides", "Rubik Storm", "Noto Sans Medefaidrin", "Noto Sans Vithkuqi", "Noto Sans Kayah Li", "Playwrite BR Guides", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Playwrite BE VLG Guides", "Playwrite DK Uloopet", "Playwrite CL Guides", "Jersey 10 Charted", "Playwrite CU Guides", "Playwrite NG Modern", "<PERSON><PERSON><PERSON>", "Rubik Lines", "Playwrite HU Guides", "<PERSON><PERSON>", "Noto Sans Tamil Supplement", "Padyakke Expanded One", "Noto Sans Bassa Vah", "Noto Sans Limbu", "Jacquard 24 Charted", "Playwrite CZ", "Linefont", "Noto Sans Osage", "Noto Sans Chakma", "Noto Sans Brahmi", "Noto Sans Inscriptional Pahlavi", "<PERSON><PERSON><PERSON>", "Playwrite NO", "Playwrite IE", "Noto Sans Duployan", "Noto Sans Multani", "Playwrite ES Deco", "Noto Sans Tai Tham", "Noto Sans Caucasian Albanian", "Noto Sans Masaram <PERSON>", "Jersey 25 Charted", "Noto Sans Lepcha", "Jersey 15 Charted", "Noto Sans Lydian", "Noto Sans Tagbanwa", "Wavefont", "Noto Sans Sogdian", "Noto Sans Palmyrene", "<PERSON><PERSON>", "Noto Sans Runic", "Noto Sans Zanabazar Square", "Playwrite FR Trad", "Playwrite DE LA", "Noto Sans Chorasmian", "Noto Sans Mandaic", "Noto Sans Rejang", "Noto Sans Inscriptional Parthian", "Noto Sans Mahajani", "Noto Sans Mayan Numerals", "Noto Sans Hanifi <PERSON>", "Noto Sans Pa<PERSON>", "Playwrite DE SAS", "Noto Sans Linear B", "Playwrite TZ", "Micro 5 Charted", "Playwrite DE VA", "Noto Sans Kawi", "Noto Sans Nabataean", "Noto Sans Lycian", "Noto Sans Gunjala Go<PERSON>", "Playwrite CA", "Noto Sans Syriac", "Playwrite ID", "Jacquarda Bastarda 9 Charted", "Playwrite IT Trad", "Noto Sans Cypriot", "Playwrite RO", "Noto Sans Buginese", "Playwrite BR", "Noto Sans Psalter <PERSON>", "Noto Sans Siddham", "Noto Sans Kaithi", "Noto Sans Old Permic", "Noto Sans Elymaic", "Noto Sans Khudawadi", "Noto Sans Grantha", "Noto Sans Old Sogdian", "<PERSON><PERSON>", "Noto Sans Modi", "Noto Sans Shavian", "Noto Sans Kharoshthi", "Noto Sans Saurashtra", "Noto Sans Manichaean", "Noto Sans Phoenician", "Noto Sans Takri", "Noto Sans Nushu", "Noto Sans Mende Kikakui", "Noto Sans Ogham", "Noto Sans Pau Cin Hau", "Noto Sans Soyombo", "Noto Sans SignWriting", "Noto Sans Meroitic", "Yarndings 12", "Noto Sans Ugaritic", "Noto Sans Bhaiksuki", "Yarndings 20", "Yarndings 12 Charted", "Yarndings 20 Charted"], "trending": ["Smooch Sans", "Noto Sans Hatran", "Playwrite VN", "Playwrite IN", "Noto Sans Wancho", "Lexend Giga", "Noto Sans Newa", "Jersey 15", "Macondo", "Noto Sans Math", "Rethink Sans", "Noto Sans Cuneiform", "Noto Sans Linear A", "Bubbler One", "Inclusive Sans", "Oxanium", "Noto Sans Lao", "Bona Nova SC", "Sixtyfour Convergence", "Material Symbols Rounded", "Sevillana", "Playwrite AU SA", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Climate Crisis", "Andada Pro", "<PERSON><PERSON>", "Noto Sans Tamil Supplement", "Rubik Gemstones", "Noto Znamenny Musical Notation", "Zen Dots", "Noto Sans Cypro Minoan", "Creepster", "Inria Sans", "Titan One", "<PERSON><PERSON><PERSON>", "Mona Sans", "Reddit Sans Condensed", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Belanosima", "Skra<PERSON>", "Stint Ultra Condensed", "Grandiflora One", "Fugaz One", "<PERSON><PERSON>", "Barr<PERSON>ci<PERSON>", "Redacted <PERSON><PERSON><PERSON>", "Major <PERSON><PERSON>", "Playwrite CU", "Pompiere", "Overpass Mono", "Qwitcher Grypen", "Metal", "Noto Sans Tirhuta", "Vibes", "<PERSON><PERSON><PERSON>", "Ubuntu Sans Mono", "Noto Sans Mahajani", "Nabla", "Anybody", "IBM Plex Mono", "Mouse Me<PERSON>irs", "Ojuju", "McLaren", "Tienne", "<PERSON><PERSON><PERSON>", "<PERSON>", "Flow Circular", "Lexend Mega", "<PERSON>", "Dan<PERSON>", "Edu AU VIC WA NT Guides", "Noto Traditional Nushu", "Sofia Sans", "Faculty Glyphic", "<PERSON><PERSON>", "Oi", "Suez One", "REM", "G<PERSON><PERSON>", "Instrument Sans", "<PERSON><PERSON><PERSON><PERSON>", "Alfa Slab One", "Vesper <PERSON>", "Geo", "Noto Sans Anatolian Hieroglyphs", "Yusei Magic", "Micro 5", "<PERSON><PERSON>", "Dot<PERSON>", "Noto Sans Linear B", "<PERSON>", "Slackside One", "Hammersmith One", "Noto Sans Symbols 2", "Devonshire", "Jersey 10", "Cairo Play", "<PERSON><PERSON>", "Handlee", "Grape Nuts", "Familjen Grotesk", "<PERSON><PERSON>", "Graduate", "Kdam Thmor Pro", "Noto Sans Egyptian Hieroglyphs", "<PERSON><PERSON>", "Geostar", "<PERSON><PERSON><PERSON>", "Ruge Boogie", "<PERSON>man", "Rubik Distressed", "Noto Sans Pa<PERSON>", "Jomhur<PERSON>", "Noto Sans Kayah Li", "Passero One", "Syne Tactile", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Noto Sans Balinese", "Noto Sans Bengali", "Playwrite DE Grund", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Reddit Sans", "<PERSON><PERSON>", "Protest Strike", "Libre Barcode 39 Text", "Megrim", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Noto Sans Syriac Eastern", "Edu AU VIC WA NT Pre", "<PERSON><PERSON><PERSON>", "Geostar Fill", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Flow Rounded", "<PERSON>", "<PERSON><PERSON>", "Agbalumo", "<PERSON><PERSON><PERSON>", "Scada", "Updock", "<PERSON><PERSON>", "Monda", "Stalemate", "Playwrite AR", "<PERSON><PERSON>", "Port Lligat Sans", "Montagu Slab", "Tiro Devanagari Hindi", "<PERSON><PERSON><PERSON>", "Atkinson Hyperlegible", "Chivo Mono", "<PERSON>ie One", "Nova Square", "Ha<PERSON><PERSON>", "Stint Ultra Expanded", "Prociono", "Loved by the King", "News Cycle", "Noto Sans Tangsa", "Noto Sans Miao", "Overpass", "Noto Serif Old Uyghur", "<PERSON><PERSON>", "IM Fell DW Pica", "<PERSON>ef", "New Tegomin", "<PERSON><PERSON>", "<PERSON><PERSON>", "Smokum", "Source Code Pro", "Noto Sans Tai Viet", "Kulim Park", "Martian Mono", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Rubik Microbe", "<PERSON>", "Playwrite IS", "Spline Sans Mono", "<PERSON><PERSON><PERSON>", "Sarina", "Go<PERSON> Hand", "Protest Riot", "Goblin One", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Sura", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Noto Sans Yi", "Kite One", "<PERSON><PERSON>", "Mogra", "Grandstander", "Whisper", "Passions Conflict", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON>", "Noto Sans Brahmi", "<PERSON>", "Chela One", "Noto Serif <PERSON>", "<PERSON> Doulaise", "Tiny5", "V<PERSON><PERSON>", "Tilt Neon", "Vampiro One", "Gluten", "Noto Sans Kannada", "Imbue", "The Nautigal", "Almendra SC", "Noto Sans Osmanya", "Black And White Picture", "<PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "Bacasime Antique", "Radio Canada", "Pa<PERSON>", "LXGW WenKai TC", "Beau Rivage", "Chonburi", "Cambay", "Tourney", "The Girl Next Door", "Rubik <PERSON>", "<PERSON>", "Road Rage", "AR One Sans", "Big Shoulders Sten<PERSON>l Display", "<PERSON>", "Slackey", "Ranchers", "Slabo 13px", "<PERSON><PERSON>", "Workbench", "<PERSON><PERSON><PERSON>", "Staatliches", "BIZ UDMincho", "<PERSON><PERSON><PERSON>", "Nova Slim", "Playwrite ES", "<PERSON><PERSON>", "Zen Loop", "Finlandica", "<PERSON><PERSON>", "Paytone One", "Radio Canada Big", "Playwrite AU TAS", "Contrail One", "Noto Sans Batak", "<PERSON><PERSON>", "Tiro Kannada", "Rubik Maps", "<PERSON><PERSON><PERSON>", "Sometype Mono", "IBM Plex Sans Condensed", "<PERSON><PERSON><PERSON><PERSON>", "Nothing You Could Do", "DotGothic16", "<PERSON><PERSON><PERSON>", "Barrio", "Gentium Book Plus", "Padyakke Expanded One", "Capriola", "Convergence", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Boogaloo", "<PERSON><PERSON>", "Mali", "Bilbo Swash Caps", "Comic Neue", "<PERSON><PERSON><PERSON>", "Jacquard 24 Charted", "<PERSON><PERSON><PERSON>", "Quando", "Cambo", "<PERSON><PERSON>", "Sorts Mill Goudy", "Inconsolata", "IBM Plex Sans Thai Looped", "Playwrite AU QLD", "Estonia", "Noto Sans Khmer", "Romanesco", "Bokor", "Moirai One", "Shadows Into Light Two", "Montez", "Birthstone Bounce", "<PERSON><PERSON><PERSON>", "Noto Sans Limbu", "Slabo 27px", "Chokokutai", "Trade Winds", "<PERSON><PERSON><PERSON>", "Edu TAS Beginner", "M PLUS 1 Code", "La Belle Aurore", "<PERSON><PERSON>k <PERSON>", "<PERSON>", "Red Rose", "Nokor<PERSON>", "Protest Revolution", "Playwrite BE WAL", "<PERSON><PERSON>", "Telex", "<PERSON><PERSON><PERSON><PERSON>", "Noto Sans Mro", "<PERSON><PERSON>", "BIZ UDPGothic", "DynaPuff", "Bonbon", "Hurricane", "<PERSON><PERSON>", "<PERSON><PERSON>", "Rochester", "Akatab", "Libre Caslon Text", "<PERSON><PERSON><PERSON>", "Bigelow Rules", "<PERSON><PERSON><PERSON>", "Sintony", "<PERSON><PERSON>", "UnifrakturCook", "Noto Sans Avestan", "Revalia", "Libre Bo<PERSON>i", "Balthazar", "<PERSON>", "Rubik Broken Fax", "Playwrite CL", "<PERSON><PERSON><PERSON>", "Dynalight", "Alkalami", "B612 Mono", "<PERSON>", "M PLUS 2", "Luxurious Roman", "<PERSON><PERSON>", "Nixie One", "Titillium Web", "Esteban", "Licorice", "Ari<PERSON>", "<PERSON><PERSON><PERSON> Condensed", "IBM Plex Serif", "Fasthand", "Jacquard 12", "Puppies Play", "Quicksand", "Flow Block", "Luckiest Guy", "Annapurna SIL", "Offside", "Noto Sans Nag Mundari", "Stick", "<PERSON><PERSON> and <PERSON><PERSON>", "Rubik Storm", "Dawning of a New Day", "Mr <PERSON>", "<PERSON><PERSON>", "Nosifer", "<PERSON><PERSON>", "Homenaje", "Playwrite FR Trad", "Playwrite NO", "<PERSON><PERSON><PERSON><PERSON>", "Fira Mono", "My Soul", "Bonheur Royale", "Give You Glory", "Train One", "Sofia Sans Extra Condensed", "Bellota Text", "Comforter Brush", "IM Fell DW Pica SC", "Kavoon", "<PERSON>", "Corinthia", "Marvel", "<PERSON><PERSON>", "Rosarivo", "Rubik 80s Fade", "<PERSON><PERSON>", "Limelight", "Bubblegum Sans", "Sigmar One", "Butterfly Kids", "Sign<PERSON>", "<PERSON>", "Rancho", "Source Sans 3", "MedievalSharp", "Rock 3D", "Kadwa", "Emblema One", "<PERSON> <PERSON><PERSON>", "Linden Hill", "<PERSON><PERSON>", "Cactus Classical Serif", "Architects Daughter", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Mr <PERSON><PERSON><PERSON>", "Alumni Sans Inline One", "Noto Sans Nushu", "Prompt", "IM Fell Double Pica SC", "Blaka Ink", "Karla Tamil Inclined", "Lavishly Yours", "Material Symbols Sharp", "Noto Sans Mongolian", "<PERSON><PERSON>", "Material Symbols Outlined", "<PERSON><PERSON> Condensed", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Marmelad", "Rubik Burned", "Noto Sans Devanagari", "<PERSON><PERSON>", "<PERSON>", "Cherry Swash", "Metrophobic", "Encode Sans SC", "Mina", "Readex Pro", "<PERSON>", "Crushed", "Ka<PERSON>", "Anaheim", "Rhodium Libre", "Blinker", "<PERSON><PERSON><PERSON>", "Fira Code", "Proza Libre", "<PERSON><PERSON>", "Ara<PERSON><PERSON>", "<PERSON><PERSON>", "B612", "Hanken Grotesk", "Yesteryear", "Akaya Telivigala", "IBM Plex Sans KR", "ZCOOL KuaiLe", "<PERSON><PERSON><PERSON>", "Vidaloka", "Diplomata", "Voces", "Belgrano", "<PERSON><PERSON>", "IM Fell English SC", "Playwrite SK", "Cherish", "Medula One", "Orelega One", "Noto Sans Old North Arabian", "Noto Sans Multani", "<PERSON><PERSON><PERSON>", "Tulpen One", "Days One", "<PERSON><PERSON><PERSON>", "Ibarra Real Nova", "Rampart One", "Pontano Sans", "<PERSON><PERSON>", "Noto Sans Masaram <PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Ole", "Cousine", "<PERSON><PERSON>", "Noto Sans Cherokee", "Lexend Deca", "Fondamento", "<PERSON><PERSON>", "<PERSON><PERSON>", "Snowburst One", "Playwrite IT Trad", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Protest Guerrilla", "<PERSON><PERSON><PERSON>", "Iceberg", "Noto Sans Buginese", "Rubik Lines", "<PERSON><PERSON><PERSON>", "Mr <PERSON>", "<PERSON><PERSON>", "Noto Sans Gurmukhi", "Noto Sans Psalter <PERSON>", "Tilt Warp", "Headland One", "Material Icons Outlined", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "K<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Trispace", "<PERSON><PERSON>", "Abyssinica SIL", "Noto Sans Thaana", "Noto Sans Bassa Vah", "Send Flowers", "Cherry Cream Soda", "<PERSON>", "Parisienne", "Playwrite PE", "Rammetto One", "<PERSON><PERSON><PERSON>", "Mrs <PERSON>", "<PERSON><PERSON>", "Homemade Apple", "Alumni Sans Collegiate One", "Playwrite TZ", "Nova Cut", "Nova Mono", "Noto Serif Myanmar", "Material Icons Round", "Playwrite BR", "Playwrite PL", "Material Icons", "<PERSON><PERSON>", "Cormorant Unicase", "Nova Oval", "Funnel Display", "Klee One", "Playwrite US Trad", "Crimson Text", "Jersey 25 Charted", "Poltawski Nowy", "<PERSON><PERSON>", "Vast Shadow", "<PERSON><PERSON><PERSON>", "Caveat", "Ballet", "Playwrite RO", "Noto Sans Meetei <PERSON>", "Nova Script", "Libre Barcode 39", "Meddon", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>", "Noto Sans Chakma", "<PERSON><PERSON><PERSON>", "Shippori Antique B1", "Denk One", "<PERSON><PERSON><PERSON>", "<PERSON>", "Playwrite DE VA", "Playwrite PT", "Flamenco", "Gloria Hallelujah", "Playwrite IE", "Tiro Tamil", "Crafty Girls", "Playwrite DE SAS", "Ovo", "Armata", "Bowlby One SC", "Amethysta", "<PERSON><PERSON>i <PERSON>", "LXGW WenKai Mono TC", "Ewert", "<PERSON><PERSON>", "<PERSON><PERSON>", "Waiting for the Sunrise", "<PERSON><PERSON>", "<PERSON>is <PERSON>", "Jersey 10 Charted", "Amarante", "<PERSON><PERSON>", "Lemon", "R<PERSON><PERSON>", "GFS Neohellenic", "<PERSON><PERSON><PERSON>", "Aboreto", "Linefont", "Newsreader", "DM Mono", "ABeeZee", "Noto Sans Saurashtra", "Sahitya", "Noto Sans Mayan Numerals", "Cedarville Cursive", "IM Fell Great Primer SC", "Germania One", "Average", "Alumni Sans Pinstripe", "Arizonia", "Noto Sans Old South Arabian", "<PERSON><PERSON>", "Catamaran", "Goudy Bookletter 1911", "Gruppo", "Baloo Thambi 2", "Noto Sans Inscriptional Parthian", "Noto Sans Nabataean", "BenchNine", "Fruktur", "<PERSON> Rocker", "<PERSON><PERSON>", "PT Sans Caption", "Nobile", "Arvo", "Noto Sans Kharoshthi", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "UnifrakturMaguntia", "Nova Flat", "Public Sans", "Righteous", "Micro 5 Charted", "<PERSON><PERSON><PERSON><PERSON>", "Birthstone", "Bitter", "Cormorant SC", "Urbanist", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Noto Sans Old Persian", "Noto Sans Modi", "<PERSON>", "<PERSON><PERSON><PERSON>", "Noto Sans Oriya", "Noto Sans NKo", "Bungee Tint", "Bayon", "Krona One", "<PERSON><PERSON>", "Salsa", "Bungee Outline", "Noto Serif Ottoman Siyaq", "<PERSON><PERSON>", "Taprom", "Noto Sans Runic", "Chelsea Market", "Playwrite CZ", "Hind", "New Amsterdam", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON> Display", "IM Fell Great Primer", "Strait", "Source Serif 4", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Playwrite NG Modern", "Lexend", "<PERSON>", "Noto Serif SC", "Instrument Serif", "<PERSON><PERSON><PERSON>", "Big Shoulders Inline Display", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Noto Sans Canadian Aboriginal", "<PERSON><PERSON><PERSON>", "Noto Sans Adlam Unjoined", "Wix Madefor Text", "Noto Sans Tai Tham", "Just Me Again Down Here", "<PERSON><PERSON>", "<PERSON><PERSON>", "Alegreya Sans", "Fresca", "Poor Story", "Libre Franklin", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Georama", "Economica", "Secular One", "Metal Mania", "Noto Serif <PERSON>", "Unlock", "Vina Sans", "Jacquarda Bastarda 9 Charted", "<PERSON><PERSON><PERSON>", "Domine", "Michroma", "Archivo Narrow", "<PERSON><PERSON>", "Dorsa", "<PERSON><PERSON><PERSON>", "Playwrite DK Loopet", "<PERSON><PERSON>", "Fascinate", "Noto Sans Malayalam", "Yeseva One", "Sofia", "Sour Gummy", "Material Icons Two Tone", "RocknRoll One", "<PERSON><PERSON><PERSON>", "Noto Sans Siddham", "<PERSON>", "<PERSON><PERSON>", "<PERSON>bot Sans", "Share Tech", "Charis SIL", "Noto Sans Cham", "<PERSON><PERSON>", "Sankofa Display", "Special Elite", "<PERSON><PERSON><PERSON>", "Sedgwick Ave", "Permanent Marker", "<PERSON><PERSON>", "El Messiri", "Plus Jakarta Sans", "Labrada", "Reggae One", "<PERSON><PERSON><PERSON>", "<PERSON>rma", "<PERSON><PERSON>", "Peddana", "Material Icons Sharp", "Orienta", "Playwrite DE LA", "Roboto Mono", "<PERSON><PERSON>", "<PERSON><PERSON>", "Libre Baskerville", "<PERSON>nd<PERSON>", "<PERSON><PERSON><PERSON>", "STIX Two Text", "<PERSON><PERSON><PERSON><PERSON> Rounded", "Mochiy Pop One", "Lusitana", "<PERSON><PERSON>", "Noto Sans Buhid", "Schibsted Grotesk", "<PERSON>", "Comfortaa", "Hachi Maru Pop", "Amatic SC", "<PERSON><PERSON>", "Candal", "Stalinist One", "Noto Sans SignWriting", "<PERSON><PERSON>", "Playwrite MX", "Nanum Gothic Coding", "Istok Web", "Noticia Text", "Advent Pro", "Wire One", "Noto Sans Mandaic", "Syne", "Noto Sans Pau Cin Hau", "Inria Serif", "Doppio One", "Average Sans", "<PERSON>", "Share Tech Mono", "Cutive", "Quattrocento Sans", "Redacted", "Bilbo", "<PERSON><PERSON><PERSON>", "Literata", "Yatra One", "Big Shoulders Text", "Moon Dance", "PT Serif", "Playwrite FR Moderne", "IM Fell French Canon SC", "<PERSON>nd <PERSON>", "<PERSON><PERSON>", "Noto Sans Ogham", "<PERSON><PERSON>", "Noto Sans Syriac", "Big Shoulders Stencil Text", "Kalnia", "Ubuntu", "<PERSON><PERSON>", "DM Sans", "Glory", "Miss Fajardo<PERSON>", "<PERSON><PERSON>", "Noto Sans Mende Kikakui", "Faster One", "<PERSON><PERSON><PERSON>", "Gloock", "<PERSON><PERSON>", "Cantata One", "<PERSON><PERSON>", "<PERSON>", "Fahkwang", "Call<PERSON><PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Julius Sans One", "<PERSON><PERSON><PERSON>", "Playwrite HR Lijeva", "Noto Sans Symbols", "Carrois Gothic", "Actor", "<PERSON><PERSON><PERSON>", "Noto Sans Manichaean", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Tiro Devanagari Marathi", "Rambla", "Namdhinggo", "Red Hat Mono", "Zen Old Mincho", "Explora", "Merriweather Sans", "<PERSON><PERSON><PERSON>", "Noto Sans Caucasian Albanian", "Gravitas One", "<PERSON><PERSON>", "Girassol", "Noto Sans Deseret", "<PERSON><PERSON>", "Piazzolla", "Monoton", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Anonymous Pro", "Just Another Hand", "Be Vietnam Pro", "Edu SA Beginner", "Fira Sans", "Shippori Mincho B1", "Noto Sans Syloti Nagri", "Noto Serif NP Hmong", "Ubuntu Condensed", "<PERSON><PERSON><PERSON>", "Bakbak One", "Roboto", "<PERSON><PERSON>", "<PERSON>ra", "Diplomata SC", "Ship<PERSON><PERSON>", "Quest<PERSON>", "Noto Sans Kawi", "<PERSON><PERSON>", "<PERSON><PERSON> Slab", "<PERSON><PERSON>", "Noto Sans New Tai Lue", "Noto Sans Old Sogdian", "Holtwood One SC", "Noto Sans Lepcha", "Carrois Gothic SC", "Iceland", "Ari<PERSON>", "Solway", "PT Serif Caption", "Yrsa", "Cabin", "EB Garamond", "<PERSON><PERSON>", "Imperial Script", "Noto Sans Bhaiksuki", "Nanum Gothic", "<PERSON><PERSON>", "Figtree", "Noto Sans Elymaic", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Noto Sans Duployan", "Coda", "Bungee Inline", "IM Fell Double Pica", "ADLaM Display", "Quattrocento", "Island Moments", "Purple Purse", "<PERSON><PERSON><PERSON>", "Seaweed Sc<PERSON>t", "Noto Sans Gunjala Go<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Castoro Titling", "Condiment", "Saira Stencil One", "Noto Sans Chorasmian", "<PERSON><PERSON><PERSON>", "<PERSON>bell", "Noto Sans Gujarati", "Damion", "Life Savers", "<PERSON>", "Fjalla One", "WindSong", "<PERSON><PERSON><PERSON><PERSON> G<PERSON>mond", "Baskervville", "Dela Gothic One", "Hind <PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Covered By Your Grace", "Genos", "<PERSON><PERSON>", "Cag<PERSON>stro", "<PERSON><PERSON>t", "Uncial Antiqua", "<PERSON><PERSON>", "Single Day", "PT Sans", "Inter", "Open Sans", "Trirong", "GFS <PERSON>ot", "Archivo", "<PERSON><PERSON>", "Sarabun", "<PERSON><PERSON>", "Playwrite GB J", "<PERSON><PERSON>", "Luxuri<PERSON>", "Philosopher", "Alegreya SC", "Fragment Mono", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Chivo", "Vazirmatn", "Rubik Glitch Pop", "<PERSON><PERSON><PERSON>", "Share", "<PERSON><PERSON><PERSON><PERSON>", "Montserrat Subrayada", "Roboto Condensed", "<PERSON><PERSON><PERSON><PERSON>", "Yarndings 12 Charted", "Modern Antiqua", "Tangerine", "<PERSON><PERSON>", "Acme", "Noto Sans SC", "Zen Tokyo Zoo", "Potta One", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Playwrite NZ", "Noto Sans Lydian", "Bungee Spice", "<PERSON><PERSON>", "Red Hat Text", "Noto Sans Lycian", "IM Fell French Canon", "Oldenburg", "Spectral SC", "<PERSON>e Font", "Noto Sans Hanifi <PERSON>", "M PLUS Rounded 1c", "<PERSON><PERSON>", "Fascinate Inline", "Yarndings 20 Charted", "Mochiy Pop P One", "Noto Sans Carian", "<PERSON><PERSON>", "Playwrite CO", "Pacifico", "Arsenal SC", "Noto Sans Javanese", "Spectral", "Noto Ser<PERSON>", "Alike", "<PERSON><PERSON><PERSON>", "Redressed", "<PERSON><PERSON><PERSON>", "Oregano", "<PERSON><PERSON>", "<PERSON><PERSON>", "Noto Sans Meroitic", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Concert One", "Bungee Shade", "<PERSON><PERSON><PERSON>", "Atomic Age", "Noto Sans Tagalog", "Quintessential", "<PERSON><PERSON>", "Space Mono", "<PERSON><PERSON><PERSON><PERSON>", "Glass Antiqua", "Noto Sans Tifinagh", "Overlock SC", "Marko One", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Indie Flower", "Are You Serious", "Wavefont", "JetBrains Mono", "<PERSON>", "Noto Sans Elbasan", "<PERSON><PERSON>", "Noto Sans Tagbanwa", "Love Light", "<PERSON><PERSON>", "<PERSON>", "IBM Plex Sans Thai", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Mandali", "<PERSON><PERSON>", "<PERSON>", "Orbitron", "<PERSON>", "<PERSON>", "<PERSON><PERSON>", "Noto Sans Sundanese", "<PERSON><PERSON>", "Shadows Into Light", "<PERSON><PERSON><PERSON>", "Sail", "K2D", "Kumbh Sans", "Noto Sans Indic Siyaq Numbers", "Noto Sans", "<PERSON><PERSON><PERSON><PERSON>", "PT Mono", "<PERSON><PERSON><PERSON>", "Edu AU VIC WA NT Arrows", "<PERSON><PERSON><PERSON>", "Delicious Handrawn", "Sawarabi Gothic", "Noto Sans Shavian", "Fira Sans Extra Condensed", "Playwrite NL", "Varta", "Bellota", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Oxygen Mono", "Asap", "PT Sans Narrow", "<PERSON> Semi Condensed", "<PERSON><PERSON><PERSON>", "Hedvig <PERSON> Sans", "Kotta One", "Yarndings 12", "Tiro Telugu", "<PERSON><PERSON><PERSON>", "Keania One", "Noto Sans Vithkuqi", "NTR", "BioRhyme Expanded", "Parkinsans", "<PERSON><PERSON><PERSON>", "Codystar", "Athiti", "Epilogue", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Six Caps", "<PERSON><PERSON>", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Stick No Bills", "Beiruti", "Ropa Sans", "Caudex", "Fjord One", "Martel Sans", "ZCOOL QingKe HuangYou", "Cuprum", "Jersey 15 Charted", "Playwrite DK Uloopet", "Outfit", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Donegal One", "<PERSON><PERSON><PERSON>", "Brygada 1918", "Jacquard 24", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Cantarell", "<PERSON><PERSON><PERSON>", "Fanwood Text", "Zen Kaku Gothic New", "MuseoModerno", "Love Ya Like A Sister", "<PERSON>", "<PERSON><PERSON><PERSON>", "IM Fell English", "Caladea", "Noto Sans Soyombo", "Fenix", "Noto Sans Khudawadi", "Playwrite AU VIC", "Noto Sans Vai", "<PERSON><PERSON>", "Nunito", "Playwrite ID", "Averia Libre", "Playwrite US Modern", "<PERSON><PERSON><PERSON>", "Playfair Display", "Baloo Chettan 2", "Kantumruy Pro", "Fuggles", "Trochut", "Norican", "Marcellus SC", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Happy Monkey", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "Ceviche One", "Noto Sans Osage", "Noto Sans Phoenician", "Noto Sans Myanmar", "Exo 2", "Assistant", "<PERSON><PERSON><PERSON>", "Lexend Exa", "Mystery Quest", "Overlock", "Combo", "<PERSON><PERSON>", "Yarndings 20", "<PERSON><PERSON><PERSON>", "Petit Formal Script", "<PERSON><PERSON><PERSON>", "Sacramento", "<PERSON>", "Noto Sans Ol Chiki", "<PERSON><PERSON><PERSON>", "Magra", "Ultra", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON> Face", "<PERSON>", "<PERSON><PERSON><PERSON>", "Quantico", "<PERSON><PERSON> the <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Freehand", "Alkatra", "<PERSON><PERSON>", "Alef", "Alegreya Sans SC", "<PERSON><PERSON><PERSON>", "<PERSON>", "Playball", "Syncopate", "Encode Sans", "BioRhyme", "Bagel <PERSON> One", "Puritan", "Noto Sans Tai Le", "Dokdo", "Lekton", "Didact Gothic", "<PERSON><PERSON><PERSON>", "Bigshot One", "Croissant One", "Stylish", "<PERSON><PERSON><PERSON>", "Pathway Gothic One", "<PERSON> Dressing", "Nova Round", "Hind Vadodara", "East Sea Dokdo", "Work Sans", "Exo", "Italianno", "Sofadi One", "Engagement", "<PERSON>", "<PERSON><PERSON><PERSON>", "Man<PERSON><PERSON>", "Noto Sans Gothic", "<PERSON><PERSON>", "<PERSON><PERSON>", "Playwrite AT", "DM Serif <PERSON>lay", "Recursive", "Press Start 2P", "Poly", "Orbit", "Play", "<PERSON><PERSON><PERSON>", "Zen Maru Gothic", "Noto Sans HK", "Antic Slab", "Battambang", "Tektur", "<PERSON><PERSON>", "Poiret One", "<PERSON><PERSON>", "Cairo", "Averia Sans Libre", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Noto Sans Georgian", "IBM Plex Sans", "<PERSON><PERSON><PERSON>", "Arsenal", "Rubik Mono One", "M PLUS Code Latin", "Forum", "Montaga", "Cantora One", "<PERSON>ript", "Noto Sans Display", "Unica One", "Buda", "Karantina", "<PERSON>", "Great Vibes", "Cutive Mono", "<PERSON>", "<PERSON><PERSON><PERSON>", "Prata", "Berkshire Swash", "Audiowide", "<PERSON><PERSON>", "Zilla Slab Highlight", "<PERSON><PERSON><PERSON>", "Sono", "Rock Salt", "<PERSON>", "Gelasio", "Baloo Tamma 2", "<PERSON><PERSON>", "Noto Sans Medefaidrin", "Noto Sans Coptic", "Noto Color Emoji", "League Spartan", "Caveat Brush", "<PERSON><PERSON>", "Signika Negative", "League Script", "Flavors", "Original Surfer", "Wittgenstein", "<PERSON><PERSON>", "Kings", "Over the Rainbow", "Cinzel Decorative", "Roboto Serif", "<PERSON><PERSON><PERSON><PERSON>", "Monomaniac One", "<PERSON><PERSON><PERSON>", "Content", "Montserrat", "Old Standard TT", "Fustat", "Annie Use Your Telescope", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "League Gothic", "Foldit", "SUSE", "Solitreo", "Chau Philomene One", "Noto Sans Lao Looped", "Scope One", "Syne Mono", "Rosario", "Stoke", "Noto Nastaliq Urdu", "<PERSON><PERSON><PERSON>", "<PERSON>ver", "Oxygen", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Maven <PERSON>", "Angkor", "<PERSON><PERSON>", "Electrolize", "Russo One", "<PERSON><PERSON><PERSON>", "Noto Sans Imperial Aramaic", "<PERSON><PERSON>", "Rowdies", "<PERSON><PERSON>", "Darumadrop One", "Baloo 2", "<PERSON><PERSON><PERSON>", "Geologica", "<PERSON>", "IBM Plex Sans JP", "Noto <PERSON>", "Noto Sans Warang Citi", "<PERSON><PERSON>", "Snippet", "Aclonica", "Nunito Sans", "Knewave", "Coming Soon", "Playwrite CA", "<PERSON> Script", "Noto Sans Bamum", "Turret Road", "Kat<PERSON><PERSON>", "Sedan", "Noto Sans Hebrew", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Sulphur Point", "Noto Sans Takri", "Federo", "T<PERSON> Dev<PERSON>gari Sanskrit", "Noto Sans Tamil", "Abril Fatface", "Ubuntu Mono", "<PERSON><PERSON><PERSON>", "Mrs <PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Alata", "<PERSON><PERSON>", "Alexandria", "<PERSON><PERSON>", "Wellfleet", "<PERSON><PERSON>", "Kumar One Outline", "Oooh Baby", "<PERSON><PERSON>", "Noto Sans Cypriot", "<PERSON><PERSON>", "Manrope", "Noto Sans Samaritan", "Edu QLD Beginner", "<PERSON><PERSON><PERSON><PERSON>", "Charm", "Text Me One", "Encode Sans Semi Expanded", "Irish Grover", "Noto Sans Telugu", "Ephesis", "<PERSON><PERSON><PERSON><PERSON>", "Space Grotesk", "Notable", "<PERSON><PERSON><PERSON><PERSON>", "Varela Round", "<PERSON>", "<PERSON><PERSON>", "Artifika", "Londrina Outline", "Supermercado One", "Fauna One", "Sunflower", "<PERSON><PERSON>", "Braah One", "Hind Madurai", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Noto Sans Sharada", "M PLUS 1p", "Noto Sans Marchen", "Red Hat Display", "Playwrite BE VLG", "<PERSON><PERSON><PERSON>", "Averia Serif <PERSON>", "Scheherazade New", "<PERSON><PERSON>", "Noto Sans Nandinagari", "Neuton", "<PERSON><PERSON>", "Noto Sans Old Italic", "Bungee Hairline", "Square Peg", "<PERSON><PERSON>", "<PERSON><PERSON>", "Tapestry", "<PERSON><PERSON><PERSON>", "Montserrat Alternates", "Noto Serif KR", "Zen Antique Soft", "Port Lligat Slab", "Water Brush", "Noto Sans Arabic", "Bona Nova", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Courier Prime", "Noto Sans Ethiopic", "Teachers", "Wallpoet", "Ga <PERSON>", "BhuTuka Expanded One", "Jacquarda Bastarda 9", "<PERSON><PERSON>", "Baloo Tammudu 2", "Sedan SC", "Archivo Black", "Shrikhand", "Viaoda Libre", "Zen Antique", "<PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON>", "Neucha", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Noto Sans Rejang", "Noto Sans Old Hungarian", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "BIZ UDGothic", "Comme", "Jockey One", "<PERSON>", "Imprima", "Waterfall", "Eater", "Lemonada", "<PERSON><PERSON><PERSON>", "Noto Sans JP", "Eagle Lake", "Noto Sans Kaithi", "Prosto One", "Encode Sans Semi Condensed", "<PERSON><PERSON>", "Jersey 20", "Afacad", "Commissioner", "Spline Sans", "Dai Banna SIL", "Zilla Slab", "Brawler", "Encode Sans Condensed", "<PERSON><PERSON>", "Hedvig <PERSON> Serif", "<PERSON><PERSON>", "Edu AU VIC WA NT Hand", "Princess <PERSON>", "Playwrite RO Guides", "<PERSON><PERSON><PERSON>", "Almarai", "Rokkitt", "Noto Sans KR", "Gidugu", "Odibee Sans", "IBM Plex Sans Hebrew", "Sumana", "<PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON>", "Mynerve", "<PERSON><PERSON><PERSON>", "Bangers", "IBM Plex Sans Arabic", "Noto Sans Armenian", "<PERSON><PERSON>", "Delius Swash Caps", "<PERSON><PERSON><PERSON><PERSON>", "Afacad Flux", "Leckerli One", "Lobster Two", "<PERSON><PERSON>", "Playwrite CZ Guides", "Baloo Bhaijaan 2", "<PERSON><PERSON>", "Piedra", "Noto Sans Old Permic", "Tin<PERSON>", "Saira Semi Condensed", "Darker Grotesque", "Fuzzy Bubbles", "Baloo Bhai 2", "Cormorant Infant", "Changa One", "Playwrite ES Guides", "<PERSON><PERSON>", "Noto Sans Lisu", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Ysabeau SC", "Playfair Display SC", "Playwrite IT Moderna", "Spirax", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Shippori Antique", "Cormorant", "Noto Sans Thai Looped", "<PERSON><PERSON>", "Babylonica", "<PERSON><PERSON><PERSON>", "Honk", "Chewy", "Noto Sans TC", "Bellefair", "Noto Sans Ugaritic", "Zen Kaku Gothic Antique", "<PERSON> Condensed", "Crete Round", "<PERSON><PERSON>", "Noto Serif <PERSON>", "Ysabeau Office", "Pangolin", "Expletus <PERSON>", "Libre Caslon Display", "<PERSON>", "Castoro", "Noto Serif HK", "Handjet", "Inknut Antiqua", "<PERSON><PERSON>", "Playwrite HR", "Bungee", "Lustria", "Silkscreen", "<PERSON><PERSON><PERSON>", "Racing Sans One", "<PERSON><PERSON>", "Markazi Text", "Averia Gruesa Libre", "Gajraj One", "ZCOOL XiaoWei", "<PERSON><PERSON><PERSON>", "Inspiration", "Big Shoulders Display", "<PERSON><PERSON>", "Inter Tight", "<PERSON><PERSON>", "Playwrite CU Guides", "Viga", "Jersey 20 Charted", "Playwrite HU Guides", "Calistoga", "<PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Coiny", "Macondo Swash Caps", "Playwrite BR Guides", "Playwrite ID Guides", "Playwrite CL Guides", "Playwrite DK Loopet Guides", "Noto Sans Old Turkic", "Playwrite CA Guides", "Gothic A1", "<PERSON><PERSON>", "Grand Hotel", "<PERSON><PERSON><PERSON>", "Khand", "Patua One", "Noto Sans Sinhala", "<PERSON>", "Playwrite PL Guides", "Splash", "<PERSON><PERSON>", "Manuale", "Rozha One", "Playpen Sans", "Antic", "Balsamiq Sans", "Khmer", "Festive", "Libre Barcode 39 Extended", "Libre Barcode EAN13 Text", "Nanum Brush Script", "Sofia Sans Condensed", "<PERSON><PERSON>", "Noto Sans Mono", "Host Grotesk", "<PERSON><PERSON>", "Playwrite PT Guides", "Wix Madefor Display", "Nerko One", "<PERSON><PERSON>", "Playwrite BE VLG Guides", "Playwrite BE WAL Guides", "Ubuntu Sans", "Milonga", "Saira Extra Condensed", "Unkempt", "Big Shoulders Inline Text", "Libre Barcode 128", "Noto Sans Grantha", "Playwrite IS Guides", "Playwrite DE SAS Guides", "Gasoek One", "<PERSON><PERSON><PERSON>", "<PERSON>", "Tac One", "Lovers Quarrel", "Reddit <PERSON>o", "Playwrite DE LA Guides", "Autour One", "Black Han Sans", "Rationale", "Playwrite AU SA Guides", "Libre Barcode 39 Extended Text", "Playwrite ZA Guides", "Playwrite IT Moderna Guides", "<PERSON><PERSON><PERSON>", "Gug<PERSON>", "Federant", "Playwrite TZ Guides", "Playwrite HR Guides", "<PERSON><PERSON>", "<PERSON><PERSON>", "Playwrite DE Grund Guides", "Playwrite NO Guides", "Noto Serif TC", "<PERSON><PERSON><PERSON>", "Playwrite NG Modern Guides", "Libre Barcode 128 Text", "Playwrite US Modern Guides", "Playfair", "Playwrite IT Trad Guides", "Playwrite ZA", "Playwrite AT Guides", "KoHo", "Playwrite DK Uloopet Guides", "Playwrite NZ Guides", "Edu AU VIC WA NT Dots", "Noto Serif Malayalam", "<PERSON><PERSON><PERSON><PERSON>", "M PLUS 1", "Playwrite AU NSW", "Playwrite IN Guides", "<PERSON><PERSON>", "Unbounded", "Ledger", "Playwrite FR Moderne Guides", "Playwrite AU NSW Guides", "<PERSON><PERSON>", "Playwrite AU TAS Guides", "Noto Serif Kannada", "Playwrite AR Guides", "Rubik Wet Paint", "Playwrite NL Guides", "Playwrite GB J Guides", "Playwrite US Trad Guides", "Noto Serif <PERSON>", "<PERSON>", "Antonio", "Raleway Dots", "<PERSON><PERSON><PERSON>", "Pixelify Sans", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Playwrite FR Trad Guides", "<PERSON>", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON>raise", "Alumni Sans", "Sofia Sans Semi Condensed", "Anton SC", "Noto Sans Zanabazar Square", "Black Ops One", "<PERSON><PERSON><PERSON>", "Rasa", "<PERSON><PERSON><PERSON><PERSON>", "Blaka Hollow", "<PERSON><PERSON><PERSON>", "Noto Sans Hanunoo", "Risque", "Italiana", "Tomorrow", "Chocolate Classical Sans", "<PERSON><PERSON><PERSON>", "Margarine", "Fira Sans Condensed", "Encode Sans Expanded", "<PERSON>", "Passion One", "BIZ UDPMincho", "Yellowtail", "Playwrite GB S Guides", "<PERSON><PERSON><PERSON>", "<PERSON>", "VT323", "<PERSON><PERSON><PERSON> Swashed", "<PERSON><PERSON>", "<PERSON><PERSON>", "Edu NSW ACT Foundation", "Baloo Bhaina 2", "<PERSON><PERSON><PERSON>", "<PERSON>", "Baskervville SC", "Tai Heritage Pro", "<PERSON><PERSON>", "Tilt Prism", "Pathway Extreme", "<PERSON><PERSON>", "Baloo Da 2", "Noto Sans Adlam", "<PERSON><PERSON>", "Nuosu SIL", "Truculenta", "IBM Plex Sans Devanagari", "Spicy Rice", "Sedgwick Ave Display", "Lobster", "Basic", "Cherry Bomb One", "Stardos Stencil", "Gentium Plus", "Ken<PERSON>", "Rubik Doodle Triangles", "Hind Siliguri", "Roboto Flex", "Asap Condensed", "<PERSON>s", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON>", "Noto Sans Khojki", "Modak", "<PERSON>en One", "Noto Sans Sogdian", "Bricolage Grotesque", "Bodoni Moda SC", "Playwrite AU QLD Guides", "Vollkorn SC", "Noto Sans Inscriptional Pahlavi", "<PERSON><PERSON><PERSON>", "Squada One", "Pirata One", "Noto Serif Telugu", "Edu VIC WA NT Beginner", "<PERSON><PERSON>", "Playwrite VN Guides", "Noto Sans NKo Unjoined", "Noto Serif <PERSON> Small Script", "DM Serif Text", "<PERSON>", "Baloo Paaji 2", "Noto Sans Sora Sompeng", "<PERSON><PERSON>", "Funnel Sans", "Bowlby One", "Playwrite ES Deco", "Playwrite HU", "Noto Sans Thai", "Noto Sans Glagolitic", "Mountains of Christmas", "Noto Music", "G<PERSON>chen Fuemen", "Caramel", "Petrona", "Twinkle Star", "Crimson Pro", "Underdog", "<PERSON><PERSON>", "Noto Sans Palmyrene", "Playwrite PE Guides", "Jacquard 12 Charted", "<PERSON><PERSON> Script Swash Caps", "Jersey 25", "Poller One", "Shantell Sans", "Playwrite SK Guides", "<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON>", "Playwrite HR Lijeva Guides", "J<PERSON>", "Playwrite DE VA Guides", "<PERSON><PERSON><PERSON><PERSON>", "Mate SC", "Playwrite IE Guides", "Playwrite GB S", "Noto Sans PhagsPa", "<PERSON><PERSON><PERSON>ur", "Playwrite AU VIC Guides", "Playwrite MX Guides", "Playwrite CO Guides", "Playwrite ES Deco Guides", "Montserrat Underline"]}}