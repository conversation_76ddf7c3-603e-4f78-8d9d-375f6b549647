.block-editor #uxbuilder-edit-button {
	display: inline-flex;
	align-items: center;
	margin-inline: 12px;
	height: 32px;
}

.block-editor #uxbuilder-edit-button svg {
	margin-inline-end: 5px;
}

.block-editor #uxbuilder-edit-button.is-busy {
	cursor: default;
	pointer-events: none;
}

html :where(.editor-styles-wrapper) {
  padding: 8px !important;
}

.editor-styles-wrapper {
	--wp--style--block-gap: 2em;
	font-family: -apple-system,BlinkMacSystemFont,Segoe UI,Roboto,Oxygen-Sans,Ubuntu,Cantarell,Helvetica Neue,sans-serif;
	font-size: 18px;
	line-height: 1.5;
}

.editor-styles-wrapper p {
	line-height: 1.8;
}

.editor-styles-wrapper .editor-post-title__block {
	font-size: 2.5em;
	font-weight: 800;
	margin-bottom: 1em;
	margin-top: 2em;
}

@media screen and (max-width: 782px) {
  .block-editor #uxbuilder-edit-button {
    font-size: 0;
  }

  .block-editor #uxbuilder-edit-button svg {
    margin-inline-end: 0px;
  }
}
