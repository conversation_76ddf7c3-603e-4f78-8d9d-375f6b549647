<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="71px" height="69px" viewBox="0 0 71 69" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 3.8.1 (29687) - http://www.bohemiancoding.com/sketch -->
    <title>blog-two-col</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0" y="0" width="70" height="50" rx="4"></rect>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="70" height="50" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
        <mask id="mask-4" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="70" height="50" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
        <rect id="path-5" x="13" y="10" width="20.1094501" height="30.4921875"></rect>
        <mask id="mask-6" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="20.1094501" height="30.4921875" fill="white">
            <use xlink:href="#path-5"></use>
        </mask>
        <rect id="path-7" x="38.3553936" y="32.1668125" width="20.1094501" height="30.4921875"></rect>
        <mask id="mask-8" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="20.1094501" height="30.4921875" fill="white">
            <use xlink:href="#path-7"></use>
        </mask>
        <rect id="path-9" x="13.3553936" y="45.1668125" width="20.1094501" height="30.4921875"></rect>
        <mask id="mask-10" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="20.1094501" height="30.4921875" fill="white">
            <use xlink:href="#path-9"></use>
        </mask>
        <rect id="path-11" x="38.3553936" y="10" width="20.1094501" height="16"></rect>
        <mask id="mask-12" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="20.1094501" height="16" fill="white">
            <use xlink:href="#path-11"></use>
        </mask>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="blog-two-col" transform="translate(0.213250, 0.236375)">
            <text id="Two-Col" font-family="Lato-Regular, Lato" font-size="13" font-weight="normal" fill="#9A8F9A">
                <tspan x="0" y="67">Two Col</tspan>
            </text>
            <mask id="mask-3" fill="white">
                <use xlink:href="#path-1"></use>
            </mask>
            <g id="Mask" stroke="#3498DB" mask="url(#mask-2)" stroke-width="2" fill-opacity="0.01" fill="#00A0D2">
                <use mask="url(#mask-4)" xlink:href="#path-1"></use>
            </g>
            <g id="Rectangle-5-Copy-7" mask="url(#mask-3)" stroke="#3498DB" stroke-width="2" fill="#3498DB" fill-opacity="0.127858922">
                <use mask="url(#mask-6)" xlink:href="#path-5"></use>
            </g>
            <g id="Rectangle-5-Copy-9" mask="url(#mask-3)" stroke="#3498DB" stroke-width="2" fill="#3498DB" fill-opacity="0.127858922">
                <use mask="url(#mask-8)" xlink:href="#path-7"></use>
            </g>
            <g id="Rectangle-5-Copy-10" mask="url(#mask-3)" stroke="#3498DB" stroke-width="2" fill="#3498DB" fill-opacity="0.127858922">
                <use mask="url(#mask-10)" xlink:href="#path-9"></use>
            </g>
            <g id="Rectangle-5-Copy-8" mask="url(#mask-3)" stroke="#3498DB" stroke-width="2" fill="#3498DB" fill-opacity="0.127858922">
                <use mask="url(#mask-12)" xlink:href="#path-11"></use>
            </g>
        </g>
    </g>
</svg>