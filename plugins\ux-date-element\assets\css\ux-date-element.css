/**
 * UX Date Element Frontend Styles
 * Version: 1.0.0
 */

.ux-date-element {
    display: inline-block;
    font-family: inherit;
    font-size: inherit;
    color: inherit;
    line-height: 1.4;
}

/* Default styling */
.ux-date-element {
    padding: 0;
    margin: 0;
}

/* Responsive behavior */
@media (max-width: 768px) {
    .ux-date-element {
        font-size: 0.9em;
    }
}

@media (max-width: 480px) {
    .ux-date-element {
        font-size: 0.8em;
    }
}
