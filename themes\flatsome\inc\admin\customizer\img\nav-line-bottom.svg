<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="42px" height="42px" viewBox="0 0 42 42" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 3.7.1 (28215) - http://www.bohemiancoding.com/sketch -->
    <title>nav-line-bottom</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0.617058501" y="0.185392157" width="39.2156863" height="39.2156863" rx="4"></rect>
        <mask id="mask-3" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="-1" y="-1" width="41.2156863" height="41.2156863">
            <rect x="-0.382941499" y="-0.814607843" width="41.2156863" height="41.2156863" fill="white"></rect>
            <use xlink:href="#path-1" fill="black"></use>
        </mask>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="nav-line-bottom" transform="translate(1.000000, 1.000000)">
            <mask id="mask-2" fill="white">
                <use xlink:href="#path-1"></use>
            </mask>
            <g id="Mask">
                <use fill-opacity="0.01" fill="#00A0D2" fill-rule="evenodd" xlink:href="#path-1"></use>
                <use stroke="#3498DB" mask="url(#mask-3)" stroke-width="2" xlink:href="#path-1"></use>
            </g>
            <rect id="Path" fill="#3498DB" opacity="0.420359142" mask="url(#mask-2)" x="9.75" y="27" width="21.5" height="3"></rect>
            <path d="M9.722,13.324 C9.79400036,13.324 9.85399976,13.327 9.902,13.333 C9.95000024,13.339 9.99299981,13.3509999 10.031,13.369 C10.0690002,13.3870001 10.1059998,13.4129998 10.142,13.447 C10.1780002,13.4810002 10.2179998,13.5259997 10.262,13.582 L14.816,19.384 C14.7999999,19.2439993 14.789,19.1070007 14.783,18.973 C14.777,18.8389993 14.774,18.7140006 14.774,18.598 L14.774,13.324 L16.196,13.324 L16.196,22 L15.362,22 C15.2339994,22 15.1280004,21.9800002 15.044,21.94 C14.9599996,21.8999998 14.8780004,21.8280005 14.798,21.724 L10.262,15.946 C10.2740001,16.0740006 10.283,16.2009994 10.289,16.327 C10.295,16.4530006 10.298,16.5679995 10.298,16.672 L10.298,22 L8.876,22 L8.876,13.324 L9.722,13.324 Z M25.574,22 L24.326,22 C24.1859993,22 24.0710005,21.9650003 23.981,21.895 C23.8909996,21.8249996 23.8260002,21.7380005 23.786,21.634 L23.138,19.864 L19.544,19.864 L18.896,21.634 C18.8639998,21.7260005 18.8010005,21.8099996 18.707,21.886 C18.6129995,21.9620004 18.4980007,22 18.362,22 L17.108,22 L20.516,13.324 L22.166,13.324 L25.574,22 Z M19.958,18.724 L22.724,18.724 L21.668,15.838 C21.6199998,15.7099994 21.5670003,15.5590009 21.509,15.385 C21.4509997,15.2109991 21.3940003,15.022001 21.338,14.818 C21.2819997,15.022001 21.2270003,15.2119991 21.173,15.388 C21.1189997,15.5640009 21.0660003,15.7179993 21.014,15.85 L19.958,18.724 Z M24.602,13.324 L25.904,13.324 C26.0440007,13.324 26.1579996,13.3579997 26.246,13.426 C26.3340004,13.4940003 26.3999998,13.5819995 26.444,13.69 L28.484,18.982 C28.5520003,19.1540009 28.6169997,19.342999 28.679,19.549 C28.7410003,19.755001 28.7999997,19.9719989 28.856,20.2 C28.9480005,19.7399977 29.0619993,19.3340018 29.198,18.982 L31.232,13.69 C31.2680002,13.5979995 31.3319995,13.5140004 31.424,13.438 C31.5160005,13.3619996 31.6299993,13.324 31.766,13.324 L33.068,13.324 L29.564,22 L28.106,22 L24.602,13.324 Z" id="NAV-Copy-3" fill="#3498DB" mask="url(#mask-2)"></path>
        </g>
    </g>
</svg>