<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="70px" height="51px" viewBox="0 0 70 51" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 3.7.2 (28276) - http://www.bohemiancoding.com/sketch -->
    <title>category-title-push</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0" y="0" width="70" height="50" rx="4"></rect>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="70" height="50" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
        <mask id="mask-4" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="70" height="50" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="category-title-push" transform="translate(0.000000, 0.767750)">
            <mask id="mask-3" fill="white">
                <use xlink:href="#path-1"></use>
            </mask>
            <g id="Mask" stroke="#3498DB" mask="url(#mask-2)" stroke-width="2" fill-opacity="0.01" fill="#00A0D2">
                <use mask="url(#mask-4)" xlink:href="#path-1"></use>
            </g>
            <rect id="Rectangle-502" fill="#3498DB" mask="url(#mask-3)" x="0" y="-2" width="70" height="35"></rect>
            <rect id="Rectangle-502" fill="#FFFFFF" mask="url(#mask-3)" x="8" y="19" width="76" height="54"></rect>
            <rect id="Rectangle-502" fill="#3498DB" opacity="0.2" mask="url(#mask-3)" x="11" y="22" width="76" height="54"></rect>
            <path d="M9,9.10300973 L9,13.4294182 L9.57564258,13.4294182 L9.57564258,11.4479958 L11.8539753,11.4479958 L11.8539753,13.4294182 L12.4296179,13.4294182 L12.4296179,9.10300973 L11.8539753,9.10300973 L11.8539753,10.9632442 L9.57564258,10.9632442 L9.57564258,9.10300973 L9,9.10300973 Z M13.7081504,11.266214 C13.7081504,11.0480746 13.7364273,10.8329682 13.7929819,10.6208883 C13.8495366,10.4088084 13.9384068,10.2179394 14.0595953,10.0482754 C14.1807839,9.87861152 14.3363068,9.74227648 14.5261688,9.63926624 C14.7160308,9.536256 14.942246,9.48475165 15.2048211,9.48475165 C15.4673962,9.48475165 15.6936114,9.536256 15.8834734,9.63926624 C16.0733354,9.74227648 16.2288584,9.87861152 16.3500469,10.0482754 C16.4712354,10.2179394 16.5601057,10.4088084 16.6166603,10.6208883 C16.6732149,10.8329682 16.7014918,11.0480746 16.7014918,11.266214 C16.7014918,11.4843533 16.6732149,11.6994597 16.6166603,11.9115396 C16.5601057,12.1236195 16.4712354,12.3144886 16.3500469,12.4841525 C16.2288584,12.6538164 16.0733354,12.7901514 15.8834734,12.8931617 C15.6936114,12.9961719 15.4673962,13.0476763 15.2048211,13.0476763 C14.942246,13.0476763 14.7160308,12.9961719 14.5261688,12.8931617 C14.3363068,12.7901514 14.1807839,12.6538164 14.0595953,12.4841525 C13.9384068,12.3144886 13.8495366,12.1236195 13.7929819,11.9115396 C13.7364273,11.6994597 13.7081504,11.4843533 13.7081504,11.266214 L13.7081504,11.266214 Z M13.1325078,11.266214 C13.1325078,11.561106 13.175933,11.8448849 13.2627848,12.117559 C13.3496366,12.3902332 13.4799123,12.6315967 13.6536158,12.8416568 C13.8273194,13.0517169 14.0434357,13.2183486 14.3019712,13.341557 C14.5605067,13.4647653 14.8614536,13.5263685 15.2048211,13.5263685 C15.5481886,13.5263685 15.8491356,13.4647653 16.1076711,13.341557 C16.3662066,13.2183486 16.5823229,13.0517169 16.7560264,12.8416568 C16.9297299,12.6315967 17.0600056,12.3902332 17.1468574,12.117559 C17.2337092,11.8448849 17.2771344,11.561106 17.2771344,11.266214 C17.2771344,10.9713219 17.2337092,10.687543 17.1468574,10.4148689 C17.0600056,10.1421947 16.9297299,9.9008312 16.7560264,9.6907711 C16.5823229,9.480711 16.3662066,9.3130694 16.1076711,9.18784126 C15.8491356,9.06261313 15.5481886,9 15.2048211,9 C14.8614536,9 14.5605067,9.06261313 14.3019712,9.18784126 C14.0434357,9.3130694 13.8273194,9.480711 13.6536158,9.6907711 C13.4799123,9.9008312 13.3496366,10.1421947 13.2627848,10.4148689 C13.175933,10.687543 13.1325078,10.9713219 13.1325078,11.266214 L13.1325078,11.266214 Z M17.9921431,9.10300973 L17.9921431,13.4294182 L18.5374887,13.4294182 L18.5374887,9.8301372 L18.5496075,9.8301372 L19.9008527,13.4294182 L20.3916638,13.4294182 L21.742909,9.8301372 L21.7550278,9.8301372 L21.7550278,13.4294182 L22.3003734,13.4294182 L22.3003734,9.10300973 L21.512652,9.10300973 L20.1432286,12.7386471 L18.7798645,9.10300973 L17.9921431,9.10300973 Z M23.2577579,9.10300973 L23.2577579,13.4294182 L26.2632181,13.4294182 L26.2632181,12.9446666 L23.8334005,12.9446666 L23.8334005,11.4479958 L26.0814363,11.4479958 L26.0814363,10.9632442 L23.8334005,10.9632442 L23.8334005,9.58776138 L26.2450399,9.58776138 L26.2450399,9.10300973 L23.2577579,9.10300973 Z M29.8564397,9 L28.068918,13.5263685 L28.5051945,13.5263685 L30.2987756,9 L29.8564397,9 Z M32.3407919,9.10300973 L32.3407919,13.4294182 L32.8861375,13.4294182 L32.8861375,9.95738451 L32.8982563,9.95738451 L35.1584109,13.4294182 L35.7885881,13.4294182 L35.7885881,9.10300973 L35.2432424,9.10300973 L35.2432424,12.6113998 L35.2311237,12.6113998 L32.9527909,9.10300973 L32.3407919,9.10300973 Z M37.4670406,11.6418965 L38.2002275,9.62411775 L38.2123463,9.62411775 L38.9334144,11.6418965 L37.4670406,11.6418965 Z M37.8972577,9.10300973 L36.2127457,13.4294182 L36.8005071,13.4294182 L37.2852588,12.1266481 L39.1151962,12.1266481 L39.5878291,13.4294182 L40.2240656,13.4294182 L38.5334943,9.10300973 L37.8972577,9.10300973 Z M42.3509135,13.4294182 L43.89,9.10300973 L43.2901198,9.10300973 L42.0418843,12.8537756 L42.0297655,12.8537756 L40.7936488,9.10300973 L40.1755905,9.10300973 L41.6964988,13.4294182 L42.3509135,13.4294182 Z" id="HOME-/-NAV" fill="#FFFFFF" mask="url(#mask-3)"></path>
        </g>
    </g>
</svg>