/**
 * UX User Elements Frontend Styles
 * Version: 2.0.0 - Multi-element support with enhanced styling options
 */

/* Base element styles */
.ux-date-element,
.ux-text-element {
    display: inline-block;
    font-family: inherit;
    font-size: inherit;
    color: inherit;
    line-height: 1.4;
    padding: 0;
    margin: 0;
}

/* Text alignment options */
.ux-date-element.text-left,
.ux-text-element.text-left {
    text-align: left;
}

.ux-date-element.text-center,
.ux-text-element.text-center {
    text-align: center;
}

.ux-date-element.text-right,
.ux-text-element.text-right {
    text-align: right;
}

/* Font size options */
.ux-date-element.font-size-small,
.ux-text-element.font-size-small {
    font-size: 0.8em;
}

.ux-date-element.font-size-medium,
.ux-text-element.font-size-medium {
    font-size: 1em;
}

.ux-date-element.font-size-large,
.ux-text-element.font-size-large {
    font-size: 1.2em;
}

.ux-date-element.font-size-xlarge,
.ux-text-element.font-size-xlarge {
    font-size: 1.5em;
}

/* Responsive behavior */
@media (max-width: 768px) {
    .ux-date-element {
        font-size: 0.95em;
    }

    .ux-date-element.font-size-small {
        font-size: 0.75em;
    }

    .ux-date-element.font-size-large {
        font-size: 1.1em;
    }

    .ux-date-element.font-size-xlarge {
        font-size: 1.3em;
    }
}

@media (max-width: 480px) {
    .ux-date-element {
        font-size: 0.9em;
    }

    .ux-date-element.font-size-small {
        font-size: 0.7em;
    }

    .ux-date-element.font-size-large {
        font-size: 1em;
    }

    .ux-date-element.font-size-xlarge {
        font-size: 1.2em;
    }
}

/* Additional utility classes for better integration */
.ux-date-element.highlight {
    font-weight: bold;
}

.ux-date-element.subtle {
    opacity: 0.8;
}

/* Ensure proper spacing in different contexts */
p .ux-date-element,
div .ux-date-element {
    margin: 0;
}

/* Block display option for full-width alignment */
.ux-date-element.display-block,
.ux-text-element.display-block {
    display: block;
    width: 100%;
}

/* Text element specific styles */
.ux-text-element a {
    color: inherit;
    text-decoration: inherit;
}

.ux-text-element a:hover {
    opacity: 0.8;
}

/* Font weight options for text elements */
.ux-text-element.font-weight-light {
    font-weight: 300;
}

.ux-text-element.font-weight-normal {
    font-weight: 400;
}

.ux-text-element.font-weight-bold {
    font-weight: 700;
}

/* Text transform options */
.ux-text-element.text-transform-uppercase {
    text-transform: uppercase;
}

.ux-text-element.text-transform-lowercase {
    text-transform: lowercase;
}

.ux-text-element.text-transform-capitalize {
    text-transform: capitalize;
}

/* Text decoration options */
.ux-text-element.text-decoration-underline {
    text-decoration: underline;
}

.ux-text-element.text-decoration-line-through {
    text-decoration: line-through;
}
