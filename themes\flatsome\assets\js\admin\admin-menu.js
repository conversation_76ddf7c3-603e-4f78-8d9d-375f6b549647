!function(t){"use strict";function e(){t(".ux-menu-item-options").each(((e,n)=>{const u=jQuery(n);u.hasClass("js-attached")||(function(e){e.find("select").on("change",(function(){const n=t(this).attr("id"),u=t(this).val(),a=e.parents("li.menu-item"),d=a.find(".ux-menu-item-options__width"),c=a.find(".ux-menu-item-options__height"),m=a.find(".ux-menu-item-options__media"),l=a.find(".ux-menu-item-options__icon-html"),r=0===i(a);"default"===u&&s([d,c]),"container-width"===u&&s([d,c]),"full-width"===u&&s([d,c]),"custom-size"===u&&o([d,c]),"media"===u&&(o([m]),s([l])),"html"===u&&(o([l]),s([m])),n.startsWith("edit-menu-item-block-")&&(r&&u?a.find(".menu-item-ux-block-indicator").length||a.find(".menu-item-title").before('<span class="menu-item-ux-block-indicator"><svg width="15" height="15" viewBox="0 0 15 15" fill="none" xmlns="http://www.w3.org/2000/svg"> <path d="M7.73009 2.41274L8.95709 3.63719L3.40181 9.18095L2.17482 7.95652L7.73009 2.41274ZM7.73009 0.242432L0 7.95652L3.40181 11.3513L11.1319 3.63719L7.73009 0.242432Z" fill="#007CBA"/> <path d="M7.8196 11.3114L8.95987 12.4493L7.8196 13.5873L6.67928 12.4493L7.8196 11.3114ZM7.8196 9.14111L4.50439 12.4492L7.8196 15.7575L11.1348 12.4492L7.8196 9.14087V9.14111Z" fill="#007CBA"/> <path d="M12.2322 6.90786L13.3725 8.0458L12.2322 9.18369L11.0921 8.04584L12.2323 6.90795L12.2322 6.90786ZM12.2323 4.73763L8.91699 8.04584L12.2322 11.3542L15.5474 8.04584L12.2322 4.73755L12.2323 4.73763Z" fill="#007CBA" fill-opacity="0.6"/> </svg></span>'):t(".menu-item-ux-block-indicator",a).remove())})),t(".ux-menu-item-options__media-control",e).on("click",".upload-button",(function(e){e.preventDefault();const n=t(this),i=n.data("item-id"),o=wp.media({multiple:!1}).on("select",(()=>{const e=o.state().get("selection").first().toJSON();t(".placeholder","#menu-item-"+i).attr("src",e.url).css("display","block"),n.parent().find("input:hidden:first").val(e.id).trigger("change"),n.parent().find(".remove-button").show()})).open()})).on("click",".remove-button",(function(e){e.preventDefault();const n=t(this),i=n.data("item-id");t(".placeholder","#menu-item-"+i).attr("src","").hide(),n.parent().find("input:hidden:first").val("").trigger("change"),n.hide()}))}(u),u.addClass("js-attached"))}))}function n(e){t(".ux-menu-item-options",e||document).each(((t,e)=>{const n=jQuery(e),u=n.parents("li.menu-item"),a=u.find(".ux-menu-item-options__section-dropdown");0===i(u)?o([a]):s([a]),n.find("select").trigger("change")}))}function i(t){const e=t.attr("class").match(/menu-item-depth-(\d+)/);return e&&e[1]?parseInt(e[1]):0}function o(t){t.forEach((t=>{t.slideDown()}))}function s(t){t.forEach((t=>{t.slideUp()}))}t(document).ready((()=>{e(),n(),wpNavMenu.menusChanged=!1,t("#menu-to-edit").on("sortstop",(function(t,e){setTimeout((()=>{n(e.item)}),1e3)}))})),t(document).on("menu-item-added",(()=>{e(),n()}))}(jQuery);