<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="70px" height="70px" viewBox="0 0 70 70" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 3.8.1 (29687) - http://www.bohemiancoding.com/sketch -->
    <title>cart-icon-basket</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0" y="0" width="70" height="70" rx="4"></rect>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="70" height="70" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
        <mask id="mask-4" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="70" height="70" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="cart-icon-basket">
            <mask id="mask-3" fill="white">
                <use xlink:href="#path-1"></use>
            </mask>
            <g id="Mask" stroke="#3498DB" mask="url(#mask-2)" stroke-width="2" fill-opacity="0.01" fill="#00A0D2">
                <use mask="url(#mask-4)" xlink:href="#path-1"></use>
            </g>
            <g id="shopping-basket" mask="url(#mask-3)" fill="#3498DB">
                <g transform="translate(15.000000, 18.000000)" id="Shape">
                    <path d="M37.5,14.8965517 C38.19,14.8965517 38.7791667,15.1390345 39.2675,15.624 C39.7558333,16.1089655 40,16.694069 40,17.3793103 C40,18.0645517 39.7558333,18.6496552 39.2675,19.1346207 C38.7791667,19.6195862 38.19,19.862069 37.5,19.862069 L37.2075,19.862069 L34.96125,32.7028966 C34.8570833,33.297931 34.5704167,33.7895172 34.10125,34.1776552 C33.6320833,34.5657931 33.0983333,34.7598621 32.5,34.7598621 L7.5,34.7598621 C6.90083333,34.7598621 6.36708333,34.5657931 5.89875,34.1776552 C5.43041667,33.7895172 5.14375,33.297931 5.03875,32.7028966 L2.7925,19.862069 L2.5,19.862069 C1.81,19.862069 1.22083333,19.6195862 0.7325,19.1346207 C0.244166667,18.6496552 -9.25185854e-17,18.0645517 0,17.3793103 C9.25185854e-17,16.694069 0.244166667,16.1089655 0.7325,15.624 C1.22083333,15.1390345 1.81,14.8965517 2.5,14.8965517 L37.5,14.8965517 L37.5,14.8965517 Z M9.4725,30.4137931 C9.81083333,30.3881379 10.0941667,30.2424828 10.3225,29.9768276 C10.5508333,29.7111724 10.6516667,29.4103448 10.625,29.0743448 L10,21.0053793 C9.97416667,20.6693793 9.8275,20.388 9.56,20.1612414 C9.2925,19.9344828 8.98958333,19.8343448 8.65125,19.8608276 C8.31291667,19.8873103 8.02958333,20.0329655 7.80125,20.2977931 C7.57291667,20.5626207 7.47208333,20.8634483 7.49875,21.2002759 L8.12375,29.2692414 C8.14958333,29.5928276 8.28291667,29.8642759 8.52375,30.0835862 C8.76458333,30.3028966 9.04791667,30.4129655 9.37375,30.4137931 L9.47125,30.4137931 L9.4725,30.4137931 Z M17.5,29.1724138 L17.5,21.1034483 C17.5,20.7674483 17.37625,20.4765517 17.12875,20.2307586 C16.88125,19.9849655 16.5883333,19.862069 16.25,19.862069 C15.9116667,19.862069 15.61875,19.9849655 15.37125,20.2307586 C15.12375,20.4765517 15,20.7674483 15,21.1034483 L15,29.1724138 C15,29.5084138 15.12375,29.7993103 15.37125,30.0451034 C15.61875,30.2908966 15.9116667,30.4137931 16.25,30.4137931 C16.5883333,30.4137931 16.88125,30.2908966 17.12875,30.0451034 C17.37625,29.7993103 17.5,29.5084138 17.5,29.1724138 L17.5,29.1724138 Z M25,29.1724138 L25,21.1034483 C25,20.7674483 24.87625,20.4765517 24.62875,20.2307586 C24.38125,19.9849655 24.0883333,19.862069 23.75,19.862069 C23.4116667,19.862069 23.11875,19.9849655 22.87125,20.2307586 C22.62375,20.4765517 22.5,20.7674483 22.5,21.1034483 L22.5,29.1724138 C22.5,29.5084138 22.62375,29.7993103 22.87125,30.0451034 C23.11875,30.2908966 23.4116667,30.4137931 23.75,30.4137931 C24.0883333,30.4137931 24.38125,30.2908966 24.62875,30.0451034 C24.87625,29.7993103 25,29.5084138 25,29.1724138 L25,29.1724138 Z M31.875,29.2692414 L32.5,21.2002759 C32.5258333,20.8642759 32.425,20.5634483 32.1975,20.2977931 C31.97,20.0321379 31.6866667,19.8864828 31.3475,19.8608276 C31.0083333,19.8351724 30.7054167,19.9353103 30.43875,20.1612414 C30.1720833,20.3871724 30.0254167,20.6685517 29.99875,21.0053793 L29.37375,29.0743448 C29.3479167,29.4103448 29.44875,29.7111724 29.67625,29.9768276 C29.90375,30.2424828 30.1870833,30.3881379 30.52625,30.4137931 L30.62375,30.4137931 C30.9495833,30.4137931 31.2329167,30.3037241 31.47375,30.0835862 C31.7145833,29.8634483 31.8479167,29.592 31.87375,29.2692414 L31.875,29.2692414 Z M9.2975,5.66317241 L7.48125,13.6551724 L4.9025,13.6551724 L6.875,5.10082759 C7.1225,3.96289655 7.70208333,3.03517241 8.61375,2.31765517 C9.52541667,1.60013793 10.5670833,1.24137931 11.73875,1.24137931 L15,1.24137931 C15,0.90537931 15.12375,0.614482759 15.37125,0.368689655 C15.61875,0.122896552 15.9116667,1.01451414e-16 16.25,5.55111512e-17 L23.75,5.55111512e-17 C24.0883333,5.55111512e-17 24.38125,0.122896552 24.62875,0.368689655 C24.87625,0.614482759 25,0.90537931 25,1.24137931 L28.26125,1.24137931 C29.4329167,1.24137931 30.4745833,1.60013793 31.38625,2.31765517 C32.2979167,3.03517241 32.8775,3.96289655 33.125,5.10082759 L35.0975,13.6551724 L32.51875,13.6551724 L30.7025,5.66317241 C30.5591667,5.0937931 30.2629167,4.62827586 29.81375,4.26662069 C29.3645833,3.90496552 28.8470833,3.72372414 28.26125,3.72289655 L25,3.72289655 C25,4.05889655 24.87625,4.3497931 24.62875,4.59558621 C24.38125,4.84137931 24.0883333,4.96427586 23.75,4.96427586 L16.25,4.96427586 C15.9116667,4.96427586 15.61875,4.84137931 15.37125,4.59558621 C15.12375,4.3497931 15,4.05889655 15,3.72289655 L11.73875,3.72289655 C11.1529167,3.72289655 10.6354167,3.90413793 10.18625,4.26662069 C9.73708333,4.62910345 9.44083333,5.09462069 9.2975,5.66317241 L9.2975,5.66317241 Z"></path>
                </g>
            </g>
        </g>
    </g>
</svg>