.banner .uxb-shortcode-dragging{background-color:rgba(var(--fs-color-primary),.3)!important;border:1px solid rgba(var(--fs-color-primary),.5)!important;opacity:.8!important}ux-banner-tool{display:block;position:absolute}ux-banner-tool .grid-h-center,ux-banner-tool .grid-v-center{backface-visibility:hidden;background:hsla(0,0%,100%,.3);position:absolute}ux-banner-tool .grid-h-center.active,ux-banner-tool .grid-v-center.active{background:#00a0d2}ux-banner-tool .grid-v-center{bottom:0;left:50%;margin-left:-1px;top:0;width:2px}ux-banner-tool .grid-h-center{height:2px;left:0;margin-top:-1px;right:0;top:50%}.google-map .map_overlay{background-color:red;height:100%;opacity:0;position:absolute;top:0;width:100%}.scroll-to,.uxb-sticky-banner:after,.uxb-sticky-column:after,.uxb-sticky-section:after{background-color:rgba(0,115,170,.2);background-image:url(data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAUAAAAFCAYAAACNbyblAAAAKElEQVQIW2NkQAfFq4wZUcSKVxkz9IadRQhCBUCKIIJIAhBBNAGQIABh4g1WC/w5bAAAAABJRU5ErkJggg==);border:1px solid rgba(0,115,170,.2);color:rgba(0,115,170,.9);display:block!important;height:30px;margin-bottom:-30px;opacity:.8;position:absolute;text-align:center;width:100%!important;z-index:999999}.scroll-to:before{color:#fff;content:attr(data-label);font-size:14px;left:0;padding-top:5px;position:absolute;text-align:center;top:0;width:100%}.has-hover-effect{outline:1px dashed rgba(var(--fs-color-primary),.3)}.banner-grid .grid-size{pointer-events:none}.banner-grid .col-inner>content{bottom:0;left:0;margin:0;position:absolute;right:0;top:0}.packery-drop-placeholder{display:none;opacity:0;visibility:hidden}.img-container{transition:opacity .6s,box-shadow .6s!important}.img-container img{pointer-events:none}.temp-image{background-color:red;color:#fff}.slider .flickity-viewport .flickity-slider{top:0}.slider .flickity-viewport .flickity-slider>*{position:absolute;top:0}.slider .flickity-prev-next-button{pointer-events:none}.slider .flickity-prev-next-button svg{pointer-events:all}.slider .flickity-page-dots{pointer-events:none}.slider .flickity-page-dots .dot{pointer-events:all}.stack>content{min-width:90px}.stack>.uxb-wrapper--text{width:auto}.stack>.uxb-wrapper--text>[ng-bind-html^="shortcode.content"]>:first-child{margin-top:0}.stack>.uxb-wrapper--text>[ng-bind-html^="shortcode.content"]>:last-child{margin-bottom:0}.stack-col>*+.uxb-placeholder{display:block;height:5px;width:100%}
