!function(){"use strict";function e(e){switch(e){case"0":case"false":case!1:return!1;case"1":case"true":case!0:return!0;default:return Boolean(e)}}const t=window.matchMedia("(prefers-reduced-motion: reduce)");let r=!1;function o(){r="undefined"==typeof UxBuilder&&t.matches}function s(e,t={}){"boolean"==typeof t&&(t={processing:t});const{style:r="normal",position:o="",processing:s=!0}=t;let n;if("string"==typeof e?n=document.querySelector(e):e instanceof Element?n=e:e instanceof jQuery&&(n=e.get(0)),!n)return;if(!s){const e=n.querySelector(".ux-loader");return e&&e.remove(),void Array.from(n.children).forEach((e=>{e.style.opacity=""}))}"static"===window.getComputedStyle(n).position&&(n.style.position="relative");const i=function(e,t){const r=["ux-loader"];["normal","spotlight"].includes(e)&&r.push(`ux-loader--style-${e}`),["sticky"].includes(t)&&r.push(`ux-loader--position-${t}`);const o=document.createElement("div");o.className=r.join(" ");const s=document.createElement("div");s.className="ux-loader__inner";const n=document.createElement("div");return n.className="loading-spin centered",s.appendChild(n),o.appendChild(s),o}(r,o);n.insertAdjacentElement("afterbegin",i),Array.from(n.children).forEach((e=>{e!==i&&(e.style.opacity=".2")}))}function n(e,t={}){return new Promise((r=>{jQuery.scrollTo(e,{...t,onAfter:()=>{requestAnimationFrame((()=>{r()}))}})}))}o(),t.addEventListener?.("change",o),document.documentElement.style,window.getComputedStyle(document.documentElement)["scroll-behavior"];class i{constructor(){this.$body=jQuery(document.body),this.$body.hasClass("ux-pjax-js-attached")||(this.$archiveWooCommerce=jQuery(".archive.woocommerce"),this.ajaxXHR=null,this.currentEntry=null,this.params=this.getParams(),this.attach())}getParams(){let{scroll_to:t="",cache_bust:r=!1,timeout:o="5000",elements:s=[],entries:n={}}=flatsomePjax||{};return n={...n,shopWithoutSelectors:{selectors:[],processing_elements:{".shop-container":{style:"spotlight",position:"sticky"}}}},Object.entries(n).forEach((([e,r])=>{n[e].elements=Array.isArray(r.elements)?r.elements:s,n[e].processing_elements=n[e].processing_elements?n[e].processing_elements:n[e].elements,n[e].processing_elements=this.normalizeObject(n[e].processing_elements),n[e].scroll_to=void 0!==n[e].scroll_to?n[e].scroll_to:t})),{scrollTo:t,cacheBust:e(r),timeout:parseInt(o,10),entries:n}}attach(){this.$body.on("price_slider_change",((e,t)=>this.onPriceSliderChange(e,t))),this.entriesSelectors=Object.values(this.params.entries).flatMap((e=>e.selectors)),this.$body.on("click",this.entriesSelectors.join(", "),(e=>this.onClick(e))),this.$archiveWooCommerce.find(".woocommerce-ordering").off("change"),this.$archiveWooCommerce.on("change",".woocommerce-ordering",(e=>this.onOrderingChange(e))),this.$body.on("experimental-flatsome-pjax",((e,t,r)=>this.onFlatsomePjax(e,t,r))),this.$body.on("experimental-flatsome-pjax-before-replace-elements",((e,t,r)=>this.onBeforeElementsReplacement(e,t,r))),this.$body.on("experimental-flatsome-pjax-before-send-request",((e,t,r)=>this.onBeforeSendRequest(e,t,r))),this.$body.on("experimental-flatsome-pjax-request-done",((e,t,r)=>this.onRequestDone(e,t,r))),window.addEventListener("popstate",(e=>this.onPopstate(e))),this.$body.addClass("ux-pjax-js-attached")}onPriceSliderChange(e,t){if(!this.$body.hasClass("ux-shop-ajax-filters"))return;const r=jQuery(".price_slider").closest("form").get(0),o=jQuery(r),s=o.attr("action")+"?"+o.serialize();this.currentEntry=this.params.entries.shopWithoutSelectors,this.$body.trigger("experimental-flatsome-pjax",s,jQuery(e.target))}onClick(e){const t=jQuery(e.currentTarget);let r,o;if(t.is("li")?(o=t.find("a"),r=o.attr("href")):(o=t,r=t.attr("href")),r){e.preventDefault();const t=e.currentTarget;let s=null;this.entriesSelectors.forEach((e=>{t.matches(e)&&(s=e)}));const n=Object.entries(this.params.entries).find((([,e])=>e.selectors.includes(s)))[0];this.currentEntry=this.params.entries[n],this.$body.trigger("experimental-flatsome-pjax",r,o)}}onOrderingChange(e){const t=jQuery(e.currentTarget),r=t.find(".orderby"),o=new URL(window.location.href),s=o.searchParams;s.set("orderby",r.val()),o.search=s.toString(),this.currentEntry=this.params.entries.shopWithoutSelectors,this.$body.trigger("experimental-flatsome-pjax",o.toString(),t)}onFlatsomePjax(t,r,o){this.ajaxXHR&&this.ajaxXHR.abort(),"?"===r.slice(-1)&&(r=r.slice(0,-1)),r=r.replace(/%2C/g,","),window.history.pushState(null,null,r),this.params.cacheBust&&(r+=(/[?&]/.test(r)?"&":"?")+(new Date).getTime());const s=e(this.currentEntry.scroll_to),i="top"===this.currentEntry.scroll_to?0:this.currentEntry.scroll_to;let a=Promise.resolve();if(s)if(0===i)0!==window.scrollY&&(a=n(0));else{const e=document.querySelector(i);e&&!function(e){const t=e.getBoundingClientRect();return t.top>=0&&t.left>=0&&t.bottom<=(window.innerHeight||document.documentElement.clientHeight)&&t.right<=(window.innerWidth||document.documentElement.clientWidth)}(e)&&(a=n(i,{offset:-window.flatsomeVars.scrollPaddingTop-15}))}this.$body.trigger("experimental-flatsome-pjax-before-send-request",[r,o]),this.ajaxXHR=jQuery.ajax({url:r,dataType:"html",timeout:this.params.timeout}).done((e=>{const t=()=>{this.$body.trigger("experimental-flatsome-pjax-before-replace-elements",[e,r]);const t=(new DOMParser).parseFromString(e,"text/html");t.body.classList.forEach((e=>{e.endsWith("-no-js")&&t.body.classList.remove(e)})),t.querySelector(".page-loader")?.remove(),document.title=t.title,document.body.setAttribute("class",t.body.classList),this.currentEntry.elements.map((e=>[e,document.querySelector(e)])).forEach((([e,r])=>{if(!r?.isConnected)return;Flatsome.detach(r);const o=t.querySelector(e);o?jQuery(r).replaceWith(o.outerHTML):r.remove()})),this.$body.trigger("experimental-flatsome-pjax-request-done",[e,r])};jQuery.when(a).then((()=>{t()}))})).fail(((e,t,o)=>{"timeout"===t?(console.error("Flatsome: the request for "+r+" timed out."),window.location=r):console.error("Flatsome: "+o)})),this.$body.trigger("experimental-flatsome-pjax-after-send-request",[r,o])}onPopstate(e){window.location.reload()}onBeforeSendRequest(e,t,r){jQuery.fn.magnificPopup&&jQuery.magnificPopup.close();for(const[e,t]of Object.entries(this.currentEntry.processing_elements))s(e,{...t&&t.style?{style:t.style}:{},...t&&t.position?{position:t.position}:{}})}onBeforeElementsReplacement(e,t,r){}onRequestDone(e,t,r){if(this.currentEntry.elements.forEach((e=>{Flatsome.attach(jQuery(e))})),this.currentEntry=null,this.stagger(),jQuery(document.body).trigger("init_price_filter"),jQuery("div.widget_shopping_cart_content").length&&jQuery(document.body).trigger("wc_fragment_refresh"),window.ga&&ga.loaded&&"function"==typeof ga){const e=document.createElement("a");e.href=r,ga("set","page",e.pathname),ga("send","pageview")}}stagger(){r||jQuery(".shop-container .products:not(.has-packery)").addClass("ux-stagger")}normalizeObject(e){const t=Object.entries(e).map((([e,t])=>isNaN(e)?[e,t]:[t,{}]));return Object.fromEntries(t)}}jQuery((()=>new i))}();