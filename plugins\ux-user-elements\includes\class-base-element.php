<?php
/**
 * Base Element Class for UX User Elements
 * 
 * This abstract class provides the foundation for all custom UX Builder elements
 * 
 * @package UX_User_Elements
 * @version 1.0.0
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Abstract Base Element Class
 */
abstract class UX_User_Elements_Base_Element {
    
    /**
     * Element tag name (must be unique)
     */
    protected $tag = '';
    
    /**
     * Element configuration
     */
    protected $config = array();
    
    /**
     * Constructor
     */
    public function __construct() {
        $this->init();
    }
    
    /**
     * Initialize the element
     */
    protected function init() {
        // Set default configuration
        $this->config = $this->get_default_config();
        
        // Allow child classes to modify config
        $this->setup_config();
        
        // Register with UX Builder if available
        if (function_exists('add_ux_builder_shortcode')) {
            add_action('ux_builder_setup', array($this, 'register_ux_builder_element'));
        }
        
        // Always register shortcode
        add_shortcode($this->tag, array($this, 'render_shortcode'));
    }
    
    /**
     * Get default element configuration
     */
    protected function get_default_config() {
        return array(
            'name'     => '',
            'category' => __('Content', 'ux-user-elements'),
            'priority' => 1,
            'options'  => array(),
        );
    }
    
    /**
     * Setup element configuration (override in child classes)
     */
    abstract protected function setup_config();
    
    /**
     * Register element with UX Builder
     */
    public function register_ux_builder_element() {
        if (!empty($this->tag) && !empty($this->config)) {
            add_ux_builder_shortcode($this->tag, $this->config);
        }
    }
    
    /**
     * Render shortcode (override in child classes)
     */
    abstract public function render_shortcode($atts, $content = '');
    
    /**
     * Get sanitized attributes with defaults
     */
    protected function get_attributes($atts, $defaults = array()) {
        $atts = shortcode_atts($defaults, $atts, $this->tag);
        
        // Sanitize all attributes
        foreach ($atts as $key => $value) {
            $atts[$key] = $this->sanitize_attribute($key, $value);
        }
        
        return $atts;
    }
    
    /**
     * Sanitize individual attribute
     */
    protected function sanitize_attribute($key, $value) {
        // Color values
        if (strpos($key, 'color') !== false) {
            return sanitize_hex_color($value);
        }
        
        // CSS classes
        if (strpos($key, 'class') !== false) {
            return sanitize_html_class($value);
        }
        
        // URLs
        if (strpos($key, 'url') !== false || strpos($key, 'link') !== false) {
            return esc_url($value);
        }
        
        // Default text sanitization
        return sanitize_text_field($value);
    }
    
    /**
     * Build CSS classes array
     */
    protected function build_css_classes($base_class, $atts = array()) {
        $classes = array($base_class);
        
        // Add alignment class
        if (!empty($atts['text_align'])) {
            $classes[] = 'text-' . sanitize_html_class($atts['text_align']);
        }
        
        // Add font size class
        if (!empty($atts['font_size']) && $atts['font_size'] !== 'custom') {
            $classes[] = 'font-size-' . sanitize_html_class($atts['font_size']);
        }
        
        // Add custom CSS class
        if (!empty($atts['css_class'])) {
            $classes[] = sanitize_html_class($atts['css_class']);
        }
        
        return $classes;
    }
    
    /**
     * Build inline styles array
     */
    protected function build_inline_styles($atts = array()) {
        $styles = array();
        
        // Custom font size
        if (!empty($atts['font_size']) && $atts['font_size'] === 'custom' && !empty($atts['custom_font_size'])) {
            $styles[] = 'font-size: ' . esc_attr($atts['custom_font_size']);
        }
        
        // Text color
        if (!empty($atts['text_color'])) {
            $styles[] = 'color: ' . esc_attr($atts['text_color']);
        }
        
        // Background color
        if (!empty($atts['background_color'])) {
            $styles[] = 'background-color: ' . esc_attr($atts['background_color']);
        }
        
        return $styles;
    }
    
    /**
     * Get common styling options for UX Builder
     */
    protected function get_common_styling_options() {
        return array(
            'text_align' => array(
                'type'    => 'radio-buttons',
                'heading' => __('Text Alignment', 'ux-user-elements'),
                'default' => 'left',
                'options' => array(
                    'left'   => __('Left', 'ux-user-elements'),
                    'center' => __('Center', 'ux-user-elements'),
                    'right'  => __('Right', 'ux-user-elements'),
                ),
            ),
            'font_size' => array(
                'type'    => 'select',
                'heading' => __('Font Size', 'ux-user-elements'),
                'default' => 'medium',
                'options' => array(
                    'small'   => __('Small', 'ux-user-elements'),
                    'medium'  => __('Medium', 'ux-user-elements'),
                    'large'   => __('Large', 'ux-user-elements'),
                    'xlarge'  => __('Extra Large', 'ux-user-elements'),
                    'custom'  => __('Custom', 'ux-user-elements'),
                ),
            ),
            'custom_font_size' => array(
                'type'        => 'textfield',
                'heading'     => __('Custom Font Size', 'ux-user-elements'),
                'default'     => '',
                'placeholder' => __('e.g., 18px, 1.2em, 120%', 'ux-user-elements'),
                'conditions'  => 'font_size === "custom"',
            ),
            'text_color' => array(
                'type'    => 'colorpicker',
                'heading' => __('Text Color', 'ux-user-elements'),
                'default' => '',
            ),
            'css_class' => array(
                'type'        => 'textfield',
                'heading'     => __('CSS Class', 'ux-user-elements'),
                'default'     => '',
                'placeholder' => __('custom-style', 'ux-user-elements'),
                'description' => __('Add custom CSS class for additional styling', 'ux-user-elements'),
            ),
        );
    }
    
    /**
     * Render HTML element with classes and styles
     */
    protected function render_element($tag, $content, $classes = array(), $styles = array(), $attributes = array()) {
        $class_attr = !empty($classes) ? ' class="' . esc_attr(implode(' ', $classes)) . '"' : '';
        $style_attr = !empty($styles) ? ' style="' . implode('; ', $styles) . '"' : '';
        
        $extra_attrs = '';
        foreach ($attributes as $attr => $value) {
            $extra_attrs .= ' ' . esc_attr($attr) . '="' . esc_attr($value) . '"';
        }
        
        return sprintf(
            '<%s%s%s%s>%s</%s>',
            $tag,
            $class_attr,
            $style_attr,
            $extra_attrs,
            $content,
            $tag
        );
    }
}
