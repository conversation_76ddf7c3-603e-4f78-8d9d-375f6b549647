<?php
/**
 * Test Page for UX Date Element
 * 
 * This file can be used to test the plugin functionality
 * Access via: your-site.com/wp-content/plugins/ux-date-element/test-page.php
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if user has admin privileges
if (!current_user_can('manage_options')) {
    wp_die('You do not have permission to access this page.');
}

?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>UX Date Element - Test Page</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .test-section {
            background: #f9f9f9;
            padding: 15px;
            margin: 20px 0;
            border-left: 4px solid #0073aa;
        }
        .test-result {
            background: #fff;
            padding: 10px;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .pass { border-left: 4px solid #46b450; }
        .fail { border-left: 4px solid #dc3232; }
        .info { border-left: 4px solid #00a0d2; }
        code {
            background: #f1f1f1;
            padding: 2px 4px;
            border-radius: 3px;
            font-family: monospace;
        }
        table {
            width: 100%;
            border-collapse: collapse;
            margin: 15px 0;
        }
        th, td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: left;
        }
        th {
            background-color: #f2f2f2;
        }
    </style>
</head>
<body>
    <h1>UX Date Element - Test Page</h1>
    
    <div class="test-section">
        <h2>Plugin Status Check</h2>
        <?php
        // Check if plugin is active
        $plugin_active = is_plugin_active('ux-date-element/ux-date-element.php');
        $class = $plugin_active ? 'pass' : 'fail';
        echo "<div class='test-result {$class}'>";
        echo "<strong>Plugin Active:</strong> " . ($plugin_active ? '✅ Yes' : '❌ No');
        echo "</div>";
        
        // Check if UX Builder is available
        $ux_builder_available = function_exists('add_ux_builder_shortcode');
        $class = $ux_builder_available ? 'pass' : 'info';
        echo "<div class='test-result {$class}'>";
        echo "<strong>UX Builder Available:</strong> " . ($ux_builder_available ? '✅ Yes' : 'ℹ️ No (Flatsome theme required)');
        echo "</div>";
        
        // Check if shortcode is registered
        global $shortcode_tags;
        $shortcode_registered = isset($shortcode_tags['ux_current_date']);
        $class = $shortcode_registered ? 'pass' : 'fail';
        echo "<div class='test-result {$class}'>";
        echo "<strong>Shortcode Registered:</strong> " . ($shortcode_registered ? '✅ Yes' : '❌ No');
        echo "</div>";
        ?>
    </div>
    
    <div class="test-section">
        <h2>Shortcode Output Tests</h2>
        
        <table>
            <thead>
                <tr>
                    <th>Shortcode</th>
                    <th>Output</th>
                    <th>Status</th>
                </tr>
            </thead>
            <tbody>
                <?php
                $test_shortcodes = array(
                    '[ux_current_date]' => 'Default format',
                    '[ux_current_date format="Y-m-d"]' => 'ISO date format',
                    '[ux_current_date format="m/d/Y"]' => 'US date format',
                    '[ux_current_date format="d/m/Y"]' => 'European date format',
                    '[ux_current_date format="l, F j, Y"]' => 'Full date with day name',
                );
                
                foreach ($test_shortcodes as $shortcode => $description) {
                    $output = do_shortcode($shortcode);
                    $has_output = !empty($output) && strlen(trim($output)) > 0;
                    $status = $has_output ? '✅ Working' : '❌ Failed';
                    $class = $has_output ? 'pass' : 'fail';
                    
                    echo "<tr class='{$class}'>";
                    echo "<td><code>" . esc_html($shortcode) . "</code><br><small>{$description}</small></td>";
                    echo "<td>" . $output . "</td>";
                    echo "<td>{$status}</td>";
                    echo "</tr>";
                }
                ?>
            </tbody>
        </table>
    </div>
    
    <div class="test-section">
        <h2>HTML Structure Test</h2>
        <?php
        $output = do_shortcode('[ux_current_date]');
        $has_wrapper = strpos($output, 'class="ux-date-element"') !== false;
        $class = $has_wrapper ? 'pass' : 'fail';
        echo "<div class='test-result {$class}'>";
        echo "<strong>CSS Class Present:</strong> " . ($has_wrapper ? '✅ Yes' : '❌ No');
        echo "</div>";
        
        echo "<div class='test-result info'>";
        echo "<strong>Raw HTML Output:</strong><br>";
        echo "<code>" . esc_html($output) . "</code>";
        echo "</div>";
        ?>
    </div>
    
    <div class="test-section">
        <h2>Security Test</h2>
        <?php
        // Test XSS protection
        $malicious_input = '<script>alert("xss")</script>';
        $output = do_shortcode('[ux_current_date format="' . $malicious_input . '"]');
        $is_safe = strpos($output, '<script>') === false;
        $class = $is_safe ? 'pass' : 'fail';
        echo "<div class='test-result {$class}'>";
        echo "<strong>XSS Protection:</strong> " . ($is_safe ? '✅ Secure' : '❌ Vulnerable');
        echo "</div>";
        ?>
    </div>
    
    <div class="test-section">
        <h2>CSS Styling Test</h2>
        <?php
        // Check if CSS file exists
        $css_file = UX_DATE_ELEMENT_PLUGIN_DIR . 'assets/css/ux-date-element.css';
        $css_exists = file_exists($css_file);
        $class = $css_exists ? 'pass' : 'fail';
        echo "<div class='test-result {$class}'>";
        echo "<strong>CSS File Exists:</strong> " . ($css_exists ? '✅ Yes' : '❌ No');
        echo "</div>";
        
        // Display styled output
        if ($css_exists) {
            echo "<div class='test-result info'>";
            echo "<strong>Styled Output Example:</strong><br>";
            echo "<style>" . file_get_contents($css_file) . "</style>";
            echo do_shortcode('[ux_current_date format="l, F j, Y"]');
            echo "</div>";
        }
        ?>
    </div>
    
    <div class="test-section">
        <h2>Performance Test</h2>
        <?php
        $start_time = microtime(true);
        for ($i = 0; $i < 100; $i++) {
            do_shortcode('[ux_current_date]');
        }
        $end_time = microtime(true);
        $execution_time = ($end_time - $start_time) * 1000; // Convert to milliseconds
        
        $class = $execution_time < 50 ? 'pass' : ($execution_time < 100 ? 'info' : 'fail');
        echo "<div class='test-result {$class}'>";
        echo "<strong>100 Shortcode Executions:</strong> " . number_format($execution_time, 2) . "ms";
        echo "</div>";
        ?>
    </div>
    
    <div class="test-section">
        <h2>Next Steps</h2>
        <div class="test-result info">
            <h3>To test UX Builder integration:</h3>
            <ol>
                <li>Ensure Flatsome theme is active</li>
                <li>Go to WordPress Admin → Plugins and activate "UX Date Element"</li>
                <li>Edit a page with UX Builder</li>
                <li>Look for "Current Date" in the Content elements panel</li>
                <li>Drag the element to your page and configure options</li>
            </ol>
        </div>
        
        <div class="test-result info">
            <h3>Run comprehensive tests:</h3>
            <ul>
                <li><a href="tests/test-plugin-activation.php?run_activation_tests=1" target="_blank">Plugin Activation Tests</a></li>
                <li><a href="tests/test-shortcode-output.php?run_shortcode_tests=1" target="_blank">Shortcode Output Tests</a></li>
            </ul>
        </div>
    </div>
    
    <p><a href="<?php echo admin_url('plugins.php'); ?>">← Back to WordPress Plugins</a></p>
</body>
</html>
