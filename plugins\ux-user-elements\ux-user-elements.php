<?php
/**
 * Plugin Name: UX User Elements
 * Plugin URI: https://example.com/ux-user-elements
 * Description: A collection of custom elements for UX Builder (Flatsome theme) including date display, custom text, and more.
 * Version: 2.0.0
 * Author: Your Name
 * Author URI: https://example.com
 * License: GPL v2 or later
 * License URI: https://www.gnu.org/licenses/gpl-2.0.html
 * Text Domain: ux-user-elements
 * Domain Path: /languages
 * Requires at least: 5.0
 * Tested up to: 6.4
 * Requires PHP: 7.4
 * Network: false
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('UX_USER_ELEMENTS_VERSION', '2.0.0');
define('UX_USER_ELEMENTS_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('UX_USER_ELEMENTS_PLUGIN_URL', plugin_dir_url(__FILE__));
define('UX_USER_ELEMENTS_PLUGIN_FILE', __FILE__);

// Load the main plugin class
require_once UX_USER_ELEMENTS_PLUGIN_DIR . 'includes/class-ux-user-elements.php';

/**
 * Initialize the plugin
 */
function ux_user_elements_init() {
    return UX_User_Elements::get_instance();
}

// Initialize the plugin
add_action('plugins_loaded', 'ux_user_elements_init');
