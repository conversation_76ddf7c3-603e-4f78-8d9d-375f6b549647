@charset "UTF-8";
.customize-control-kirki-background {
  position: relative;
}
.customize-control-kirki-background .iris-picker-inner {
  display: grid;
  grid-template-columns: 1fr 20px 20px;
  grid-gap: 7px;
}
.customize-control-kirki-background .iris-picker-inner > * {
  width: 100% !important;
  margin-left: 0 !important;
}
.customize-control-kirki-background .background-attachment h4,
.customize-control-kirki-background .background-color h4,
.customize-control-kirki-background .background-position h4,
.customize-control-kirki-background .background-repeat h4,
.customize-control-kirki-background .background-size h4 {
  margin-bottom: 5px;
}
.customize-control-kirki-background .background-attachment .buttonset,
.customize-control-kirki-background .background-size .buttonset {
  display: flex;
  flex-wrap: wrap;
}
.customize-control-kirki-background .background-attachment .buttonset .switch-label,
.customize-control-kirki-background .background-size .buttonset .switch-label {
  background: rgba(0, 0, 0, 0.05);
  border: 1px solid rgba(0, 0, 0, 0.1);
  color: #555;
  padding: 0.5em 1em;
  margin: 0;
  text-align: center;
  flex-grow: 1;
}
.customize-control-kirki-background .background-attachment .buttonset .switch-input:checked + .switch-label,
.customize-control-kirki-background .background-size .buttonset .switch-input:checked + .switch-label {
  background-color: #3498DB;
  color: #fff;
}

.customize-control-kirki-code textarea {
  width: 100%;
  min-height: 200px;
}

.customize-control-kirki-color-palette {
  position: relative;
}
.customize-control-kirki-color-palette label {
  position: relative;
  display: inline-block;
  padding: 0;
  margin: 0;
}
.customize-control-kirki-color-palette .colors-wrapper {
  max-height: 300px;
  overflow-y: auto;
  padding: 10px;
  display: flex;
  flex-wrap: wrap;
}
.customize-control-kirki-color-palette .colors-wrapper .color-palette-color {
  color: transparent;
  display: block;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border: 1px solid rgba(0, 0, 0, 0.2);
}
.customize-control-kirki-color-palette .colors-wrapper.round label {
  padding: 3px;
}
.customize-control-kirki-color-palette .colors-wrapper.round .color-palette-color {
  border-radius: 50%;
}
.customize-control-kirki-color-palette .colors-wrapper.box-shadow .color-palette-color {
  box-shadow: inset 3px 3px 13px 2px rgba(0, 0, 0, 0.2);
}
.customize-control-kirki-color-palette .colors-wrapper input:checked + label .color-palette-color {
  border: 0;
  border: 4px solid #3498DB;
  position: relative;
  left: -3px;
  top: -3px;
  z-index: 1;
}
.customize-control-kirki-color-palette .colors-wrapper.with-margin label {
  margin: 3px;
}
.customize-control-kirki-color-palette .colors-wrapper input {
  display: none;
}
.customize-control-kirki-color input[data-type=hue] + .iris-strip-horiz .iris-slider {
  background-image: -webkit-linear-gradient(left, red, #ff7f00, yellow, #80ff00, lime, #00ff80, aqua, #007fff, blue, #7f00ff, fuchsia, #ff0080, red) !important;
  widows: 100% !important;
}
.customize-control-kirki-color .kirki-input-container[data-has-alpha=true] .iris-picker-inner {
  display: grid;
  grid-template-columns: 1fr 20px 20px;
  grid-gap: 7px;
}
.customize-control-kirki-color .kirki-input-container[data-has-alpha=true] .iris-picker-inner > * {
  width: 100% !important;
  margin-left: 0 !important;
}
.customize-control-kirki-color .iris-only-strip {
  width: 100% !important;
}

.rtl .customize-control-kirki-color .iris-border .iris-palette-container {
  left: auto;
  right: 10px;
}
.rtl .customize-control-kirki-color .wp-color-result .color-alpha {
  left: auto !important;
  right: 0;
}

.customize-control-kirki-dashicons {
  position: relative;
}
.customize-control-kirki-dashicons label {
  position: relative;
  display: inline-block;
}
.customize-control-kirki-dashicons .icons-wrapper {
  max-height: 300px;
  overflow-y: scroll;
}
.customize-control-kirki-dashicons .icons-wrapper h4 {
  font-weight: 300;
  margin: 0.7em 0;
}
.customize-control-kirki-dashicons .icons-wrapper .dashicons {
  padding: 3px;
  font-size: 25px;
  width: 25px;
  height: 25px;
  border: 2px solid transparent;
}
.customize-control-kirki-dashicons .icons-wrapper input {
  display: none;
}
.customize-control-kirki-dashicons .icons-wrapper input:checked + label .dashicons {
  border: 2px solid #3498DB;
  color: #000;
}

.wp-customizer div.ui-datepicker {
  z-index: 500001 !important;
  width: 255px;
  background: #fff;
  border: 1px solid #dedede;
  padding: 4px;
}
.wp-customizer div.ui-datepicker .ui-datepicker-header {
  padding: 10px;
  font-weight: 700;
}
.wp-customizer div.ui-datepicker .ui-datepicker-header .ui-datepicker-next,
.wp-customizer div.ui-datepicker .ui-datepicker-header .ui-datepicker-prev {
  padding: 10px;
  display: block;
  position: absolute;
  width: 1em;
  overflow: hidden;
  cursor: pointer;
}
.wp-customizer div.ui-datepicker .ui-datepicker-header .ui-datepicker-next:after, .wp-customizer div.ui-datepicker .ui-datepicker-header .ui-datepicker-next:before,
.wp-customizer div.ui-datepicker .ui-datepicker-header .ui-datepicker-prev:after,
.wp-customizer div.ui-datepicker .ui-datepicker-header .ui-datepicker-prev:before {
  font-family: dashicons;
}
.wp-customizer div.ui-datepicker .ui-datepicker-header .ui-datepicker-next .ui-icon,
.wp-customizer div.ui-datepicker .ui-datepicker-header .ui-datepicker-prev .ui-icon {
  display: none;
}
.wp-customizer div.ui-datepicker .ui-datepicker-header .ui-datepicker-prev {
  left: 0;
}
.wp-customizer div.ui-datepicker .ui-datepicker-header .ui-datepicker-prev:before {
  content: "";
}
.wp-customizer div.ui-datepicker .ui-datepicker-header .ui-datepicker-next {
  right: 0;
}
.wp-customizer div.ui-datepicker .ui-datepicker-header .ui-datepicker-next:after {
  content: "";
}
.wp-customizer div.ui-datepicker .ui-datepicker-header .ui-datepicker-title {
  text-align: center;
}
.wp-customizer div.ui-datepicker .ui-datepicker-calendar {
  border-collapse: collapse;
  width: 100%;
}
.wp-customizer div.ui-datepicker .ui-datepicker-calendar thead {
  background: #f3f5f7;
  padding: 5px;
}
.wp-customizer div.ui-datepicker .ui-datepicker-calendar tr td a {
  display: block;
  padding: 5px;
  color: #333;
  text-decoration: none;
  text-align: center;
}
.wp-customizer div.ui-datepicker .ui-datepicker-calendar tr td a.ui-state-active, .wp-customizer div.ui-datepicker .ui-datepicker-calendar tr td a:hover {
  color: #fff;
  background-color: #3498DB;
}
.wp-customizer div.ui-datepicker .ui-datepicker-calendar tr td.ui-state-disabled a, .wp-customizer div.ui-datepicker .ui-datepicker-calendar tr td.ui-state-disabled .ui-state-default a {
  color: #999;
}

.customize-control-kirki-dimensions {
  position: relative;
}
.customize-control-kirki-dimensions .wrapper {
  border: 1px solid rgba(0, 0, 0, 0.1);
  padding: 10px;
}
.customize-control-kirki-dimensions .wrapper .control {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}
.customize-control-kirki-dimensions .wrapper .control > div {
  width: 48%;
}
.customize-control-kirki-dimensions .wrapper .control > div h5 {
  margin: 10px 0 7px;
}
.customize-control-kirki-dimensions .wrapper .control > div .inner {
  display: flex;
}

.customize-control-kirki-editor textarea {
  width: 100%;
}

.customize-control-kirki-generic input {
  width: 100%;
}
.customize-control-kirki-generic textarea {
  width: 100%;
  border: 1px solid rgba(0, 0, 0, 0.1);
  -webkit-box-shadow: none;
  box-shadow: none;
}

.customize-control-kirki-multicolor .wp-picker-holder {
  margin-bottom: 7px;
}
.customize-control-kirki-multicolor .iris-picker-inner {
  display: grid;
  grid-template-columns: 1fr 20px 20px;
  grid-gap: 7px;
}
.customize-control-kirki-multicolor .iris-picker-inner > * {
  width: 100% !important;
  margin-left: 0 !important;
}
.customize-control-kirki-multicolor .multicolor-single-color-wrapper {
  display: flex;
  justify-content: space-between;
}
.customize-control-kirki-multicolor .multicolor-single-label {
  order: 2;
}
.customize-control-kirki-multicolor .wp-picker-container {
  width: 100%;
}
.customize-control-kirki-multicolor .wp-picker-container > .wp-color-result {
  width: 100%;
}
.customize-control-kirki-multicolor .wp-picker-container.wp-picker-active + .multicolor-single-label {
  display: none;
}

.customize-control-kirki-number .customize-control-content {
  display: flex;
  align-items: stretch;
}
.customize-control-kirki-number .customize-control-content input {
  width: 100%;
  -moz-appearance: textfield;
}
.customize-control-kirki-number .customize-control-content input::-webkit-inner-spin-button, .customize-control-kirki-number .customize-control-content input::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}
.customize-control-kirki-number .customize-control-content .quantity {
  min-width: 2rem;
  max-width: 2rem;
  text-align: center;
  line-height: 24px;
}
.customize-control-kirki-palette {
  position: relative;
}
.customize-control-kirki-palette input[type=radio] {
  display: none;
}
.customize-control-kirki-palette input[type=radio]:checked + label {
  border: 2px solid #3498DB;
}
.customize-control-kirki-palette label {
  background: none;
  padding: 0;
  border-top: 3px solid transparent;
  border-bottom: 3px solid transparent;
  margin-bottom: 5px;
  display: flex;
}
.customize-control-kirki-palette label span {
  padding: 10px 0;
  flex-grow: 1;
  font-size: 0;
  line-height: 10px;
  color: rgba(0, 0, 0, 0);
  -webkit-transition: all 200ms ease-in-out;
  -moz-transition: all 200ms ease-in-out;
  -ms-transition: all 200ms ease-in-out;
  -o-transition: all 200ms ease-in-out;
  transition: all 200ms ease-in-out;
  border-top: 1px solid rgba(0, 0, 0, 0.1);
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);
}
.customize-control-kirki-palette label span:first-child {
  border-left: 1px solid rgba(0, 0, 0, 0.1);
}
.customize-control-kirki-palette label span:last-child {
  border-right: 1px solid rgba(0, 0, 0, 0.1);
}
.customize-control-kirki-palette label span:hover {
  padding: 10px;
  flex-grow: 3;
  min-width: 60px;
  font-size: 10px;
  line-height: 10px;
  color: #000;
}

.customize-control-kirki-radio-buttonset .buttonset {
  display: flex;
  flex-wrap: wrap;
}
.customize-control-kirki-radio-buttonset .buttonset .switch-label {
  background: rgba(0, 0, 0, 0.1);
  border: 1px rgba(0, 0, 0, 0.1);
  color: #555d66;
  margin: 0;
  text-align: center;
  padding: 0.5em 1em;
  flex-grow: 1;
}
.customize-control-kirki-radio-buttonset .buttonset .switch-input:checked + .switch-label {
  background-color: #00a0d2;
  color: rgba(255, 255, 255, 0.8);
}
.customize-control-kirki-radio-buttonset .screen-reader-text:focus {
  clip-path: inset(50%);
  -webkit-clip-path: inset(50%);
}

.customize-control-kirki-radio-image > .image {
  display: flex;
  flex-wrap: wrap;
}
.customize-control-kirki-radio-image label {
  position: relative;
  display: inline-block;
}
.customize-control-kirki-radio-image label .image-label {
  display: none;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.7);
  font-weight: bold;
}
.customize-control-kirki-radio-image label .image-label .inner {
  width: 100%;
  height: 100%;
  text-align: center;
  padding: 0.5em;
  vertical-align: middle;
}
.customize-control-kirki-radio-image label:hover .image-label {
  display: block;
}
.customize-control-kirki-radio-image input {
  display: none;
}
.customize-control-kirki-radio-image input img {
  border: 1px solid transparent;
}
.customize-control-kirki-radio-image input:checked + label img {
  -webkit-box-shadow: 0 0 5px 2px rgba(0, 0, 0, 0.25);
  box-shadow: 0 0 5px 2px rgba(0, 0, 0, 0.25);
  border: 1px solid #3498DB;
}
.customize-control-kirki-radio-image input + label .image-clickable {
  position: absolute;
  top: 0;
  bottom: 0;
  left: 0;
  right: 0;
  width: 100%;
  height: 100%;
}

.customize-control-kirki-radio {
  position: relative;
}
.customize-control-kirki-radio input[type=radio] {
  width: 18px;
  height: 18px;
}
.customize-control-kirki-radio input[type=radio]:checked:before {
  width: 10px;
  height: 10px;
  margin: 3px;
}
.customize-control-kirki-radio label {
  display: list-item;
  margin-bottom: 7px;
}
.customize-control-kirki-radio label .option-description {
  display: block;
  color: rgba(0, 0, 0, 0.35);
  font-size: 0.9em;
  padding-left: 25px;
}

.customize-control-repeater {
  position: relative;
}
.customize-control-repeater .repeater-fields .repeater-row {
  border: 1px solid #e5e5e5;
  margin-top: 0.5rem;
  background: #eee;
  position: relative;
}
.customize-control-repeater .repeater-fields .repeater-row.minimized {
  border: 1px solid #dfdfdf;
  padding: 0;
}
.customize-control-repeater .repeater-fields .repeater-row.minimized:hover {
  border: 1px solid #e5e5e5;
}
.customize-control-repeater .repeater-fields .repeater-row.minimized .repeater-row-content {
  display: none;
}
.customize-control-repeater .repeater-fields .repeater-row label {
  margin-bottom: 12px;
  clear: both;
}
.customize-control-repeater .repeater-fields .repeater-row .repeater-field.repeater-field- {
  display: none;
}
.customize-control-repeater .repeater-fields .repeater-row .repeater-field.repeater-field-radio-image input {
  display: none;
}
.customize-control-repeater .repeater-fields .repeater-row .repeater-field.repeater-field-radio-image input img {
  border: 1px solid transparent;
}
.customize-control-repeater .repeater-fields .repeater-row .repeater-field.repeater-field-radio-image input:checked + label img {
  -webkit-box-shadow: 0 0 5px 2px rgba(0, 0, 0, 0.25);
  box-shadow: 0 0 5px 2px rgba(0, 0, 0, 0.25);
  border: 1px solid #3498DB;
}
.customize-control-repeater .repeater-fields .repeater-row .repeater-field:last-child {
  border-bottom: none;
  padding-bottom: 0;
}
.customize-control-repeater button.repeater-add {
  margin-top: 1rem;
}
.customize-control-repeater .repeater-row-content {
  padding: 10px 15px;
  background: #fff;
}
.customize-control-repeater .repeater-field {
  margin-bottom: 12px;
  width: 100%;
  clear: both;
  padding-bottom: 12px;
  border-bottom: 1px dotted #CCC;
}
.customize-control-repeater .repeater-field .customize-control-title {
  font-size: 13px;
  line-height: initial;
}
.customize-control-repeater .repeater-field .customize-control-description {
  font-size: 13px;
  line-height: initial;
}
.customize-control-repeater .repeater-field.repeater-field-hidden {
  margin: 0;
  padding: 0;
  border: 0;
}
.customize-control-repeater .repeater-field-select select {
  margin-left: 0;
}
.customize-control-repeater .repeater-field-checkbox label {
  line-height: 28px;
}
.customize-control-repeater .repeater-field-checkbox input {
  line-height: 28px;
  margin-right: 5px;
}
.customize-control-repeater .repeater-field-textarea textarea {
  width: 100%;
  resize: vertical;
}
.customize-control-repeater .repeater-row-header {
  background: white;
  border-bottom: 1px solid #dfdfdf;
  position: relative;
  padding: 10px 15px;
  height: auto;
  min-height: 20px;
  line-height: 30px;
  overflow: hidden;
  word-wrap: break-word;
}
.customize-control-repeater .repeater-row-header:hover {
  cursor: move;
}
.customize-control-repeater .repeater-row-header .dashicons {
  font-size: 18px;
  position: absolute;
  right: 12px;
  top: 2px;
  color: #a0a5aa;
}
.customize-control-repeater .repeater-row-label {
  font-size: 13px;
  font-weight: 600;
  line-height: 20px;
  display: block;
  width: 90%;
  overflow: hidden;
  height: 18px;
}
.customize-control-repeater .repeater-row-remove {
  color: #a00;
}
.customize-control-repeater .repeater-row-remove:hover {
  color: #f00;
}
.customize-control-repeater .repeater-minimize {
  line-height: 36px;
}
.customize-control-repeater .remove-button,
.customize-control-repeater .upload-button {
  width: 48%;
}

.kirki-image-attachment {
  margin: 0;
  text-align: center;
  margin-bottom: 10px;
}
.kirki-image-attachment img {
  display: inline-block;
}

.kirki-file-attachment {
  margin: 0;
  text-align: center;
  margin-bottom: 10px;
}
.kirki-file-attachment .file {
  display: block;
  padding: 10px 5px;
  border: 1px dotted #c3c3c3;
  background: #f9f9f9;
}

.limit {
  padding: 3px;
  border-radius: 3px;
}
.limit.highlight {
  background: #D32F2F;
  color: #fff;
}

.customize-control-kirki-slider .wrapper {
  display: flex;
  align-items: center;
  padding: 15px 0 7px;
  position: relative;
}
.customize-control-kirki-slider .wrapper .slider-reset {
  position: absolute;
  top: 0;
  right: 0;
  font-size: 12px;
  transition: 0.3s ease-in-out;
  color: rgba(0, 0, 0, 0.3);
}
.customize-control-kirki-slider .wrapper .slider-reset:hover {
  transform: scale(1.3);
  color: #DC3232;
}
.customize-control-kirki-slider .wrapper input[type=range] {
  display: block;
  -webkit-appearance: none;
  background-color: #bdc3c7;
  width: 100%;
  height: 5px;
  border-radius: 5px;
  margin: 0 auto;
  outline: 0;
}
.customize-control-kirki-slider .wrapper input[type=range]::-webkit-slider-thumb {
  -webkit-appearance: none;
  background-color: #0085ba;
  width: 17px;
  height: 17px;
  border-radius: 50%;
  border: 1px solid #006799;
  cursor: pointer;
  transition: 0.3s ease-in-out;
}
​ .customize-control-kirki-slider .wrapper input[type=range]::-webkit-slider-thumb:hover {
  background-color: #006799;
  border: 2px solid #0085ba;
}
.customize-control-kirki-slider .wrapper input[type=range]::-webkit-slider-thumb:active {
  transform: scale(1.2);
}
.customize-control-kirki-slider .wrapper input[type=text] {
  font-size: 13px;
  background: transparent;
  border: none;
  box-shadow: none;
  text-align: right;
  padding: 0;
  width: 40px;
}
.customize-control-kirki-slider .wrapper .value {
  display: flex;
  align-items: baseline;
}

.rtl .customize-control-kirki-slider .wrapper .slider-reset {
  right: auto;
  left: 0;
}
.rtl .customize-control-kirki-slider .wrapper input[type=text] {
  text-align: left;
}

.customize-control-kirki-sortable ul.ui-sortable li {
  padding: 5px 10px;
  border: 1px solid #333;
  background: #fff;
}
.customize-control-kirki-sortable ul.ui-sortable li .dashicons.dashicons-menu {
  float: right;
}
.customize-control-kirki-sortable ul.ui-sortable li .dashicons.visibility {
  margin-right: 10px;
}
.customize-control-kirki-sortable ul.ui-sortable li.invisible {
  color: #aaa;
  border: 1px dashed #aaa;
}
.customize-control-kirki-sortable ul.ui-sortable li.invisible .dashicons.visibility {
  color: #aaa;
}

.customize-control-kirki-switch {
  position: relative;
}
.customize-control-kirki-switch .switch-off,
.customize-control-kirki-switch .switch-on {
  opacity: 1;
  padding: 8px;
  font-size: 14px;
  line-height: 18px;
}
.customize-control-kirki-switch .switch-on {
  color: #fff;
  opacity: 0;
  padding-right: 0;
}
.customize-control-kirki-switch .switch-off {
  color: #777;
  padding-left: 0;
}
.customize-control-kirki-switch .switch {
  border: none;
  margin-bottom: 1.5rem;
  outline: 0;
  padding: 0;
  user-select: none;
  border-radius: 3rem;
}
.customize-control-kirki-switch label {
  background: #b4b9be;
  float: left;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  margin-bottom: 1rem;
  position: relative;
  transition: left 0.15s ease-out;
  border-radius: 3rem;
}
.customize-control-kirki-switch label:after {
  background: #FFFFFF;
  content: "";
  display: block;
  position: absolute;
  left: 5px;
  top: 5px;
  width: calc(.85rem + 10px);
  height: calc(.85rem + 10px);
  transition: all 0.25s ease-in-out;
  border-radius: 3rem;
}
.customize-control-kirki-switch input + label {
  margin-left: 0;
  margin-right: 0;
  min-width: 62px;
}
.customize-control-kirki-switch input:checked + label {
  background: #0073aa;
}
.customize-control-kirki-switch input:checked + label:after {
  left: auto;
  right: 5px;
  background: #ffffff;
}
.customize-control-kirki-switch input:checked + label .switch-on {
  opacity: 1;
}
.customize-control-kirki-switch input:checked + label .switch-off {
  opacity: 0;
}
.customize-control-kirki-switch .screen-reader-text:focus {
  clip-path: inset(50%);
  -webkit-clip-path: inset(50%);
}

.customize-control-kirki-toggle {
  position: relative;
}
.customize-control-kirki-toggle .screen-reader-text:focus {
  clip-path: inset(50%);
  -webkit-clip-path: inset(50%);
}
.customize-control-kirki-toggle label {
  display: flex;
  flex-wrap: wrap;
}
.customize-control-kirki-toggle label .customize-control-title {
  width: calc(100% - 40px);
}
.customize-control-kirki-toggle label .description {
  order: 99;
}
.customize-control-kirki-toggle .switch {
  border: 1px solid #b4b9be;
  display: inline-block;
  width: 35px;
  height: 12px;
  border-radius: 8px;
  background: #b4b9be;
  vertical-align: middle;
  position: relative;
  top: 4px;
  cursor: pointer;
  user-select: none;
  transition: background 350ms ease;
}
.customize-control-kirki-toggle .switch:after, .customize-control-kirki-toggle .switch:before {
  content: "";
  display: block;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  position: absolute;
  top: 50%;
  left: -3px;
  transition: all 350ms cubic-bezier(0, 0.95, 0.38, 0.98), background 150ms ease;
}
.customize-control-kirki-toggle .switch:before {
  background: rgba(0, 0, 0, 0.2);
  transform: translate3d(0, -50%, 0) scale(0);
}
.customize-control-kirki-toggle .switch:after {
  background: #999;
  border: 1px solid rgba(0, 0, 0, 0.1);
  transform: translate3d(0, -50%, 0);
}
.customize-control-kirki-toggle .switch:active:before {
  transform: translate3d(0, -50%, 0) scale(3);
}
.customize-control-kirki-toggle input:checked + .switch:before {
  background: rgba(0, 115, 170, 0.075);
  transform: translate3d(100%, -50%, 0) scale(1);
}
.customize-control-kirki-toggle input:checked + .switch:after {
  background: #0073aa;
  transform: translate3d(100%, -50%, 0);
}
.customize-control-kirki-toggle input:checked + .switch:active:before {
  background: rgba(0, 115, 170, 0.075);
  transform: translate3d(100%, -50%, 0) scale(3);
}

.customize-control-kirki-typography {
  position: relative;
}
.customize-control-kirki-typography .wrapper {
  padding: 10px;
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
  border: 1px solid rgba(0, 0, 0, 0.1);
}
.customize-control-kirki-typography .wrapper h5 {
  margin: 0.67em 0 0;
}
.customize-control-kirki-typography .wrapper .color,
.customize-control-kirki-typography .wrapper .font-backup,
.customize-control-kirki-typography .wrapper .font-family,
.customize-control-kirki-typography .wrapper .font-size,
.customize-control-kirki-typography .wrapper .letter-spacing,
.customize-control-kirki-typography .wrapper .line-height,
.customize-control-kirki-typography .wrapper .margin-bottom,
.customize-control-kirki-typography .wrapper .margin-top,
.customize-control-kirki-typography .wrapper .text-align,
.customize-control-kirki-typography .wrapper .text-transform,
.customize-control-kirki-typography .wrapper .variant {
  width: 100%;
  float: none;
  clear: both;
}
.customize-control-kirki-typography .wrapper .font-size,
.customize-control-kirki-typography .wrapper .letter-spacing,
.customize-control-kirki-typography .wrapper .line-height,
.customize-control-kirki-typography .wrapper .margin-bottom,
.customize-control-kirki-typography .wrapper .margin-top,
.customize-control-kirki-typography .wrapper .text-transform {
  width: 48%;
}
.customize-control-kirki-typography .wrapper .text-align .text-align-choices {
  display: flex;
}
.customize-control-kirki-typography .wrapper .text-align .text-align-choices label {
  width: 100%;
  padding: 5px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0);
}
.customize-control-kirki-typography .wrapper .text-align .text-align-choices input {
  display: none;
}
.customize-control-kirki-typography .wrapper .text-align .text-align-choices input:checked + label {
  border-color: #0085ba;
}
.customize-control-kirki-typography .wrapper .color {
  width: auto;
}

/*# sourceMappingURL=styles.css.map */
