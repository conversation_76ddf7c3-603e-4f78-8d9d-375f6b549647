<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="42px" height="42px" viewBox="0 0 42 42" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 3.7.1 (28215) - http://www.bohemiancoding.com/sketch -->
    <title>nav-tabs</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0.617058501" y="0.185392157" width="39.2156863" height="39.2156863" rx="4"></rect>
        <mask id="mask-3" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="-1" y="-1" width="41.2156863" height="41.2156863">
            <rect x="-0.382941499" y="-0.814607843" width="41.2156863" height="41.2156863" fill="white"></rect>
            <use xlink:href="#path-1" fill="black"></use>
        </mask>
        <rect id="path-4" x="6" y="5" width="39.2156863" height="46" rx="4"></rect>
        <mask id="mask-5" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="-1" y="-1" width="41.2156863" height="48">
            <rect x="5" y="4" width="41.2156863" height="48" fill="white"></rect>
            <use xlink:href="#path-4" fill="black"></use>
        </mask>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="nav-tabs" transform="translate(1.000000, 1.000000)">
            <mask id="mask-2" fill="white">
                <use xlink:href="#path-1"></use>
            </mask>
            <g id="Mask">
                <use fill-opacity="0.01" fill="#00A0D2" fill-rule="evenodd" xlink:href="#path-1"></use>
                <use stroke="#3498DB" mask="url(#mask-3)" stroke-width="2" xlink:href="#path-1"></use>
            </g>
            <g id="Rectangle-437" mask="url(#mask-2)">
                <use fill-opacity="0.127858922" fill="#3498DB" fill-rule="evenodd" xlink:href="#path-4"></use>
                <use stroke="#3498DB" mask="url(#mask-5)" stroke-width="2" xlink:href="#path-4"></use>
            </g>
            <path d="M13.722,16.324 C13.7940004,16.324 13.8539998,16.327 13.902,16.333 C13.9500002,16.339 13.9929998,16.3509999 14.031,16.369 C14.0690002,16.3870001 14.1059998,16.4129998 14.142,16.447 C14.1780002,16.4810002 14.2179998,16.5259997 14.262,16.582 L18.816,22.384 C18.7999999,22.2439993 18.789,22.1070007 18.783,21.973 C18.777,21.8389993 18.774,21.7140006 18.774,21.598 L18.774,16.324 L20.196,16.324 L20.196,25 L19.362,25 C19.2339994,25 19.1280004,24.9800002 19.044,24.94 C18.9599996,24.8999998 18.8780004,24.8280005 18.798,24.724 L14.262,18.946 C14.2740001,19.0740006 14.283,19.2009994 14.289,19.327 C14.295,19.4530006 14.298,19.5679995 14.298,19.672 L14.298,25 L12.876,25 L12.876,16.324 L13.722,16.324 Z M29.574,25 L28.326,25 C28.1859993,25 28.0710005,24.9650004 27.981,24.895 C27.8909996,24.8249997 27.8260002,24.7380005 27.786,24.634 L27.138,22.864 L23.544,22.864 L22.896,24.634 C22.8639998,24.7260005 22.8010005,24.8099996 22.707,24.886 C22.6129995,24.9620004 22.4980007,25 22.362,25 L21.108,25 L24.516,16.324 L26.166,16.324 L29.574,25 Z M23.958,21.724 L26.724,21.724 L25.668,18.838 C25.6199998,18.7099994 25.5670003,18.5590009 25.509,18.385 C25.4509997,18.2109991 25.3940003,18.022001 25.338,17.818 C25.2819997,18.022001 25.2270003,18.2119991 25.173,18.388 C25.1189997,18.5640009 25.0660003,18.7179993 25.014,18.85 L23.958,21.724 Z M28.602,16.324 L29.904,16.324 C30.0440007,16.324 30.1579996,16.3579997 30.246,16.426 C30.3340004,16.4940003 30.3999998,16.5819995 30.444,16.69 L32.484,21.982 C32.5520003,22.1540009 32.6169997,22.342999 32.679,22.549 C32.7410003,22.755001 32.7999997,22.9719989 32.856,23.2 C32.9480005,22.7399977 33.0619993,22.3340018 33.198,21.982 L35.232,16.69 C35.2680002,16.5979995 35.3319995,16.5140004 35.424,16.438 C35.5160005,16.3619996 35.6299993,16.324 35.766,16.324 L37.068,16.324 L33.564,25 L32.106,25 L28.602,16.324 Z" id="NAV-Copy-4" fill="#3498DB" mask="url(#mask-2)"></path>
        </g>
    </g>
</svg>