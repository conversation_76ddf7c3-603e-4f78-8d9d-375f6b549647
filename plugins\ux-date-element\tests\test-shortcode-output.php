<?php
/**
 * Test Shortcode Output
 * 
 * This file contains tests to verify the shortcode produces correct output
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Test Shortcode Output Class
 */
class UX_Date_Element_Shortcode_Test {
    
    /**
     * Run all shortcode tests
     */
    public static function run_tests() {
        $results = array();
        
        $results['shortcode_basic_output'] = self::test_shortcode_basic_output();
        $results['shortcode_format_parameter'] = self::test_shortcode_format_parameter();
        $results['shortcode_html_structure'] = self::test_shortcode_html_structure();
        $results['shortcode_sanitization'] = self::test_shortcode_sanitization();
        
        return $results;
    }
    
    /**
     * Test basic shortcode output
     */
    public static function test_shortcode_basic_output() {
        $output = do_shortcode('[ux_current_date]');
        $has_output = !empty($output) && strlen($output) > 0;
        
        return array(
            'test' => 'Shortcode Basic Output',
            'passed' => $has_output,
            'message' => $has_output ? 'Shortcode produces output: ' . substr($output, 0, 50) . '...' : 'Shortcode produces no output'
        );
    }
    
    /**
     * Test shortcode with format parameter
     */
    public static function test_shortcode_format_parameter() {
        $output = do_shortcode('[ux_current_date format="Y-m-d"]');
        $expected_pattern = '/\d{4}-\d{2}-\d{2}/'; // YYYY-MM-DD pattern
        $matches_pattern = preg_match($expected_pattern, $output);
        
        return array(
            'test' => 'Shortcode Format Parameter',
            'passed' => $matches_pattern,
            'message' => $matches_pattern ? 'Format parameter works correctly' : 'Format parameter not working: ' . $output
        );
    }
    
    /**
     * Test shortcode HTML structure
     */
    public static function test_shortcode_html_structure() {
        $output = do_shortcode('[ux_current_date]');
        $has_wrapper_div = strpos($output, '<div class="ux-date-element">') !== false;
        $has_closing_div = strpos($output, '</div>') !== false;
        $valid_structure = $has_wrapper_div && $has_closing_div;
        
        return array(
            'test' => 'Shortcode HTML Structure',
            'passed' => $valid_structure,
            'message' => $valid_structure ? 'HTML structure is correct' : 'HTML structure is invalid'
        );
    }
    
    /**
     * Test shortcode input sanitization
     */
    public static function test_shortcode_sanitization() {
        // Test with potentially malicious input
        $malicious_format = '<script>alert("xss")</script>';
        $output = do_shortcode('[ux_current_date format="' . $malicious_format . '"]');
        $is_sanitized = strpos($output, '<script>') === false;
        
        return array(
            'test' => 'Shortcode Input Sanitization',
            'passed' => $is_sanitized,
            'message' => $is_sanitized ? 'Input is properly sanitized' : 'Input sanitization failed'
        );
    }
    
    /**
     * Test different date formats
     */
    public static function test_date_formats() {
        $formats = array(
            'F j, Y' => '/[A-Za-z]+ \d{1,2}, \d{4}/', // December 27, 2025
            'm/d/Y' => '/\d{1,2}\/\d{1,2}\/\d{4}/', // 12/27/2025
            'Y-m-d' => '/\d{4}-\d{2}-\d{2}/', // 2025-12-27
        );
        
        $results = array();
        foreach ($formats as $format => $pattern) {
            $output = do_shortcode('[ux_current_date format="' . $format . '"]');
            $matches = preg_match($pattern, $output);
            $results[] = array(
                'test' => "Date Format: {$format}",
                'passed' => $matches,
                'message' => $matches ? "Format works: {$output}" : "Format failed: {$output}"
            );
        }
        
        return $results;
    }
    
    /**
     * Display test results
     */
    public static function display_results($results) {
        echo "<h3>Shortcode Output Test Results</h3>\n";
        echo "<table border='1' cellpadding='5' cellspacing='0'>\n";
        echo "<tr><th>Test</th><th>Status</th><th>Message</th></tr>\n";
        
        foreach ($results as $result) {
            $status = $result['passed'] ? '✅ PASS' : '❌ FAIL';
            $row_class = $result['passed'] ? 'pass' : 'fail';
            echo "<tr class='{$row_class}'>";
            echo "<td>{$result['test']}</td>";
            echo "<td>{$status}</td>";
            echo "<td>" . esc_html($result['message']) . "</td>";
            echo "</tr>\n";
        }
        
        echo "</table>\n";
        
        // Summary
        $total_tests = count($results);
        $passed_tests = count(array_filter($results, function($r) { return $r['passed']; }));
        echo "<p><strong>Summary: {$passed_tests}/{$total_tests} tests passed</strong></p>\n";
    }
}

// Auto-run tests if this file is accessed directly via web browser
if (isset($_GET['run_shortcode_tests']) && $_GET['run_shortcode_tests'] === '1') {
    if (current_user_can('manage_options')) {
        $results = UX_Date_Element_Shortcode_Test::run_tests();
        
        // Also test different date formats
        $format_results = UX_Date_Element_Shortcode_Test::test_date_formats();
        $results = array_merge($results, $format_results);
        
        ?>
        <!DOCTYPE html>
        <html>
        <head>
            <title>UX Date Element - Shortcode Tests</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                table { border-collapse: collapse; width: 100%; }
                th, td { text-align: left; padding: 8px; }
                .pass { background-color: #d4edda; }
                .fail { background-color: #f8d7da; }
            </style>
        </head>
        <body>
            <?php UX_Date_Element_Shortcode_Test::display_results($results); ?>
            
            <h3>Live Shortcode Examples</h3>
            <p><strong>Default format:</strong> <?php echo do_shortcode('[ux_current_date]'); ?></p>
            <p><strong>Y-m-d format:</strong> <?php echo do_shortcode('[ux_current_date format="Y-m-d"]'); ?></p>
            <p><strong>m/d/Y format:</strong> <?php echo do_shortcode('[ux_current_date format="m/d/Y"]'); ?></p>
            <p><strong>l, F j, Y format:</strong> <?php echo do_shortcode('[ux_current_date format="l, F j, Y"]'); ?></p>
            
            <p><a href="<?php echo admin_url('plugins.php'); ?>">← Back to Plugins</a></p>
        </body>
        </html>
        <?php
        exit;
    }
}
