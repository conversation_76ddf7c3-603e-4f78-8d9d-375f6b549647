@charset "UTF-8";
.tooltip-wrapper {
  float: right;
  position: relative;
}
.tooltip-wrapper .tooltip-trigger {
  text-decoration: none;
  cursor: pointer;
}
.tooltip-wrapper .tooltip-content {
  position: absolute;
  width: 200px;
  height: auto;
  top: -10px;
  left: -225px;
  background: rgba(0, 0, 0, 0.8);
  color: #fff;
  padding: 10px;
  z-index: 99999;
  border-radius: 3px;
  line-height: 1.4em;
}
.tooltip-wrapper .tooltip-content a {
  color: #fff;
}
.tooltip-wrapper .tooltip-content:after {
  content: "";
  font-family: dashicons;
  position: absolute;
  right: -11px;
  top: 11px;
  color: rgba(0, 0, 0, 0.8);
  font-size: 20px;
}

/*# sourceMappingURL=tooltip.css.map */
