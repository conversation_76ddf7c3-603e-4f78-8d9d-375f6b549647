"use strict";(self.flatsomeChunks=self.flatsomeChunks||[]).push([[970],{1204:function(t,e,r){function i(t){return(i="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t})(t)}function n(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function a(t,e){if(null==t)return{};var r,i,n=function(t,e){if(null==t)return{};var r,i,n={},a=Object.keys(t);for(i=0;i<a.length;i++)r=a[i],e.indexOf(r)>=0||(n[r]=t[r]);return n}(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)r=a[i],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}function s(t,e){var r=e.get(t);if(!r)throw new TypeError("attempted to get private field on non-instance");return r.get?r.get.call(t):r.value}r.r(e),r.d(e,{LottieInteractivity:function(){return h},create:function(){return T}});var o={player:"lottie-player"},l="[lottieInteractivity]:",h=function(){function t(){var e=this,r=arguments.length>0&&void 0!==arguments[0]?arguments[0]:o,h=r.actions,T=r.container,M=r.mode,D=r.player,I=a(r,["actions","container","mode","player"]);if(function(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}(this,t),p.set(this,{writable:!0,value:function(){if(e.player){var t=function(){e.player.addEventListener("enterFrame",s(e,_)),e.container.addEventListener("mouseenter",s(e,P)),e.container.addEventListener("mouseleave",s(e,E)),e.container.addEventListener("touchstart",s(e,P),{passive:!0}),e.container.addEventListener("touchend",s(e,E),{passive:!0})},r=function(){e.container.addEventListener("mouseenter",s(e,P)),e.container.addEventListener("mouseleave",s(e,E)),e.container.addEventListener("touchstart",s(e,P),{passive:!0}),e.container.addEventListener("touchend",s(e,E),{passive:!0})};e.stateHandler.set("loop",(function(){e.actions[e.interactionIdx].loop?e.player.loop=parseInt(e.actions[e.interactionIdx].loop)-1:e.player.loop=!0,e.player.autoplay=!0})),e.stateHandler.set("autoplay",(function(){e.player.loop=!1,e.player.autoplay=!0})),e.stateHandler.set("click",(function(){e.player.loop=!1,e.player.autoplay=!1,e.container.addEventListener("click",s(e,c))})),e.stateHandler.set("hover",(function(){e.player.loop=!1,e.player.autoplay=!1,e.container.addEventListener("mouseenter",s(e,c)),e.container.addEventListener("touchstart",s(e,c),{passive:!0})})),e.stateHandler.set("hold",r),e.stateHandler.set("pauseHold",r),e.transitionHandler.set("click",(function(){e.container.addEventListener("click",s(e,d))})),e.transitionHandler.set("hover",(function(){e.container.addEventListener("mouseenter",s(e,d)),e.container.addEventListener("touchstart",s(e,d),{passive:!0})})),e.transitionHandler.set("hold",t),e.transitionHandler.set("pauseHold",t),e.transitionHandler.set("repeat",(function(){e.player.loop=!0,e.player.autoplay=!0,e.player.addEventListener("loopComplete",(function t(){s(e,v).call(e,{handler:t})}))})),e.transitionHandler.set("onComplete",(function(){"loop"===e.actions[e.interactionIdx].state?e.player.addEventListener("loopComplete",s(e,g)):e.player.addEventListener("complete",s(e,g))})),e.transitionHandler.set("seek",(function(){e.player.stop(),e.player.addEventListener("enterFrame",s(e,b)),e.container.addEventListener("mousemove",s(e,u)),e.container.addEventListener("touchmove",s(e,m),{passive:!1}),e.container.addEventListener("mouseout",s(e,y))}))}}}),c.set(this,{writable:!0,value:function(){var t=e.actions[e.interactionIdx].forceFlag;t||!0!==e.player.isPaused?t&&s(e,x).call(e,!0):s(e,x).call(e,!0)}}),f.set(this,{writable:!0,value:function(){0===e.clickCounter?(e.player.play(),e.clickCounter++):(e.clickCounter++,e.player.setDirection(-1*e.player.playDirection),e.player.play())}}),d.set(this,{writable:!0,value:function(){var t=e.actions[e.interactionIdx].forceFlag,r=e.actions[e.interactionIdx].state,i=e.actions[e.interactionIdx].transition;if("chain"===e.mode){if(e.actions[e.interactionIdx].count){var n=parseInt(e.actions[e.interactionIdx].count);if(e.clickCounter<n-1)return void(e.clickCounter+=1)}return e.clickCounter=0,!t&&"click"===i&&"click"===r||"hover"===i&&"hover"===r?e.transitionHandler.get("onComplete").call():e.nextInteraction(),e.container.removeEventListener("click",s(e,d)),void e.container.removeEventListener("mouseenter",s(e,d))}t||!0!==e.player.isPaused?t&&e.player.goToAndPlay(0,!0):e.player.goToAndPlay(0,!0)}}),u.set(this,{writable:!0,value:function(t){s(e,C).call(e,t.clientX,t.clientY)}}),m.set(this,{writable:!0,value:function(t){t.cancelable&&t.preventDefault(),s(e,C).call(e,t.touches[0].clientX,t.touches[0].clientY)}}),y.set(this,{writable:!0,value:function(){s(e,C).call(e,-1,-1)}}),g.set(this,{writable:!0,value:function(){"loop"===e.actions[e.interactionIdx].state?e.player.removeEventListener("loopComplete",s(e,g)):e.player.removeEventListener("complete",s(e,g)),e.nextInteraction()}}),v.set(this,{writable:!0,value:function(t){var r=t.handler,i=1;e.actions[e.interactionIdx].repeat&&(i=e.actions[e.interactionIdx].repeat),e.playCounter>=i-1?(e.playCounter=0,e.player.removeEventListener("loopComplete",r),e.player.loop=!1,e.player.autoplay=!1,e.nextInteraction()):e.playCounter+=1}}),b.set(this,{writable:!0,value:function(){var t=e.actions[e.interactionIdx].frames;t&&e.player.currentFrame>=parseInt(t[1])-1&&(e.player.removeEventListener("enterFrame",s(e,b)),e.container.removeEventListener("mousemove",s(e,u)),e.container.removeEventListener("mouseout",s(e,y)),setTimeout(e.nextInteraction,0))}}),_.set(this,{writable:!0,value:function(){var t=e.actions[e.interactionIdx].frames;(t&&e.player.currentFrame>=t[1]||e.player.currentFrame>=e.player.totalFrames-1)&&(e.player.removeEventListener("enterFrame",s(e,_)),e.container.removeEventListener("mouseenter",s(e,P)),e.container.removeEventListener("mouseleave",s(e,E)),e.container.removeEventListener("touchstart",s(e,P),{passive:!0}),e.container.removeEventListener("touchend",s(e,E),{passive:!0}),e.player.pause(),e.holdStatus=!1,e.nextInteraction()),-1===e.player.playDirection&&t&&e.player.currentFrame<t[0]&&e.player.pause()}}),P.set(this,{writable:!0,value:function(){-1!==e.player.playDirection&&null!==e.holdStatus&&e.holdStatus||(e.player.setDirection(1),e.player.play(),e.holdStatus=!0)}}),E.set(this,{writable:!0,value:function(){"hold"===e.actions[e.interactionIdx].transition||"hold"===e.actions[e.interactionIdx].state||"hold"===e.actions[0].type?(e.player.setDirection(-1),e.player.play()):"pauseHold"!==e.actions[e.interactionIdx].transition&&"pauseHold"!==e.actions[e.interactionIdx].state&&"pauseHold"!==e.actions[0].type||e.player.pause(),e.holdStatus=!1}}),S.set(this,{writable:!0,value:function(){if(e.container.removeEventListener("click",s(e,d)),e.container.removeEventListener("click",s(e,c)),e.container.removeEventListener("mouseenter",s(e,d)),e.container.removeEventListener("touchstart",s(e,d)),e.container.removeEventListener("touchmove",s(e,m)),e.container.removeEventListener("mouseenter",s(e,c)),e.container.removeEventListener("touchstart",s(e,c)),e.container.removeEventListener("mouseenter",s(e,P)),e.container.removeEventListener("touchstart",s(e,P)),e.container.removeEventListener("mouseleave",s(e,E)),e.container.removeEventListener("mousemove",s(e,u)),e.container.removeEventListener("mouseout",s(e,y)),e.container.removeEventListener("touchend",s(e,E)),e.player)try{e.player.removeEventListener("loopComplete",s(e,g)),e.player.removeEventListener("complete",s(e,g)),e.player.removeEventListener("enterFrame",s(e,b)),e.player.removeEventListener("enterFrame",s(e,_))}catch(t){}}}),n(this,"jumpToInteraction",(function(t){s(e,S).call(e),e.interactionIdx=t,e.interactionIdx<0?e.interactionIdx=0:e.interactionIdx,e.nextInteraction(!1)})),n(this,"nextInteraction",(function(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];e.oldInterctionIdx=e.interactionIdx,s(e,S).call(e),e.player.loop=!1;var r=e.actions[e.interactionIdx].jumpTo;r?r>=0&&r<e.actions.length?(e.interactionIdx=r,s(e,A).call(e,{ignorePath:!1})):(e.interactionIdx=0,e.player.goToAndStop(0,!0),s(e,A).call(e,{ignorePath:!1})):(t&&e.interactionIdx++,e.interactionIdx>=e.actions.length?e.actions[e.actions.length-1].reset?(e.interactionIdx=0,e.player.resetSegments(!0),e.actions[e.interactionIdx].frames?e.player.goToAndStop(e.actions[e.interactionIdx].frames,!0):e.player.goToAndStop(0,!0),s(e,A).call(e,{ignorePath:!1})):(e.interactionIdx=e.actions.length-1,s(e,A).call(e,{ignorePath:!1})):s(e,A).call(e,{ignorePath:!1})),e.container.dispatchEvent(new CustomEvent("transition",{bubbles:!0,composed:!0,detail:{oldIndex:e.oldInterctionIdx,newIndex:e.interactionIdx}}))})),x.set(this,{writable:!0,value:function(t){var r=e.actions[e.interactionIdx].frames;if(!r)return e.player.resetSegments(!0),void e.player.goToAndPlay(0,!0);"string"==typeof r?e.player.goToAndPlay(r,t):e.player.playSegments(r,t)}}),w.set(this,{writable:!0,value:function(){var t=e.actions[e.interactionIdx].path;if(!t)if("object"===i(e.enteredPlayer)&&"AnimationItem"===e.enteredPlayer.constructor.name){if(t=e.enteredPlayer,e.player===t)return void s(e,A).call(e,{ignorePath:!0})}else{var r=(t=e.loadedAnimation).substr(t.lastIndexOf("/")+1);if(r=r.substr(0,r.lastIndexOf(".json")),e.player.fileName===r)return void s(e,A).call(e,{ignorePath:!0})}var n=e.container.getBoundingClientRect(),a="width: "+n.width+"px !important; height: "+n.height+"px !important; background: "+e.container.style.background;if(e.container.setAttribute("style",a),"object"!==i(e.enteredPlayer)||"AnimationItem"!==e.enteredPlayer.constructor.name){if("string"==typeof e.enteredPlayer){var o=document.querySelector(e.enteredPlayer);o&&"LOTTIE-PLAYER"===o.nodeName&&(e.attachedListeners||(o.addEventListener("ready",(function(){e.container.style.width="",e.container.style.height=""})),o.addEventListener("load",(function(){e.player=o.getLottie(),s(e,A).call(e,{ignorePath:!0})})),e.attachedListeners=!0),o.load(t))}else e.enteredPlayer instanceof HTMLElement&&"LOTTIE-PLAYER"===e.enteredPlayer.nodeName&&(e.attachedListeners||(e.enteredPlayer.addEventListener("ready",(function(){e.container.style.width="",e.container.style.height=""})),e.enteredPlayer.addEventListener("load",(function(){e.player=e.enteredPlayer.getLottie(),s(e,A).call(e,{ignorePath:!0})})),e.attachedListeners=!0),e.enteredPlayer.load(t));if(!e.player)throw new Error("".concat(l," Specified player is invalid."),e.enteredPlayer)}else{if(!window.lottie)throw new Error("".concat(l," A Lottie player is required."));e.stop(),e.container.innerHTML="","object"===i(t)&&"AnimationItem"===t.constructor.name?e.player=window.lottie.loadAnimation({loop:!1,autoplay:!1,animationData:t.animationData,container:e.container}):e.player=window.lottie.loadAnimation({loop:!1,autoplay:!1,path:t,container:e.container}),e.player.addEventListener("DOMLoaded",(function(){e.container.style.width="",e.container.style.height="",s(e,A).call(e,{ignorePath:!0})}))}e.clickCounter=0,e.playCounter=0}}),A.set(this,{writable:!0,value:function(t){var r=t.ignorePath,i=e.actions[e.interactionIdx].frames,n=e.actions[e.interactionIdx].state,a=e.actions[e.interactionIdx].transition,o=e.actions[e.interactionIdx].path,l=e.stateHandler.get(n),h=e.transitionHandler.get(a),p=e.actions[e.interactionIdx].speed?e.actions[e.interactionIdx].speed:1,c=e.actions[e.interactionIdx].delay?e.actions[e.interactionIdx].delay:0;r||!(o||e.actions[e.actions.length-1].reset&&0===e.interactionIdx)?setTimeout((function(){i&&(e.player.autoplay=!1,e.player.resetSegments(!0),e.player.goToAndStop(i[0],!0)),l?l.call():"none"===n&&(e.player.loop=!1,e.player.autoplay=!1),h&&h.call(),e.player.autoplay&&(e.player.resetSegments(!0),s(e,x).call(e,!0)),e.player.setSpeed(p)}),c):s(e,w).call(e)}}),C.set(this,{writable:!0,value:function(t,r){if(-1!==t&&-1!==r){var i=e.getContainerCursorPosition(t,r);t=i.x,r=i.y}var n=e.actions.find((function(e){var i=e.position;if(i){if(Array.isArray(i.x)&&Array.isArray(i.y))return t>=i.x[0]&&t<=i.x[1]&&r>=i.y[0]&&r<=i.y[1];if(!Number.isNaN(i.x)&&!Number.isNaN(i.y))return t===i.x&&r===i.y}return!1}));if(n)if("seek"===n.type||"seek"===n.transition){var a=(t-n.position.x[0])/(n.position.x[1]-n.position.x[0]),s=(r-n.position.y[0])/(n.position.y[1]-n.position.y[0]);e.player.playSegments(n.frames,!0),n.position.y[0]<0&&n.position.y[1]>1?e.player.goToAndStop(Math.floor(a*e.player.totalFrames),!0):e.player.goToAndStop(Math.ceil((a+s)/2*e.player.totalFrames),!0)}else"loop"===n.type?e.player.playSegments(n.frames,!0):"play"===n.type?(!0===e.player.isPaused&&e.player.resetSegments(),e.player.playSegments(n.frames)):"stop"===n.type&&(e.player.resetSegments(!0),e.player.goToAndStop(n.frames[0],!0))}}),k.set(this,{writable:!0,value:function(){var t=e.getContainerVisibility(),r=e.actions.find((function(e){var r=e.visibility;return t>=r[0]&&t<=r[1]}));if(r)if("seek"===r.type){var i=r.frames[0],n=2==r.frames.length?r.frames[1]:e.player.totalFrames-1;null!==e.assignedSegment&&(e.player.resetSegments(!0),e.assignedSegment=null),e.player.goToAndStop(i+Math.round((t-r.visibility[0])/(r.visibility[1]-r.visibility[0])*(n-i)),!0)}else if("loop"===r.type)e.player.loop=!0,(null===e.assignedSegment||e.assignedSegment!==r.frames||!0===e.player.isPaused)&&(e.player.playSegments(r.frames,!0),e.assignedSegment=r.frames);else if("play"===r.type||"playOnce"===r.type){if("playOnce"===r.type&&!e.scrolledAndPlayed)return e.scrolledAndPlayed=!0,e.player.resetSegments(!0),void(r.frames?e.player.playSegments(r.frames,!0):e.player.play());"play"===r.type&&e.player.isPaused&&(e.player.resetSegments(!0),r.frames?e.player.playSegments(r.frames,!0):e.player.play())}else"stop"===r.type&&e.player.goToAndStop(r.frames[0],!0)}}),this.enteredPlayer=D,"object"!==i(D)||"AnimationItem"!==D.constructor.name){if("string"==typeof D){var F=document.querySelector(D);F&&"LOTTIE-PLAYER"===F.nodeName&&(D=F.getLottie())}else D instanceof HTMLElement&&"LOTTIE-PLAYER"===D.nodeName&&(D=D.getLottie());if(!D)throw new Error(l+"Specified player:"+D+" is invalid.")}"string"==typeof T&&(T=document.querySelector(T)),T||(T=D.wrapper),this.player=D,this.loadedAnimation=this.player.path+this.player.fileName+".json",this.attachedListeners=!1,this.container=T,this.mode=M,this.actions=h,this.options=I,this.assignedSegment=null,this.scrolledAndPlayed=!1,this.interactionIdx=0,this.oldInterctionIdx=0,this.clickCounter=0,this.playCounter=0,this.stateHandler=new Map,this.transitionHandler=new Map}var e;return(e=[{key:"getContainerVisibility",value:function(){var t=this.container.getBoundingClientRect(),e=t.top,r=t.height;return(window.innerHeight-e)/(window.innerHeight+r)}},{key:"getContainerCursorPosition",value:function(t,e){var r=this.container.getBoundingClientRect(),i=r.top;return{x:(t-r.left)/r.width,y:(e-i)/r.height}}},{key:"initScrollMode",value:function(){this.player.stop(),window.addEventListener("scroll",s(this,k),!0)}},{key:"initCursorMode",value:function(){this.actions&&1===this.actions.length?"click"===this.actions[0].type?(this.player.loop=!1,this.player.stop(),this.container.addEventListener("click",s(this,d))):"hover"===this.actions[0].type?(this.player.loop=!1,this.player.stop(),this.container.addEventListener("mouseenter",s(this,d)),this.container.addEventListener("touchstart",s(this,d),{passive:!0})):"toggle"===this.actions[0].type?(this.player.loop=!1,this.player.stop(),this.container.addEventListener("click",s(this,f))):"hold"===this.actions[0].type||"pauseHold"===this.actions[0].type?(this.container.addEventListener("mouseenter",s(this,P)),this.container.addEventListener("mouseleave",s(this,E)),this.container.addEventListener("touchstart",s(this,P),{passive:!0}),this.container.addEventListener("touchend",s(this,E),{passive:!0})):"seek"===this.actions[0].type&&(this.player.loop=!0,this.player.stop(),this.container.addEventListener("mousemove",s(this,u)),this.container.addEventListener("touchmove",s(this,m),{passive:!1}),this.container.addEventListener("mouseout",s(this,y))):(this.player.loop=!0,this.player.stop(),this.container.addEventListener("mousemove",s(this,u)),this.container.addEventListener("mouseleave",s(this,y)),s(this,C).call(this,-1,-1))}},{key:"initChainMode",value:function(){s(this,p).call(this),this.player.loop=!1,this.player.stop(),s(this,A).call(this,{ignorePath:!1})}},{key:"start",value:function(){var t=this;"scroll"===this.mode?this.player.isLoaded?this.initScrollMode():this.player.addEventListener("DOMLoaded",(function(){t.initScrollMode()})):"cursor"===this.mode?this.player.isLoaded?this.initCursorMode():this.player.addEventListener("DOMLoaded",(function(){t.initCursorMode()})):"chain"===this.mode&&(this.player.isLoaded?this.initChainMode():this.player.addEventListener("DOMLoaded",(function(){t.initChainMode()})))}},{key:"redefineOptions",value:function(t){var e=t.actions,r=t.container,n=t.mode,s=t.player,o=a(t,["actions","container","mode","player"]);if(this.stop(),this.enteredPlayer=s,"object"!==i(s)||"AnimationItem"!==s.constructor.name){if("string"==typeof s){var h=document.querySelector(s);h&&"LOTTIE-PLAYER"===h.nodeName&&(s=h.getLottie())}else s instanceof HTMLElement&&"LOTTIE-PLAYER"===s.nodeName&&(s=s.getLottie());if(!s)throw new Error(l+"Specified player:"+s+" is invalid.",s)}"string"==typeof r&&(r=document.querySelector(r)),r||(r=s.wrapper),this.player=s,this.loadedAnimation=this.player.path+this.player.fileName+".json",this.attachedListeners=!1,this.container=r,this.mode=n,this.actions=e,this.options=o,this.assignedSegment=null,this.scrolledAndPlayed=!1,this.interactionIdx=0,this.clickCounter=0,this.playCounter=0,this.holdStatus=null,this.stateHandler=new Map,this.transitionHandler=new Map,this.start()}},{key:"stop",value:function(){if("scroll"===this.mode&&window.removeEventListener("scroll",s(this,k),!0),"cursor"===this.mode&&(this.container.removeEventListener("click",s(this,d)),this.container.removeEventListener("click",s(this,f)),this.container.removeEventListener("mouseenter",s(this,d)),this.container.removeEventListener("touchstart",s(this,d)),this.container.removeEventListener("touchmove",s(this,m)),this.container.removeEventListener("mousemove",s(this,u)),this.container.removeEventListener("mouseleave",s(this,y)),this.container.removeEventListener("touchstart",s(this,P)),this.container.removeEventListener("touchend",s(this,E))),"chain"===this.mode&&(this.container.removeEventListener("click",s(this,d)),this.container.removeEventListener("click",s(this,c)),this.container.removeEventListener("mouseenter",s(this,d)),this.container.removeEventListener("touchstart",s(this,d)),this.container.removeEventListener("touchmove",s(this,m)),this.container.removeEventListener("mouseenter",s(this,c)),this.container.removeEventListener("touchstart",s(this,c)),this.container.removeEventListener("mouseenter",s(this,P)),this.container.removeEventListener("touchstart",s(this,P)),this.container.removeEventListener("mouseleave",s(this,E)),this.container.removeEventListener("mousemove",s(this,u)),this.container.removeEventListener("mouseout",s(this,y)),this.container.removeEventListener("touchend",s(this,E)),this.player))try{this.player.removeEventListener("loopComplete",s(this,g)),this.player.removeEventListener("complete",s(this,g)),this.player.removeEventListener("enterFrame",s(this,b)),this.player.removeEventListener("enterFrame",s(this,_))}catch(t){}this.player&&(this.player.destroy(),this.player=null)}}])&&function(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}(t.prototype,e),t}(),p=new WeakMap,c=new WeakMap,f=new WeakMap,d=new WeakMap,u=new WeakMap,m=new WeakMap,y=new WeakMap,g=new WeakMap,v=new WeakMap,b=new WeakMap,_=new WeakMap,P=new WeakMap,E=new WeakMap,S=new WeakMap,x=new WeakMap,w=new WeakMap,A=new WeakMap,C=new WeakMap,k=new WeakMap,T=function(t){var e=new h(t);return e.start(),e};e.default=T},9371:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){function _asyncIterator(t){var e,r,i,n=2;for("undefined"!=typeof Symbol&&(r=Symbol.asyncIterator,i=Symbol.iterator);n--;){if(r&&null!=(e=t[r]))return e.call(t);if(i&&null!=(e=t[i]))return new AsyncFromSyncIterator(e.call(t));r="@@asyncIterator",i="@@iterator"}throw new TypeError("Object is not async iterable")}function AsyncFromSyncIterator(t){function e(t){if(Object(t)!==t)return Promise.reject(new TypeError(t+" is not an object."));var e=t.done;return Promise.resolve(t.value).then((function(t){return{value:t,done:e}}))}return AsyncFromSyncIterator=function(t){this.s=t,this.n=t.next},AsyncFromSyncIterator.prototype={s:null,n:null,next:function(){return e(this.n.apply(this.s,arguments))},return:function(t){var r=this.s.return;return void 0===r?Promise.resolve({value:t,done:!0}):e(r.apply(this.s,arguments))},throw:function(t){var r=this.s.return;return void 0===r?Promise.reject(t):e(r.apply(this.s,arguments))}},new AsyncFromSyncIterator(t)}var REACT_ELEMENT_TYPE;function _jsx(t,e,r,i){REACT_ELEMENT_TYPE||(REACT_ELEMENT_TYPE="function"==typeof Symbol&&Symbol.for&&Symbol.for("react.element")||60103);var n=t&&t.defaultProps,a=arguments.length-3;if(e||0===a||(e={children:void 0}),1===a)e.children=i;else if(a>1){for(var s=new Array(a),o=0;o<a;o++)s[o]=arguments[o+3];e.children=s}if(e&&n)for(var l in n)void 0===e[l]&&(e[l]=n[l]);else e||(e=n||{});return{$$typeof:REACT_ELEMENT_TYPE,type:t,key:void 0===r?null:""+r,ref:null,props:e,_owner:null}}function ownKeys(t,e){var r=Object.keys(t);if(Object.getOwnPropertySymbols){var i=Object.getOwnPropertySymbols(t);e&&(i=i.filter((function(e){return Object.getOwnPropertyDescriptor(t,e).enumerable}))),r.push.apply(r,i)}return r}function _objectSpread2(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?arguments[e]:{};e%2?ownKeys(Object(r),!0).forEach((function(e){_defineProperty(t,e,r[e])})):Object.getOwnPropertyDescriptors?Object.defineProperties(t,Object.getOwnPropertyDescriptors(r)):ownKeys(Object(r)).forEach((function(e){Object.defineProperty(t,e,Object.getOwnPropertyDescriptor(r,e))}))}return t}function _typeof(t){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_typeof(t)}function _wrapRegExp(){_wrapRegExp=function(t,e){return new r(t,void 0,e)};var t=RegExp.prototype,e=new WeakMap;function r(t,i,n){var a=new RegExp(t,i);return e.set(a,n||e.get(t)),_setPrototypeOf(a,r.prototype)}function i(t,r){var i=e.get(r);return Object.keys(i).reduce((function(e,r){return e[r]=t[i[r]],e}),Object.create(null))}return _inherits(r,RegExp),r.prototype.exec=function(e){var r=t.exec.call(this,e);return r&&(r.groups=i(r,this)),r},r.prototype[Symbol.replace]=function(r,n){if("string"==typeof n){var a=e.get(this);return t[Symbol.replace].call(this,r,n.replace(/\$<([^>]+)>/g,(function(t,e){return"$"+a[e]})))}if("function"==typeof n){var s=this;return t[Symbol.replace].call(this,r,(function(){var t=arguments;return"object"!=typeof t[t.length-1]&&(t=[].slice.call(t)).push(i(t,s)),n.apply(this,t)}))}return t[Symbol.replace].call(this,r,n)},_wrapRegExp.apply(this,arguments)}function _AwaitValue(t){this.wrapped=t}function _AsyncGenerator(t){var e,r;function i(e,r){try{var a=t[e](r),s=a.value,o=s instanceof _AwaitValue;Promise.resolve(o?s.wrapped:s).then((function(t){o?i("return"===e?"return":"next",t):n(a.done?"return":"normal",t)}),(function(t){i("throw",t)}))}catch(t){n("throw",t)}}function n(t,n){switch(t){case"return":e.resolve({value:n,done:!0});break;case"throw":e.reject(n);break;default:e.resolve({value:n,done:!1})}(e=e.next)?i(e.key,e.arg):r=null}this._invoke=function(t,n){return new Promise((function(a,s){var o={key:t,arg:n,resolve:a,reject:s,next:null};r?r=r.next=o:(e=r=o,i(t,n))}))},"function"!=typeof t.return&&(this.return=void 0)}function _wrapAsyncGenerator(t){return function(){return new _AsyncGenerator(t.apply(this,arguments))}}function _awaitAsyncGenerator(t){return new _AwaitValue(t)}function _asyncGeneratorDelegate(t,e){var r={},i=!1;function n(r,n){return i=!0,n=new Promise((function(e){e(t[r](n))})),{done:!1,value:e(n)}}return r["undefined"!=typeof Symbol&&Symbol.iterator||"@@iterator"]=function(){return this},r.next=function(t){return i?(i=!1,t):n("next",t)},"function"==typeof t.throw&&(r.throw=function(t){if(i)throw i=!1,t;return n("throw",t)}),"function"==typeof t.return&&(r.return=function(t){return i?(i=!1,t):n("return",t)}),r}function asyncGeneratorStep(t,e,r,i,n,a,s){try{var o=t[a](s),l=o.value}catch(t){return void r(t)}o.done?e(l):Promise.resolve(l).then(i,n)}function _asyncToGenerator(t){return function(){var e=this,r=arguments;return new Promise((function(i,n){var a=t.apply(e,r);function s(t){asyncGeneratorStep(a,i,n,s,o,"next",t)}function o(t){asyncGeneratorStep(a,i,n,s,o,"throw",t)}s(void 0)}))}}function _classCallCheck(t,e){if(!(t instanceof e))throw new TypeError("Cannot call a class as a function")}function _defineProperties(t,e){for(var r=0;r<e.length;r++){var i=e[r];i.enumerable=i.enumerable||!1,i.configurable=!0,"value"in i&&(i.writable=!0),Object.defineProperty(t,i.key,i)}}function _createClass(t,e,r){return e&&_defineProperties(t.prototype,e),r&&_defineProperties(t,r),Object.defineProperty(t,"prototype",{writable:!1}),t}function _defineEnumerableProperties(t,e){for(var r in e)(a=e[r]).configurable=a.enumerable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,r,a);if(Object.getOwnPropertySymbols)for(var i=Object.getOwnPropertySymbols(e),n=0;n<i.length;n++){var a,s=i[n];(a=e[s]).configurable=a.enumerable=!0,"value"in a&&(a.writable=!0),Object.defineProperty(t,s,a)}return t}function _defaults(t,e){for(var r=Object.getOwnPropertyNames(e),i=0;i<r.length;i++){var n=r[i],a=Object.getOwnPropertyDescriptor(e,n);a&&a.configurable&&void 0===t[n]&&Object.defineProperty(t,n,a)}return t}function _defineProperty(t,e,r){return e in t?Object.defineProperty(t,e,{value:r,enumerable:!0,configurable:!0,writable:!0}):t[e]=r,t}function _extends(){return _extends=Object.assign||function(t){for(var e=1;e<arguments.length;e++){var r=arguments[e];for(var i in r)Object.prototype.hasOwnProperty.call(r,i)&&(t[i]=r[i])}return t},_extends.apply(this,arguments)}function _objectSpread(t){for(var e=1;e<arguments.length;e++){var r=null!=arguments[e]?Object(arguments[e]):{},i=Object.keys(r);"function"==typeof Object.getOwnPropertySymbols&&i.push.apply(i,Object.getOwnPropertySymbols(r).filter((function(t){return Object.getOwnPropertyDescriptor(r,t).enumerable}))),i.forEach((function(e){_defineProperty(t,e,r[e])}))}return t}function _inherits(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Super expression must either be null or a function");t.prototype=Object.create(e&&e.prototype,{constructor:{value:t,writable:!0,configurable:!0}}),Object.defineProperty(t,"prototype",{writable:!1}),e&&_setPrototypeOf(t,e)}function _inheritsLoose(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t,_setPrototypeOf(t,e)}function _getPrototypeOf(t){return _getPrototypeOf=Object.setPrototypeOf?Object.getPrototypeOf:function(t){return t.__proto__||Object.getPrototypeOf(t)},_getPrototypeOf(t)}function _setPrototypeOf(t,e){return _setPrototypeOf=Object.setPrototypeOf||function(t,e){return t.__proto__=e,t},_setPrototypeOf(t,e)}function _isNativeReflectConstruct(){if("undefined"==typeof Reflect||!Reflect.construct)return!1;if(Reflect.construct.sham)return!1;if("function"==typeof Proxy)return!0;try{return Boolean.prototype.valueOf.call(Reflect.construct(Boolean,[],(function(){}))),!0}catch(t){return!1}}function _construct(t,e,r){return _construct=_isNativeReflectConstruct()?Reflect.construct:function(t,e,r){var i=[null];i.push.apply(i,e);var n=new(Function.bind.apply(t,i));return r&&_setPrototypeOf(n,r.prototype),n},_construct.apply(null,arguments)}function _isNativeFunction(t){return-1!==Function.toString.call(t).indexOf("[native code]")}function _wrapNativeSuper(t){var e="function"==typeof Map?new Map:void 0;return _wrapNativeSuper=function(t){if(null===t||!_isNativeFunction(t))return t;if("function"!=typeof t)throw new TypeError("Super expression must either be null or a function");if(void 0!==e){if(e.has(t))return e.get(t);e.set(t,r)}function r(){return _construct(t,arguments,_getPrototypeOf(this).constructor)}return r.prototype=Object.create(t.prototype,{constructor:{value:r,enumerable:!1,writable:!0,configurable:!0}}),_setPrototypeOf(r,t)},_wrapNativeSuper(t)}function _instanceof(t,e){return null!=e&&"undefined"!=typeof Symbol&&e[Symbol.hasInstance]?!!e[Symbol.hasInstance](t):t instanceof e}function _interopRequireDefault(t){return t&&t.__esModule?t:{default:t}}function _getRequireWildcardCache(t){if("function"!=typeof WeakMap)return null;var e=new WeakMap,r=new WeakMap;return(_getRequireWildcardCache=function(t){return t?r:e})(t)}function _interopRequireWildcard(t,e){if(!e&&t&&t.__esModule)return t;if(null===t||"object"!=typeof t&&"function"!=typeof t)return{default:t};var r=_getRequireWildcardCache(e);if(r&&r.has(t))return r.get(t);var i={},n=Object.defineProperty&&Object.getOwnPropertyDescriptor;for(var a in t)if("default"!==a&&Object.prototype.hasOwnProperty.call(t,a)){var s=n?Object.getOwnPropertyDescriptor(t,a):null;s&&(s.get||s.set)?Object.defineProperty(i,a,s):i[a]=t[a]}return i.default=t,r&&r.set(t,i),i}function _newArrowCheck(t,e){if(t!==e)throw new TypeError("Cannot instantiate an arrow function")}function _objectDestructuringEmpty(t){if(null==t)throw new TypeError("Cannot destructure undefined")}function _objectWithoutPropertiesLoose(t,e){if(null==t)return{};var r,i,n={},a=Object.keys(t);for(i=0;i<a.length;i++)r=a[i],e.indexOf(r)>=0||(n[r]=t[r]);return n}function _objectWithoutProperties(t,e){if(null==t)return{};var r,i,n=_objectWithoutPropertiesLoose(t,e);if(Object.getOwnPropertySymbols){var a=Object.getOwnPropertySymbols(t);for(i=0;i<a.length;i++)r=a[i],e.indexOf(r)>=0||Object.prototype.propertyIsEnumerable.call(t,r)&&(n[r]=t[r])}return n}function _assertThisInitialized(t){if(void 0===t)throw new ReferenceError("this hasn't been initialised - super() hasn't been called");return t}function _possibleConstructorReturn(t,e){if(e&&("object"==typeof e||"function"==typeof e))return e;if(void 0!==e)throw new TypeError("Derived constructors may only return object or undefined");return _assertThisInitialized(t)}function _createSuper(t){var e=_isNativeReflectConstruct();return function(){var r,i=_getPrototypeOf(t);if(e){var n=_getPrototypeOf(this).constructor;r=Reflect.construct(i,arguments,n)}else r=i.apply(this,arguments);return _possibleConstructorReturn(this,r)}}function _superPropBase(t,e){for(;!Object.prototype.hasOwnProperty.call(t,e)&&null!==(t=_getPrototypeOf(t)););return t}function _get(){return _get="undefined"!=typeof Reflect&&Reflect.get?Reflect.get:function(t,e,r){var i=_superPropBase(t,e);if(i){var n=Object.getOwnPropertyDescriptor(i,e);return n.get?n.get.call(arguments.length<3?t:r):n.value}},_get.apply(this,arguments)}function set(t,e,r,i){return set="undefined"!=typeof Reflect&&Reflect.set?Reflect.set:function(t,e,r,i){var n,a=_superPropBase(t,e);if(a){if((n=Object.getOwnPropertyDescriptor(a,e)).set)return n.set.call(i,r),!0;if(!n.writable)return!1}if(n=Object.getOwnPropertyDescriptor(i,e)){if(!n.writable)return!1;n.value=r,Object.defineProperty(i,e,n)}else _defineProperty(i,e,r);return!0},set(t,e,r,i)}function _set(t,e,r,i,n){if(!set(t,e,r,i||t)&&n)throw new Error("failed to set property");return r}function _taggedTemplateLiteral(t,e){return e||(e=t.slice(0)),Object.freeze(Object.defineProperties(t,{raw:{value:Object.freeze(e)}}))}function _taggedTemplateLiteralLoose(t,e){return e||(e=t.slice(0)),t.raw=e,t}function _readOnlyError(t){throw new TypeError('"'+t+'" is read-only')}function _writeOnlyError(t){throw new TypeError('"'+t+'" is write-only')}function _classNameTDZError(t){throw new Error('Class "'+t+'" cannot be referenced in computed property keys.')}function _temporalUndefined(){}function _tdz(t){throw new ReferenceError(t+" is not defined - temporal dead zone")}function _temporalRef(t,e){return t===_temporalUndefined?_tdz(e):t}function _slicedToArray(t,e){return _arrayWithHoles(t)||_iterableToArrayLimit(t,e)||_unsupportedIterableToArray(t,e)||_nonIterableRest()}function _slicedToArrayLoose(t,e){return _arrayWithHoles(t)||_iterableToArrayLimitLoose(t,e)||_unsupportedIterableToArray(t,e)||_nonIterableRest()}function _toArray(t){return _arrayWithHoles(t)||_iterableToArray(t)||_unsupportedIterableToArray(t)||_nonIterableRest()}function _toConsumableArray(t){return _arrayWithoutHoles(t)||_iterableToArray(t)||_unsupportedIterableToArray(t)||_nonIterableSpread()}function _arrayWithoutHoles(t){if(Array.isArray(t))return _arrayLikeToArray(t)}function _arrayWithHoles(t){if(Array.isArray(t))return t}function _maybeArrayLike(t,e,r){if(e&&!Array.isArray(e)&&"number"==typeof e.length){var i=e.length;return _arrayLikeToArray(e,void 0!==r&&r<i?r:i)}return t(e,r)}function _iterableToArray(t){if("undefined"!=typeof Symbol&&null!=t[Symbol.iterator]||null!=t["@@iterator"])return Array.from(t)}function _iterableToArrayLimit(t,e){var r=null==t?null:"undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(null!=r){var i,n,a=[],s=!0,o=!1;try{for(r=r.call(t);!(s=(i=r.next()).done)&&(a.push(i.value),!e||a.length!==e);s=!0);}catch(t){o=!0,n=t}finally{try{s||null==r.return||r.return()}finally{if(o)throw n}}return a}}function _iterableToArrayLimitLoose(t,e){var r=t&&("undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"]);if(null!=r){var i=[];for(r=r.call(t),_step;!(_step=r.next()).done&&(i.push(_step.value),!e||i.length!==e););return i}}function _unsupportedIterableToArray(t,e){if(t){if("string"==typeof t)return _arrayLikeToArray(t,e);var r=Object.prototype.toString.call(t).slice(8,-1);return"Object"===r&&t.constructor&&(r=t.constructor.name),"Map"===r||"Set"===r?Array.from(t):"Arguments"===r||/^(?:Ui|I)nt(?:8|16|32)(?:Clamped)?Array$/.test(r)?_arrayLikeToArray(t,e):void 0}}function _arrayLikeToArray(t,e){(null==e||e>t.length)&&(e=t.length);for(var r=0,i=new Array(e);r<e;r++)i[r]=t[r];return i}function _nonIterableSpread(){throw new TypeError("Invalid attempt to spread non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _nonIterableRest(){throw new TypeError("Invalid attempt to destructure non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _createForOfIteratorHelper(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(!r){if(Array.isArray(t)||(r=_unsupportedIterableToArray(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var i=0,n=function(){};return{s:n,n:function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}},e:function(t){throw t},f:n}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}var a,s=!0,o=!1;return{s:function(){r=r.call(t)},n:function(){var t=r.next();return s=t.done,t},e:function(t){o=!0,a=t},f:function(){try{s||null==r.return||r.return()}finally{if(o)throw a}}}}function _createForOfIteratorHelperLoose(t,e){var r="undefined"!=typeof Symbol&&t[Symbol.iterator]||t["@@iterator"];if(r)return(r=r.call(t)).next.bind(r);if(Array.isArray(t)||(r=_unsupportedIterableToArray(t))||e&&t&&"number"==typeof t.length){r&&(t=r);var i=0;return function(){return i>=t.length?{done:!0}:{done:!1,value:t[i++]}}}throw new TypeError("Invalid attempt to iterate non-iterable instance.\nIn order to be iterable, non-array objects must have a [Symbol.iterator]() method.")}function _skipFirstGeneratorNext(t){return function(){var e=t.apply(this,arguments);return e.next(),e}}function _toPrimitive(t,e){if("object"!=typeof t||null===t)return t;var r=t[Symbol.toPrimitive];if(void 0!==r){var i=r.call(t,e||"default");if("object"!=typeof i)return i;throw new TypeError("@@toPrimitive must return a primitive value.")}return("string"===e?String:Number)(t)}function _toPropertyKey(t){var e=_toPrimitive(t,"string");return"symbol"==typeof e?e:String(e)}function _initializerWarningHelper(t,e){throw new Error("Decorating class property failed. Please ensure that proposal-class-properties is enabled and runs after the decorators transform.")}function _initializerDefineProperty(t,e,r,i){r&&Object.defineProperty(t,e,{enumerable:r.enumerable,configurable:r.configurable,writable:r.writable,value:r.initializer?r.initializer.call(i):void 0})}function _applyDecoratedDescriptor(t,e,r,i,n){var a={};return Object.keys(i).forEach((function(t){a[t]=i[t]})),a.enumerable=!!a.enumerable,a.configurable=!!a.configurable,("value"in a||a.initializer)&&(a.writable=!0),a=r.slice().reverse().reduce((function(r,i){return i(t,e,r)||r}),a),n&&void 0!==a.initializer&&(a.value=a.initializer?a.initializer.call(n):void 0,a.initializer=void 0),void 0===a.initializer&&(Object.defineProperty(t,e,a),a=null),a}__webpack_require__.r(__webpack_exports__),__webpack_require__.d(__webpack_exports__,{LottiePlayer:function(){return LottiePlayer},PlayMode:function(){return PlayMode},PlayerEvents:function(){return PlayerEvents},PlayerState:function(){return PlayerState},parseSrc:function(){return parseSrc}}),_AsyncGenerator.prototype["function"==typeof Symbol&&Symbol.asyncIterator||"@@asyncIterator"]=function(){return this},_AsyncGenerator.prototype.next=function(t){return this._invoke("next",t)},_AsyncGenerator.prototype.throw=function(t){return this._invoke("throw",t)},_AsyncGenerator.prototype.return=function(t){return this._invoke("return",t)};var id=0;function _classPrivateFieldLooseKey(t){return"__private_"+id+++"_"+t}function _classPrivateFieldLooseBase(t,e){if(!Object.prototype.hasOwnProperty.call(t,e))throw new TypeError("attempted to use private field on non-instance");return t}function _classPrivateFieldGet(t,e){return _classApplyDescriptorGet(t,_classExtractFieldDescriptor(t,e,"get"))}function _classPrivateFieldSet(t,e,r){return _classApplyDescriptorSet(t,_classExtractFieldDescriptor(t,e,"set"),r),r}function _classPrivateFieldDestructureSet(t,e){return _classApplyDescriptorDestructureSet(t,_classExtractFieldDescriptor(t,e,"set"))}function _classExtractFieldDescriptor(t,e,r){if(!e.has(t))throw new TypeError("attempted to "+r+" private field on non-instance");return e.get(t)}function _classStaticPrivateFieldSpecGet(t,e,r){return _classCheckPrivateStaticAccess(t,e),_classCheckPrivateStaticFieldDescriptor(r,"get"),_classApplyDescriptorGet(t,r)}function _classStaticPrivateFieldSpecSet(t,e,r,i){return _classCheckPrivateStaticAccess(t,e),_classCheckPrivateStaticFieldDescriptor(r,"set"),_classApplyDescriptorSet(t,r,i),i}function _classStaticPrivateMethodGet(t,e,r){return _classCheckPrivateStaticAccess(t,e),r}function _classStaticPrivateMethodSet(){throw new TypeError("attempted to set read only static private field")}function _classApplyDescriptorGet(t,e){return e.get?e.get.call(t):e.value}function _classApplyDescriptorSet(t,e,r){if(e.set)e.set.call(t,r);else{if(!e.writable)throw new TypeError("attempted to set read only private field");e.value=r}}function _classApplyDescriptorDestructureSet(t,e){if(e.set)return"__destrObj"in e||(e.__destrObj={set value(r){e.set.call(t,r)}}),e.__destrObj;if(!e.writable)throw new TypeError("attempted to set read only private field");return e}function _classStaticPrivateFieldDestructureSet(t,e,r){return _classCheckPrivateStaticAccess(t,e),_classCheckPrivateStaticFieldDescriptor(r,"set"),_classApplyDescriptorDestructureSet(t,r)}function _classCheckPrivateStaticAccess(t,e){if(t!==e)throw new TypeError("Private static access of wrong provenance")}function _classCheckPrivateStaticFieldDescriptor(t,e){if(void 0===t)throw new TypeError("attempted to "+e+" private static field before its declaration")}function _decorate(t,e,r,i){var n=_getDecoratorsApi();if(i)for(var a=0;a<i.length;a++)n=i[a](n);var s=e((function(t){n.initializeInstanceElements(t,o.elements)}),r),o=n.decorateClass(_coalesceClassElements(s.d.map(_createElementDescriptor)),t);return n.initializeClassElements(s.F,o.elements),n.runClassFinishers(s.F,o.finishers)}function _getDecoratorsApi(){_getDecoratorsApi=function(){return t};var t={elementsDefinitionOrder:[["method"],["field"]],initializeInstanceElements:function(t,e){["method","field"].forEach((function(r){e.forEach((function(e){e.kind===r&&"own"===e.placement&&this.defineClassElement(t,e)}),this)}),this)},initializeClassElements:function(t,e){var r=t.prototype;["method","field"].forEach((function(i){e.forEach((function(e){var n=e.placement;if(e.kind===i&&("static"===n||"prototype"===n)){var a="static"===n?t:r;this.defineClassElement(a,e)}}),this)}),this)},defineClassElement:function(t,e){var r=e.descriptor;if("field"===e.kind){var i=e.initializer;r={enumerable:r.enumerable,writable:r.writable,configurable:r.configurable,value:void 0===i?void 0:i.call(t)}}Object.defineProperty(t,e.key,r)},decorateClass:function(t,e){var r=[],i=[],n={static:[],prototype:[],own:[]};if(t.forEach((function(t){this.addElementPlacement(t,n)}),this),t.forEach((function(t){if(!_hasDecorators(t))return r.push(t);var e=this.decorateElement(t,n);r.push(e.element),r.push.apply(r,e.extras),i.push.apply(i,e.finishers)}),this),!e)return{elements:r,finishers:i};var a=this.decorateConstructor(r,e);return i.push.apply(i,a.finishers),a.finishers=i,a},addElementPlacement:function(t,e,r){var i=e[t.placement];if(!r&&-1!==i.indexOf(t.key))throw new TypeError("Duplicated element ("+t.key+")");i.push(t.key)},decorateElement:function(t,e){for(var r=[],i=[],n=t.decorators,a=n.length-1;a>=0;a--){var s=e[t.placement];s.splice(s.indexOf(t.key),1);var o=this.fromElementDescriptor(t),l=this.toElementFinisherExtras((0,n[a])(o)||o);t=l.element,this.addElementPlacement(t,e),l.finisher&&i.push(l.finisher);var h=l.extras;if(h){for(var p=0;p<h.length;p++)this.addElementPlacement(h[p],e);r.push.apply(r,h)}}return{element:t,finishers:i,extras:r}},decorateConstructor:function(t,e){for(var r=[],i=e.length-1;i>=0;i--){var n=this.fromClassDescriptor(t),a=this.toClassDescriptor((0,e[i])(n)||n);if(void 0!==a.finisher&&r.push(a.finisher),void 0!==a.elements){t=a.elements;for(var s=0;s<t.length-1;s++)for(var o=s+1;o<t.length;o++)if(t[s].key===t[o].key&&t[s].placement===t[o].placement)throw new TypeError("Duplicated element ("+t[s].key+")")}}return{elements:t,finishers:r}},fromElementDescriptor:function(t){var e={kind:t.kind,key:t.key,placement:t.placement,descriptor:t.descriptor};return Object.defineProperty(e,Symbol.toStringTag,{value:"Descriptor",configurable:!0}),"field"===t.kind&&(e.initializer=t.initializer),e},toElementDescriptors:function(t){if(void 0!==t)return _toArray(t).map((function(t){var e=this.toElementDescriptor(t);return this.disallowProperty(t,"finisher","An element descriptor"),this.disallowProperty(t,"extras","An element descriptor"),e}),this)},toElementDescriptor:function(t){var e=String(t.kind);if("method"!==e&&"field"!==e)throw new TypeError('An element descriptor\'s .kind property must be either "method" or "field", but a decorator created an element descriptor with .kind "'+e+'"');var r=_toPropertyKey(t.key),i=String(t.placement);if("static"!==i&&"prototype"!==i&&"own"!==i)throw new TypeError('An element descriptor\'s .placement property must be one of "static", "prototype" or "own", but a decorator created an element descriptor with .placement "'+i+'"');var n=t.descriptor;this.disallowProperty(t,"elements","An element descriptor");var a={kind:e,key:r,placement:i,descriptor:Object.assign({},n)};return"field"!==e?this.disallowProperty(t,"initializer","A method descriptor"):(this.disallowProperty(n,"get","The property descriptor of a field descriptor"),this.disallowProperty(n,"set","The property descriptor of a field descriptor"),this.disallowProperty(n,"value","The property descriptor of a field descriptor"),a.initializer=t.initializer),a},toElementFinisherExtras:function(t){return{element:this.toElementDescriptor(t),finisher:_optionalCallableProperty(t,"finisher"),extras:this.toElementDescriptors(t.extras)}},fromClassDescriptor:function(t){var e={kind:"class",elements:t.map(this.fromElementDescriptor,this)};return Object.defineProperty(e,Symbol.toStringTag,{value:"Descriptor",configurable:!0}),e},toClassDescriptor:function(t){var e=String(t.kind);if("class"!==e)throw new TypeError('A class descriptor\'s .kind property must be "class", but a decorator created a class descriptor with .kind "'+e+'"');this.disallowProperty(t,"key","A class descriptor"),this.disallowProperty(t,"placement","A class descriptor"),this.disallowProperty(t,"descriptor","A class descriptor"),this.disallowProperty(t,"initializer","A class descriptor"),this.disallowProperty(t,"extras","A class descriptor");var r=_optionalCallableProperty(t,"finisher");return{elements:this.toElementDescriptors(t.elements),finisher:r}},runClassFinishers:function(t,e){for(var r=0;r<e.length;r++){var i=(0,e[r])(t);if(void 0!==i){if("function"!=typeof i)throw new TypeError("Finishers must return a constructor.");t=i}}return t},disallowProperty:function(t,e,r){if(void 0!==t[e])throw new TypeError(r+" can't have a ."+e+" property.")}};return t}function _createElementDescriptor(t){var e,r=_toPropertyKey(t.key);"method"===t.kind?e={value:t.value,writable:!0,configurable:!0,enumerable:!1}:"get"===t.kind?e={get:t.value,configurable:!0,enumerable:!1}:"set"===t.kind?e={set:t.value,configurable:!0,enumerable:!1}:"field"===t.kind&&(e={configurable:!0,writable:!0,enumerable:!0});var i={kind:"field"===t.kind?"field":"method",key:r,placement:t.static?"static":"field"===t.kind?"own":"prototype",descriptor:e};return t.decorators&&(i.decorators=t.decorators),"field"===t.kind&&(i.initializer=t.value),i}function _coalesceGetterSetter(t,e){void 0!==t.descriptor.get?e.descriptor.get=t.descriptor.get:e.descriptor.set=t.descriptor.set}function _coalesceClassElements(t){for(var e=[],r=function(t){return"method"===t.kind&&t.key===a.key&&t.placement===a.placement},i=0;i<t.length;i++){var n,a=t[i];if("method"===a.kind&&(n=e.find(r)))if(_isDataDescriptor(a.descriptor)||_isDataDescriptor(n.descriptor)){if(_hasDecorators(a)||_hasDecorators(n))throw new ReferenceError("Duplicated methods ("+a.key+") can't be decorated.");n.descriptor=a.descriptor}else{if(_hasDecorators(a)){if(_hasDecorators(n))throw new ReferenceError("Decorators can't be placed on different accessors with for the same property ("+a.key+").");n.decorators=a.decorators}_coalesceGetterSetter(a,n)}else e.push(a)}return e}function _hasDecorators(t){return t.decorators&&t.decorators.length}function _isDataDescriptor(t){return void 0!==t&&!(void 0===t.value&&void 0===t.writable)}function _optionalCallableProperty(t,e){var r=t[e];if(void 0!==r&&"function"!=typeof r)throw new TypeError("Expected '"+e+"' to be a function");return r}function _classPrivateMethodGet(t,e,r){if(!e.has(t))throw new TypeError("attempted to get private field on non-instance");return r}function _checkPrivateRedeclaration(t,e){if(e.has(t))throw new TypeError("Cannot initialize the same private elements twice on an object")}function _classPrivateFieldInitSpec(t,e,r){_checkPrivateRedeclaration(t,e),e.set(t,r)}function _classPrivateMethodInitSpec(t,e){_checkPrivateRedeclaration(t,e),e.add(t)}function _classPrivateMethodSet(){throw new TypeError("attempted to reassign private method")}var _extendStatics=function(t,e){return _extendStatics=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r])},_extendStatics(t,e)};function __extends(t,e){if("function"!=typeof e&&null!==e)throw new TypeError("Class extends value "+String(e)+" is not a constructor or null");function r(){this.constructor=t}_extendStatics(t,e),t.prototype=null===e?Object.create(e):(r.prototype=e.prototype,new r)}var _assign=function(){return _assign=Object.assign||function(t){for(var e,r=1,i=arguments.length;r<i;r++)for(var n in e=arguments[r])Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n]);return t},_assign.apply(this,arguments)};function __rest(t,e){var r={};for(var i in t)Object.prototype.hasOwnProperty.call(t,i)&&e.indexOf(i)<0&&(r[i]=t[i]);if(null!=t&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(i=Object.getOwnPropertySymbols(t);n<i.length;n++)e.indexOf(i[n])<0&&Object.prototype.propertyIsEnumerable.call(t,i[n])&&(r[i[n]]=t[i[n]])}return r}function __decorate(t,e,r,i){var n,a=arguments.length,s=a<3?e:null===i?i=Object.getOwnPropertyDescriptor(e,r):i;if("object"==typeof Reflect&&"function"==typeof Reflect.decorate)s=Reflect.decorate(t,e,r,i);else for(var o=t.length-1;o>=0;o--)(n=t[o])&&(s=(a<3?n(s):a>3?n(e,r,s):n(e,r))||s);return a>3&&s&&Object.defineProperty(e,r,s),s}function __param(t,e){return function(r,i){e(r,i,t)}}function __metadata(t,e){if("object"==typeof Reflect&&"function"==typeof Reflect.metadata)return Reflect.metadata(t,e)}function __awaiter(t,e,r,i){return new(r||(r=Promise))((function(n,a){function s(t){try{l(i.next(t))}catch(t){a(t)}}function o(t){try{l(i.throw(t))}catch(t){a(t)}}function l(t){var e;t.done?n(t.value):(e=t.value,e instanceof r?e:new r((function(t){t(e)}))).then(s,o)}l((i=i.apply(t,e||[])).next())}))}function __generator(t,e){var r,i,n,a,s={label:0,sent:function(){if(1&n[0])throw n[1];return n[1]},trys:[],ops:[]};return a={next:o(0),throw:o(1),return:o(2)},"function"==typeof Symbol&&(a[Symbol.iterator]=function(){return this}),a;function o(a){return function(o){return function(a){if(r)throw new TypeError("Generator is already executing.");for(;s;)try{if(r=1,i&&(n=2&a[0]?i.return:a[0]?i.throw||((n=i.return)&&n.call(i),0):i.next)&&!(n=n.call(i,a[1])).done)return n;switch(i=0,n&&(a=[2&a[0],n.value]),a[0]){case 0:case 1:n=a;break;case 4:return s.label++,{value:a[1],done:!1};case 5:s.label++,i=a[1],a=[0];continue;case 7:a=s.ops.pop(),s.trys.pop();continue;default:if(!((n=(n=s.trys).length>0&&n[n.length-1])||6!==a[0]&&2!==a[0])){s=0;continue}if(3===a[0]&&(!n||a[1]>n[0]&&a[1]<n[3])){s.label=a[1];break}if(6===a[0]&&s.label<n[1]){s.label=n[1],n=a;break}if(n&&s.label<n[2]){s.label=n[2],s.ops.push(a);break}n[2]&&s.ops.pop(),s.trys.pop();continue}a=e.call(t,s)}catch(t){a=[6,t],i=0}finally{r=n=0}if(5&a[0])throw a[1];return{value:a[0]?a[1]:void 0,done:!0}}([a,o])}}}var __createBinding=Object.create?function(t,e,r,i){void 0===i&&(i=r),Object.defineProperty(t,i,{enumerable:!0,get:function(){return e[r]}})}:function(t,e,r,i){void 0===i&&(i=r),t[i]=e[r]};function __exportStar(t,e){for(var r in t)"default"===r||Object.prototype.hasOwnProperty.call(e,r)||__createBinding(e,t,r)}function __values(t){var e="function"==typeof Symbol&&Symbol.iterator,r=e&&t[e],i=0;if(r)return r.call(t);if(t&&"number"==typeof t.length)return{next:function(){return t&&i>=t.length&&(t=void 0),{value:t&&t[i++],done:!t}}};throw new TypeError(e?"Object is not iterable.":"Symbol.iterator is not defined.")}function __read(t,e){var r="function"==typeof Symbol&&t[Symbol.iterator];if(!r)return t;var i,n,a=r.call(t),s=[];try{for(;(void 0===e||e-- >0)&&!(i=a.next()).done;)s.push(i.value)}catch(t){n={error:t}}finally{try{i&&!i.done&&(r=a.return)&&r.call(a)}finally{if(n)throw n.error}}return s}function __spread(){for(var t=[],e=0;e<arguments.length;e++)t=t.concat(__read(arguments[e]));return t}function __spreadArrays(){for(var t=0,e=0,r=arguments.length;e<r;e++)t+=arguments[e].length;var i=Array(t),n=0;for(e=0;e<r;e++)for(var a=arguments[e],s=0,o=a.length;s<o;s++,n++)i[n]=a[s];return i}function __spreadArray(t,e,r){if(r||2===arguments.length)for(var i,n=0,a=e.length;n<a;n++)!i&&n in e||(i||(i=Array.prototype.slice.call(e,0,n)),i[n]=e[n]);return t.concat(i||Array.prototype.slice.call(e))}function __await(t){return this instanceof __await?(this.v=t,this):new __await(t)}function __asyncGenerator(t,e,r){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var i,n=r.apply(t,e||[]),a=[];return i={},s("next"),s("throw"),s("return"),i[Symbol.asyncIterator]=function(){return this},i;function s(t){n[t]&&(i[t]=function(e){return new Promise((function(r,i){a.push([t,e,r,i])>1||o(t,e)}))})}function o(t,e){try{!function(t){t.value instanceof __await?Promise.resolve(t.value.v).then(l,h):p(a[0][2],t)}(n[t](e))}catch(t){p(a[0][3],t)}}function l(t){o("next",t)}function h(t){o("throw",t)}function p(t,e){t(e),a.shift(),a.length&&o(a[0][0],a[0][1])}}function __asyncDelegator(t){var e,r;return e={},i("next"),i("throw",(function(t){throw t})),i("return"),e[Symbol.iterator]=function(){return this},e;function i(i,n){e[i]=t[i]?function(e){return(r=!r)?{value:__await(t[i](e)),done:"return"===i}:n?n(e):e}:n}}function __asyncValues(t){if(!Symbol.asyncIterator)throw new TypeError("Symbol.asyncIterator is not defined.");var e,r=t[Symbol.asyncIterator];return r?r.call(t):(t="function"==typeof __values?__values(t):t[Symbol.iterator](),e={},i("next"),i("throw"),i("return"),e[Symbol.asyncIterator]=function(){return this},e);function i(r){e[r]=t[r]&&function(e){return new Promise((function(i,n){!function(t,e,r,i){Promise.resolve(i).then((function(e){t({value:e,done:r})}),e)}(i,n,(e=t[r](e)).done,e.value)}))}}}function __makeTemplateObject(t,e){return Object.defineProperty?Object.defineProperty(t,"raw",{value:e}):t.raw=e,t}var __setModuleDefault=Object.create?function(t,e){Object.defineProperty(t,"default",{enumerable:!0,value:e})}:function(t,e){t.default=e};function __importStar(t){if(t&&t.__esModule)return t;var e={};if(null!=t)for(var r in t)"default"!==r&&Object.prototype.hasOwnProperty.call(t,r)&&__createBinding(e,t,r);return __setModuleDefault(e,t),e}function __importDefault(t){return t&&t.__esModule?t:{default:t}}function __classPrivateFieldGet(t,e,r,i){if("a"===r&&!i)throw new TypeError("Private accessor was defined without a getter");if("function"==typeof e?t!==e||!i:!e.has(t))throw new TypeError("Cannot read private member from an object whose class did not declare it");return"m"===r?i:"a"===r?i.call(t):i?i.value:e.get(t)}function __classPrivateFieldSet(t,e,r,i,n){if("m"===i)throw new TypeError("Private method is not writable");if("a"===i&&!n)throw new TypeError("Private accessor was defined without a setter");if("function"==typeof e?t!==e||!n:!e.has(t))throw new TypeError("Cannot write private member to an object whose class did not declare it");return"a"===i?n.call(t,r):n?n.value=r:e.set(t,r),r}var t$3=window.ShadowRoot&&(void 0===window.ShadyCSS||window.ShadyCSS.nativeShadow)&&"adoptedStyleSheets"in Document.prototype&&"replace"in CSSStyleSheet.prototype,e$8=Symbol(),n$5=new Map;class s$3{constructor(t,e){if(this._$cssResult$=!0,e!==e$8)throw Error("CSSResult is not constructable. Use `unsafeCSS` or `css` instead.");this.cssText=t}get styleSheet(){var t=n$5.get(this.cssText);return t$3&&void 0===t&&(n$5.set(this.cssText,t=new CSSStyleSheet),t.replaceSync(this.cssText)),t}toString(){return this.cssText}}var o$5=t=>new s$3("string"==typeof t?t:t+"",e$8),r$3=function(t){for(var e=arguments.length,r=new Array(e>1?e-1:0),i=1;i<e;i++)r[i-1]=arguments[i];var n=1===t.length?t[0]:r.reduce(((e,r,i)=>e+(t=>{if(!0===t._$cssResult$)return t.cssText;if("number"==typeof t)return t;throw Error("Value passed to 'css' function must be a 'css' function result: "+t+". Use 'unsafeCSS' to pass non-literal values, but take care to ensure page security.")})(r)+t[i+1]),t[0]);return new s$3(n,e$8)},i$3=(t,e)=>{t$3?t.adoptedStyleSheets=e.map((t=>t instanceof CSSStyleSheet?t:t.styleSheet)):e.forEach((e=>{var r=document.createElement("style"),i=window.litNonce;void 0!==i&&r.setAttribute("nonce",i),r.textContent=e.cssText,t.appendChild(r)}))},S$1=t$3?t=>t:t=>t instanceof CSSStyleSheet?(t=>{var e="";for(var r of t.cssRules)e+=r.cssText;return o$5(e)})(t):t,s$2,e$7=window.trustedTypes,r$2=e$7?e$7.emptyScript:"",h$2=window.reactiveElementPolyfillSupport,o$4={toAttribute(t,e){switch(e){case Boolean:t=t?r$2:null;break;case Object:case Array:t=null==t?t:JSON.stringify(t)}return t},fromAttribute(t,e){var r=t;switch(e){case Boolean:r=null!==t;break;case Number:r=null===t?null:Number(t);break;case Object:case Array:try{r=JSON.parse(t)}catch(t){r=null}}return r}},n$4=(t,e)=>e!==t&&(e==e||t==t),l$3={attribute:!0,type:String,converter:o$4,reflect:!1,hasChanged:n$4},t$2;class a$1 extends HTMLElement{constructor(){super(),this._$Et=new Map,this.isUpdatePending=!1,this.hasUpdated=!1,this._$Ei=null,this.o()}static addInitializer(t){var e;null!==(e=this.l)&&void 0!==e||(this.l=[]),this.l.push(t)}static get observedAttributes(){this.finalize();var t=[];return this.elementProperties.forEach(((e,r)=>{var i=this._$Eh(r,e);void 0!==i&&(this._$Eu.set(i,r),t.push(i))})),t}static createProperty(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:l$3;if(e.state&&(e.attribute=!1),this.finalize(),this.elementProperties.set(t,e),!e.noAccessor&&!this.prototype.hasOwnProperty(t)){var r="symbol"==typeof t?Symbol():"__"+t,i=this.getPropertyDescriptor(t,r,e);void 0!==i&&Object.defineProperty(this.prototype,t,i)}}static getPropertyDescriptor(t,e,r){return{get(){return this[e]},set(i){var n=this[t];this[e]=i,this.requestUpdate(t,n,r)},configurable:!0,enumerable:!0}}static getPropertyOptions(t){return this.elementProperties.get(t)||l$3}static finalize(){if(this.hasOwnProperty("finalized"))return!1;this.finalized=!0;var t=Object.getPrototypeOf(this);if(t.finalize(),this.elementProperties=new Map(t.elementProperties),this._$Eu=new Map,this.hasOwnProperty("properties")){var e=this.properties,r=[...Object.getOwnPropertyNames(e),...Object.getOwnPropertySymbols(e)];for(var i of r)this.createProperty(i,e[i])}return this.elementStyles=this.finalizeStyles(this.styles),!0}static finalizeStyles(t){var e=[];if(Array.isArray(t)){var r=new Set(t.flat(1/0).reverse());for(var i of r)e.unshift(S$1(i))}else void 0!==t&&e.push(S$1(t));return e}static _$Eh(t,e){var r=e.attribute;return!1===r?void 0:"string"==typeof r?r:"string"==typeof t?t.toLowerCase():void 0}o(){var t;this._$Ep=new Promise((t=>this.enableUpdating=t)),this._$AL=new Map,this._$Em(),this.requestUpdate(),null===(t=this.constructor.l)||void 0===t||t.forEach((t=>t(this)))}addController(t){var e,r;(null!==(e=this._$Eg)&&void 0!==e?e:this._$Eg=[]).push(t),void 0!==this.renderRoot&&this.isConnected&&(null===(r=t.hostConnected)||void 0===r||r.call(t))}removeController(t){var e;null===(e=this._$Eg)||void 0===e||e.splice(this._$Eg.indexOf(t)>>>0,1)}_$Em(){this.constructor.elementProperties.forEach(((t,e)=>{this.hasOwnProperty(e)&&(this._$Et.set(e,this[e]),delete this[e])}))}createRenderRoot(){var t,e=null!==(t=this.shadowRoot)&&void 0!==t?t:this.attachShadow(this.constructor.shadowRootOptions);return i$3(e,this.constructor.elementStyles),e}connectedCallback(){var t;void 0===this.renderRoot&&(this.renderRoot=this.createRenderRoot()),this.enableUpdating(!0),null===(t=this._$Eg)||void 0===t||t.forEach((t=>{var e;return null===(e=t.hostConnected)||void 0===e?void 0:e.call(t)}))}enableUpdating(t){}disconnectedCallback(){var t;null===(t=this._$Eg)||void 0===t||t.forEach((t=>{var e;return null===(e=t.hostDisconnected)||void 0===e?void 0:e.call(t)}))}attributeChangedCallback(t,e,r){this._$AK(t,r)}_$ES(t,e){var r,i,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:l$3,a=this.constructor._$Eh(t,n);if(void 0!==a&&!0===n.reflect){var s=(null!==(i=null===(r=n.converter)||void 0===r?void 0:r.toAttribute)&&void 0!==i?i:o$4.toAttribute)(e,n.type);this._$Ei=t,null==s?this.removeAttribute(a):this.setAttribute(a,s),this._$Ei=null}}_$AK(t,e){var r,i,n,a=this.constructor,s=a._$Eu.get(t);if(void 0!==s&&this._$Ei!==s){var o=a.getPropertyOptions(s),l=o.converter,h=null!==(n=null!==(i=null===(r=l)||void 0===r?void 0:r.fromAttribute)&&void 0!==i?i:"function"==typeof l?l:null)&&void 0!==n?n:o$4.fromAttribute;this._$Ei=s,this[s]=h(e,o.type),this._$Ei=null}}requestUpdate(t,e,r){var i=!0;void 0!==t&&(((r=r||this.constructor.getPropertyOptions(t)).hasChanged||n$4)(this[t],e)?(this._$AL.has(t)||this._$AL.set(t,e),!0===r.reflect&&this._$Ei!==t&&(void 0===this._$E_&&(this._$E_=new Map),this._$E_.set(t,r))):i=!1),!this.isUpdatePending&&i&&(this._$Ep=this._$EC())}_$EC(){var t=this;return _asyncToGenerator((function*(){t.isUpdatePending=!0;try{yield t._$Ep}catch(e){Promise.reject(e)}var e=t.scheduleUpdate();return null!=e&&(yield e),!t.isUpdatePending}))()}scheduleUpdate(){return this.performUpdate()}performUpdate(){var t;if(this.isUpdatePending){this.hasUpdated,this._$Et&&(this._$Et.forEach(((t,e)=>this[e]=t)),this._$Et=void 0);var e=!1,r=this._$AL;try{(e=this.shouldUpdate(r))?(this.willUpdate(r),null===(t=this._$Eg)||void 0===t||t.forEach((t=>{var e;return null===(e=t.hostUpdate)||void 0===e?void 0:e.call(t)})),this.update(r)):this._$EU()}catch(t){throw e=!1,this._$EU(),t}e&&this._$AE(r)}}willUpdate(t){}_$AE(t){var e;null===(e=this._$Eg)||void 0===e||e.forEach((t=>{var e;return null===(e=t.hostUpdated)||void 0===e?void 0:e.call(t)})),this.hasUpdated||(this.hasUpdated=!0,this.firstUpdated(t)),this.updated(t)}_$EU(){this._$AL=new Map,this.isUpdatePending=!1}get updateComplete(){return this.getUpdateComplete()}getUpdateComplete(){return this._$Ep}shouldUpdate(t){return!0}update(t){void 0!==this._$E_&&(this._$E_.forEach(((t,e)=>this._$ES(e,this[e],t))),this._$E_=void 0),this._$EU()}updated(t){}firstUpdated(t){}}a$1.finalized=!0,a$1.elementProperties=new Map,a$1.elementStyles=[],a$1.shadowRootOptions={mode:"open"},null==h$2||h$2({ReactiveElement:a$1}),(null!==(s$2=globalThis.reactiveElementVersions)&&void 0!==s$2?s$2:globalThis.reactiveElementVersions=[]).push("1.2.1");var i$2=globalThis.trustedTypes,s$1=i$2?i$2.createPolicy("lit-html",{createHTML:t=>t}):void 0,e$6="lit$".concat((Math.random()+"").slice(9),"$"),o$3="?"+e$6,n$3="<".concat(o$3,">"),l$2=document,h$1=function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:"";return l$2.createComment(t)},r$1=t=>null===t||"object"!=typeof t&&"function"!=typeof t,d=Array.isArray,u=t=>{var e;return d(t)||"function"==typeof(null===(e=t)||void 0===e?void 0:e[Symbol.iterator])},c=/<(?:(!--|\/[^a-zA-Z])|(\/?[a-zA-Z][^>\s]*)|(\/?$))/g,v=/-->/g,a=/>/g,f=/>|[ 	\n\r](?:([^\s"'>=/]+)([ 	\n\r]*=[ 	\n\r]*(?:[^ 	\n\r"'`<>=]|("|')|))|$)/g,_=/'/g,m=/"/g,g=/^(?:script|style|textarea)$/i,p=t=>function(e){for(var r=arguments.length,i=new Array(r>1?r-1:0),n=1;n<r;n++)i[n-1]=arguments[n];return{_$litType$:t,strings:e,values:i}},$=p(1),y=p(2),b=Symbol.for("lit-noChange"),w=Symbol.for("lit-nothing"),T=new WeakMap,x=(t,e,r)=>{var i,n,a=null!==(i=null==r?void 0:r.renderBefore)&&void 0!==i?i:e,s=a._$litPart$;if(void 0===s){var o=null!==(n=null==r?void 0:r.renderBefore)&&void 0!==n?n:null;a._$litPart$=s=new N(e.insertBefore(h$1(),o),o,void 0,null!=r?r:{})}return s._$AI(t),s},A=l$2.createTreeWalker(l$2,129,null,!1),C=(t,e)=>{for(var r,i=t.length-1,n=[],s=2===e?"<svg>":"",o=c,l=0;l<i;l++){for(var h=t[l],p=void 0,d=void 0,u=-1,y=0;y<h.length&&(o.lastIndex=y,null!==(d=o.exec(h)));)y=o.lastIndex,o===c?"!--"===d[1]?o=v:void 0!==d[1]?o=a:void 0!==d[2]?(g.test(d[2])&&(r=RegExp("</"+d[2],"g")),o=f):void 0!==d[3]&&(o=f):o===f?">"===d[0]?(o=null!=r?r:c,u=-1):void 0===d[1]?u=-2:(u=o.lastIndex-d[2].length,p=d[1],o=void 0===d[3]?f:'"'===d[3]?m:_):o===m||o===_?o=f:o===v||o===a?o=c:(o=f,r=void 0);var b=o===f&&t[l+1].startsWith("/>")?" ":"";s+=o===c?h+n$3:u>=0?(n.push(p),h.slice(0,u)+"$lit$"+h.slice(u)+e$6+b):h+e$6+(-2===u?(n.push(void 0),l):b)}var P=s+(t[i]||"<?>")+(2===e?"</svg>":"");if(!Array.isArray(t)||!t.hasOwnProperty("raw"))throw Error("invalid template strings array");return[void 0!==s$1?s$1.createHTML(P):P,n]};class E{constructor(t,e){var r,{strings:i,_$litType$:n}=t;this.parts=[];var a=0,s=0,o=i.length-1,l=this.parts,[h,p]=C(i,n);if(this.el=E.createElement(h,e),A.currentNode=this.el.content,2===n){var c=this.el.content,f=c.firstChild;f.remove(),c.append(...f.childNodes)}for(;null!==(r=A.nextNode())&&l.length<o;){if(1===r.nodeType){if(r.hasAttributes()){var d=[];for(var u of r.getAttributeNames())if(u.endsWith("$lit$")||u.startsWith(e$6)){var m=p[s++];if(d.push(u),void 0!==m){var y=r.getAttribute(m.toLowerCase()+"$lit$").split(e$6),v=/([.?@])?(.*)/.exec(m);l.push({type:1,index:a,name:v[2],strings:y,ctor:"."===v[1]?M:"?"===v[1]?H:"@"===v[1]?I:S})}else l.push({type:6,index:a})}for(var b of d)r.removeAttribute(b)}if(g.test(r.tagName)){var _=r.textContent.split(e$6),P=_.length-1;if(P>0){r.textContent=i$2?i$2.emptyScript:"";for(var x=0;x<P;x++)r.append(_[x],h$1()),A.nextNode(),l.push({type:2,index:++a});r.append(_[P],h$1())}}}else if(8===r.nodeType)if(r.data===o$3)l.push({type:2,index:a});else for(var w=-1;-1!==(w=r.data.indexOf(e$6,w+1));)l.push({type:7,index:a}),w+=e$6.length-1;a++}}static createElement(t,e){var r=l$2.createElement("template");return r.innerHTML=t,r}}function P(t,e){var r,i,n,a,s=arguments.length>2&&void 0!==arguments[2]?arguments[2]:t,o=arguments.length>3?arguments[3]:void 0;if(e===b)return e;var l=void 0!==o?null===(r=s._$Cl)||void 0===r?void 0:r[o]:s._$Cu,h=r$1(e)?void 0:e._$litDirective$;return(null==l?void 0:l.constructor)!==h&&(null===(i=null==l?void 0:l._$AO)||void 0===i||i.call(l,!1),void 0===h?l=void 0:(l=new h(t))._$AT(t,s,o),void 0!==o?(null!==(n=(a=s)._$Cl)&&void 0!==n?n:a._$Cl=[])[o]=l:s._$Cu=l),void 0!==l&&(e=P(t,l._$AS(t,e.values),l,o)),e}class V{constructor(t,e){this.v=[],this._$AN=void 0,this._$AD=t,this._$AM=e}get parentNode(){return this._$AM.parentNode}get _$AU(){return this._$AM._$AU}p(t){var e,{el:{content:r},parts:i}=this._$AD,n=(null!==(e=null==t?void 0:t.creationScope)&&void 0!==e?e:l$2).importNode(r,!0);A.currentNode=n;for(var a=A.nextNode(),s=0,o=0,l=i[0];void 0!==l;){if(s===l.index){var h=void 0;2===l.type?h=new N(a,a.nextSibling,this,t):1===l.type?h=new l.ctor(a,l.name,l.strings,this,t):6===l.type&&(h=new L(a,this,t)),this.v.push(h),l=i[++o]}s!==(null==l?void 0:l.index)&&(a=A.nextNode(),s++)}return n}m(t){var e=0;for(var r of this.v)void 0!==r&&(void 0!==r.strings?(r._$AI(t,r,e),e+=r.strings.length-2):r._$AI(t[e])),e++}}class N{constructor(t,e,r,i){var n;this.type=2,this._$AH=w,this._$AN=void 0,this._$AA=t,this._$AB=e,this._$AM=r,this.options=i,this._$Cg=null===(n=null==i?void 0:i.isConnected)||void 0===n||n}get _$AU(){var t,e;return null!==(e=null===(t=this._$AM)||void 0===t?void 0:t._$AU)&&void 0!==e?e:this._$Cg}get parentNode(){var t=this._$AA.parentNode,e=this._$AM;return void 0!==e&&11===t.nodeType&&(t=e.parentNode),t}get startNode(){return this._$AA}get endNode(){return this._$AB}_$AI(t){t=P(this,t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:this),r$1(t)?t===w||null==t||""===t?(this._$AH!==w&&this._$AR(),this._$AH=w):t!==this._$AH&&t!==b&&this.$(t):void 0!==t._$litType$?this.T(t):void 0!==t.nodeType?this.S(t):u(t)?this.A(t):this.$(t)}M(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this._$AB;return this._$AA.parentNode.insertBefore(t,e)}S(t){this._$AH!==t&&(this._$AR(),this._$AH=this.M(t))}$(t){this._$AH!==w&&r$1(this._$AH)?this._$AA.nextSibling.data=t:this.S(l$2.createTextNode(t)),this._$AH=t}T(t){var e,{values:r,_$litType$:i}=t,n="number"==typeof i?this._$AC(t):(void 0===i.el&&(i.el=E.createElement(i.h,this.options)),i);if((null===(e=this._$AH)||void 0===e?void 0:e._$AD)===n)this._$AH.m(r);else{var a=new V(n,this),s=a.p(this.options);a.m(r),this.S(s),this._$AH=a}}_$AC(t){var e=T.get(t.strings);return void 0===e&&T.set(t.strings,e=new E(t)),e}A(t){d(this._$AH)||(this._$AH=[],this._$AR());var e,r=this._$AH,i=0;for(var n of t)i===r.length?r.push(e=new N(this.M(h$1()),this.M(h$1()),this,this.options)):e=r[i],e._$AI(n),i++;i<r.length&&(this._$AR(e&&e._$AB.nextSibling,i),r.length=i)}_$AR(){var t,e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:this._$AA.nextSibling,r=arguments.length>1?arguments[1]:void 0;for(null===(t=this._$AP)||void 0===t||t.call(this,!1,!0,r);e&&e!==this._$AB;){var i=e.nextSibling;e.remove(),e=i}}setConnected(t){var e;void 0===this._$AM&&(this._$Cg=t,null===(e=this._$AP)||void 0===e||e.call(this,t))}}class S{constructor(t,e,r,i,n){this.type=1,this._$AH=w,this._$AN=void 0,this.element=t,this.name=e,this._$AM=i,this.options=n,r.length>2||""!==r[0]||""!==r[1]?(this._$AH=Array(r.length-1).fill(new String),this.strings=r):this._$AH=w}get tagName(){return this.element.tagName}get _$AU(){return this._$AM._$AU}_$AI(t){var e=arguments.length>1&&void 0!==arguments[1]?arguments[1]:this,r=arguments.length>2?arguments[2]:void 0,i=arguments.length>3?arguments[3]:void 0,n=this.strings,a=!1;if(void 0===n)t=P(this,t,e,0),(a=!r$1(t)||t!==this._$AH&&t!==b)&&(this._$AH=t);else{var s,o,l=t;for(t=n[0],s=0;s<n.length-1;s++)(o=P(this,l[r+s],e,s))===b&&(o=this._$AH[s]),a||(a=!r$1(o)||o!==this._$AH[s]),o===w?t=w:t!==w&&(t+=(null!=o?o:"")+n[s+1]),this._$AH[s]=o}a&&!i&&this.k(t)}k(t){t===w?this.element.removeAttribute(this.name):this.element.setAttribute(this.name,null!=t?t:"")}}class M extends S{constructor(){super(...arguments),this.type=3}k(t){this.element[this.name]=t===w?void 0:t}}var k=i$2?i$2.emptyScript:"";class H extends S{constructor(){super(...arguments),this.type=4}k(t){t&&t!==w?this.element.setAttribute(this.name,k):this.element.removeAttribute(this.name)}}class I extends S{constructor(t,e,r,i,n){super(t,e,r,i,n),this.type=5}_$AI(t){var e;if((t=null!==(e=P(this,t,arguments.length>1&&void 0!==arguments[1]?arguments[1]:this,0))&&void 0!==e?e:w)!==b){var r=this._$AH,i=t===w&&r!==w||t.capture!==r.capture||t.once!==r.once||t.passive!==r.passive,n=t!==w&&(r===w||i);i&&this.element.removeEventListener(this.name,this,r),n&&this.element.addEventListener(this.name,this,t),this._$AH=t}}handleEvent(t){var e,r;"function"==typeof this._$AH?this._$AH.call(null!==(r=null===(e=this.options)||void 0===e?void 0:e.host)&&void 0!==r?r:this.element,t):this._$AH.handleEvent(t)}}class L{constructor(t,e,r){this.element=t,this.type=6,this._$AN=void 0,this._$AM=e,this.options=r}get _$AU(){return this._$AM._$AU}_$AI(t){P(this,t)}}var R={P:"$lit$",V:e$6,L:o$3,I:1,N:C,R:V,D:u,j:P,H:N,O:S,F:H,B:I,W:M,Z:L},z=window.litHtmlPolyfillSupport,l$1,o$2;null==z||z(E,N),(null!==(t$2=globalThis.litHtmlVersions)&&void 0!==t$2?t$2:globalThis.litHtmlVersions=[]).push("2.1.2");var r=a$1;class s extends a$1{constructor(){super(...arguments),this.renderOptions={host:this},this._$Dt=void 0}createRenderRoot(){var t,e,r=super.createRenderRoot();return null!==(t=(e=this.renderOptions).renderBefore)&&void 0!==t||(e.renderBefore=r.firstChild),r}update(t){var e=this.render();this.hasUpdated||(this.renderOptions.isConnected=this.isConnected),super.update(t),this._$Dt=x(e,this.renderRoot,this.renderOptions)}connectedCallback(){var t;super.connectedCallback(),null===(t=this._$Dt)||void 0===t||t.setConnected(!0)}disconnectedCallback(){var t;super.disconnectedCallback(),null===(t=this._$Dt)||void 0===t||t.setConnected(!1)}render(){return b}}s.finalized=!0,s._$litElement$=!0,null===(l$1=globalThis.litElementHydrateSupport)||void 0===l$1||l$1.call(globalThis,{LitElement:s});var n$2=globalThis.litElementPolyfillSupport;null==n$2||n$2({LitElement:s});var h={_$AK:(t,e,r)=>{t._$AK(e,r)},_$AL:t=>t._$AL};(null!==(o$2=globalThis.litElementVersions)&&void 0!==o$2?o$2:globalThis.litElementVersions=[]).push("3.1.2");var n$1=t=>e=>"function"==typeof e?((t,e)=>(window.customElements.define(t,e),e))(t,e):((t,e)=>{var{kind:r,elements:i}=e;return{kind:r,elements:i,finisher(e){window.customElements.define(t,e)}}})(t,e),i$1=(t,e)=>"method"===e.kind&&e.descriptor&&!("value"in e.descriptor)?_objectSpread2(_objectSpread2({},e),{},{finisher(r){r.createProperty(e.key,t)}}):{kind:"field",key:Symbol(),placement:"own",descriptor:{},originalKey:e.key,initializer(){"function"==typeof e.initializer&&(this[e.key]=e.initializer.call(this))},finisher(r){r.createProperty(e.key,t)}};function e$5(t){return(e,r)=>void 0!==r?((t,e,r)=>{e.constructor.createProperty(r,t)})(t,e,r):i$1(t,e)}function t$1(t){return e$5(_objectSpread2(_objectSpread2({},t),{},{state:!0}))}var e$4=(t,e,r)=>{Object.defineProperty(e,r,t)},t=(t,e)=>({kind:"method",placement:"prototype",key:e.key,descriptor:t}),o$1=t=>{var{finisher:e,descriptor:r}=t;return(t,i)=>{var n;if(void 0===i){var a=null!==(n=t.originalKey)&&void 0!==n?n:t.key,s=null!=r?{kind:"method",placement:"prototype",key:a,descriptor:r(t.key)}:_objectSpread2(_objectSpread2({},t),{},{key:a});return null!=e&&(s.finisher=function(t){e(t,a)}),s}var o=t.constructor;void 0!==r&&Object.defineProperty(t,i,r(i)),null==e||e(o,i)}},n;function e$3(t){return o$1({finisher:(e,r)=>{Object.assign(e.prototype[r],t)}})}function i(t,e){return o$1({descriptor:r=>{var i={get(){var e,r;return null!==(r=null===(e=this.renderRoot)||void 0===e?void 0:e.querySelector(t))&&void 0!==r?r:null},enumerable:!0,configurable:!0};if(e){var n="symbol"==typeof r?Symbol():"__"+r;i.get=function(){var e,r;return void 0===this[n]&&(this[n]=null!==(r=null===(e=this.renderRoot)||void 0===e?void 0:e.querySelector(t))&&void 0!==r?r:null),this[n]}}return i}})}function e$2(t){return o$1({descriptor:e=>({get(){var e,r;return null!==(r=null===(e=this.renderRoot)||void 0===e?void 0:e.querySelectorAll(t))&&void 0!==r?r:[]},enumerable:!0,configurable:!0})})}function e$1(t){return o$1({descriptor:e=>({get(){var e=this;return _asyncToGenerator((function*(){var r;return yield e.updateComplete,null===(r=e.renderRoot)||void 0===r?void 0:r.querySelector(t)}))()},enumerable:!0,configurable:!0})})}var e=null!=(null===(n=window.HTMLSlotElement)||void 0===n?void 0:n.prototype.assignedElements)?(t,e)=>t.assignedElements(e):(t,e)=>t.assignedNodes(e).filter((t=>t.nodeType===Node.ELEMENT_NODE));function l(t){var{slot:r,selector:i}=null!=t?t:{};return o$1({descriptor:n=>({get(){var n,a="slot"+(r?"[name=".concat(r,"]"):":not([name])"),s=null===(n=this.renderRoot)||void 0===n?void 0:n.querySelector(a),o=null!=s?e(s,t):[];return i?o.filter((t=>t.matches(i))):o},enumerable:!0,configurable:!0})})}function o(t,e,r){var i,n=t;return"object"==typeof t?(n=t.slot,i=t):i={flatten:e},r?l({slot:n,flatten:e,selector:r}):o$1({descriptor:t=>({get(){var t,e,r="slot"+(n?"[name=".concat(n,"]"):":not([name])"),a=null===(t=this.renderRoot)||void 0===t?void 0:t.querySelector(r);return null!==(e=null==a?void 0:a.assignedNodes(i))&&void 0!==e?e:[]},enumerable:!0,configurable:!0})})}var commonjsGlobal="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:void 0!==__webpack_require__.g?__webpack_require__.g:"undefined"!=typeof self?self:{};function getDefaultExportFromCjs(t){return t&&t.__esModule&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function getDefaultExportFromNamespaceIfPresent(t){return t&&Object.prototype.hasOwnProperty.call(t,"default")?t.default:t}function getDefaultExportFromNamespaceIfNotNamed(t){return t&&Object.prototype.hasOwnProperty.call(t,"default")&&1===Object.keys(t).length?t.default:t}function getAugmentedNamespace(t){if(t.__esModule)return t;var e=Object.defineProperty({},"__esModule",{value:!0});return Object.keys(t).forEach((function(r){var i=Object.getOwnPropertyDescriptor(t,r);Object.defineProperty(e,r,i.get?i:{enumerable:!0,get:function(){return t[r]}})})),e}function commonjsRequire(t){throw new Error('Could not dynamically require "'+t+'". Please configure the dynamicRequireTargets or/and ignoreDynamicRequires option of @rollup/plugin-commonjs appropriately for this require call to work.')}var lottie$1={exports:{}};(function(module,exports){var factory;"undefined"!=typeof navigator&&(factory=function(){var svgNS="http://www.w3.org/2000/svg",locationHref="",_useWebWorker=!1,initialDefaultFrame=-999999,setWebWorker=function(t){_useWebWorker=!!t},getWebWorker=function(){return _useWebWorker},setLocationHref=function(t){locationHref=t},getLocationHref=function(){return locationHref};function createTag(t){return document.createElement(t)}function extendPrototype(t,e){var r,i,n=t.length;for(r=0;r<n;r+=1)for(var a in i=t[r].prototype)Object.prototype.hasOwnProperty.call(i,a)&&(e.prototype[a]=i[a])}function getDescriptor(t,e){return Object.getOwnPropertyDescriptor(t,e)}function createProxyFunction(t){function e(){}return e.prototype=t,e}var audioControllerFactory=function(){function t(t){this.audios=[],this.audioFactory=t,this._volume=1,this._isMuted=!1}return t.prototype={addAudio:function(t){this.audios.push(t)},pause:function(){var t,e=this.audios.length;for(t=0;t<e;t+=1)this.audios[t].pause()},resume:function(){var t,e=this.audios.length;for(t=0;t<e;t+=1)this.audios[t].resume()},setRate:function(t){var e,r=this.audios.length;for(e=0;e<r;e+=1)this.audios[e].setRate(t)},createAudio:function(t){return this.audioFactory?this.audioFactory(t):window.Howl?new window.Howl({src:[t]}):{isPlaying:!1,play:function(){this.isPlaying=!0},seek:function(){this.isPlaying=!1},playing:function(){},rate:function(){},setVolume:function(){}}},setAudioFactory:function(t){this.audioFactory=t},setVolume:function(t){this._volume=t,this._updateVolume()},mute:function(){this._isMuted=!0,this._updateVolume()},unmute:function(){this._isMuted=!1,this._updateVolume()},getVolume:function(){return this._volume},_updateVolume:function(){var t,e=this.audios.length;for(t=0;t<e;t+=1)this.audios[t].volume(this._volume*(this._isMuted?0:1))}},function(){return new t}}(),createTypedArray=function(){function t(t,e){var r,i=0,n=[];switch(t){case"int16":case"uint8c":r=1;break;default:r=1.1}for(i=0;i<e;i+=1)n.push(r);return n}return"function"==typeof Uint8ClampedArray&&"function"==typeof Float32Array?function(e,r){return"float32"===e?new Float32Array(r):"int16"===e?new Int16Array(r):"uint8c"===e?new Uint8ClampedArray(r):t(e,r)}:t}();function createSizedArray(t){return Array.apply(null,{length:t})}function _typeof$6(t){return _typeof$6="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_typeof$6(t)}var subframeEnabled=!0,expressionsPlugin=null,expressionsInterfaces=null,idPrefix$1="",isSafari=/^((?!chrome|android).)*safari/i.test(navigator.userAgent),_shouldRoundValues=!1,bmPow=Math.pow,bmSqrt=Math.sqrt,bmFloor=Math.floor,bmMax=Math.max,bmMin=Math.min,BMMath={};function ProjectInterface$1(){return{}}!function(){var t,e=["abs","acos","acosh","asin","asinh","atan","atanh","atan2","ceil","cbrt","expm1","clz32","cos","cosh","exp","floor","fround","hypot","imul","log","log1p","log2","log10","max","min","pow","random","round","sign","sin","sinh","sqrt","tan","tanh","trunc","E","LN10","LN2","LOG10E","LOG2E","PI","SQRT1_2","SQRT2"],r=e.length;for(t=0;t<r;t+=1)BMMath[e[t]]=Math[e[t]]}(),BMMath.random=Math.random,BMMath.abs=function(t){if("object"===_typeof$6(t)&&t.length){var e,r=createSizedArray(t.length),i=t.length;for(e=0;e<i;e+=1)r[e]=Math.abs(t[e]);return r}return Math.abs(t)};var defaultCurveSegments=150,degToRads=Math.PI/180,roundCorner=.5519;function roundValues(t){_shouldRoundValues=!!t}function bmRnd(t){return _shouldRoundValues?Math.round(t):t}function styleDiv(t){t.style.position="absolute",t.style.top=0,t.style.left=0,t.style.display="block",t.style.transformOrigin="0 0",t.style.webkitTransformOrigin="0 0",t.style.backfaceVisibility="visible",t.style.webkitBackfaceVisibility="visible",t.style.transformStyle="preserve-3d",t.style.webkitTransformStyle="preserve-3d",t.style.mozTransformStyle="preserve-3d"}function BMEnterFrameEvent(t,e,r,i){this.type=t,this.currentTime=e,this.totalTime=r,this.direction=i<0?-1:1}function BMCompleteEvent(t,e){this.type=t,this.direction=e<0?-1:1}function BMCompleteLoopEvent(t,e,r,i){this.type=t,this.currentLoop=r,this.totalLoops=e,this.direction=i<0?-1:1}function BMSegmentStartEvent(t,e,r){this.type=t,this.firstFrame=e,this.totalFrames=r}function BMDestroyEvent(t,e){this.type=t,this.target=e}function BMRenderFrameErrorEvent(t,e){this.type="renderFrameError",this.nativeError=t,this.currentTime=e}function BMConfigErrorEvent(t){this.type="configError",this.nativeError=t}function BMAnimationConfigErrorEvent(t,e){this.type=t,this.nativeError=e}var createElementID=(_count=0,function(){return idPrefix$1+"__lottie_element_"+(_count+=1)}),_count;function HSVtoRGB(t,e,r){var i,n,a,s,o,l,h,p;switch(l=r*(1-e),h=r*(1-(o=6*t-(s=Math.floor(6*t)))*e),p=r*(1-(1-o)*e),s%6){case 0:i=r,n=p,a=l;break;case 1:i=h,n=r,a=l;break;case 2:i=l,n=r,a=p;break;case 3:i=l,n=h,a=r;break;case 4:i=p,n=l,a=r;break;case 5:i=r,n=l,a=h}return[i,n,a]}function RGBtoHSV(t,e,r){var i,n=Math.max(t,e,r),a=Math.min(t,e,r),s=n-a,o=0===n?0:s/n,l=n/255;switch(n){case a:i=0;break;case t:i=e-r+s*(e<r?6:0),i/=6*s;break;case e:i=r-t+2*s,i/=6*s;break;case r:i=t-e+4*s,i/=6*s}return[i,o,l]}function addSaturationToRGB(t,e){var r=RGBtoHSV(255*t[0],255*t[1],255*t[2]);return r[1]+=e,r[1]>1?r[1]=1:r[1]<=0&&(r[1]=0),HSVtoRGB(r[0],r[1],r[2])}function addBrightnessToRGB(t,e){var r=RGBtoHSV(255*t[0],255*t[1],255*t[2]);return r[2]+=e,r[2]>1?r[2]=1:r[2]<0&&(r[2]=0),HSVtoRGB(r[0],r[1],r[2])}function addHueToRGB(t,e){var r=RGBtoHSV(255*t[0],255*t[1],255*t[2]);return r[0]+=e/360,r[0]>1?r[0]-=1:r[0]<0&&(r[0]+=1),HSVtoRGB(r[0],r[1],r[2])}var rgbToHex=function(){var t,e,r=[];for(t=0;t<256;t+=1)e=t.toString(16),r[t]=1===e.length?"0"+e:e;return function(t,e,i){return t<0&&(t=0),e<0&&(e=0),i<0&&(i=0),"#"+r[t]+r[e]+r[i]}}(),setSubframeEnabled=function(t){subframeEnabled=!!t},getSubframeEnabled=function(){return subframeEnabled},setExpressionsPlugin=function(t){expressionsPlugin=t},getExpressionsPlugin=function(){return expressionsPlugin},setExpressionInterfaces=function(t){expressionsInterfaces=t},getExpressionInterfaces=function(){return expressionsInterfaces},setDefaultCurveSegments=function(t){defaultCurveSegments=t},getDefaultCurveSegments=function(){return defaultCurveSegments},setIdPrefix=function(t){idPrefix$1=t},getIdPrefix=function(){return idPrefix$1};function createNS(t){return document.createElementNS(svgNS,t)}function _typeof$5(t){return _typeof$5="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_typeof$5(t)}var dataManager=function(){var t,e,r=1,i=[],n={onmessage:function(){},postMessage:function(e){t({data:e})}},a={postMessage:function(t){n.onmessage({data:t})}};function s(){e||(e=function(e){if(window.Worker&&window.Blob&&getWebWorker()){var r=new Blob(["var _workerSelf = self; self.onmessage = ",e.toString()],{type:"text/javascript"}),i=URL.createObjectURL(r);return new Worker(i)}return t=e,n}((function(t){if(a.dataManager||(a.dataManager=function(){function t(n,a){var s,o,l,h,p,f,d=n.length;for(o=0;o<d;o+=1)if("ks"in(s=n[o])&&!s.completed){if(s.completed=!0,s.hasMask){var u=s.masksProperties;for(h=u.length,l=0;l<h;l+=1)if(u[l].pt.k.i)i(u[l].pt.k);else for(f=u[l].pt.k.length,p=0;p<f;p+=1)u[l].pt.k[p].s&&i(u[l].pt.k[p].s[0]),u[l].pt.k[p].e&&i(u[l].pt.k[p].e[0])}0===s.ty?(s.layers=e(s.refId,a),t(s.layers,a)):4===s.ty?r(s.shapes):5===s.ty&&c(s)}}function e(t,e){var r=function(t,e){for(var r=0,i=e.length;r<i;){if(e[r].id===t)return e[r];r+=1}return null}(t,e);return r?r.layers.__used?JSON.parse(JSON.stringify(r.layers)):(r.layers.__used=!0,r.layers):null}function r(t){var e,n,a;for(e=t.length-1;e>=0;e-=1)if("sh"===t[e].ty)if(t[e].ks.k.i)i(t[e].ks.k);else for(a=t[e].ks.k.length,n=0;n<a;n+=1)t[e].ks.k[n].s&&i(t[e].ks.k[n].s[0]),t[e].ks.k[n].e&&i(t[e].ks.k[n].e[0]);else"gr"===t[e].ty&&r(t[e].it)}function i(t){var e,r=t.i.length;for(e=0;e<r;e+=1)t.i[e][0]+=t.v[e][0],t.i[e][1]+=t.v[e][1],t.o[e][0]+=t.v[e][0],t.o[e][1]+=t.v[e][1]}function n(t,e){var r=e?e.split("."):[100,100,100];return t[0]>r[0]||!(r[0]>t[0])&&(t[1]>r[1]||!(r[1]>t[1])&&(t[2]>r[2]||!(r[2]>t[2])&&null))}var a,s=function(){var t=[4,4,14];function e(t){var e,r,i,n=t.length;for(e=0;e<n;e+=1)5===t[e].ty&&(i=(r=t[e]).t.d,r.t.d={k:[{s:i,t:0}]})}return function(r){if(n(t,r.v)&&(e(r.layers),r.assets)){var i,a=r.assets.length;for(i=0;i<a;i+=1)r.assets[i].layers&&e(r.assets[i].layers)}}}(),o=(a=[4,7,99],function(t){if(t.chars&&!n(a,t.v)){var e,i=t.chars.length;for(e=0;e<i;e+=1){var s=t.chars[e];s.data&&s.data.shapes&&(r(s.data.shapes),s.data.ip=0,s.data.op=99999,s.data.st=0,s.data.sr=1,s.data.ks={p:{k:[0,0],a:0},s:{k:[100,100],a:0},a:{k:[0,0],a:0},r:{k:0,a:0},o:{k:100,a:0}},t.chars[e].t||(s.data.shapes.push({ty:"no"}),s.data.shapes[0].it.push({p:{k:[0,0],a:0},s:{k:[100,100],a:0},a:{k:[0,0],a:0},r:{k:0,a:0},o:{k:100,a:0},sk:{k:0,a:0},sa:{k:0,a:0},ty:"tr"})))}}}),l=function(){var t=[5,7,15];function e(t){var e,r,i=t.length;for(e=0;e<i;e+=1)5===t[e].ty&&(r=void 0,"number"==typeof(r=t[e].t.p).a&&(r.a={a:0,k:r.a}),"number"==typeof r.p&&(r.p={a:0,k:r.p}),"number"==typeof r.r&&(r.r={a:0,k:r.r}))}return function(r){if(n(t,r.v)&&(e(r.layers),r.assets)){var i,a=r.assets.length;for(i=0;i<a;i+=1)r.assets[i].layers&&e(r.assets[i].layers)}}}(),h=function(){var t=[4,1,9];function e(t){var r,i,n,a=t.length;for(r=0;r<a;r+=1)if("gr"===t[r].ty)e(t[r].it);else if("fl"===t[r].ty||"st"===t[r].ty)if(t[r].c.k&&t[r].c.k[0].i)for(n=t[r].c.k.length,i=0;i<n;i+=1)t[r].c.k[i].s&&(t[r].c.k[i].s[0]/=255,t[r].c.k[i].s[1]/=255,t[r].c.k[i].s[2]/=255,t[r].c.k[i].s[3]/=255),t[r].c.k[i].e&&(t[r].c.k[i].e[0]/=255,t[r].c.k[i].e[1]/=255,t[r].c.k[i].e[2]/=255,t[r].c.k[i].e[3]/=255);else t[r].c.k[0]/=255,t[r].c.k[1]/=255,t[r].c.k[2]/=255,t[r].c.k[3]/=255}function r(t){var r,i=t.length;for(r=0;r<i;r+=1)4===t[r].ty&&e(t[r].shapes)}return function(e){if(n(t,e.v)&&(r(e.layers),e.assets)){var i,a=e.assets.length;for(i=0;i<a;i+=1)e.assets[i].layers&&r(e.assets[i].layers)}}}(),p=function(){var t=[4,4,18];function e(t){var r,i,n;for(r=t.length-1;r>=0;r-=1)if("sh"===t[r].ty)if(t[r].ks.k.i)t[r].ks.k.c=t[r].closed;else for(n=t[r].ks.k.length,i=0;i<n;i+=1)t[r].ks.k[i].s&&(t[r].ks.k[i].s[0].c=t[r].closed),t[r].ks.k[i].e&&(t[r].ks.k[i].e[0].c=t[r].closed);else"gr"===t[r].ty&&e(t[r].it)}function r(t){var r,i,n,a,s,o,l=t.length;for(i=0;i<l;i+=1){if((r=t[i]).hasMask){var h=r.masksProperties;for(a=h.length,n=0;n<a;n+=1)if(h[n].pt.k.i)h[n].pt.k.c=h[n].cl;else for(o=h[n].pt.k.length,s=0;s<o;s+=1)h[n].pt.k[s].s&&(h[n].pt.k[s].s[0].c=h[n].cl),h[n].pt.k[s].e&&(h[n].pt.k[s].e[0].c=h[n].cl)}4===r.ty&&e(r.shapes)}}return function(e){if(n(t,e.v)&&(r(e.layers),e.assets)){var i,a=e.assets.length;for(i=0;i<a;i+=1)e.assets[i].layers&&r(e.assets[i].layers)}}}();function c(t){0===t.t.a.length&&t.t.p}var f={completeData:function(r){r.__complete||(h(r),s(r),o(r),l(r),p(r),t(r.layers,r.assets),function(r,i){if(r){var n=0,a=r.length;for(n=0;n<a;n+=1)1===r[n].t&&(r[n].data.layers=e(r[n].data.refId,i),t(r[n].data.layers,i))}}(r.chars,r.assets),r.__complete=!0)}};return f.checkColors=h,f.checkChars=o,f.checkPathProperties=l,f.checkShapes=p,f.completeLayers=t,f}()),a.assetLoader||(a.assetLoader=function(){function t(t){var e=t.getResponseHeader("content-type");return e&&"json"===t.responseType&&-1!==e.indexOf("json")||t.response&&"object"===_typeof$5(t.response)?t.response:t.response&&"string"==typeof t.response?JSON.parse(t.response):t.responseText?JSON.parse(t.responseText):null}return{load:function(e,r,i,n){var a,s=new XMLHttpRequest;try{s.responseType="json"}catch(t){}s.onreadystatechange=function(){if(4===s.readyState)if(200===s.status)a=t(s),i(a);else try{a=t(s),i(a)}catch(t){n&&n(t)}};try{s.open(["G","E","T"].join(""),e,!0)}catch(t){s.open(["G","E","T"].join(""),r+"/"+e,!0)}s.send()}}}()),"loadAnimation"===t.data.type)a.assetLoader.load(t.data.path,t.data.fullPath,(function(e){a.dataManager.completeData(e),a.postMessage({id:t.data.id,payload:e,status:"success"})}),(function(){a.postMessage({id:t.data.id,status:"error"})}));else if("complete"===t.data.type){var e=t.data.animation;a.dataManager.completeData(e),a.postMessage({id:t.data.id,payload:e,status:"success"})}else"loadData"===t.data.type&&a.assetLoader.load(t.data.path,t.data.fullPath,(function(e){a.postMessage({id:t.data.id,payload:e,status:"success"})}),(function(){a.postMessage({id:t.data.id,status:"error"})}))})),e.onmessage=function(t){var e=t.data,r=e.id,n=i[r];i[r]=null,"success"===e.status?n.onComplete(e.payload):n.onError&&n.onError()})}function o(t,e){var n="processId_"+(r+=1);return i[n]={onComplete:t,onError:e},n}return{loadAnimation:function(t,r,i){s();var n=o(r,i);e.postMessage({type:"loadAnimation",path:t,fullPath:window.location.origin+window.location.pathname,id:n})},loadData:function(t,r,i){s();var n=o(r,i);e.postMessage({type:"loadData",path:t,fullPath:window.location.origin+window.location.pathname,id:n})},completeAnimation:function(t,r,i){s();var n=o(r,i);e.postMessage({type:"complete",animation:t,id:n})}}}(),ImagePreloader=function(){var t=function(){var t=createTag("canvas");t.width=1,t.height=1;var e=t.getContext("2d");return e.fillStyle="rgba(0,0,0,0)",e.fillRect(0,0,1,1),t}();function e(){this.loadedAssets+=1,this.loadedAssets===this.totalImages&&this.loadedFootagesCount===this.totalFootages&&this.imagesLoadedCb&&this.imagesLoadedCb(null)}function r(){this.loadedFootagesCount+=1,this.loadedAssets===this.totalImages&&this.loadedFootagesCount===this.totalFootages&&this.imagesLoadedCb&&this.imagesLoadedCb(null)}function i(t,e,r){var i="";if(t.e)i=t.p;else if(e){var n=t.p;-1!==n.indexOf("images/")&&(n=n.split("/")[1]),i=e+n}else i=r,i+=t.u?t.u:"",i+=t.p;return i}function n(t){var e=0,r=setInterval(function(){(t.getBBox().width||e>500)&&(this._imageLoaded(),clearInterval(r)),e+=1}.bind(this),50)}function a(t){var e={assetData:t},r=i(t,this.assetsPath,this.path);return dataManager.loadData(r,function(t){e.img=t,this._footageLoaded()}.bind(this),function(){e.img={},this._footageLoaded()}.bind(this)),e}function s(){this._imageLoaded=e.bind(this),this._footageLoaded=r.bind(this),this.testImageLoaded=n.bind(this),this.createFootageData=a.bind(this),this.assetsPath="",this.path="",this.totalImages=0,this.totalFootages=0,this.loadedAssets=0,this.loadedFootagesCount=0,this.imagesLoadedCb=null,this.images=[]}return s.prototype={loadAssets:function(t,e){var r;this.imagesLoadedCb=e;var i=t.length;for(r=0;r<i;r+=1)t[r].layers||(t[r].t&&"seq"!==t[r].t?3===t[r].t&&(this.totalFootages+=1,this.images.push(this.createFootageData(t[r]))):(this.totalImages+=1,this.images.push(this._createImageData(t[r]))))},setAssetsPath:function(t){this.assetsPath=t||""},setPath:function(t){this.path=t||""},loadedImages:function(){return this.totalImages===this.loadedAssets},loadedFootages:function(){return this.totalFootages===this.loadedFootagesCount},destroy:function(){this.imagesLoadedCb=null,this.images.length=0},getAsset:function(t){for(var e=0,r=this.images.length;e<r;){if(this.images[e].assetData===t)return this.images[e].img;e+=1}return null},createImgData:function(e){var r=i(e,this.assetsPath,this.path),n=createTag("img");n.crossOrigin="anonymous",n.addEventListener("load",this._imageLoaded,!1),n.addEventListener("error",function(){a.img=t,this._imageLoaded()}.bind(this),!1),n.src=r;var a={img:n,assetData:e};return a},createImageData:function(e){var r=i(e,this.assetsPath,this.path),n=createNS("image");isSafari?this.testImageLoaded(n):n.addEventListener("load",this._imageLoaded,!1),n.addEventListener("error",function(){a.img=t,this._imageLoaded()}.bind(this),!1),n.setAttributeNS("http://www.w3.org/1999/xlink","href",r),this._elementHelper.append?this._elementHelper.append(n):this._elementHelper.appendChild(n);var a={img:n,assetData:e};return a},imageLoaded:e,footageLoaded:r,setCacheType:function(t,e){"svg"===t?(this._elementHelper=e,this._createImageData=this.createImageData.bind(this)):this._createImageData=this.createImgData.bind(this)}},s}();function BaseEvent(){}BaseEvent.prototype={triggerEvent:function(t,e){if(this._cbs[t])for(var r=this._cbs[t],i=0;i<r.length;i+=1)r[i](e)},addEventListener:function(t,e){return this._cbs[t]||(this._cbs[t]=[]),this._cbs[t].push(e),function(){this.removeEventListener(t,e)}.bind(this)},removeEventListener:function(t,e){if(e){if(this._cbs[t]){for(var r=0,i=this._cbs[t].length;r<i;)this._cbs[t][r]===e&&(this._cbs[t].splice(r,1),r-=1,i-=1),r+=1;this._cbs[t].length||(this._cbs[t]=null)}}else this._cbs[t]=null}};var markerParser=function(){function t(t){for(var e,r=t.split("\r\n"),i={},n=0,a=0;a<r.length;a+=1)2===(e=r[a].split(":")).length&&(i[e[0]]=e[1].trim(),n+=1);if(0===n)throw new Error;return i}return function(e){for(var r=[],i=0;i<e.length;i+=1){var n=e[i],a={time:n.tm,duration:n.dr};try{a.payload=JSON.parse(e[i].cm)}catch(r){try{a.payload=t(e[i].cm)}catch(t){a.payload={name:e[i].cm}}}r.push(a)}return r}}(),ProjectInterface=function(){function t(t){this.compositions.push(t)}return function(){function e(t){for(var e=0,r=this.compositions.length;e<r;){if(this.compositions[e].data&&this.compositions[e].data.nm===t)return this.compositions[e].prepareFrame&&this.compositions[e].data.xt&&this.compositions[e].prepareFrame(this.currentFrame),this.compositions[e].compInterface;e+=1}return null}return e.compositions=[],e.currentFrame=0,e.registerComposition=t,e}}(),renderers={},registerRenderer=function(t,e){renderers[t]=e};function getRenderer(t){return renderers[t]}function _typeof$4(t){return _typeof$4="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_typeof$4(t)}var AnimationItem=function(){this._cbs=[],this.name="",this.path="",this.isLoaded=!1,this.currentFrame=0,this.currentRawFrame=0,this.firstFrame=0,this.totalFrames=0,this.frameRate=0,this.frameMult=0,this.playSpeed=1,this.playDirection=1,this.playCount=0,this.animationData={},this.assets=[],this.isPaused=!0,this.autoplay=!1,this.loop=!0,this.renderer=null,this.animationID=createElementID(),this.assetsPath="",this.timeCompleted=0,this.segmentPos=0,this.isSubframeEnabled=getSubframeEnabled(),this.segments=[],this._idle=!0,this._completedLoop=!1,this.projectInterface=ProjectInterface(),this.imagePreloader=new ImagePreloader,this.audioController=audioControllerFactory(),this.markers=[],this.configAnimation=this.configAnimation.bind(this),this.onSetupError=this.onSetupError.bind(this),this.onSegmentComplete=this.onSegmentComplete.bind(this),this.drawnFrameEvent=new BMEnterFrameEvent("drawnFrame",0,0,0)};extendPrototype([BaseEvent],AnimationItem),AnimationItem.prototype.setParams=function(t){(t.wrapper||t.container)&&(this.wrapper=t.wrapper||t.container);var e="svg";t.animType?e=t.animType:t.renderer&&(e=t.renderer);var r=getRenderer(e);this.renderer=new r(this,t.rendererSettings),this.imagePreloader.setCacheType(e,this.renderer.globalData.defs),this.renderer.setProjectInterface(this.projectInterface),this.animType=e,""===t.loop||null===t.loop||void 0===t.loop||!0===t.loop?this.loop=!0:!1===t.loop?this.loop=!1:this.loop=parseInt(t.loop,10),this.autoplay=!("autoplay"in t)||t.autoplay,this.name=t.name?t.name:"",this.autoloadSegments=!Object.prototype.hasOwnProperty.call(t,"autoloadSegments")||t.autoloadSegments,this.assetsPath=t.assetsPath,this.initialSegment=t.initialSegment,t.audioFactory&&this.audioController.setAudioFactory(t.audioFactory),t.animationData?this.setupAnimation(t.animationData):t.path&&(-1!==t.path.lastIndexOf("\\")?this.path=t.path.substr(0,t.path.lastIndexOf("\\")+1):this.path=t.path.substr(0,t.path.lastIndexOf("/")+1),this.fileName=t.path.substr(t.path.lastIndexOf("/")+1),this.fileName=this.fileName.substr(0,this.fileName.lastIndexOf(".json")),dataManager.loadAnimation(t.path,this.configAnimation,this.onSetupError))},AnimationItem.prototype.onSetupError=function(){this.trigger("data_failed")},AnimationItem.prototype.setupAnimation=function(t){dataManager.completeAnimation(t,this.configAnimation)},AnimationItem.prototype.setData=function(t,e){e&&"object"!==_typeof$4(e)&&(e=JSON.parse(e));var r={wrapper:t,animationData:e},i=t.attributes;r.path=i.getNamedItem("data-animation-path")?i.getNamedItem("data-animation-path").value:i.getNamedItem("data-bm-path")?i.getNamedItem("data-bm-path").value:i.getNamedItem("bm-path")?i.getNamedItem("bm-path").value:"",r.animType=i.getNamedItem("data-anim-type")?i.getNamedItem("data-anim-type").value:i.getNamedItem("data-bm-type")?i.getNamedItem("data-bm-type").value:i.getNamedItem("bm-type")?i.getNamedItem("bm-type").value:i.getNamedItem("data-bm-renderer")?i.getNamedItem("data-bm-renderer").value:i.getNamedItem("bm-renderer")?i.getNamedItem("bm-renderer").value:"canvas";var n=i.getNamedItem("data-anim-loop")?i.getNamedItem("data-anim-loop").value:i.getNamedItem("data-bm-loop")?i.getNamedItem("data-bm-loop").value:i.getNamedItem("bm-loop")?i.getNamedItem("bm-loop").value:"";"false"===n?r.loop=!1:"true"===n?r.loop=!0:""!==n&&(r.loop=parseInt(n,10));var a=i.getNamedItem("data-anim-autoplay")?i.getNamedItem("data-anim-autoplay").value:i.getNamedItem("data-bm-autoplay")?i.getNamedItem("data-bm-autoplay").value:!i.getNamedItem("bm-autoplay")||i.getNamedItem("bm-autoplay").value;r.autoplay="false"!==a,r.name=i.getNamedItem("data-name")?i.getNamedItem("data-name").value:i.getNamedItem("data-bm-name")?i.getNamedItem("data-bm-name").value:i.getNamedItem("bm-name")?i.getNamedItem("bm-name").value:"","false"===(i.getNamedItem("data-anim-prerender")?i.getNamedItem("data-anim-prerender").value:i.getNamedItem("data-bm-prerender")?i.getNamedItem("data-bm-prerender").value:i.getNamedItem("bm-prerender")?i.getNamedItem("bm-prerender").value:"")&&(r.prerender=!1),this.setParams(r)},AnimationItem.prototype.includeLayers=function(t){t.op>this.animationData.op&&(this.animationData.op=t.op,this.totalFrames=Math.floor(t.op-this.animationData.ip));var e,r,i=this.animationData.layers,n=i.length,a=t.layers,s=a.length;for(r=0;r<s;r+=1)for(e=0;e<n;){if(i[e].id===a[r].id){i[e]=a[r];break}e+=1}if((t.chars||t.fonts)&&(this.renderer.globalData.fontManager.addChars(t.chars),this.renderer.globalData.fontManager.addFonts(t.fonts,this.renderer.globalData.defs)),t.assets)for(n=t.assets.length,e=0;e<n;e+=1)this.animationData.assets.push(t.assets[e]);this.animationData.__complete=!1,dataManager.completeAnimation(this.animationData,this.onSegmentComplete)},AnimationItem.prototype.onSegmentComplete=function(t){this.animationData=t;var e=getExpressionsPlugin();e&&e.initExpressions(this),this.loadNextSegment()},AnimationItem.prototype.loadNextSegment=function(){var t=this.animationData.segments;if(!t||0===t.length||!this.autoloadSegments)return this.trigger("data_ready"),void(this.timeCompleted=this.totalFrames);var e=t.shift();this.timeCompleted=e.time*this.frameRate;var r=this.path+this.fileName+"_"+this.segmentPos+".json";this.segmentPos+=1,dataManager.loadData(r,this.includeLayers.bind(this),function(){this.trigger("data_failed")}.bind(this))},AnimationItem.prototype.loadSegments=function(){this.animationData.segments||(this.timeCompleted=this.totalFrames),this.loadNextSegment()},AnimationItem.prototype.imagesLoaded=function(){this.trigger("loaded_images"),this.checkLoaded()},AnimationItem.prototype.preloadImages=function(){this.imagePreloader.setAssetsPath(this.assetsPath),this.imagePreloader.setPath(this.path),this.imagePreloader.loadAssets(this.animationData.assets,this.imagesLoaded.bind(this))},AnimationItem.prototype.configAnimation=function(t){if(this.renderer)try{this.animationData=t,this.initialSegment?(this.totalFrames=Math.floor(this.initialSegment[1]-this.initialSegment[0]),this.firstFrame=Math.round(this.initialSegment[0])):(this.totalFrames=Math.floor(this.animationData.op-this.animationData.ip),this.firstFrame=Math.round(this.animationData.ip)),this.renderer.configAnimation(t),t.assets||(t.assets=[]),this.assets=this.animationData.assets,this.frameRate=this.animationData.fr,this.frameMult=this.animationData.fr/1e3,this.renderer.searchExtraCompositions(t.assets),this.markers=markerParser(t.markers||[]),this.trigger("config_ready"),this.preloadImages(),this.loadSegments(),this.updaFrameModifier(),this.waitForFontsLoaded(),this.isPaused&&this.audioController.pause()}catch(t){this.triggerConfigError(t)}},AnimationItem.prototype.waitForFontsLoaded=function(){this.renderer&&(this.renderer.globalData.fontManager.isLoaded?this.checkLoaded():setTimeout(this.waitForFontsLoaded.bind(this),20))},AnimationItem.prototype.checkLoaded=function(){if(!this.isLoaded&&this.renderer.globalData.fontManager.isLoaded&&(this.imagePreloader.loadedImages()||"canvas"!==this.renderer.rendererType)&&this.imagePreloader.loadedFootages()){this.isLoaded=!0;var t=getExpressionsPlugin();t&&t.initExpressions(this),this.renderer.initItems(),setTimeout(function(){this.trigger("DOMLoaded")}.bind(this),0),this.gotoFrame(),this.autoplay&&this.play()}},AnimationItem.prototype.resize=function(t,e){var r="number"==typeof t?t:void 0,i="number"==typeof e?e:void 0;this.renderer.updateContainerSize(r,i)},AnimationItem.prototype.setSubframe=function(t){this.isSubframeEnabled=!!t},AnimationItem.prototype.gotoFrame=function(){this.currentFrame=this.isSubframeEnabled?this.currentRawFrame:~~this.currentRawFrame,this.timeCompleted!==this.totalFrames&&this.currentFrame>this.timeCompleted&&(this.currentFrame=this.timeCompleted),this.trigger("enterFrame"),this.renderFrame(),this.trigger("drawnFrame")},AnimationItem.prototype.renderFrame=function(){if(!1!==this.isLoaded&&this.renderer)try{this.renderer.renderFrame(this.currentFrame+this.firstFrame)}catch(t){this.triggerRenderFrameError(t)}},AnimationItem.prototype.play=function(t){t&&this.name!==t||!0===this.isPaused&&(this.isPaused=!1,this.trigger("_pause"),this.audioController.resume(),this._idle&&(this._idle=!1,this.trigger("_active")))},AnimationItem.prototype.pause=function(t){t&&this.name!==t||!1===this.isPaused&&(this.isPaused=!0,this.trigger("_play"),this._idle=!0,this.trigger("_idle"),this.audioController.pause())},AnimationItem.prototype.togglePause=function(t){t&&this.name!==t||(!0===this.isPaused?this.play():this.pause())},AnimationItem.prototype.stop=function(t){t&&this.name!==t||(this.pause(),this.playCount=0,this._completedLoop=!1,this.setCurrentRawFrameValue(0))},AnimationItem.prototype.getMarkerData=function(t){for(var e,r=0;r<this.markers.length;r+=1)if((e=this.markers[r]).payload&&e.payload.name===t)return e;return null},AnimationItem.prototype.goToAndStop=function(t,e,r){if(!r||this.name===r){var i=Number(t);if(isNaN(i)){var n=this.getMarkerData(t);n&&this.goToAndStop(n.time,!0)}else e?this.setCurrentRawFrameValue(t):this.setCurrentRawFrameValue(t*this.frameModifier);this.pause()}},AnimationItem.prototype.goToAndPlay=function(t,e,r){if(!r||this.name===r){var i=Number(t);if(isNaN(i)){var n=this.getMarkerData(t);n&&(n.duration?this.playSegments([n.time,n.time+n.duration],!0):this.goToAndStop(n.time,!0))}else this.goToAndStop(i,e,r);this.play()}},AnimationItem.prototype.advanceTime=function(t){if(!0!==this.isPaused&&!1!==this.isLoaded){var e=this.currentRawFrame+t*this.frameModifier,r=!1;e>=this.totalFrames-1&&this.frameModifier>0?this.loop&&this.playCount!==this.loop?e>=this.totalFrames?(this.playCount+=1,this.checkSegments(e%this.totalFrames)||(this.setCurrentRawFrameValue(e%this.totalFrames),this._completedLoop=!0,this.trigger("loopComplete"))):this.setCurrentRawFrameValue(e):this.checkSegments(e>this.totalFrames?e%this.totalFrames:0)||(r=!0,e=this.totalFrames-1):e<0?this.checkSegments(e%this.totalFrames)||(!this.loop||this.playCount--<=0&&!0!==this.loop?(r=!0,e=0):(this.setCurrentRawFrameValue(this.totalFrames+e%this.totalFrames),this._completedLoop?this.trigger("loopComplete"):this._completedLoop=!0)):this.setCurrentRawFrameValue(e),r&&(this.setCurrentRawFrameValue(e),this.pause(),this.trigger("complete"))}},AnimationItem.prototype.adjustSegment=function(t,e){this.playCount=0,t[1]<t[0]?(this.frameModifier>0&&(this.playSpeed<0?this.setSpeed(-this.playSpeed):this.setDirection(-1)),this.totalFrames=t[0]-t[1],this.timeCompleted=this.totalFrames,this.firstFrame=t[1],this.setCurrentRawFrameValue(this.totalFrames-.001-e)):t[1]>t[0]&&(this.frameModifier<0&&(this.playSpeed<0?this.setSpeed(-this.playSpeed):this.setDirection(1)),this.totalFrames=t[1]-t[0],this.timeCompleted=this.totalFrames,this.firstFrame=t[0],this.setCurrentRawFrameValue(.001+e)),this.trigger("segmentStart")},AnimationItem.prototype.setSegment=function(t,e){var r=-1;this.isPaused&&(this.currentRawFrame+this.firstFrame<t?r=t:this.currentRawFrame+this.firstFrame>e&&(r=e-t)),this.firstFrame=t,this.totalFrames=e-t,this.timeCompleted=this.totalFrames,-1!==r&&this.goToAndStop(r,!0)},AnimationItem.prototype.playSegments=function(t,e){if(e&&(this.segments.length=0),"object"===_typeof$4(t[0])){var r,i=t.length;for(r=0;r<i;r+=1)this.segments.push(t[r])}else this.segments.push(t);this.segments.length&&e&&this.adjustSegment(this.segments.shift(),0),this.isPaused&&this.play()},AnimationItem.prototype.resetSegments=function(t){this.segments.length=0,this.segments.push([this.animationData.ip,this.animationData.op]),t&&this.checkSegments(0)},AnimationItem.prototype.checkSegments=function(t){return!!this.segments.length&&(this.adjustSegment(this.segments.shift(),t),!0)},AnimationItem.prototype.destroy=function(t){t&&this.name!==t||!this.renderer||(this.renderer.destroy(),this.imagePreloader.destroy(),this.trigger("destroy"),this._cbs=null,this.onEnterFrame=null,this.onLoopComplete=null,this.onComplete=null,this.onSegmentStart=null,this.onDestroy=null,this.renderer=null,this.renderer=null,this.imagePreloader=null,this.projectInterface=null)},AnimationItem.prototype.setCurrentRawFrameValue=function(t){this.currentRawFrame=t,this.gotoFrame()},AnimationItem.prototype.setSpeed=function(t){this.playSpeed=t,this.updaFrameModifier()},AnimationItem.prototype.setDirection=function(t){this.playDirection=t<0?-1:1,this.updaFrameModifier()},AnimationItem.prototype.setVolume=function(t,e){e&&this.name!==e||this.audioController.setVolume(t)},AnimationItem.prototype.getVolume=function(){return this.audioController.getVolume()},AnimationItem.prototype.mute=function(t){t&&this.name!==t||this.audioController.mute()},AnimationItem.prototype.unmute=function(t){t&&this.name!==t||this.audioController.unmute()},AnimationItem.prototype.updaFrameModifier=function(){this.frameModifier=this.frameMult*this.playSpeed*this.playDirection,this.audioController.setRate(this.playSpeed*this.playDirection)},AnimationItem.prototype.getPath=function(){return this.path},AnimationItem.prototype.getAssetsPath=function(t){var e="";if(t.e)e=t.p;else if(this.assetsPath){var r=t.p;-1!==r.indexOf("images/")&&(r=r.split("/")[1]),e=this.assetsPath+r}else e=this.path,e+=t.u?t.u:"",e+=t.p;return e},AnimationItem.prototype.getAssetData=function(t){for(var e=0,r=this.assets.length;e<r;){if(t===this.assets[e].id)return this.assets[e];e+=1}return null},AnimationItem.prototype.hide=function(){this.renderer.hide()},AnimationItem.prototype.show=function(){this.renderer.show()},AnimationItem.prototype.getDuration=function(t){return t?this.totalFrames:this.totalFrames/this.frameRate},AnimationItem.prototype.updateDocumentData=function(t,e,r){try{this.renderer.getElementByPath(t).updateDocumentData(e,r)}catch(t){}},AnimationItem.prototype.trigger=function(t){if(this._cbs&&this._cbs[t])switch(t){case"enterFrame":this.triggerEvent(t,new BMEnterFrameEvent(t,this.currentFrame,this.totalFrames,this.frameModifier));break;case"drawnFrame":this.drawnFrameEvent.currentTime=this.currentFrame,this.drawnFrameEvent.totalTime=this.totalFrames,this.drawnFrameEvent.direction=this.frameModifier,this.triggerEvent(t,this.drawnFrameEvent);break;case"loopComplete":this.triggerEvent(t,new BMCompleteLoopEvent(t,this.loop,this.playCount,this.frameMult));break;case"complete":this.triggerEvent(t,new BMCompleteEvent(t,this.frameMult));break;case"segmentStart":this.triggerEvent(t,new BMSegmentStartEvent(t,this.firstFrame,this.totalFrames));break;case"destroy":this.triggerEvent(t,new BMDestroyEvent(t,this));break;default:this.triggerEvent(t)}"enterFrame"===t&&this.onEnterFrame&&this.onEnterFrame.call(this,new BMEnterFrameEvent(t,this.currentFrame,this.totalFrames,this.frameMult)),"loopComplete"===t&&this.onLoopComplete&&this.onLoopComplete.call(this,new BMCompleteLoopEvent(t,this.loop,this.playCount,this.frameMult)),"complete"===t&&this.onComplete&&this.onComplete.call(this,new BMCompleteEvent(t,this.frameMult)),"segmentStart"===t&&this.onSegmentStart&&this.onSegmentStart.call(this,new BMSegmentStartEvent(t,this.firstFrame,this.totalFrames)),"destroy"===t&&this.onDestroy&&this.onDestroy.call(this,new BMDestroyEvent(t,this))},AnimationItem.prototype.triggerRenderFrameError=function(t){var e=new BMRenderFrameErrorEvent(t,this.currentFrame);this.triggerEvent("error",e),this.onError&&this.onError.call(this,e)},AnimationItem.prototype.triggerConfigError=function(t){var e=new BMConfigErrorEvent(t,this.currentFrame);this.triggerEvent("error",e),this.onError&&this.onError.call(this,e)};var animationManager=function(){var t={},e=[],r=0,i=0,n=0,a=!0,s=!1;function o(t){for(var r=0,n=t.target;r<i;)e[r].animation===n&&(e.splice(r,1),r-=1,i-=1,n.isPaused||p()),r+=1}function l(t,r){if(!t)return null;for(var n=0;n<i;){if(e[n].elem===t&&null!==e[n].elem)return e[n].animation;n+=1}var a=new AnimationItem;return c(a,t),a.setData(t,r),a}function h(){n+=1,u()}function p(){n-=1}function c(t,r){t.addEventListener("destroy",o),t.addEventListener("_active",h),t.addEventListener("_idle",p),e.push({elem:r,animation:t}),i+=1}function f(t){var o,l=t-r;for(o=0;o<i;o+=1)e[o].animation.advanceTime(l);r=t,n&&!s?window.requestAnimationFrame(f):a=!0}function d(t){r=t,window.requestAnimationFrame(f)}function u(){!s&&n&&a&&(window.requestAnimationFrame(d),a=!1)}return t.registerAnimation=l,t.loadAnimation=function(t){var e=new AnimationItem;return c(e,null),e.setParams(t),e},t.setSpeed=function(t,r){var n;for(n=0;n<i;n+=1)e[n].animation.setSpeed(t,r)},t.setDirection=function(t,r){var n;for(n=0;n<i;n+=1)e[n].animation.setDirection(t,r)},t.play=function(t){var r;for(r=0;r<i;r+=1)e[r].animation.play(t)},t.pause=function(t){var r;for(r=0;r<i;r+=1)e[r].animation.pause(t)},t.stop=function(t){var r;for(r=0;r<i;r+=1)e[r].animation.stop(t)},t.togglePause=function(t){var r;for(r=0;r<i;r+=1)e[r].animation.togglePause(t)},t.searchAnimations=function(t,e,r){var i,n=[].concat([].slice.call(document.getElementsByClassName("lottie")),[].slice.call(document.getElementsByClassName("bodymovin"))),a=n.length;for(i=0;i<a;i+=1)r&&n[i].setAttribute("data-bm-type",r),l(n[i],t);if(e&&0===a){r||(r="svg");var s=document.getElementsByTagName("body")[0];s.innerText="";var o=createTag("div");o.style.width="100%",o.style.height="100%",o.setAttribute("data-bm-type",r),s.appendChild(o),l(o,t)}},t.resize=function(){var t;for(t=0;t<i;t+=1)e[t].animation.resize()},t.goToAndStop=function(t,r,n){var a;for(a=0;a<i;a+=1)e[a].animation.goToAndStop(t,r,n)},t.destroy=function(t){var r;for(r=i-1;r>=0;r-=1)e[r].animation.destroy(t)},t.freeze=function(){s=!0},t.unfreeze=function(){s=!1,u()},t.setVolume=function(t,r){var n;for(n=0;n<i;n+=1)e[n].animation.setVolume(t,r)},t.mute=function(t){var r;for(r=0;r<i;r+=1)e[r].animation.mute(t)},t.unmute=function(t){var r;for(r=0;r<i;r+=1)e[r].animation.unmute(t)},t.getRegisteredAnimations=function(){var t,r=e.length,i=[];for(t=0;t<r;t+=1)i.push(e[t].animation);return i},t}(),BezierFactory=function(){var t={getBezierEasing:function(t,r,i,n,a){var s=a||("bez_"+t+"_"+r+"_"+i+"_"+n).replace(/\./g,"p");if(e[s])return e[s];var o=new h([t,r,i,n]);return e[s]=o,o}},e={},r=.1,i="function"==typeof Float32Array;function n(t,e){return 1-3*e+3*t}function a(t,e){return 3*e-6*t}function s(t){return 3*t}function o(t,e,r){return((n(e,r)*t+a(e,r))*t+s(e))*t}function l(t,e,r){return 3*n(e,r)*t*t+2*a(e,r)*t+s(e)}function h(t){this._p=t,this._mSampleValues=i?new Float32Array(11):new Array(11),this._precomputed=!1,this.get=this.get.bind(this)}return h.prototype={get:function(t){var e=this._p[0],r=this._p[1],i=this._p[2],n=this._p[3];return this._precomputed||this._precompute(),e===r&&i===n?t:0===t?0:1===t?1:o(this._getTForX(t),r,n)},_precompute:function(){var t=this._p[0],e=this._p[1],r=this._p[2],i=this._p[3];this._precomputed=!0,t===e&&r===i||this._calcSampleValues()},_calcSampleValues:function(){for(var t=this._p[0],e=this._p[2],i=0;i<11;++i)this._mSampleValues[i]=o(i*r,t,e)},_getTForX:function(t){for(var e=this._p[0],i=this._p[2],n=this._mSampleValues,a=0,s=1;10!==s&&n[s]<=t;++s)a+=r;var h=a+(t-n[--s])/(n[s+1]-n[s])*r,p=l(h,e,i);return p>=.001?function(t,e,r,i){for(var n=0;n<4;++n){var a=l(e,r,i);if(0===a)return e;e-=(o(e,r,i)-t)/a}return e}(t,h,e,i):0===p?h:function(t,e,r,i,n){var a,s,l=0;do{(a=o(s=e+(r-e)/2,i,n)-t)>0?r=s:e=s}while(Math.abs(a)>1e-7&&++l<10);return s}(t,a,a+r,e,i)}},t}(),pooling={double:function(t){return t.concat(createSizedArray(t.length))}},poolFactory=function(t,e,r){var i=0,n=t,a=createSizedArray(n);return{newElement:function(){return i?a[i-=1]:e()},release:function(t){i===n&&(a=pooling.double(a),n*=2),r&&r(t),a[i]=t,i+=1}}},bezierLengthPool=poolFactory(8,(function(){return{addedLength:0,percents:createTypedArray("float32",getDefaultCurveSegments()),lengths:createTypedArray("float32",getDefaultCurveSegments())}})),segmentsLengthPool=poolFactory(8,(function(){return{lengths:[],totalLength:0}}),(function(t){var e,r=t.lengths.length;for(e=0;e<r;e+=1)bezierLengthPool.release(t.lengths[e]);t.lengths.length=0}));function bezFunction(){var t=Math;function e(t,e,r,i,n,a){var s=t*i+e*n+r*a-n*i-a*t-r*e;return s>-.001&&s<.001}var r=function(t,e,r,i){var n,a,s,o,l,h,p=getDefaultCurveSegments(),c=0,f=[],d=[],u=bezierLengthPool.newElement();for(s=r.length,n=0;n<p;n+=1){for(l=n/(p-1),h=0,a=0;a<s;a+=1)o=bmPow(1-l,3)*t[a]+3*bmPow(1-l,2)*l*r[a]+3*(1-l)*bmPow(l,2)*i[a]+bmPow(l,3)*e[a],f[a]=o,null!==d[a]&&(h+=bmPow(f[a]-d[a],2)),d[a]=f[a];h&&(c+=h=bmSqrt(h)),u.percents[n]=l,u.lengths[n]=c}return u.addedLength=c,u};function i(t){this.segmentLength=0,this.points=new Array(t)}function n(t,e){this.partialLength=t,this.point=e}var a,s=(a={},function(t,r,s,o){var l=(t[0]+"_"+t[1]+"_"+r[0]+"_"+r[1]+"_"+s[0]+"_"+s[1]+"_"+o[0]+"_"+o[1]).replace(/\./g,"p");if(!a[l]){var h,p,c,f,d,u,m,y=getDefaultCurveSegments(),g=0,v=null;2===t.length&&(t[0]!==r[0]||t[1]!==r[1])&&e(t[0],t[1],r[0],r[1],t[0]+s[0],t[1]+s[1])&&e(t[0],t[1],r[0],r[1],r[0]+o[0],r[1]+o[1])&&(y=2);var b=new i(y);for(c=s.length,h=0;h<y;h+=1){for(m=createSizedArray(c),d=h/(y-1),u=0,p=0;p<c;p+=1)f=bmPow(1-d,3)*t[p]+3*bmPow(1-d,2)*d*(t[p]+s[p])+3*(1-d)*bmPow(d,2)*(r[p]+o[p])+bmPow(d,3)*r[p],m[p]=f,null!==v&&(u+=bmPow(m[p]-v[p],2));g+=u=bmSqrt(u),b.points[h]=new n(u,m),v=m}b.segmentLength=g,a[l]=b}return a[l]});function o(t,e){var r=e.percents,i=e.lengths,n=r.length,a=bmFloor((n-1)*t),s=t*e.addedLength,o=0;if(a===n-1||0===a||s===i[a])return r[a];for(var l=i[a]>s?-1:1,h=!0;h;)if(i[a]<=s&&i[a+1]>s?(o=(s-i[a])/(i[a+1]-i[a]),h=!1):a+=l,a<0||a>=n-1){if(a===n-1)return r[a];h=!1}return r[a]+(r[a+1]-r[a])*o}var l=createTypedArray("float32",8);return{getSegmentsLength:function(t){var e,i=segmentsLengthPool.newElement(),n=t.c,a=t.v,s=t.o,o=t.i,l=t._length,h=i.lengths,p=0;for(e=0;e<l-1;e+=1)h[e]=r(a[e],a[e+1],s[e],o[e+1]),p+=h[e].addedLength;return n&&l&&(h[e]=r(a[e],a[0],s[e],o[0]),p+=h[e].addedLength),i.totalLength=p,i},getNewSegment:function(e,r,i,n,a,s,h){a<0?a=0:a>1&&(a=1);var p,c=o(a,h),f=o(s=s>1?1:s,h),d=e.length,u=1-c,m=1-f,y=u*u*u,g=c*u*u*3,v=c*c*u*3,b=c*c*c,_=u*u*m,P=c*u*m+u*c*m+u*u*f,E=c*c*m+u*c*f+c*u*f,S=c*c*f,x=u*m*m,w=c*m*m+u*f*m+u*m*f,A=c*f*m+u*f*f+c*m*f,C=c*f*f,k=m*m*m,T=f*m*m+m*f*m+m*m*f,M=f*f*m+m*f*f+f*m*f,D=f*f*f;for(p=0;p<d;p+=1)l[4*p]=t.round(1e3*(y*e[p]+g*i[p]+v*n[p]+b*r[p]))/1e3,l[4*p+1]=t.round(1e3*(_*e[p]+P*i[p]+E*n[p]+S*r[p]))/1e3,l[4*p+2]=t.round(1e3*(x*e[p]+w*i[p]+A*n[p]+C*r[p]))/1e3,l[4*p+3]=t.round(1e3*(k*e[p]+T*i[p]+M*n[p]+D*r[p]))/1e3;return l},getPointInSegment:function(e,r,i,n,a,s){var l=o(a,s),h=1-l;return[t.round(1e3*(h*h*h*e[0]+(l*h*h+h*l*h+h*h*l)*i[0]+(l*l*h+h*l*l+l*h*l)*n[0]+l*l*l*r[0]))/1e3,t.round(1e3*(h*h*h*e[1]+(l*h*h+h*l*h+h*h*l)*i[1]+(l*l*h+h*l*l+l*h*l)*n[1]+l*l*l*r[1]))/1e3]},buildBezierData:s,pointOnLine2D:e,pointOnLine3D:function(r,i,n,a,s,o,l,h,p){if(0===n&&0===o&&0===p)return e(r,i,a,s,l,h);var c,f=t.sqrt(t.pow(a-r,2)+t.pow(s-i,2)+t.pow(o-n,2)),d=t.sqrt(t.pow(l-r,2)+t.pow(h-i,2)+t.pow(p-n,2)),u=t.sqrt(t.pow(l-a,2)+t.pow(h-s,2)+t.pow(p-o,2));return(c=f>d?f>u?f-d-u:u-d-f:u>d?u-d-f:d-f-u)>-1e-4&&c<1e-4}}}var bez=bezFunction(),PropertyFactory=function(){var t=initialDefaultFrame,e=Math.abs;function r(t,e){var r,n=this.offsetTime;"multidimensional"===this.propType&&(r=createTypedArray("float32",this.pv.length));for(var a,s,o,l,h,p,c,f,d,u=e.lastIndex,m=u,y=this.keyframes.length-1,g=!0;g;){if(a=this.keyframes[m],s=this.keyframes[m+1],m===y-1&&t>=s.t-n){a.h&&(a=s),u=0;break}if(s.t-n>t){u=m;break}m<y-1?m+=1:(u=0,g=!1)}o=this.keyframesMetadata[m]||{};var v,b,_,P,E,S,x,w,A,C,k=s.t-n,T=a.t-n;if(a.to){o.bezierData||(o.bezierData=bez.buildBezierData(a.s,s.s||a.e,a.to,a.ti));var M=o.bezierData;if(t>=k||t<T){var D=t>=k?M.points.length-1:0;for(h=M.points[D].point.length,l=0;l<h;l+=1)r[l]=M.points[D].point[l]}else{o.__fnct?d=o.__fnct:(d=BezierFactory.getBezierEasing(a.o.x,a.o.y,a.i.x,a.i.y,a.n).get,o.__fnct=d),p=d((t-T)/(k-T));var I,F=M.segmentLength*p,L=e.lastFrame<t&&e._lastKeyframeIndex===m?e._lastAddedLength:0;for(f=e.lastFrame<t&&e._lastKeyframeIndex===m?e._lastPoint:0,g=!0,c=M.points.length;g;){if(L+=M.points[f].partialLength,0===F||0===p||f===M.points.length-1){for(h=M.points[f].point.length,l=0;l<h;l+=1)r[l]=M.points[f].point[l];break}if(F>=L&&F<L+M.points[f+1].partialLength){for(I=(F-L)/M.points[f+1].partialLength,h=M.points[f].point.length,l=0;l<h;l+=1)r[l]=M.points[f].point[l]+(M.points[f+1].point[l]-M.points[f].point[l])*I;break}f<c-1?f+=1:g=!1}e._lastPoint=f,e._lastAddedLength=L-M.points[f].partialLength,e._lastKeyframeIndex=m}}else{var R,B,O,V,$;if(y=a.s.length,v=s.s||a.e,this.sh&&1!==a.h)t>=k?(r[0]=v[0],r[1]=v[1],r[2]=v[2]):t<=T?(r[0]=a.s[0],r[1]=a.s[1],r[2]=a.s[2]):(b=r,_=function(t,e,r){var i,n,a,s,o,l=[],h=t[0],p=t[1],c=t[2],f=t[3],d=e[0],u=e[1],m=e[2],y=e[3];return(n=h*d+p*u+c*m+f*y)<0&&(n=-n,d=-d,u=-u,m=-m,y=-y),1-n>1e-6?(i=Math.acos(n),a=Math.sin(i),s=Math.sin((1-r)*i)/a,o=Math.sin(r*i)/a):(s=1-r,o=r),l[0]=s*h+o*d,l[1]=s*p+o*u,l[2]=s*c+o*m,l[3]=s*f+o*y,l}(i(a.s),i(v),(t-T)/(k-T)),P=_[0],E=_[1],S=_[2],x=_[3],w=Math.atan2(2*E*x-2*P*S,1-2*E*E-2*S*S),A=Math.asin(2*P*E+2*S*x),C=Math.atan2(2*P*x-2*E*S,1-2*P*P-2*S*S),b[0]=w/degToRads,b[1]=A/degToRads,b[2]=C/degToRads);else for(m=0;m<y;m+=1)1!==a.h&&(t>=k?p=1:t<T?p=0:(a.o.x.constructor===Array?(o.__fnct||(o.__fnct=[]),o.__fnct[m]?d=o.__fnct[m]:(R=void 0===a.o.x[m]?a.o.x[0]:a.o.x[m],B=void 0===a.o.y[m]?a.o.y[0]:a.o.y[m],O=void 0===a.i.x[m]?a.i.x[0]:a.i.x[m],V=void 0===a.i.y[m]?a.i.y[0]:a.i.y[m],d=BezierFactory.getBezierEasing(R,B,O,V).get,o.__fnct[m]=d)):o.__fnct?d=o.__fnct:(R=a.o.x,B=a.o.y,O=a.i.x,V=a.i.y,d=BezierFactory.getBezierEasing(R,B,O,V).get,a.keyframeMetadata=d),p=d((t-T)/(k-T)))),v=s.s||a.e,$=1===a.h?a.s[m]:a.s[m]+(v[m]-a.s[m])*p,"multidimensional"===this.propType?r[m]=$:r=$}return e.lastIndex=u,r}function i(t){var e=t[0]*degToRads,r=t[1]*degToRads,i=t[2]*degToRads,n=Math.cos(e/2),a=Math.cos(r/2),s=Math.cos(i/2),o=Math.sin(e/2),l=Math.sin(r/2),h=Math.sin(i/2);return[o*l*s+n*a*h,o*a*s+n*l*h,n*l*s-o*a*h,n*a*s-o*l*h]}function n(){var e=this.comp.renderedFrame-this.offsetTime,r=this.keyframes[0].t-this.offsetTime,i=this.keyframes[this.keyframes.length-1].t-this.offsetTime;if(!(e===this._caching.lastFrame||this._caching.lastFrame!==t&&(this._caching.lastFrame>=i&&e>=i||this._caching.lastFrame<r&&e<r))){this._caching.lastFrame>=e&&(this._caching._lastKeyframeIndex=-1,this._caching.lastIndex=0);var n=this.interpolateValue(e,this._caching);this.pv=n}return this._caching.lastFrame=e,this.pv}function a(t){var r;if("unidimensional"===this.propType)r=t*this.mult,e(this.v-r)>1e-5&&(this.v=r,this._mdf=!0);else for(var i=0,n=this.v.length;i<n;)r=t[i]*this.mult,e(this.v[i]-r)>1e-5&&(this.v[i]=r,this._mdf=!0),i+=1}function s(){if(this.elem.globalData.frameId!==this.frameId&&this.effectsSequence.length)if(this.lock)this.setVValue(this.pv);else{var t;this.lock=!0,this._mdf=this._isFirstFrame;var e=this.effectsSequence.length,r=this.kf?this.pv:this.data.k;for(t=0;t<e;t+=1)r=this.effectsSequence[t](r);this.setVValue(r),this._isFirstFrame=!1,this.lock=!1,this.frameId=this.elem.globalData.frameId}}function o(t){this.effectsSequence.push(t),this.container.addDynamicProperty(this)}function l(t,e,r,i){this.propType="unidimensional",this.mult=r||1,this.data=e,this.v=r?e.k*r:e.k,this.pv=e.k,this._mdf=!1,this.elem=t,this.container=i,this.comp=t.comp,this.k=!1,this.kf=!1,this.vel=0,this.effectsSequence=[],this._isFirstFrame=!0,this.getValue=s,this.setVValue=a,this.addEffect=o}function h(t,e,r,i){var n;this.propType="multidimensional",this.mult=r||1,this.data=e,this._mdf=!1,this.elem=t,this.container=i,this.comp=t.comp,this.k=!1,this.kf=!1,this.frameId=-1;var l=e.k.length;for(this.v=createTypedArray("float32",l),this.pv=createTypedArray("float32",l),this.vel=createTypedArray("float32",l),n=0;n<l;n+=1)this.v[n]=e.k[n]*this.mult,this.pv[n]=e.k[n];this._isFirstFrame=!0,this.effectsSequence=[],this.getValue=s,this.setVValue=a,this.addEffect=o}function p(e,i,l,h){this.propType="unidimensional",this.keyframes=i.k,this.keyframesMetadata=[],this.offsetTime=e.data.st,this.frameId=-1,this._caching={lastFrame:t,lastIndex:0,value:0,_lastKeyframeIndex:-1},this.k=!0,this.kf=!0,this.data=i,this.mult=l||1,this.elem=e,this.container=h,this.comp=e.comp,this.v=t,this.pv=t,this._isFirstFrame=!0,this.getValue=s,this.setVValue=a,this.interpolateValue=r,this.effectsSequence=[n.bind(this)],this.addEffect=o}function c(e,i,l,h){var p;this.propType="multidimensional";var c,f,d,u,m=i.k.length;for(p=0;p<m-1;p+=1)i.k[p].to&&i.k[p].s&&i.k[p+1]&&i.k[p+1].s&&(c=i.k[p].s,f=i.k[p+1].s,d=i.k[p].to,u=i.k[p].ti,(2===c.length&&(c[0]!==f[0]||c[1]!==f[1])&&bez.pointOnLine2D(c[0],c[1],f[0],f[1],c[0]+d[0],c[1]+d[1])&&bez.pointOnLine2D(c[0],c[1],f[0],f[1],f[0]+u[0],f[1]+u[1])||3===c.length&&(c[0]!==f[0]||c[1]!==f[1]||c[2]!==f[2])&&bez.pointOnLine3D(c[0],c[1],c[2],f[0],f[1],f[2],c[0]+d[0],c[1]+d[1],c[2]+d[2])&&bez.pointOnLine3D(c[0],c[1],c[2],f[0],f[1],f[2],f[0]+u[0],f[1]+u[1],f[2]+u[2]))&&(i.k[p].to=null,i.k[p].ti=null),c[0]===f[0]&&c[1]===f[1]&&0===d[0]&&0===d[1]&&0===u[0]&&0===u[1]&&(2===c.length||c[2]===f[2]&&0===d[2]&&0===u[2])&&(i.k[p].to=null,i.k[p].ti=null));this.effectsSequence=[n.bind(this)],this.data=i,this.keyframes=i.k,this.keyframesMetadata=[],this.offsetTime=e.data.st,this.k=!0,this.kf=!0,this._isFirstFrame=!0,this.mult=l||1,this.elem=e,this.container=h,this.comp=e.comp,this.getValue=s,this.setVValue=a,this.interpolateValue=r,this.frameId=-1;var y=i.k[0].s.length;for(this.v=createTypedArray("float32",y),this.pv=createTypedArray("float32",y),p=0;p<y;p+=1)this.v[p]=t,this.pv[p]=t;this._caching={lastFrame:t,lastIndex:0,value:createTypedArray("float32",y)},this.addEffect=o}var f={getProp:function(t,e,r,i,n){var a;if(e.k.length)if("number"==typeof e.k[0])a=new h(t,e,i,n);else switch(r){case 0:a=new p(t,e,i,n);break;case 1:a=new c(t,e,i,n)}else a=new l(t,e,i,n);return a.effectsSequence.length&&n.addDynamicProperty(a),a}};return f}();function DynamicPropertyContainer(){}DynamicPropertyContainer.prototype={addDynamicProperty:function(t){-1===this.dynamicProperties.indexOf(t)&&(this.dynamicProperties.push(t),this.container.addDynamicProperty(this),this._isAnimated=!0)},iterateDynamicProperties:function(){var t;this._mdf=!1;var e=this.dynamicProperties.length;for(t=0;t<e;t+=1)this.dynamicProperties[t].getValue(),this.dynamicProperties[t]._mdf&&(this._mdf=!0)},initDynamicPropertyContainer:function(t){this.container=t,this.dynamicProperties=[],this._mdf=!1,this._isAnimated=!1}};var pointPool=poolFactory(8,(function(){return createTypedArray("float32",2)}));function ShapePath(){this.c=!1,this._length=0,this._maxLength=8,this.v=createSizedArray(this._maxLength),this.o=createSizedArray(this._maxLength),this.i=createSizedArray(this._maxLength)}ShapePath.prototype.setPathData=function(t,e){this.c=t,this.setLength(e);for(var r=0;r<e;)this.v[r]=pointPool.newElement(),this.o[r]=pointPool.newElement(),this.i[r]=pointPool.newElement(),r+=1},ShapePath.prototype.setLength=function(t){for(;this._maxLength<t;)this.doubleArrayLength();this._length=t},ShapePath.prototype.doubleArrayLength=function(){this.v=this.v.concat(createSizedArray(this._maxLength)),this.i=this.i.concat(createSizedArray(this._maxLength)),this.o=this.o.concat(createSizedArray(this._maxLength)),this._maxLength*=2},ShapePath.prototype.setXYAt=function(t,e,r,i,n){var a;switch(this._length=Math.max(this._length,i+1),this._length>=this._maxLength&&this.doubleArrayLength(),r){case"v":a=this.v;break;case"i":a=this.i;break;case"o":a=this.o;break;default:a=[]}(!a[i]||a[i]&&!n)&&(a[i]=pointPool.newElement()),a[i][0]=t,a[i][1]=e},ShapePath.prototype.setTripleAt=function(t,e,r,i,n,a,s,o){this.setXYAt(t,e,"v",s,o),this.setXYAt(r,i,"o",s,o),this.setXYAt(n,a,"i",s,o)},ShapePath.prototype.reverse=function(){var t=new ShapePath;t.setPathData(this.c,this._length);var e=this.v,r=this.o,i=this.i,n=0;this.c&&(t.setTripleAt(e[0][0],e[0][1],i[0][0],i[0][1],r[0][0],r[0][1],0,!1),n=1);var a,s=this._length-1,o=this._length;for(a=n;a<o;a+=1)t.setTripleAt(e[s][0],e[s][1],i[s][0],i[s][1],r[s][0],r[s][1],a,!1),s-=1;return t},ShapePath.prototype.length=function(){return this._length};var shapePool=(factory=poolFactory(4,(function(){return new ShapePath}),(function(t){var e,r=t._length;for(e=0;e<r;e+=1)pointPool.release(t.v[e]),pointPool.release(t.i[e]),pointPool.release(t.o[e]),t.v[e]=null,t.i[e]=null,t.o[e]=null;t._length=0,t.c=!1})),factory.clone=function(t){var e,r=factory.newElement(),i=void 0===t._length?t.v.length:t._length;for(r.setLength(i),r.c=t.c,e=0;e<i;e+=1)r.setTripleAt(t.v[e][0],t.v[e][1],t.o[e][0],t.o[e][1],t.i[e][0],t.i[e][1],e);return r},factory),factory;function ShapeCollection(){this._length=0,this._maxLength=4,this.shapes=createSizedArray(this._maxLength)}ShapeCollection.prototype.addShape=function(t){this._length===this._maxLength&&(this.shapes=this.shapes.concat(createSizedArray(this._maxLength)),this._maxLength*=2),this.shapes[this._length]=t,this._length+=1},ShapeCollection.prototype.releaseShapes=function(){var t;for(t=0;t<this._length;t+=1)shapePool.release(this.shapes[t]);this._length=0};var shapeCollectionPool=(ob={newShapeCollection:function(){return _length?pool[_length-=1]:new ShapeCollection},release:function(t){var e,r=t._length;for(e=0;e<r;e+=1)shapePool.release(t.shapes[e]);t._length=0,_length===_maxLength&&(pool=pooling.double(pool),_maxLength*=2),pool[_length]=t,_length+=1}},_length=0,_maxLength=4,pool=createSizedArray(_maxLength),ob),ob,_length,_maxLength,pool,ShapePropertyFactory=function(){var t=-999999;function e(t,e,r){var i,n,a,s,o,l,h,p,c,f=r.lastIndex,d=this.keyframes;if(t<d[0].t-this.offsetTime)i=d[0].s[0],a=!0,f=0;else if(t>=d[d.length-1].t-this.offsetTime)i=d[d.length-1].s?d[d.length-1].s[0]:d[d.length-2].e[0],a=!0;else{for(var u,m,y,g=f,v=d.length-1,b=!0;b&&(u=d[g],!((m=d[g+1]).t-this.offsetTime>t));)g<v-1?g+=1:b=!1;if(y=this.keyframesMetadata[g]||{},f=g,!(a=1===u.h)){if(t>=m.t-this.offsetTime)p=1;else if(t<u.t-this.offsetTime)p=0;else{var _;y.__fnct?_=y.__fnct:(_=BezierFactory.getBezierEasing(u.o.x,u.o.y,u.i.x,u.i.y).get,y.__fnct=_),p=_((t-(u.t-this.offsetTime))/(m.t-this.offsetTime-(u.t-this.offsetTime)))}n=m.s?m.s[0]:u.e[0]}i=u.s[0]}for(l=e._length,h=i.i[0].length,r.lastIndex=f,s=0;s<l;s+=1)for(o=0;o<h;o+=1)c=a?i.i[s][o]:i.i[s][o]+(n.i[s][o]-i.i[s][o])*p,e.i[s][o]=c,c=a?i.o[s][o]:i.o[s][o]+(n.o[s][o]-i.o[s][o])*p,e.o[s][o]=c,c=a?i.v[s][o]:i.v[s][o]+(n.v[s][o]-i.v[s][o])*p,e.v[s][o]=c}function r(){var e=this.comp.renderedFrame-this.offsetTime,r=this.keyframes[0].t-this.offsetTime,i=this.keyframes[this.keyframes.length-1].t-this.offsetTime,n=this._caching.lastFrame;return n!==t&&(n<r&&e<r||n>i&&e>i)||(this._caching.lastIndex=n<e?this._caching.lastIndex:0,this.interpolateShape(e,this.pv,this._caching)),this._caching.lastFrame=e,this.pv}function i(){this.paths=this.localShapeCollection}function n(t){(function(t,e){if(t._length!==e._length||t.c!==e.c)return!1;var r,i=t._length;for(r=0;r<i;r+=1)if(t.v[r][0]!==e.v[r][0]||t.v[r][1]!==e.v[r][1]||t.o[r][0]!==e.o[r][0]||t.o[r][1]!==e.o[r][1]||t.i[r][0]!==e.i[r][0]||t.i[r][1]!==e.i[r][1])return!1;return!0})(this.v,t)||(this.v=shapePool.clone(t),this.localShapeCollection.releaseShapes(),this.localShapeCollection.addShape(this.v),this._mdf=!0,this.paths=this.localShapeCollection)}function a(){if(this.elem.globalData.frameId!==this.frameId)if(this.effectsSequence.length)if(this.lock)this.setVValue(this.pv);else{var t,e;this.lock=!0,this._mdf=!1,t=this.kf?this.pv:this.data.ks?this.data.ks.k:this.data.pt.k;var r=this.effectsSequence.length;for(e=0;e<r;e+=1)t=this.effectsSequence[e](t);this.setVValue(t),this.lock=!1,this.frameId=this.elem.globalData.frameId}else this._mdf=!1}function s(t,e,r){this.propType="shape",this.comp=t.comp,this.container=t,this.elem=t,this.data=e,this.k=!1,this.kf=!1,this._mdf=!1;var n=3===r?e.pt.k:e.ks.k;this.v=shapePool.clone(n),this.pv=shapePool.clone(this.v),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.paths=this.localShapeCollection,this.paths.addShape(this.v),this.reset=i,this.effectsSequence=[]}function o(t){this.effectsSequence.push(t),this.container.addDynamicProperty(this)}function l(e,n,a){this.propType="shape",this.comp=e.comp,this.elem=e,this.container=e,this.offsetTime=e.data.st,this.keyframes=3===a?n.pt.k:n.ks.k,this.keyframesMetadata=[],this.k=!0,this.kf=!0;var s=this.keyframes[0].s[0].i.length;this.v=shapePool.newElement(),this.v.setPathData(this.keyframes[0].s[0].c,s),this.pv=shapePool.clone(this.v),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.paths=this.localShapeCollection,this.paths.addShape(this.v),this.lastFrame=t,this.reset=i,this._caching={lastFrame:t,lastIndex:0},this.effectsSequence=[r.bind(this)]}s.prototype.interpolateShape=e,s.prototype.getValue=a,s.prototype.setVValue=n,s.prototype.addEffect=o,l.prototype.getValue=a,l.prototype.interpolateShape=e,l.prototype.setVValue=n,l.prototype.addEffect=o;var h=function(){var t=roundCorner;function e(t,e){this.v=shapePool.newElement(),this.v.setPathData(!0,4),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.paths=this.localShapeCollection,this.localShapeCollection.addShape(this.v),this.d=e.d,this.elem=t,this.comp=t.comp,this.frameId=-1,this.initDynamicPropertyContainer(t),this.p=PropertyFactory.getProp(t,e.p,1,0,this),this.s=PropertyFactory.getProp(t,e.s,1,0,this),this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertEllToPath())}return e.prototype={reset:i,getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf&&this.convertEllToPath())},convertEllToPath:function(){var e=this.p.v[0],r=this.p.v[1],i=this.s.v[0]/2,n=this.s.v[1]/2,a=3!==this.d,s=this.v;s.v[0][0]=e,s.v[0][1]=r-n,s.v[1][0]=a?e+i:e-i,s.v[1][1]=r,s.v[2][0]=e,s.v[2][1]=r+n,s.v[3][0]=a?e-i:e+i,s.v[3][1]=r,s.i[0][0]=a?e-i*t:e+i*t,s.i[0][1]=r-n,s.i[1][0]=a?e+i:e-i,s.i[1][1]=r-n*t,s.i[2][0]=a?e+i*t:e-i*t,s.i[2][1]=r+n,s.i[3][0]=a?e-i:e+i,s.i[3][1]=r+n*t,s.o[0][0]=a?e+i*t:e-i*t,s.o[0][1]=r-n,s.o[1][0]=a?e+i:e-i,s.o[1][1]=r+n*t,s.o[2][0]=a?e-i*t:e+i*t,s.o[2][1]=r+n,s.o[3][0]=a?e-i:e+i,s.o[3][1]=r-n*t}},extendPrototype([DynamicPropertyContainer],e),e}(),p=function(){function t(t,e){this.v=shapePool.newElement(),this.v.setPathData(!0,0),this.elem=t,this.comp=t.comp,this.data=e,this.frameId=-1,this.d=e.d,this.initDynamicPropertyContainer(t),1===e.sy?(this.ir=PropertyFactory.getProp(t,e.ir,0,0,this),this.is=PropertyFactory.getProp(t,e.is,0,.01,this),this.convertToPath=this.convertStarToPath):this.convertToPath=this.convertPolygonToPath,this.pt=PropertyFactory.getProp(t,e.pt,0,0,this),this.p=PropertyFactory.getProp(t,e.p,1,0,this),this.r=PropertyFactory.getProp(t,e.r,0,degToRads,this),this.or=PropertyFactory.getProp(t,e.or,0,0,this),this.os=PropertyFactory.getProp(t,e.os,0,.01,this),this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.localShapeCollection.addShape(this.v),this.paths=this.localShapeCollection,this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertToPath())}return t.prototype={reset:i,getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf&&this.convertToPath())},convertStarToPath:function(){var t,e,r,i,n=2*Math.floor(this.pt.v),a=2*Math.PI/n,s=!0,o=this.or.v,l=this.ir.v,h=this.os.v,p=this.is.v,c=2*Math.PI*o/(2*n),f=2*Math.PI*l/(2*n),d=-Math.PI/2;d+=this.r.v;var u=3===this.data.d?-1:1;for(this.v._length=0,t=0;t<n;t+=1){r=s?h:p,i=s?c:f;var m=(e=s?o:l)*Math.cos(d),y=e*Math.sin(d),g=0===m&&0===y?0:y/Math.sqrt(m*m+y*y),v=0===m&&0===y?0:-m/Math.sqrt(m*m+y*y);m+=+this.p.v[0],y+=+this.p.v[1],this.v.setTripleAt(m,y,m-g*i*r*u,y-v*i*r*u,m+g*i*r*u,y+v*i*r*u,t,!0),s=!s,d+=a*u}},convertPolygonToPath:function(){var t,e=Math.floor(this.pt.v),r=2*Math.PI/e,i=this.or.v,n=this.os.v,a=2*Math.PI*i/(4*e),s=.5*-Math.PI,o=3===this.data.d?-1:1;for(s+=this.r.v,this.v._length=0,t=0;t<e;t+=1){var l=i*Math.cos(s),h=i*Math.sin(s),p=0===l&&0===h?0:h/Math.sqrt(l*l+h*h),c=0===l&&0===h?0:-l/Math.sqrt(l*l+h*h);l+=+this.p.v[0],h+=+this.p.v[1],this.v.setTripleAt(l,h,l-p*a*n*o,h-c*a*n*o,l+p*a*n*o,h+c*a*n*o,t,!0),s+=r*o}this.paths.length=0,this.paths[0]=this.v}},extendPrototype([DynamicPropertyContainer],t),t}(),c=function(){function t(t,e){this.v=shapePool.newElement(),this.v.c=!0,this.localShapeCollection=shapeCollectionPool.newShapeCollection(),this.localShapeCollection.addShape(this.v),this.paths=this.localShapeCollection,this.elem=t,this.comp=t.comp,this.frameId=-1,this.d=e.d,this.initDynamicPropertyContainer(t),this.p=PropertyFactory.getProp(t,e.p,1,0,this),this.s=PropertyFactory.getProp(t,e.s,1,0,this),this.r=PropertyFactory.getProp(t,e.r,0,0,this),this.dynamicProperties.length?this.k=!0:(this.k=!1,this.convertRectToPath())}return t.prototype={convertRectToPath:function(){var t=this.p.v[0],e=this.p.v[1],r=this.s.v[0]/2,i=this.s.v[1]/2,n=bmMin(r,i,this.r.v),a=n*(1-roundCorner);this.v._length=0,2===this.d||1===this.d?(this.v.setTripleAt(t+r,e-i+n,t+r,e-i+n,t+r,e-i+a,0,!0),this.v.setTripleAt(t+r,e+i-n,t+r,e+i-a,t+r,e+i-n,1,!0),0!==n?(this.v.setTripleAt(t+r-n,e+i,t+r-n,e+i,t+r-a,e+i,2,!0),this.v.setTripleAt(t-r+n,e+i,t-r+a,e+i,t-r+n,e+i,3,!0),this.v.setTripleAt(t-r,e+i-n,t-r,e+i-n,t-r,e+i-a,4,!0),this.v.setTripleAt(t-r,e-i+n,t-r,e-i+a,t-r,e-i+n,5,!0),this.v.setTripleAt(t-r+n,e-i,t-r+n,e-i,t-r+a,e-i,6,!0),this.v.setTripleAt(t+r-n,e-i,t+r-a,e-i,t+r-n,e-i,7,!0)):(this.v.setTripleAt(t-r,e+i,t-r+a,e+i,t-r,e+i,2),this.v.setTripleAt(t-r,e-i,t-r,e-i+a,t-r,e-i,3))):(this.v.setTripleAt(t+r,e-i+n,t+r,e-i+a,t+r,e-i+n,0,!0),0!==n?(this.v.setTripleAt(t+r-n,e-i,t+r-n,e-i,t+r-a,e-i,1,!0),this.v.setTripleAt(t-r+n,e-i,t-r+a,e-i,t-r+n,e-i,2,!0),this.v.setTripleAt(t-r,e-i+n,t-r,e-i+n,t-r,e-i+a,3,!0),this.v.setTripleAt(t-r,e+i-n,t-r,e+i-a,t-r,e+i-n,4,!0),this.v.setTripleAt(t-r+n,e+i,t-r+n,e+i,t-r+a,e+i,5,!0),this.v.setTripleAt(t+r-n,e+i,t+r-a,e+i,t+r-n,e+i,6,!0),this.v.setTripleAt(t+r,e+i-n,t+r,e+i-n,t+r,e+i-a,7,!0)):(this.v.setTripleAt(t-r,e-i,t-r+a,e-i,t-r,e-i,1,!0),this.v.setTripleAt(t-r,e+i,t-r,e+i-a,t-r,e+i,2,!0),this.v.setTripleAt(t+r,e+i,t+r-a,e+i,t+r,e+i,3,!0)))},getValue:function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf&&this.convertRectToPath())},reset:i},extendPrototype([DynamicPropertyContainer],t),t}(),f={getShapeProp:function(t,e,r){var i;return 3===r||4===r?i=(3===r?e.pt:e.ks).k.length?new l(t,e,r):new s(t,e,r):5===r?i=new c(t,e):6===r?i=new h(t,e):7===r&&(i=new p(t,e)),i.k&&t.addDynamicProperty(i),i},getConstructorFunction:function(){return s},getKeyframedConstructorFunction:function(){return l}};return f}(),Matrix=function(){var t=Math.cos,e=Math.sin,r=Math.tan,i=Math.round;function n(){return this.props[0]=1,this.props[1]=0,this.props[2]=0,this.props[3]=0,this.props[4]=0,this.props[5]=1,this.props[6]=0,this.props[7]=0,this.props[8]=0,this.props[9]=0,this.props[10]=1,this.props[11]=0,this.props[12]=0,this.props[13]=0,this.props[14]=0,this.props[15]=1,this}function a(r){if(0===r)return this;var i=t(r),n=e(r);return this._t(i,-n,0,0,n,i,0,0,0,0,1,0,0,0,0,1)}function s(r){if(0===r)return this;var i=t(r),n=e(r);return this._t(1,0,0,0,0,i,-n,0,0,n,i,0,0,0,0,1)}function o(r){if(0===r)return this;var i=t(r),n=e(r);return this._t(i,0,n,0,0,1,0,0,-n,0,i,0,0,0,0,1)}function l(r){if(0===r)return this;var i=t(r),n=e(r);return this._t(i,-n,0,0,n,i,0,0,0,0,1,0,0,0,0,1)}function h(t,e){return this._t(1,e,t,1,0,0)}function p(t,e){return this.shear(r(t),r(e))}function c(i,n){var a=t(n),s=e(n);return this._t(a,s,0,0,-s,a,0,0,0,0,1,0,0,0,0,1)._t(1,0,0,0,r(i),1,0,0,0,0,1,0,0,0,0,1)._t(a,-s,0,0,s,a,0,0,0,0,1,0,0,0,0,1)}function f(t,e,r){return r||0===r||(r=1),1===t&&1===e&&1===r?this:this._t(t,0,0,0,0,e,0,0,0,0,r,0,0,0,0,1)}function d(t,e,r,i,n,a,s,o,l,h,p,c,f,d,u,m){return this.props[0]=t,this.props[1]=e,this.props[2]=r,this.props[3]=i,this.props[4]=n,this.props[5]=a,this.props[6]=s,this.props[7]=o,this.props[8]=l,this.props[9]=h,this.props[10]=p,this.props[11]=c,this.props[12]=f,this.props[13]=d,this.props[14]=u,this.props[15]=m,this}function u(t,e,r){return r=r||0,0!==t||0!==e||0!==r?this._t(1,0,0,0,0,1,0,0,0,0,1,0,t,e,r,1):this}function m(t,e,r,i,n,a,s,o,l,h,p,c,f,d,u,m){var y=this.props;if(1===t&&0===e&&0===r&&0===i&&0===n&&1===a&&0===s&&0===o&&0===l&&0===h&&1===p&&0===c)return y[12]=y[12]*t+y[15]*f,y[13]=y[13]*a+y[15]*d,y[14]=y[14]*p+y[15]*u,y[15]*=m,this._identityCalculated=!1,this;var g=y[0],v=y[1],b=y[2],_=y[3],P=y[4],E=y[5],S=y[6],x=y[7],w=y[8],A=y[9],C=y[10],k=y[11],T=y[12],M=y[13],D=y[14],I=y[15];return y[0]=g*t+v*n+b*l+_*f,y[1]=g*e+v*a+b*h+_*d,y[2]=g*r+v*s+b*p+_*u,y[3]=g*i+v*o+b*c+_*m,y[4]=P*t+E*n+S*l+x*f,y[5]=P*e+E*a+S*h+x*d,y[6]=P*r+E*s+S*p+x*u,y[7]=P*i+E*o+S*c+x*m,y[8]=w*t+A*n+C*l+k*f,y[9]=w*e+A*a+C*h+k*d,y[10]=w*r+A*s+C*p+k*u,y[11]=w*i+A*o+C*c+k*m,y[12]=T*t+M*n+D*l+I*f,y[13]=T*e+M*a+D*h+I*d,y[14]=T*r+M*s+D*p+I*u,y[15]=T*i+M*o+D*c+I*m,this._identityCalculated=!1,this}function y(){return this._identityCalculated||(this._identity=!(1!==this.props[0]||0!==this.props[1]||0!==this.props[2]||0!==this.props[3]||0!==this.props[4]||1!==this.props[5]||0!==this.props[6]||0!==this.props[7]||0!==this.props[8]||0!==this.props[9]||1!==this.props[10]||0!==this.props[11]||0!==this.props[12]||0!==this.props[13]||0!==this.props[14]||1!==this.props[15]),this._identityCalculated=!0),this._identity}function g(t){for(var e=0;e<16;){if(t.props[e]!==this.props[e])return!1;e+=1}return!0}function v(t){var e;for(e=0;e<16;e+=1)t.props[e]=this.props[e];return t}function b(t){var e;for(e=0;e<16;e+=1)this.props[e]=t[e]}function _(t,e,r){return{x:t*this.props[0]+e*this.props[4]+r*this.props[8]+this.props[12],y:t*this.props[1]+e*this.props[5]+r*this.props[9]+this.props[13],z:t*this.props[2]+e*this.props[6]+r*this.props[10]+this.props[14]}}function P(t,e,r){return t*this.props[0]+e*this.props[4]+r*this.props[8]+this.props[12]}function E(t,e,r){return t*this.props[1]+e*this.props[5]+r*this.props[9]+this.props[13]}function S(t,e,r){return t*this.props[2]+e*this.props[6]+r*this.props[10]+this.props[14]}function x(){var t=this.props[0]*this.props[5]-this.props[1]*this.props[4],e=this.props[5]/t,r=-this.props[1]/t,i=-this.props[4]/t,n=this.props[0]/t,a=(this.props[4]*this.props[13]-this.props[5]*this.props[12])/t,s=-(this.props[0]*this.props[13]-this.props[1]*this.props[12])/t,o=new Matrix;return o.props[0]=e,o.props[1]=r,o.props[4]=i,o.props[5]=n,o.props[12]=a,o.props[13]=s,o}function w(t){return this.getInverseMatrix().applyToPointArray(t[0],t[1],t[2]||0)}function A(t){var e,r=t.length,i=[];for(e=0;e<r;e+=1)i[e]=w(t[e]);return i}function C(t,e,r){var i=createTypedArray("float32",6);if(this.isIdentity())i[0]=t[0],i[1]=t[1],i[2]=e[0],i[3]=e[1],i[4]=r[0],i[5]=r[1];else{var n=this.props[0],a=this.props[1],s=this.props[4],o=this.props[5],l=this.props[12],h=this.props[13];i[0]=t[0]*n+t[1]*s+l,i[1]=t[0]*a+t[1]*o+h,i[2]=e[0]*n+e[1]*s+l,i[3]=e[0]*a+e[1]*o+h,i[4]=r[0]*n+r[1]*s+l,i[5]=r[0]*a+r[1]*o+h}return i}function k(t,e,r){return this.isIdentity()?[t,e,r]:[t*this.props[0]+e*this.props[4]+r*this.props[8]+this.props[12],t*this.props[1]+e*this.props[5]+r*this.props[9]+this.props[13],t*this.props[2]+e*this.props[6]+r*this.props[10]+this.props[14]]}function T(t,e){if(this.isIdentity())return t+","+e;var r=this.props;return Math.round(100*(t*r[0]+e*r[4]+r[12]))/100+","+Math.round(100*(t*r[1]+e*r[5]+r[13]))/100}function M(){for(var t=0,e=this.props,r="matrix3d(";t<16;)r+=i(1e4*e[t])/1e4,r+=15===t?")":",",t+=1;return r}function D(t){return t<1e-6&&t>0||t>-1e-6&&t<0?i(1e4*t)/1e4:t}function I(){var t=this.props;return"matrix("+D(t[0])+","+D(t[1])+","+D(t[4])+","+D(t[5])+","+D(t[12])+","+D(t[13])+")"}return function(){this.reset=n,this.rotate=a,this.rotateX=s,this.rotateY=o,this.rotateZ=l,this.skew=p,this.skewFromAxis=c,this.shear=h,this.scale=f,this.setTransform=d,this.translate=u,this.transform=m,this.applyToPoint=_,this.applyToX=P,this.applyToY=E,this.applyToZ=S,this.applyToPointArray=k,this.applyToTriplePoints=C,this.applyToPointStringified=T,this.toCSS=M,this.to2dCSS=I,this.clone=v,this.cloneFromProps=b,this.equals=g,this.inversePoints=A,this.inversePoint=w,this.getInverseMatrix=x,this._t=this.transform,this.isIdentity=y,this._identity=!0,this._identityCalculated=!1,this.props=createTypedArray("float32",16),this.reset()}}();function _typeof$3(t){return _typeof$3="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_typeof$3(t)}var lottie={},standalone="__[STANDALONE]__",animationData="__[ANIMATIONDATA]__",renderer="";function setLocation(t){setLocationHref(t)}function searchAnimations(){!0===standalone?animationManager.searchAnimations(animationData,standalone,renderer):animationManager.searchAnimations()}function setSubframeRendering(t){setSubframeEnabled(t)}function setPrefix(t){setIdPrefix(t)}function loadAnimation(t){return!0===standalone&&(t.animationData=JSON.parse(animationData)),animationManager.loadAnimation(t)}function setQuality(t){if("string"==typeof t)switch(t){case"high":setDefaultCurveSegments(200);break;default:case"medium":setDefaultCurveSegments(50);break;case"low":setDefaultCurveSegments(10)}else!isNaN(t)&&t>1&&setDefaultCurveSegments(t);getDefaultCurveSegments()>=50?roundValues(!1):roundValues(!0)}function inBrowser(){return"undefined"!=typeof navigator}function installPlugin(t,e){"expressions"===t&&setExpressionsPlugin(e)}function getFactory(t){switch(t){case"propertyFactory":return PropertyFactory;case"shapePropertyFactory":return ShapePropertyFactory;case"matrix":return Matrix;default:return null}}function checkReady(){"complete"===document.readyState&&(clearInterval(readyStateCheckInterval),searchAnimations())}function getQueryVariable(t){for(var e=queryString.split("&"),r=0;r<e.length;r+=1){var i=e[r].split("=");if(decodeURIComponent(i[0])==t)return decodeURIComponent(i[1])}return null}lottie.play=animationManager.play,lottie.pause=animationManager.pause,lottie.setLocationHref=setLocation,lottie.togglePause=animationManager.togglePause,lottie.setSpeed=animationManager.setSpeed,lottie.setDirection=animationManager.setDirection,lottie.stop=animationManager.stop,lottie.searchAnimations=searchAnimations,lottie.registerAnimation=animationManager.registerAnimation,lottie.loadAnimation=loadAnimation,lottie.setSubframeRendering=setSubframeRendering,lottie.resize=animationManager.resize,lottie.goToAndStop=animationManager.goToAndStop,lottie.destroy=animationManager.destroy,lottie.setQuality=setQuality,lottie.inBrowser=inBrowser,lottie.installPlugin=installPlugin,lottie.freeze=animationManager.freeze,lottie.unfreeze=animationManager.unfreeze,lottie.setVolume=animationManager.setVolume,lottie.mute=animationManager.mute,lottie.unmute=animationManager.unmute,lottie.getRegisteredAnimations=animationManager.getRegisteredAnimations,lottie.useWebWorker=setWebWorker,lottie.setIDPrefix=setPrefix,lottie.__getFactory=getFactory,lottie.version="5.10.0";var queryString="";if(standalone){var scripts=document.getElementsByTagName("script"),index=scripts.length-1,myScript=scripts[index]||{src:""};queryString=myScript.src?myScript.src.replace(/^[^\?]+\??/,""):"",renderer=getQueryVariable("renderer")}var readyStateCheckInterval=setInterval(checkReady,100);try{"object"!==_typeof$3(exports)&&(window.bodymovin=lottie)}catch(t){}var ShapeModifiers=function(){var t={},e={};return t.registerModifier=function(t,r){e[t]||(e[t]=r)},t.getModifier=function(t,r,i){return new e[t](r,i)},t}();function ShapeModifier(){}function TrimModifier(){}function PuckerAndBloatModifier(){}ShapeModifier.prototype.initModifierProperties=function(){},ShapeModifier.prototype.addShapeToModifier=function(){},ShapeModifier.prototype.addShape=function(t){if(!this.closed){t.sh.container.addDynamicProperty(t.sh);var e={shape:t.sh,data:t,localShapeCollection:shapeCollectionPool.newShapeCollection()};this.shapes.push(e),this.addShapeToModifier(e),this._isAnimated&&t.setAsAnimated()}},ShapeModifier.prototype.init=function(t,e){this.shapes=[],this.elem=t,this.initDynamicPropertyContainer(t),this.initModifierProperties(t,e),this.frameId=initialDefaultFrame,this.closed=!1,this.k=!1,this.dynamicProperties.length?this.k=!0:this.getValue(!0)},ShapeModifier.prototype.processKeys=function(){this.elem.globalData.frameId!==this.frameId&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties())},extendPrototype([DynamicPropertyContainer],ShapeModifier),extendPrototype([ShapeModifier],TrimModifier),TrimModifier.prototype.initModifierProperties=function(t,e){this.s=PropertyFactory.getProp(t,e.s,0,.01,this),this.e=PropertyFactory.getProp(t,e.e,0,.01,this),this.o=PropertyFactory.getProp(t,e.o,0,0,this),this.sValue=0,this.eValue=0,this.getValue=this.processKeys,this.m=e.m,this._isAnimated=!!this.s.effectsSequence.length||!!this.e.effectsSequence.length||!!this.o.effectsSequence.length},TrimModifier.prototype.addShapeToModifier=function(t){t.pathsData=[]},TrimModifier.prototype.calculateShapeEdges=function(t,e,r,i,n){var a=[];e<=1?a.push({s:t,e:e}):t>=1?a.push({s:t-1,e:e-1}):(a.push({s:t,e:1}),a.push({s:0,e:e-1}));var s,o,l=[],h=a.length;for(s=0;s<h;s+=1){var p,c;(o=a[s]).e*n<i||o.s*n>i+r||(p=o.s*n<=i?0:(o.s*n-i)/r,c=o.e*n>=i+r?1:(o.e*n-i)/r,l.push([p,c]))}return l.length||l.push([0,0]),l},TrimModifier.prototype.releasePathsData=function(t){var e,r=t.length;for(e=0;e<r;e+=1)segmentsLengthPool.release(t[e]);return t.length=0,t},TrimModifier.prototype.processShapes=function(t){var e,r,i,n;if(this._mdf||t){var a=this.o.v%360/360;if(a<0&&(a+=1),(e=this.s.v>1?1+a:this.s.v<0?0+a:this.s.v+a)>(r=this.e.v>1?1+a:this.e.v<0?0+a:this.e.v+a)){var s=e;e=r,r=s}e=1e-4*Math.round(1e4*e),r=1e-4*Math.round(1e4*r),this.sValue=e,this.eValue=r}else e=this.sValue,r=this.eValue;var o,l,h,p,c,f=this.shapes.length,d=0;if(r===e)for(n=0;n<f;n+=1)this.shapes[n].localShapeCollection.releaseShapes(),this.shapes[n].shape._mdf=!0,this.shapes[n].shape.paths=this.shapes[n].localShapeCollection,this._mdf&&(this.shapes[n].pathsData.length=0);else if(1===r&&0===e||0===r&&1===e){if(this._mdf)for(n=0;n<f;n+=1)this.shapes[n].pathsData.length=0,this.shapes[n].shape._mdf=!0}else{var u,m,y=[];for(n=0;n<f;n+=1)if((u=this.shapes[n]).shape._mdf||this._mdf||t||2===this.m){if(l=(i=u.shape.paths)._length,c=0,!u.shape._mdf&&u.pathsData.length)c=u.totalShapeLength;else{for(h=this.releasePathsData(u.pathsData),o=0;o<l;o+=1)p=bez.getSegmentsLength(i.shapes[o]),h.push(p),c+=p.totalLength;u.totalShapeLength=c,u.pathsData=h}d+=c,u.shape._mdf=!0}else u.shape.paths=u.localShapeCollection;var g,v=e,b=r,_=0;for(n=f-1;n>=0;n-=1)if((u=this.shapes[n]).shape._mdf){for((m=u.localShapeCollection).releaseShapes(),2===this.m&&f>1?(g=this.calculateShapeEdges(e,r,u.totalShapeLength,_,d),_+=u.totalShapeLength):g=[[v,b]],l=g.length,o=0;o<l;o+=1){v=g[o][0],b=g[o][1],y.length=0,b<=1?y.push({s:u.totalShapeLength*v,e:u.totalShapeLength*b}):v>=1?y.push({s:u.totalShapeLength*(v-1),e:u.totalShapeLength*(b-1)}):(y.push({s:u.totalShapeLength*v,e:u.totalShapeLength}),y.push({s:0,e:u.totalShapeLength*(b-1)}));var P=this.addShapes(u,y[0]);if(y[0].s!==y[0].e){if(y.length>1)if(u.shape.paths.shapes[u.shape.paths._length-1].c){var E=P.pop();this.addPaths(P,m),P=this.addShapes(u,y[1],E)}else this.addPaths(P,m),P=this.addShapes(u,y[1]);this.addPaths(P,m)}}u.shape.paths=m}}},TrimModifier.prototype.addPaths=function(t,e){var r,i=t.length;for(r=0;r<i;r+=1)e.addShape(t[r])},TrimModifier.prototype.addSegment=function(t,e,r,i,n,a,s){n.setXYAt(e[0],e[1],"o",a),n.setXYAt(r[0],r[1],"i",a+1),s&&n.setXYAt(t[0],t[1],"v",a),n.setXYAt(i[0],i[1],"v",a+1)},TrimModifier.prototype.addSegmentFromArray=function(t,e,r,i){e.setXYAt(t[1],t[5],"o",r),e.setXYAt(t[2],t[6],"i",r+1),i&&e.setXYAt(t[0],t[4],"v",r),e.setXYAt(t[3],t[7],"v",r+1)},TrimModifier.prototype.addShapes=function(t,e,r){var i,n,a,s,o,l,h,p,c=t.pathsData,f=t.shape.paths.shapes,d=t.shape.paths._length,u=0,m=[],y=!0;for(r?(o=r._length,p=r._length):(r=shapePool.newElement(),o=0,p=0),m.push(r),i=0;i<d;i+=1){for(l=c[i].lengths,r.c=f[i].c,a=f[i].c?l.length:l.length+1,n=1;n<a;n+=1)if(u+(s=l[n-1]).addedLength<e.s)u+=s.addedLength,r.c=!1;else{if(u>e.e){r.c=!1;break}e.s<=u&&e.e>=u+s.addedLength?(this.addSegment(f[i].v[n-1],f[i].o[n-1],f[i].i[n],f[i].v[n],r,o,y),y=!1):(h=bez.getNewSegment(f[i].v[n-1],f[i].v[n],f[i].o[n-1],f[i].i[n],(e.s-u)/s.addedLength,(e.e-u)/s.addedLength,l[n-1]),this.addSegmentFromArray(h,r,o,y),y=!1,r.c=!1),u+=s.addedLength,o+=1}if(f[i].c&&l.length){if(s=l[n-1],u<=e.e){var g=l[n-1].addedLength;e.s<=u&&e.e>=u+g?(this.addSegment(f[i].v[n-1],f[i].o[n-1],f[i].i[0],f[i].v[0],r,o,y),y=!1):(h=bez.getNewSegment(f[i].v[n-1],f[i].v[0],f[i].o[n-1],f[i].i[0],(e.s-u)/g,(e.e-u)/g,l[n-1]),this.addSegmentFromArray(h,r,o,y),y=!1,r.c=!1)}else r.c=!1;u+=s.addedLength,o+=1}if(r._length&&(r.setXYAt(r.v[p][0],r.v[p][1],"i",p),r.setXYAt(r.v[r._length-1][0],r.v[r._length-1][1],"o",r._length-1)),u>e.e)break;i<d-1&&(r=shapePool.newElement(),y=!0,m.push(r),o=0)}return m},extendPrototype([ShapeModifier],PuckerAndBloatModifier),PuckerAndBloatModifier.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.amount=PropertyFactory.getProp(t,e.a,0,null,this),this._isAnimated=!!this.amount.effectsSequence.length},PuckerAndBloatModifier.prototype.processPath=function(t,e){var r=e/100,i=[0,0],n=t._length,a=0;for(a=0;a<n;a+=1)i[0]+=t.v[a][0],i[1]+=t.v[a][1];i[0]/=n,i[1]/=n;var s,o,l,h,p,c,f=shapePool.newElement();for(f.c=t.c,a=0;a<n;a+=1)s=t.v[a][0]+(i[0]-t.v[a][0])*r,o=t.v[a][1]+(i[1]-t.v[a][1])*r,l=t.o[a][0]+(i[0]-t.o[a][0])*-r,h=t.o[a][1]+(i[1]-t.o[a][1])*-r,p=t.i[a][0]+(i[0]-t.i[a][0])*-r,c=t.i[a][1]+(i[1]-t.i[a][1])*-r,f.setTripleAt(s,o,l,h,p,c,a);return f},PuckerAndBloatModifier.prototype.processShapes=function(t){var e,r,i,n,a,s,o=this.shapes.length,l=this.amount.v;if(0!==l)for(r=0;r<o;r+=1){if(s=(a=this.shapes[r]).localShapeCollection,a.shape._mdf||this._mdf||t)for(s.releaseShapes(),a.shape._mdf=!0,e=a.shape.paths.shapes,n=a.shape.paths._length,i=0;i<n;i+=1)s.addShape(this.processPath(e[i],l));a.shape.paths=a.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)};var TransformPropertyFactory=function(){var t=[0,0];function e(t,e,r){if(this.elem=t,this.frameId=-1,this.propType="transform",this.data=e,this.v=new Matrix,this.pre=new Matrix,this.appliedTransformations=0,this.initDynamicPropertyContainer(r||t),e.p&&e.p.s?(this.px=PropertyFactory.getProp(t,e.p.x,0,0,this),this.py=PropertyFactory.getProp(t,e.p.y,0,0,this),e.p.z&&(this.pz=PropertyFactory.getProp(t,e.p.z,0,0,this))):this.p=PropertyFactory.getProp(t,e.p||{k:[0,0,0]},1,0,this),e.rx){if(this.rx=PropertyFactory.getProp(t,e.rx,0,degToRads,this),this.ry=PropertyFactory.getProp(t,e.ry,0,degToRads,this),this.rz=PropertyFactory.getProp(t,e.rz,0,degToRads,this),e.or.k[0].ti){var i,n=e.or.k.length;for(i=0;i<n;i+=1)e.or.k[i].to=null,e.or.k[i].ti=null}this.or=PropertyFactory.getProp(t,e.or,1,degToRads,this),this.or.sh=!0}else this.r=PropertyFactory.getProp(t,e.r||{k:0},0,degToRads,this);e.sk&&(this.sk=PropertyFactory.getProp(t,e.sk,0,degToRads,this),this.sa=PropertyFactory.getProp(t,e.sa,0,degToRads,this)),this.a=PropertyFactory.getProp(t,e.a||{k:[0,0,0]},1,0,this),this.s=PropertyFactory.getProp(t,e.s||{k:[100,100,100]},1,.01,this),e.o?this.o=PropertyFactory.getProp(t,e.o,0,.01,t):this.o={_mdf:!1,v:1},this._isDirty=!0,this.dynamicProperties.length||this.getValue(!0)}return e.prototype={applyToMatrix:function(t){var e=this._mdf;this.iterateDynamicProperties(),this._mdf=this._mdf||e,this.a&&t.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.s&&t.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.sk&&t.skewFromAxis(-this.sk.v,this.sa.v),this.r?t.rotate(-this.r.v):t.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.data.p.s?this.data.p.z?t.translate(this.px.v,this.py.v,-this.pz.v):t.translate(this.px.v,this.py.v,0):t.translate(this.p.v[0],this.p.v[1],-this.p.v[2])},getValue:function(e){if(this.elem.globalData.frameId!==this.frameId){if(this._isDirty&&(this.precalculateMatrix(),this._isDirty=!1),this.iterateDynamicProperties(),this._mdf||e){var r;if(this.v.cloneFromProps(this.pre.props),this.appliedTransformations<1&&this.v.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.appliedTransformations<2&&this.v.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.sk&&this.appliedTransformations<3&&this.v.skewFromAxis(-this.sk.v,this.sa.v),this.r&&this.appliedTransformations<4?this.v.rotate(-this.r.v):!this.r&&this.appliedTransformations<4&&this.v.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.autoOriented){var i,n;if(r=this.elem.globalData.frameRate,this.p&&this.p.keyframes&&this.p.getValueAtTime)this.p._caching.lastFrame+this.p.offsetTime<=this.p.keyframes[0].t?(i=this.p.getValueAtTime((this.p.keyframes[0].t+.01)/r,0),n=this.p.getValueAtTime(this.p.keyframes[0].t/r,0)):this.p._caching.lastFrame+this.p.offsetTime>=this.p.keyframes[this.p.keyframes.length-1].t?(i=this.p.getValueAtTime(this.p.keyframes[this.p.keyframes.length-1].t/r,0),n=this.p.getValueAtTime((this.p.keyframes[this.p.keyframes.length-1].t-.05)/r,0)):(i=this.p.pv,n=this.p.getValueAtTime((this.p._caching.lastFrame+this.p.offsetTime-.01)/r,this.p.offsetTime));else if(this.px&&this.px.keyframes&&this.py.keyframes&&this.px.getValueAtTime&&this.py.getValueAtTime){i=[],n=[];var a=this.px,s=this.py;a._caching.lastFrame+a.offsetTime<=a.keyframes[0].t?(i[0]=a.getValueAtTime((a.keyframes[0].t+.01)/r,0),i[1]=s.getValueAtTime((s.keyframes[0].t+.01)/r,0),n[0]=a.getValueAtTime(a.keyframes[0].t/r,0),n[1]=s.getValueAtTime(s.keyframes[0].t/r,0)):a._caching.lastFrame+a.offsetTime>=a.keyframes[a.keyframes.length-1].t?(i[0]=a.getValueAtTime(a.keyframes[a.keyframes.length-1].t/r,0),i[1]=s.getValueAtTime(s.keyframes[s.keyframes.length-1].t/r,0),n[0]=a.getValueAtTime((a.keyframes[a.keyframes.length-1].t-.01)/r,0),n[1]=s.getValueAtTime((s.keyframes[s.keyframes.length-1].t-.01)/r,0)):(i=[a.pv,s.pv],n[0]=a.getValueAtTime((a._caching.lastFrame+a.offsetTime-.01)/r,a.offsetTime),n[1]=s.getValueAtTime((s._caching.lastFrame+s.offsetTime-.01)/r,s.offsetTime))}else i=n=t;this.v.rotate(-Math.atan2(i[1]-n[1],i[0]-n[0]))}this.data.p&&this.data.p.s?this.data.p.z?this.v.translate(this.px.v,this.py.v,-this.pz.v):this.v.translate(this.px.v,this.py.v,0):this.v.translate(this.p.v[0],this.p.v[1],-this.p.v[2])}this.frameId=this.elem.globalData.frameId}},precalculateMatrix:function(){if(!this.a.k&&(this.pre.translate(-this.a.v[0],-this.a.v[1],this.a.v[2]),this.appliedTransformations=1,!this.s.effectsSequence.length)){if(this.pre.scale(this.s.v[0],this.s.v[1],this.s.v[2]),this.appliedTransformations=2,this.sk){if(this.sk.effectsSequence.length||this.sa.effectsSequence.length)return;this.pre.skewFromAxis(-this.sk.v,this.sa.v),this.appliedTransformations=3}this.r?this.r.effectsSequence.length||(this.pre.rotate(-this.r.v),this.appliedTransformations=4):this.rz.effectsSequence.length||this.ry.effectsSequence.length||this.rx.effectsSequence.length||this.or.effectsSequence.length||(this.pre.rotateZ(-this.rz.v).rotateY(this.ry.v).rotateX(this.rx.v).rotateZ(-this.or.v[2]).rotateY(this.or.v[1]).rotateX(this.or.v[0]),this.appliedTransformations=4)}},autoOrient:function(){}},extendPrototype([DynamicPropertyContainer],e),e.prototype.addDynamicProperty=function(t){this._addDynamicProperty(t),this.elem.addDynamicProperty(t),this._isDirty=!0},e.prototype._addDynamicProperty=DynamicPropertyContainer.prototype.addDynamicProperty,{getTransformProperty:function(t,r,i){return new e(t,r,i)}}}();function RepeaterModifier(){}function RoundCornersModifier(){}function floatEqual(t,e){return 1e5*Math.abs(t-e)<=Math.min(Math.abs(t),Math.abs(e))}function floatZero(t){return Math.abs(t)<=1e-5}function lerp(t,e,r){return t*(1-r)+e*r}function lerpPoint(t,e,r){return[lerp(t[0],e[0],r),lerp(t[1],e[1],r)]}function quadRoots(t,e,r){if(0===t)return[];var i=e*e-4*t*r;if(i<0)return[];var n=-e/(2*t);if(0===i)return[n];var a=Math.sqrt(i)/(2*t);return[n-a,n+a]}function polynomialCoefficients(t,e,r,i){return[3*e-t-3*r+i,3*t-6*e+3*r,-3*t+3*e,t]}function singlePoint(t){return new PolynomialBezier(t,t,t,t,!1)}function PolynomialBezier(t,e,r,i,n){n&&pointEqual(t,e)&&(e=lerpPoint(t,i,1/3)),n&&pointEqual(r,i)&&(r=lerpPoint(t,i,2/3));var a=polynomialCoefficients(t[0],e[0],r[0],i[0]),s=polynomialCoefficients(t[1],e[1],r[1],i[1]);this.a=[a[0],s[0]],this.b=[a[1],s[1]],this.c=[a[2],s[2]],this.d=[a[3],s[3]],this.points=[t,e,r,i]}function extrema(t,e){var r=t.points[0][e],i=t.points[t.points.length-1][e];if(r>i){var n=i;i=r,r=n}for(var a=quadRoots(3*t.a[e],2*t.b[e],t.c[e]),s=0;s<a.length;s+=1)if(a[s]>0&&a[s]<1){var o=t.point(a[s])[e];o<r?r=o:o>i&&(i=o)}return{min:r,max:i}}function intersectData(t,e,r){var i=t.boundingBox();return{cx:i.cx,cy:i.cy,width:i.width,height:i.height,bez:t,t:(e+r)/2,t1:e,t2:r}}function splitData(t){var e=t.bez.split(.5);return[intersectData(e[0],t.t1,t.t),intersectData(e[1],t.t,t.t2)]}function boxIntersect(t,e){return 2*Math.abs(t.cx-e.cx)<t.width+e.width&&2*Math.abs(t.cy-e.cy)<t.height+e.height}function intersectsImpl(t,e,r,i,n,a){if(boxIntersect(t,e))if(r>=a||t.width<=i&&t.height<=i&&e.width<=i&&e.height<=i)n.push([t.t,e.t]);else{var s=splitData(t),o=splitData(e);intersectsImpl(s[0],o[0],r+1,i,n,a),intersectsImpl(s[0],o[1],r+1,i,n,a),intersectsImpl(s[1],o[0],r+1,i,n,a),intersectsImpl(s[1],o[1],r+1,i,n,a)}}function crossProduct(t,e){return[t[1]*e[2]-t[2]*e[1],t[2]*e[0]-t[0]*e[2],t[0]*e[1]-t[1]*e[0]]}function lineIntersection(t,e,r,i){var n=[t[0],t[1],1],a=[e[0],e[1],1],s=[r[0],r[1],1],o=[i[0],i[1],1],l=crossProduct(crossProduct(n,a),crossProduct(s,o));return floatZero(l[2])?null:[l[0]/l[2],l[1]/l[2]]}function polarOffset(t,e,r){return[t[0]+Math.cos(e)*r,t[1]-Math.sin(e)*r]}function pointDistance(t,e){return Math.hypot(t[0]-e[0],t[1]-e[1])}function pointEqual(t,e){return floatEqual(t[0],e[0])&&floatEqual(t[1],e[1])}function ZigZagModifier(){}function setPoint(t,e,r,i,n,a,s){var o=r-Math.PI/2,l=r+Math.PI/2,h=e[0]+Math.cos(r)*i*n,p=e[1]-Math.sin(r)*i*n;t.setTripleAt(h,p,h+Math.cos(o)*a,p-Math.sin(o)*a,h+Math.cos(l)*s,p-Math.sin(l)*s,t.length())}function getPerpendicularVector(t,e){var r=[e[0]-t[0],e[1]-t[1]],i=.5*-Math.PI;return[Math.cos(i)*r[0]-Math.sin(i)*r[1],Math.sin(i)*r[0]+Math.cos(i)*r[1]]}function getProjectingAngle(t,e){var r=0===e?t.length()-1:e-1,i=(e+1)%t.length(),n=getPerpendicularVector(t.v[r],t.v[i]);return Math.atan2(0,1)-Math.atan2(n[1],n[0])}function zigZagCorner(t,e,r,i,n,a,s){var o=getProjectingAngle(e,r),l=e.v[r%e._length],h=e.v[0===r?e._length-1:r-1],p=e.v[(r+1)%e._length],c=2===a?Math.sqrt(Math.pow(l[0]-h[0],2)+Math.pow(l[1]-h[1],2)):0,f=2===a?Math.sqrt(Math.pow(l[0]-p[0],2)+Math.pow(l[1]-p[1],2)):0;setPoint(t,e.v[r%e._length],o,s,i,f/(2*(n+1)),c/(2*(n+1)),a)}function zigZagSegment(t,e,r,i,n,a){for(var s=0;s<i;s+=1){var o=(s+1)/(i+1),l=2===n?Math.sqrt(Math.pow(e.points[3][0]-e.points[0][0],2)+Math.pow(e.points[3][1]-e.points[0][1],2)):0,h=e.normalAngle(o);setPoint(t,e.point(o),h,a,r,l/(2*(i+1)),l/(2*(i+1)),n),a=-a}return a}function linearOffset(t,e,r){var i=Math.atan2(e[0]-t[0],e[1]-t[1]);return[polarOffset(t,i,r),polarOffset(e,i,r)]}function offsetSegment(t,e){var r,i,n,a,s,o,l;r=(l=linearOffset(t.points[0],t.points[1],e))[0],i=l[1],n=(l=linearOffset(t.points[1],t.points[2],e))[0],a=l[1],s=(l=linearOffset(t.points[2],t.points[3],e))[0],o=l[1];var h=lineIntersection(r,i,n,a);null===h&&(h=i);var p=lineIntersection(s,o,n,a);return null===p&&(p=s),new PolynomialBezier(r,h,p,o)}function joinLines(t,e,r,i,n){var a=e.points[3],s=r.points[0];if(3===i)return a;if(pointEqual(a,s))return a;if(2===i){var o=-e.tangentAngle(1),l=-r.tangentAngle(0)+Math.PI,h=lineIntersection(a,polarOffset(a,o+Math.PI/2,100),s,polarOffset(s,o+Math.PI/2,100)),p=h?pointDistance(h,a):pointDistance(a,s)/2,c=polarOffset(a,o,2*p*roundCorner);return t.setXYAt(c[0],c[1],"o",t.length()-1),c=polarOffset(s,l,2*p*roundCorner),t.setTripleAt(s[0],s[1],s[0],s[1],c[0],c[1],t.length()),s}var f=lineIntersection(pointEqual(a,e.points[2])?e.points[0]:e.points[2],a,s,pointEqual(s,r.points[1])?r.points[3]:r.points[1]);return f&&pointDistance(f,a)<n?(t.setTripleAt(f[0],f[1],f[0],f[1],f[0],f[1],t.length()),f):a}function getIntersection(t,e){var r=t.intersections(e);return r.length&&floatEqual(r[0][0],1)&&r.shift(),r.length?r[0]:null}function pruneSegmentIntersection(t,e){var r=t.slice(),i=e.slice(),n=getIntersection(t[t.length-1],e[0]);return n&&(r[t.length-1]=t[t.length-1].split(n[0])[0],i[0]=e[0].split(n[1])[1]),t.length>1&&e.length>1&&(n=getIntersection(t[0],e[e.length-1]))?[[t[0].split(n[0])[0]],[e[e.length-1].split(n[1])[1]]]:[r,i]}function pruneIntersections(t){for(var e,r=1;r<t.length;r+=1)e=pruneSegmentIntersection(t[r-1],t[r]),t[r-1]=e[0],t[r]=e[1];return t.length>1&&(e=pruneSegmentIntersection(t[t.length-1],t[0]),t[t.length-1]=e[0],t[0]=e[1]),t}function offsetSegmentSplit(t,e){var r,i,n,a,s=t.inflectionPoints();if(0===s.length)return[offsetSegment(t,e)];if(1===s.length||floatEqual(s[1],1))return r=(n=t.split(s[0]))[0],i=n[1],[offsetSegment(r,e),offsetSegment(i,e)];r=(n=t.split(s[0]))[0];var o=(s[1]-s[0])/(1-s[0]);return a=(n=n[1].split(o))[0],i=n[1],[offsetSegment(r,e),offsetSegment(a,e),offsetSegment(i,e)]}function OffsetPathModifier(){}function getFontProperties(t){for(var e=t.fStyle?t.fStyle.split(" "):[],r="normal",i="normal",n=e.length,a=0;a<n;a+=1)switch(e[a].toLowerCase()){case"italic":i="italic";break;case"bold":r="700";break;case"black":r="900";break;case"medium":r="500";break;case"regular":case"normal":r="400";break;case"light":case"thin":r="200"}return{style:i,weight:t.fWeight||r}}extendPrototype([ShapeModifier],RepeaterModifier),RepeaterModifier.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.c=PropertyFactory.getProp(t,e.c,0,null,this),this.o=PropertyFactory.getProp(t,e.o,0,null,this),this.tr=TransformPropertyFactory.getTransformProperty(t,e.tr,this),this.so=PropertyFactory.getProp(t,e.tr.so,0,.01,this),this.eo=PropertyFactory.getProp(t,e.tr.eo,0,.01,this),this.data=e,this.dynamicProperties.length||this.getValue(!0),this._isAnimated=!!this.dynamicProperties.length,this.pMatrix=new Matrix,this.rMatrix=new Matrix,this.sMatrix=new Matrix,this.tMatrix=new Matrix,this.matrix=new Matrix},RepeaterModifier.prototype.applyTransforms=function(t,e,r,i,n,a){var s=a?-1:1,o=i.s.v[0]+(1-i.s.v[0])*(1-n),l=i.s.v[1]+(1-i.s.v[1])*(1-n);t.translate(i.p.v[0]*s*n,i.p.v[1]*s*n,i.p.v[2]),e.translate(-i.a.v[0],-i.a.v[1],i.a.v[2]),e.rotate(-i.r.v*s*n),e.translate(i.a.v[0],i.a.v[1],i.a.v[2]),r.translate(-i.a.v[0],-i.a.v[1],i.a.v[2]),r.scale(a?1/o:o,a?1/l:l),r.translate(i.a.v[0],i.a.v[1],i.a.v[2])},RepeaterModifier.prototype.init=function(t,e,r,i){for(this.elem=t,this.arr=e,this.pos=r,this.elemsData=i,this._currentCopies=0,this._elements=[],this._groups=[],this.frameId=-1,this.initDynamicPropertyContainer(t),this.initModifierProperties(t,e[r]);r>0;)r-=1,this._elements.unshift(e[r]);this.dynamicProperties.length?this.k=!0:this.getValue(!0)},RepeaterModifier.prototype.resetElements=function(t){var e,r=t.length;for(e=0;e<r;e+=1)t[e]._processed=!1,"gr"===t[e].ty&&this.resetElements(t[e].it)},RepeaterModifier.prototype.cloneElements=function(t){var e=JSON.parse(JSON.stringify(t));return this.resetElements(e),e},RepeaterModifier.prototype.changeGroupRender=function(t,e){var r,i=t.length;for(r=0;r<i;r+=1)t[r]._render=e,"gr"===t[r].ty&&this.changeGroupRender(t[r].it,e)},RepeaterModifier.prototype.processShapes=function(t){var e,r,i,n,a,s=!1;if(this._mdf||t){var o,l=Math.ceil(this.c.v);if(this._groups.length<l){for(;this._groups.length<l;){var h={it:this.cloneElements(this._elements),ty:"gr"};h.it.push({a:{a:0,ix:1,k:[0,0]},nm:"Transform",o:{a:0,ix:7,k:100},p:{a:0,ix:2,k:[0,0]},r:{a:1,ix:6,k:[{s:0,e:0,t:0},{s:0,e:0,t:1}]},s:{a:0,ix:3,k:[100,100]},sa:{a:0,ix:5,k:0},sk:{a:0,ix:4,k:0},ty:"tr"}),this.arr.splice(0,0,h),this._groups.splice(0,0,h),this._currentCopies+=1}this.elem.reloadShapes(),s=!0}for(a=0,i=0;i<=this._groups.length-1;i+=1){if(o=a<l,this._groups[i]._render=o,this.changeGroupRender(this._groups[i].it,o),!o){var p=this.elemsData[i].it,c=p[p.length-1];0!==c.transform.op.v?(c.transform.op._mdf=!0,c.transform.op.v=0):c.transform.op._mdf=!1}a+=1}this._currentCopies=l;var f=this.o.v,d=f%1,u=f>0?Math.floor(f):Math.ceil(f),m=this.pMatrix.props,y=this.rMatrix.props,g=this.sMatrix.props;this.pMatrix.reset(),this.rMatrix.reset(),this.sMatrix.reset(),this.tMatrix.reset(),this.matrix.reset();var v,b,_=0;if(f>0){for(;_<u;)this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!1),_+=1;d&&(this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,d,!1),_+=d)}else if(f<0){for(;_>u;)this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!0),_-=1;d&&(this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,-d,!0),_-=d)}for(i=1===this.data.m?0:this._currentCopies-1,n=1===this.data.m?1:-1,a=this._currentCopies;a;){if(b=(r=(e=this.elemsData[i].it)[e.length-1].transform.mProps.v.props).length,e[e.length-1].transform.mProps._mdf=!0,e[e.length-1].transform.op._mdf=!0,e[e.length-1].transform.op.v=1===this._currentCopies?this.so.v:this.so.v+(this.eo.v-this.so.v)*(i/(this._currentCopies-1)),0!==_){for((0!==i&&1===n||i!==this._currentCopies-1&&-1===n)&&this.applyTransforms(this.pMatrix,this.rMatrix,this.sMatrix,this.tr,1,!1),this.matrix.transform(y[0],y[1],y[2],y[3],y[4],y[5],y[6],y[7],y[8],y[9],y[10],y[11],y[12],y[13],y[14],y[15]),this.matrix.transform(g[0],g[1],g[2],g[3],g[4],g[5],g[6],g[7],g[8],g[9],g[10],g[11],g[12],g[13],g[14],g[15]),this.matrix.transform(m[0],m[1],m[2],m[3],m[4],m[5],m[6],m[7],m[8],m[9],m[10],m[11],m[12],m[13],m[14],m[15]),v=0;v<b;v+=1)r[v]=this.matrix.props[v];this.matrix.reset()}else for(this.matrix.reset(),v=0;v<b;v+=1)r[v]=this.matrix.props[v];_+=1,a-=1,i+=n}}else for(a=this._currentCopies,i=0,n=1;a;)r=(e=this.elemsData[i].it)[e.length-1].transform.mProps.v.props,e[e.length-1].transform.mProps._mdf=!1,e[e.length-1].transform.op._mdf=!1,a-=1,i+=n;return s},RepeaterModifier.prototype.addShape=function(){},extendPrototype([ShapeModifier],RoundCornersModifier),RoundCornersModifier.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.rd=PropertyFactory.getProp(t,e.r,0,null,this),this._isAnimated=!!this.rd.effectsSequence.length},RoundCornersModifier.prototype.processPath=function(t,e){var r,i=shapePool.newElement();i.c=t.c;var n,a,s,o,l,h,p,c,f,d,u,m,y=t._length,g=0;for(r=0;r<y;r+=1)n=t.v[r],s=t.o[r],a=t.i[r],n[0]===s[0]&&n[1]===s[1]&&n[0]===a[0]&&n[1]===a[1]?0!==r&&r!==y-1||t.c?(o=0===r?t.v[y-1]:t.v[r-1],h=(l=Math.sqrt(Math.pow(n[0]-o[0],2)+Math.pow(n[1]-o[1],2)))?Math.min(l/2,e)/l:0,p=u=n[0]+(o[0]-n[0])*h,c=m=n[1]-(n[1]-o[1])*h,f=p-(p-n[0])*roundCorner,d=c-(c-n[1])*roundCorner,i.setTripleAt(p,c,f,d,u,m,g),g+=1,o=r===y-1?t.v[0]:t.v[r+1],h=(l=Math.sqrt(Math.pow(n[0]-o[0],2)+Math.pow(n[1]-o[1],2)))?Math.min(l/2,e)/l:0,p=f=n[0]+(o[0]-n[0])*h,c=d=n[1]+(o[1]-n[1])*h,u=p-(p-n[0])*roundCorner,m=c-(c-n[1])*roundCorner,i.setTripleAt(p,c,f,d,u,m,g),g+=1):(i.setTripleAt(n[0],n[1],s[0],s[1],a[0],a[1],g),g+=1):(i.setTripleAt(t.v[r][0],t.v[r][1],t.o[r][0],t.o[r][1],t.i[r][0],t.i[r][1],g),g+=1);return i},RoundCornersModifier.prototype.processShapes=function(t){var e,r,i,n,a,s,o=this.shapes.length,l=this.rd.v;if(0!==l)for(r=0;r<o;r+=1){if(s=(a=this.shapes[r]).localShapeCollection,a.shape._mdf||this._mdf||t)for(s.releaseShapes(),a.shape._mdf=!0,e=a.shape.paths.shapes,n=a.shape.paths._length,i=0;i<n;i+=1)s.addShape(this.processPath(e[i],l));a.shape.paths=a.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)},PolynomialBezier.prototype.point=function(t){return[((this.a[0]*t+this.b[0])*t+this.c[0])*t+this.d[0],((this.a[1]*t+this.b[1])*t+this.c[1])*t+this.d[1]]},PolynomialBezier.prototype.derivative=function(t){return[(3*t*this.a[0]+2*this.b[0])*t+this.c[0],(3*t*this.a[1]+2*this.b[1])*t+this.c[1]]},PolynomialBezier.prototype.tangentAngle=function(t){var e=this.derivative(t);return Math.atan2(e[1],e[0])},PolynomialBezier.prototype.normalAngle=function(t){var e=this.derivative(t);return Math.atan2(e[0],e[1])},PolynomialBezier.prototype.inflectionPoints=function(){var t=this.a[1]*this.b[0]-this.a[0]*this.b[1];if(floatZero(t))return[];var e=-.5*(this.a[1]*this.c[0]-this.a[0]*this.c[1])/t,r=e*e-1/3*(this.b[1]*this.c[0]-this.b[0]*this.c[1])/t;if(r<0)return[];var i=Math.sqrt(r);return floatZero(i)?i>0&&i<1?[e]:[]:[e-i,e+i].filter((function(t){return t>0&&t<1}))},PolynomialBezier.prototype.split=function(t){if(t<=0)return[singlePoint(this.points[0]),this];if(t>=1)return[this,singlePoint(this.points[this.points.length-1])];var e=lerpPoint(this.points[0],this.points[1],t),r=lerpPoint(this.points[1],this.points[2],t),i=lerpPoint(this.points[2],this.points[3],t),n=lerpPoint(e,r,t),a=lerpPoint(r,i,t),s=lerpPoint(n,a,t);return[new PolynomialBezier(this.points[0],e,n,s,!0),new PolynomialBezier(s,a,i,this.points[3],!0)]},PolynomialBezier.prototype.bounds=function(){return{x:extrema(this,0),y:extrema(this,1)}},PolynomialBezier.prototype.boundingBox=function(){var t=this.bounds();return{left:t.x.min,right:t.x.max,top:t.y.min,bottom:t.y.max,width:t.x.max-t.x.min,height:t.y.max-t.y.min,cx:(t.x.max+t.x.min)/2,cy:(t.y.max+t.y.min)/2}},PolynomialBezier.prototype.intersections=function(t,e,r){void 0===e&&(e=2),void 0===r&&(r=7);var i=[];return intersectsImpl(intersectData(this,0,1),intersectData(t,0,1),0,e,i,r),i},PolynomialBezier.shapeSegment=function(t,e){var r=(e+1)%t.length();return new PolynomialBezier(t.v[e],t.o[e],t.i[r],t.v[r],!0)},PolynomialBezier.shapeSegmentInverted=function(t,e){var r=(e+1)%t.length();return new PolynomialBezier(t.v[r],t.i[r],t.o[e],t.v[e],!0)},extendPrototype([ShapeModifier],ZigZagModifier),ZigZagModifier.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.amplitude=PropertyFactory.getProp(t,e.s,0,null,this),this.frequency=PropertyFactory.getProp(t,e.r,0,null,this),this.pointsType=PropertyFactory.getProp(t,e.pt,0,null,this),this._isAnimated=0!==this.amplitude.effectsSequence.length||0!==this.frequency.effectsSequence.length||0!==this.pointsType.effectsSequence.length},ZigZagModifier.prototype.processPath=function(t,e,r,i){var n=t._length,a=shapePool.newElement();if(a.c=t.c,t.c||(n-=1),0===n)return a;var s=-1,o=PolynomialBezier.shapeSegment(t,0);zigZagCorner(a,t,0,e,r,i,s);for(var l=0;l<n;l+=1)s=zigZagSegment(a,o,e,r,i,-s),o=l!==n-1||t.c?PolynomialBezier.shapeSegment(t,(l+1)%n):null,zigZagCorner(a,t,l+1,e,r,i,s);return a},ZigZagModifier.prototype.processShapes=function(t){var e,r,i,n,a,s,o=this.shapes.length,l=this.amplitude.v,h=Math.max(0,Math.round(this.frequency.v)),p=this.pointsType.v;if(0!==l)for(r=0;r<o;r+=1){if(s=(a=this.shapes[r]).localShapeCollection,a.shape._mdf||this._mdf||t)for(s.releaseShapes(),a.shape._mdf=!0,e=a.shape.paths.shapes,n=a.shape.paths._length,i=0;i<n;i+=1)s.addShape(this.processPath(e[i],l,h,p));a.shape.paths=a.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)},extendPrototype([ShapeModifier],OffsetPathModifier),OffsetPathModifier.prototype.initModifierProperties=function(t,e){this.getValue=this.processKeys,this.amount=PropertyFactory.getProp(t,e.a,0,null,this),this.miterLimit=PropertyFactory.getProp(t,e.ml,0,null,this),this.lineJoin=e.lj,this._isAnimated=0!==this.amount.effectsSequence.length},OffsetPathModifier.prototype.processPath=function(t,e,r,i){var n=shapePool.newElement();n.c=t.c;var a,s,o,l=t.length();t.c||(l-=1);var h=[];for(a=0;a<l;a+=1)o=PolynomialBezier.shapeSegment(t,a),h.push(offsetSegmentSplit(o,e));if(!t.c)for(a=l-1;a>=0;a-=1)o=PolynomialBezier.shapeSegmentInverted(t,a),h.push(offsetSegmentSplit(o,e));h=pruneIntersections(h);var p=null,c=null;for(a=0;a<h.length;a+=1){var f=h[a];for(c&&(p=joinLines(n,c,f[0],r,i)),c=f[f.length-1],s=0;s<f.length;s+=1)o=f[s],p&&pointEqual(o.points[0],p)?n.setXYAt(o.points[1][0],o.points[1][1],"o",n.length()-1):n.setTripleAt(o.points[0][0],o.points[0][1],o.points[1][0],o.points[1][1],o.points[0][0],o.points[0][1],n.length()),n.setTripleAt(o.points[3][0],o.points[3][1],o.points[3][0],o.points[3][1],o.points[2][0],o.points[2][1],n.length()),p=o.points[3]}return h.length&&joinLines(n,c,h[0][0],r,i),n},OffsetPathModifier.prototype.processShapes=function(t){var e,r,i,n,a,s,o=this.shapes.length,l=this.amount.v,h=this.miterLimit.v,p=this.lineJoin;if(0!==l)for(r=0;r<o;r+=1){if(s=(a=this.shapes[r]).localShapeCollection,a.shape._mdf||this._mdf||t)for(s.releaseShapes(),a.shape._mdf=!0,e=a.shape.paths.shapes,n=a.shape.paths._length,i=0;i<n;i+=1)s.addShape(this.processPath(e[i],l,p,h));a.shape.paths=a.localShapeCollection}this.dynamicProperties.length||(this._mdf=!1)};var FontManager=function(){var t={w:0,size:0,shapes:[],data:{shapes:[]}},e=[];e=e.concat([2304,2305,2306,2307,2362,2363,2364,2364,2366,2367,2368,2369,2370,2371,2372,2373,2374,2375,2376,2377,2378,2379,2380,2381,2382,2383,2387,2388,2389,2390,2391,2402,2403]);var r=["d83cdffb","d83cdffc","d83cdffd","d83cdffe","d83cdfff"],i=[65039,8205];function n(t,e){var r=createTag("span");r.setAttribute("aria-hidden",!0),r.style.fontFamily=e;var i=createTag("span");i.innerText="giItT1WQy@!-/#",r.style.position="absolute",r.style.left="-10000px",r.style.top="-10000px",r.style.fontSize="300px",r.style.fontVariant="normal",r.style.fontStyle="normal",r.style.fontWeight="normal",r.style.letterSpacing="0",r.appendChild(i),document.body.appendChild(r);var n=i.offsetWidth;return i.style.fontFamily=function(t){var e,r=t.split(","),i=r.length,n=[];for(e=0;e<i;e+=1)"sans-serif"!==r[e]&&"monospace"!==r[e]&&n.push(r[e]);return n.join(",")}(t)+", "+e,{node:i,w:n,parent:r}}function a(t,e){var r,i=document.body&&e?"svg":"canvas",n=getFontProperties(t);if("svg"===i){var a=createNS("text");a.style.fontSize="100px",a.setAttribute("font-family",t.fFamily),a.setAttribute("font-style",n.style),a.setAttribute("font-weight",n.weight),a.textContent="1",t.fClass?(a.style.fontFamily="inherit",a.setAttribute("class",t.fClass)):a.style.fontFamily=t.fFamily,e.appendChild(a),r=a}else{var s=new OffscreenCanvas(500,500).getContext("2d");s.font=n.style+" "+n.weight+" 100px "+t.fFamily,r=s}return{measureText:function(t){return"svg"===i?(r.textContent=t,r.getComputedTextLength()):r.measureText(t).width}}}var s=function(){this.fonts=[],this.chars=null,this.typekitLoaded=0,this.isLoaded=!1,this._warned=!1,this.initTime=Date.now(),this.setIsLoadedBinded=this.setIsLoaded.bind(this),this.checkLoadedFontsBinded=this.checkLoadedFonts.bind(this)};s.isModifier=function(t,e){var i=t.toString(16)+e.toString(16);return-1!==r.indexOf(i)},s.isZeroWidthJoiner=function(t,e){return e?t===i[0]&&e===i[1]:t===i[1]},s.isCombinedCharacter=function(t){return-1!==e.indexOf(t)};var o={addChars:function(t){if(t){var e;this.chars||(this.chars=[]);var r,i,n=t.length,a=this.chars.length;for(e=0;e<n;e+=1){for(r=0,i=!1;r<a;)this.chars[r].style===t[e].style&&this.chars[r].fFamily===t[e].fFamily&&this.chars[r].ch===t[e].ch&&(i=!0),r+=1;i||(this.chars.push(t[e]),a+=1)}}},addFonts:function(t,e){if(t){if(this.chars)return this.isLoaded=!0,void(this.fonts=t.list);if(!document.body)return this.isLoaded=!0,t.list.forEach((function(t){t.helper=a(t),t.cache={}})),void(this.fonts=t.list);var r,i=t.list,s=i.length,o=s;for(r=0;r<s;r+=1){var l,h,p=!0;if(i[r].loaded=!1,i[r].monoCase=n(i[r].fFamily,"monospace"),i[r].sansCase=n(i[r].fFamily,"sans-serif"),i[r].fPath){if("p"===i[r].fOrigin||3===i[r].origin){if((l=document.querySelectorAll('style[f-forigin="p"][f-family="'+i[r].fFamily+'"], style[f-origin="3"][f-family="'+i[r].fFamily+'"]')).length>0&&(p=!1),p){var c=createTag("style");c.setAttribute("f-forigin",i[r].fOrigin),c.setAttribute("f-origin",i[r].origin),c.setAttribute("f-family",i[r].fFamily),c.type="text/css",c.innerText="@font-face {font-family: "+i[r].fFamily+"; font-style: normal; src: url('"+i[r].fPath+"');}",e.appendChild(c)}}else if("g"===i[r].fOrigin||1===i[r].origin){for(l=document.querySelectorAll('link[f-forigin="g"], link[f-origin="1"]'),h=0;h<l.length;h+=1)-1!==l[h].href.indexOf(i[r].fPath)&&(p=!1);if(p){var f=createTag("link");f.setAttribute("f-forigin",i[r].fOrigin),f.setAttribute("f-origin",i[r].origin),f.type="text/css",f.rel="stylesheet",f.href=i[r].fPath,document.body.appendChild(f)}}else if("t"===i[r].fOrigin||2===i[r].origin){for(l=document.querySelectorAll('script[f-forigin="t"], script[f-origin="2"]'),h=0;h<l.length;h+=1)i[r].fPath===l[h].src&&(p=!1);if(p){var d=createTag("link");d.setAttribute("f-forigin",i[r].fOrigin),d.setAttribute("f-origin",i[r].origin),d.setAttribute("rel","stylesheet"),d.setAttribute("href",i[r].fPath),e.appendChild(d)}}}else i[r].loaded=!0,o-=1;i[r].helper=a(i[r],e),i[r].cache={},this.fonts.push(i[r])}0===o?this.isLoaded=!0:setTimeout(this.checkLoadedFonts.bind(this),100)}else this.isLoaded=!0},getCharData:function(e,r,i){for(var n=0,a=this.chars.length;n<a;){if(this.chars[n].ch===e&&this.chars[n].style===r&&this.chars[n].fFamily===i)return this.chars[n];n+=1}return("string"==typeof e&&13!==e.charCodeAt(0)||!e)&&console&&console.warn&&!this._warned&&(this._warned=!0,console.warn("Missing character from exported characters list: ",e,r,i)),t},getFontByName:function(t){for(var e=0,r=this.fonts.length;e<r;){if(this.fonts[e].fName===t)return this.fonts[e];e+=1}return this.fonts[0]},measureText:function(t,e,r){var i=this.getFontByName(e),n=t.charCodeAt(0);if(!i.cache[n+1]){var a=i.helper;if(" "===t){var s=a.measureText("|"+t+"|"),o=a.measureText("||");i.cache[n+1]=(s-o)/100}else i.cache[n+1]=a.measureText(t)/100}return i.cache[n+1]*r},checkLoadedFonts:function(){var t,e,r,i=this.fonts.length,n=i;for(t=0;t<i;t+=1)this.fonts[t].loaded?n-=1:"n"===this.fonts[t].fOrigin||0===this.fonts[t].origin?this.fonts[t].loaded=!0:(e=this.fonts[t].monoCase.node,r=this.fonts[t].monoCase.w,e.offsetWidth!==r?(n-=1,this.fonts[t].loaded=!0):(e=this.fonts[t].sansCase.node,r=this.fonts[t].sansCase.w,e.offsetWidth!==r&&(n-=1,this.fonts[t].loaded=!0)),this.fonts[t].loaded&&(this.fonts[t].sansCase.parent.parentNode.removeChild(this.fonts[t].sansCase.parent),this.fonts[t].monoCase.parent.parentNode.removeChild(this.fonts[t].monoCase.parent)));0!==n&&Date.now()-this.initTime<5e3?setTimeout(this.checkLoadedFontsBinded,20):setTimeout(this.setIsLoadedBinded,10)},setIsLoaded:function(){this.isLoaded=!0}};return s.prototype=o,s}();function RenderableElement(){}RenderableElement.prototype={initRenderable:function(){this.isInRange=!1,this.hidden=!1,this.isTransparent=!1,this.renderableComponents=[]},addRenderableComponent:function(t){-1===this.renderableComponents.indexOf(t)&&this.renderableComponents.push(t)},removeRenderableComponent:function(t){-1!==this.renderableComponents.indexOf(t)&&this.renderableComponents.splice(this.renderableComponents.indexOf(t),1)},prepareRenderableFrame:function(t){this.checkLayerLimits(t)},checkTransparency:function(){this.finalTransform.mProp.o.v<=0?!this.isTransparent&&this.globalData.renderConfig.hideOnTransparent&&(this.isTransparent=!0,this.hide()):this.isTransparent&&(this.isTransparent=!1,this.show())},checkLayerLimits:function(t){this.data.ip-this.data.st<=t&&this.data.op-this.data.st>t?!0!==this.isInRange&&(this.globalData._mdf=!0,this._mdf=!0,this.isInRange=!0,this.show()):!1!==this.isInRange&&(this.globalData._mdf=!0,this.isInRange=!1,this.hide())},renderRenderable:function(){var t,e=this.renderableComponents.length;for(t=0;t<e;t+=1)this.renderableComponents[t].renderFrame(this._isFirstFrame)},sourceRectAtTime:function(){return{top:0,left:0,width:100,height:100}},getLayerSize:function(){return 5===this.data.ty?{w:this.data.textData.width,h:this.data.textData.height}:{w:this.data.width,h:this.data.height}}};var getBlendMode=(blendModeEnums={0:"source-over",1:"multiply",2:"screen",3:"overlay",4:"darken",5:"lighten",6:"color-dodge",7:"color-burn",8:"hard-light",9:"soft-light",10:"difference",11:"exclusion",12:"hue",13:"saturation",14:"color",15:"luminosity"},function(t){return blendModeEnums[t]||""}),blendModeEnums;function SliderEffect(t,e,r){this.p=PropertyFactory.getProp(e,t.v,0,0,r)}function AngleEffect(t,e,r){this.p=PropertyFactory.getProp(e,t.v,0,0,r)}function ColorEffect(t,e,r){this.p=PropertyFactory.getProp(e,t.v,1,0,r)}function PointEffect(t,e,r){this.p=PropertyFactory.getProp(e,t.v,1,0,r)}function LayerIndexEffect(t,e,r){this.p=PropertyFactory.getProp(e,t.v,0,0,r)}function MaskIndexEffect(t,e,r){this.p=PropertyFactory.getProp(e,t.v,0,0,r)}function CheckboxEffect(t,e,r){this.p=PropertyFactory.getProp(e,t.v,0,0,r)}function NoValueEffect(){this.p={}}function EffectsManager(t,e){var r,i=t.ef||[];this.effectElements=[];var n,a=i.length;for(r=0;r<a;r+=1)n=new GroupEffect(i[r],e),this.effectElements.push(n)}function GroupEffect(t,e){this.init(t,e)}function BaseElement(){}function FrameElement(){}function FootageElement(t,e,r){this.initFrame(),this.initRenderable(),this.assetData=e.getAssetData(t.refId),this.footageData=e.imageLoader.getAsset(this.assetData),this.initBaseData(t,e,r)}function AudioElement(t,e,r){this.initFrame(),this.initRenderable(),this.assetData=e.getAssetData(t.refId),this.initBaseData(t,e,r),this._isPlaying=!1,this._canPlay=!1;var i=this.globalData.getAssetsPath(this.assetData);this.audio=this.globalData.audioController.createAudio(i),this._currentTime=0,this.globalData.audioController.addAudio(this),this._volumeMultiplier=1,this._volume=1,this._previousVolume=null,this.tm=t.tm?PropertyFactory.getProp(this,t.tm,0,e.frameRate,this):{_placeholder:!0},this.lv=PropertyFactory.getProp(this,t.au&&t.au.lv?t.au.lv:{k:[100]},1,.01,this)}function BaseRenderer(){}function TransformElement(){}function MaskElement(t,e,r){this.data=t,this.element=e,this.globalData=r,this.storedData=[],this.masksProperties=this.data.masksProperties||[],this.maskElement=null;var i,n,a=this.globalData.defs,s=this.masksProperties?this.masksProperties.length:0;this.viewData=createSizedArray(s),this.solidPath="";var o,l,h,p,c,f,d=this.masksProperties,u=0,m=[],y=createElementID(),g="clipPath",v="clip-path";for(i=0;i<s;i+=1)if(("a"!==d[i].mode&&"n"!==d[i].mode||d[i].inv||100!==d[i].o.k||d[i].o.x)&&(g="mask",v="mask"),"s"!==d[i].mode&&"i"!==d[i].mode||0!==u?h=null:((h=createNS("rect")).setAttribute("fill","#ffffff"),h.setAttribute("width",this.element.comp.data.w||0),h.setAttribute("height",this.element.comp.data.h||0),m.push(h)),n=createNS("path"),"n"===d[i].mode)this.viewData[i]={op:PropertyFactory.getProp(this.element,d[i].o,0,.01,this.element),prop:ShapePropertyFactory.getShapeProp(this.element,d[i],3),elem:n,lastPath:""},a.appendChild(n);else{var b;if(u+=1,n.setAttribute("fill","s"===d[i].mode?"#000000":"#ffffff"),n.setAttribute("clip-rule","nonzero"),0!==d[i].x.k?(g="mask",v="mask",f=PropertyFactory.getProp(this.element,d[i].x,0,null,this.element),b=createElementID(),(p=createNS("filter")).setAttribute("id",b),(c=createNS("feMorphology")).setAttribute("operator","erode"),c.setAttribute("in","SourceGraphic"),c.setAttribute("radius","0"),p.appendChild(c),a.appendChild(p),n.setAttribute("stroke","s"===d[i].mode?"#000000":"#ffffff")):(c=null,f=null),this.storedData[i]={elem:n,x:f,expan:c,lastPath:"",lastOperator:"",filterId:b,lastRadius:0},"i"===d[i].mode){l=m.length;var _=createNS("g");for(o=0;o<l;o+=1)_.appendChild(m[o]);var P=createNS("mask");P.setAttribute("mask-type","alpha"),P.setAttribute("id",y+"_"+u),P.appendChild(n),a.appendChild(P),_.setAttribute("mask","url("+getLocationHref()+"#"+y+"_"+u+")"),m.length=0,m.push(_)}else m.push(n);d[i].inv&&!this.solidPath&&(this.solidPath=this.createLayerSolidPath()),this.viewData[i]={elem:n,lastPath:"",op:PropertyFactory.getProp(this.element,d[i].o,0,.01,this.element),prop:ShapePropertyFactory.getShapeProp(this.element,d[i],3),invRect:h},this.viewData[i].prop.k||this.drawPath(d[i],this.viewData[i].prop.v,this.viewData[i])}for(this.maskElement=createNS(g),s=m.length,i=0;i<s;i+=1)this.maskElement.appendChild(m[i]);u>0&&(this.maskElement.setAttribute("id",y),this.element.maskedElement.setAttribute(v,"url("+getLocationHref()+"#"+y+")"),a.appendChild(this.maskElement)),this.viewData.length&&this.element.addRenderableComponent(this)}extendPrototype([DynamicPropertyContainer],GroupEffect),GroupEffect.prototype.getValue=GroupEffect.prototype.iterateDynamicProperties,GroupEffect.prototype.init=function(t,e){var r;this.data=t,this.effectElements=[],this.initDynamicPropertyContainer(e);var i,n=this.data.ef.length,a=this.data.ef;for(r=0;r<n;r+=1){switch(i=null,a[r].ty){case 0:i=new SliderEffect(a[r],e,this);break;case 1:i=new AngleEffect(a[r],e,this);break;case 2:i=new ColorEffect(a[r],e,this);break;case 3:i=new PointEffect(a[r],e,this);break;case 4:case 7:i=new CheckboxEffect(a[r],e,this);break;case 10:i=new LayerIndexEffect(a[r],e,this);break;case 11:i=new MaskIndexEffect(a[r],e,this);break;case 5:i=new EffectsManager(a[r],e,this);break;default:i=new NoValueEffect(a[r],e,this)}i&&this.effectElements.push(i)}},BaseElement.prototype={checkMasks:function(){if(!this.data.hasMask)return!1;for(var t=0,e=this.data.masksProperties.length;t<e;){if("n"!==this.data.masksProperties[t].mode&&!1!==this.data.masksProperties[t].cl)return!0;t+=1}return!1},initExpressions:function(){var t=getExpressionInterfaces();if(t){var e=t("layer"),r=t("effects"),i=t("shape"),n=t("text"),a=t("comp");this.layerInterface=e(this),this.data.hasMask&&this.maskManager&&this.layerInterface.registerMaskInterface(this.maskManager);var s=r.createEffectsInterface(this,this.layerInterface);this.layerInterface.registerEffectsInterface(s),0===this.data.ty||this.data.xt?this.compInterface=a(this):4===this.data.ty?(this.layerInterface.shapeInterface=i(this.shapesData,this.itemsData,this.layerInterface),this.layerInterface.content=this.layerInterface.shapeInterface):5===this.data.ty&&(this.layerInterface.textInterface=n(this),this.layerInterface.text=this.layerInterface.textInterface)}},setBlendMode:function(){var t=getBlendMode(this.data.bm);(this.baseElement||this.layerElement).style["mix-blend-mode"]=t},initBaseData:function(t,e,r){this.globalData=e,this.comp=r,this.data=t,this.layerId=createElementID(),this.data.sr||(this.data.sr=1),this.effectsManager=new EffectsManager(this.data,this,this.dynamicProperties)},getType:function(){return this.type},sourceRectAtTime:function(){}},FrameElement.prototype={initFrame:function(){this._isFirstFrame=!1,this.dynamicProperties=[],this._mdf=!1},prepareProperties:function(t,e){var r,i=this.dynamicProperties.length;for(r=0;r<i;r+=1)(e||this._isParent&&"transform"===this.dynamicProperties[r].propType)&&(this.dynamicProperties[r].getValue(),this.dynamicProperties[r]._mdf&&(this.globalData._mdf=!0,this._mdf=!0))},addDynamicProperty:function(t){-1===this.dynamicProperties.indexOf(t)&&this.dynamicProperties.push(t)}},FootageElement.prototype.prepareFrame=function(){},extendPrototype([RenderableElement,BaseElement,FrameElement],FootageElement),FootageElement.prototype.getBaseElement=function(){return null},FootageElement.prototype.renderFrame=function(){},FootageElement.prototype.destroy=function(){},FootageElement.prototype.initExpressions=function(){var t=getExpressionInterfaces();if(t){var e=t("footage");this.layerInterface=e(this)}},FootageElement.prototype.getFootageData=function(){return this.footageData},AudioElement.prototype.prepareFrame=function(t){if(this.prepareRenderableFrame(t,!0),this.prepareProperties(t,!0),this.tm._placeholder)this._currentTime=t/this.data.sr;else{var e=this.tm.v;this._currentTime=e}this._volume=this.lv.v[0];var r=this._volume*this._volumeMultiplier;this._previousVolume!==r&&(this._previousVolume=r,this.audio.volume(r))},extendPrototype([RenderableElement,BaseElement,FrameElement],AudioElement),AudioElement.prototype.renderFrame=function(){this.isInRange&&this._canPlay&&(this._isPlaying?(!this.audio.playing()||Math.abs(this._currentTime/this.globalData.frameRate-this.audio.seek())>.1)&&this.audio.seek(this._currentTime/this.globalData.frameRate):(this.audio.play(),this.audio.seek(this._currentTime/this.globalData.frameRate),this._isPlaying=!0))},AudioElement.prototype.show=function(){},AudioElement.prototype.hide=function(){this.audio.pause(),this._isPlaying=!1},AudioElement.prototype.pause=function(){this.audio.pause(),this._isPlaying=!1,this._canPlay=!1},AudioElement.prototype.resume=function(){this._canPlay=!0},AudioElement.prototype.setRate=function(t){this.audio.rate(t)},AudioElement.prototype.volume=function(t){this._volumeMultiplier=t,this._previousVolume=t*this._volume,this.audio.volume(this._previousVolume)},AudioElement.prototype.getBaseElement=function(){return null},AudioElement.prototype.destroy=function(){},AudioElement.prototype.sourceRectAtTime=function(){},AudioElement.prototype.initExpressions=function(){},BaseRenderer.prototype.checkLayers=function(t){var e,r,i=this.layers.length;for(this.completeLayers=!0,e=i-1;e>=0;e-=1)this.elements[e]||(r=this.layers[e]).ip-r.st<=t-this.layers[e].st&&r.op-r.st>t-this.layers[e].st&&this.buildItem(e),this.completeLayers=!!this.elements[e]&&this.completeLayers;this.checkPendingElements()},BaseRenderer.prototype.createItem=function(t){switch(t.ty){case 2:return this.createImage(t);case 0:return this.createComp(t);case 1:return this.createSolid(t);case 3:default:return this.createNull(t);case 4:return this.createShape(t);case 5:return this.createText(t);case 6:return this.createAudio(t);case 13:return this.createCamera(t);case 15:return this.createFootage(t)}},BaseRenderer.prototype.createCamera=function(){throw new Error("You're using a 3d camera. Try the html renderer.")},BaseRenderer.prototype.createAudio=function(t){return new AudioElement(t,this.globalData,this)},BaseRenderer.prototype.createFootage=function(t){return new FootageElement(t,this.globalData,this)},BaseRenderer.prototype.buildAllItems=function(){var t,e=this.layers.length;for(t=0;t<e;t+=1)this.buildItem(t);this.checkPendingElements()},BaseRenderer.prototype.includeLayers=function(t){var e;this.completeLayers=!1;var r,i=t.length,n=this.layers.length;for(e=0;e<i;e+=1)for(r=0;r<n;){if(this.layers[r].id===t[e].id){this.layers[r]=t[e];break}r+=1}},BaseRenderer.prototype.setProjectInterface=function(t){this.globalData.projectInterface=t},BaseRenderer.prototype.initItems=function(){this.globalData.progressiveLoad||this.buildAllItems()},BaseRenderer.prototype.buildElementParenting=function(t,e,r){for(var i=this.elements,n=this.layers,a=0,s=n.length;a<s;)n[a].ind==e&&(i[a]&&!0!==i[a]?(r.push(i[a]),i[a].setAsParent(),void 0!==n[a].parent?this.buildElementParenting(t,n[a].parent,r):t.setHierarchy(r)):(this.buildItem(a),this.addPendingElement(t))),a+=1},BaseRenderer.prototype.addPendingElement=function(t){this.pendingElements.push(t)},BaseRenderer.prototype.searchExtraCompositions=function(t){var e,r=t.length;for(e=0;e<r;e+=1)if(t[e].xt){var i=this.createComp(t[e]);i.initExpressions(),this.globalData.projectInterface.registerComposition(i)}},BaseRenderer.prototype.getElementByPath=function(t){var e,r=t.shift();if("number"==typeof r)e=this.elements[r];else{var i,n=this.elements.length;for(i=0;i<n;i+=1)if(this.elements[i].data.nm===r){e=this.elements[i];break}}return 0===t.length?e:e.getElementByPath(t)},BaseRenderer.prototype.setupGlobalData=function(t,e){this.globalData.fontManager=new FontManager,this.globalData.fontManager.addChars(t.chars),this.globalData.fontManager.addFonts(t.fonts,e),this.globalData.getAssetData=this.animationItem.getAssetData.bind(this.animationItem),this.globalData.getAssetsPath=this.animationItem.getAssetsPath.bind(this.animationItem),this.globalData.imageLoader=this.animationItem.imagePreloader,this.globalData.audioController=this.animationItem.audioController,this.globalData.frameId=0,this.globalData.frameRate=t.fr,this.globalData.nm=t.nm,this.globalData.compSize={w:t.w,h:t.h}},TransformElement.prototype={initTransform:function(){this.finalTransform={mProp:this.data.ks?TransformPropertyFactory.getTransformProperty(this,this.data.ks,this):{o:0},_matMdf:!1,_opMdf:!1,mat:new Matrix},this.data.ao&&(this.finalTransform.mProp.autoOriented=!0),this.data.ty},renderTransform:function(){if(this.finalTransform._opMdf=this.finalTransform.mProp.o._mdf||this._isFirstFrame,this.finalTransform._matMdf=this.finalTransform.mProp._mdf||this._isFirstFrame,this.hierarchy){var t,e=this.finalTransform.mat,r=0,i=this.hierarchy.length;if(!this.finalTransform._matMdf)for(;r<i;){if(this.hierarchy[r].finalTransform.mProp._mdf){this.finalTransform._matMdf=!0;break}r+=1}if(this.finalTransform._matMdf)for(t=this.finalTransform.mProp.v.props,e.cloneFromProps(t),r=0;r<i;r+=1)t=this.hierarchy[r].finalTransform.mProp.v.props,e.transform(t[0],t[1],t[2],t[3],t[4],t[5],t[6],t[7],t[8],t[9],t[10],t[11],t[12],t[13],t[14],t[15])}},globalToLocal:function(t){var e=[];e.push(this.finalTransform);for(var r,i=!0,n=this.comp;i;)n.finalTransform?(n.data.hasMask&&e.splice(0,0,n.finalTransform),n=n.comp):i=!1;var a,s=e.length;for(r=0;r<s;r+=1)a=e[r].mat.applyToPointArray(0,0,0),t=[t[0]-a[0],t[1]-a[1],0];return t},mHelper:new Matrix},MaskElement.prototype.getMaskProperty=function(t){return this.viewData[t].prop},MaskElement.prototype.renderFrame=function(t){var e,r=this.element.finalTransform.mat,i=this.masksProperties.length;for(e=0;e<i;e+=1)if((this.viewData[e].prop._mdf||t)&&this.drawPath(this.masksProperties[e],this.viewData[e].prop.v,this.viewData[e]),(this.viewData[e].op._mdf||t)&&this.viewData[e].elem.setAttribute("fill-opacity",this.viewData[e].op.v),"n"!==this.masksProperties[e].mode&&(this.viewData[e].invRect&&(this.element.finalTransform.mProp._mdf||t)&&this.viewData[e].invRect.setAttribute("transform",r.getInverseMatrix().to2dCSS()),this.storedData[e].x&&(this.storedData[e].x._mdf||t))){var n=this.storedData[e].expan;this.storedData[e].x.v<0?("erode"!==this.storedData[e].lastOperator&&(this.storedData[e].lastOperator="erode",this.storedData[e].elem.setAttribute("filter","url("+getLocationHref()+"#"+this.storedData[e].filterId+")")),n.setAttribute("radius",-this.storedData[e].x.v)):("dilate"!==this.storedData[e].lastOperator&&(this.storedData[e].lastOperator="dilate",this.storedData[e].elem.setAttribute("filter",null)),this.storedData[e].elem.setAttribute("stroke-width",2*this.storedData[e].x.v))}},MaskElement.prototype.getMaskelement=function(){return this.maskElement},MaskElement.prototype.createLayerSolidPath=function(){var t="M0,0 ";return t+=" h"+this.globalData.compSize.w,t+=" v"+this.globalData.compSize.h,(t+=" h-"+this.globalData.compSize.w)+" v-"+this.globalData.compSize.h+" "},MaskElement.prototype.drawPath=function(t,e,r){var i,n,a=" M"+e.v[0][0]+","+e.v[0][1];for(n=e._length,i=1;i<n;i+=1)a+=" C"+e.o[i-1][0]+","+e.o[i-1][1]+" "+e.i[i][0]+","+e.i[i][1]+" "+e.v[i][0]+","+e.v[i][1];if(e.c&&n>1&&(a+=" C"+e.o[i-1][0]+","+e.o[i-1][1]+" "+e.i[0][0]+","+e.i[0][1]+" "+e.v[0][0]+","+e.v[0][1]),r.lastPath!==a){var s="";r.elem&&(e.c&&(s=t.inv?this.solidPath+a:a),r.elem.setAttribute("d",s)),r.lastPath=a}},MaskElement.prototype.destroy=function(){this.element=null,this.globalData=null,this.maskElement=null,this.data=null,this.masksProperties=null};var filtersFactory=function(){var t={createFilter:function(t,e){var r=createNS("filter");return r.setAttribute("id",t),!0!==e&&(r.setAttribute("filterUnits","objectBoundingBox"),r.setAttribute("x","0%"),r.setAttribute("y","0%"),r.setAttribute("width","100%"),r.setAttribute("height","100%")),r},createAlphaToLuminanceFilter:function(){var t=createNS("feColorMatrix");return t.setAttribute("type","matrix"),t.setAttribute("color-interpolation-filters","sRGB"),t.setAttribute("values","0 0 0 1 0  0 0 0 1 0  0 0 0 1 0  0 0 0 1 1"),t}};return t}(),featureSupport=function(){var t={maskType:!0};return(/MSIE 10/i.test(navigator.userAgent)||/MSIE 9/i.test(navigator.userAgent)||/rv:11.0/i.test(navigator.userAgent)||/Edge\/\d./i.test(navigator.userAgent))&&(t.maskType=!1),t}(),registeredEffects={},idPrefix="filter_result_";function SVGEffects(t){var e,r,i="SourceGraphic",n=t.data.ef?t.data.ef.length:0,a=createElementID(),s=filtersFactory.createFilter(a,!0),o=0;for(this.filters=[],e=0;e<n;e+=1){r=null;var l=t.data.ef[e].ty;registeredEffects[l]&&(r=new(0,registeredEffects[l].effect)(s,t.effectsManager.effectElements[e],t,idPrefix+o,i),i=idPrefix+o,registeredEffects[l].countsAsEffect&&(o+=1)),r&&this.filters.push(r)}o&&(t.globalData.defs.appendChild(s),t.layerElement.setAttribute("filter","url("+getLocationHref()+"#"+a+")")),this.filters.length&&t.addRenderableComponent(this)}function registerEffect(t,e,r){registeredEffects[t]={effect:e,countsAsEffect:r}}function SVGBaseElement(){}function HierarchyElement(){}function RenderableDOMElement(){}function IImageElement(t,e,r){this.assetData=e.getAssetData(t.refId),this.initElement(t,e,r),this.sourceRect={top:0,left:0,width:this.assetData.w,height:this.assetData.h}}function ProcessedElement(t,e){this.elem=t,this.pos=e}function IShapeElement(){}SVGEffects.prototype.renderFrame=function(t){var e,r=this.filters.length;for(e=0;e<r;e+=1)this.filters[e].renderFrame(t)},SVGBaseElement.prototype={initRendererElement:function(){this.layerElement=createNS("g")},createContainerElements:function(){this.matteElement=createNS("g"),this.transformedElement=this.layerElement,this.maskedElement=this.layerElement,this._sizeChanged=!1;var t=null;if(this.data.td){this.matteMasks={};var e=createNS("symbol");e.setAttribute("id",this.layerId);var r=createNS("g");r.appendChild(this.layerElement),e.appendChild(r),t=r,this.globalData.defs.appendChild(e)}else this.data.tt?(this.matteElement.appendChild(this.layerElement),t=this.matteElement,this.baseElement=this.matteElement):this.baseElement=this.layerElement;if(this.data.ln&&this.layerElement.setAttribute("id",this.data.ln),this.data.cl&&this.layerElement.setAttribute("class",this.data.cl),0===this.data.ty&&!this.data.hd){var i=createNS("clipPath"),n=createNS("path");n.setAttribute("d","M0,0 L"+this.data.w+",0 L"+this.data.w+","+this.data.h+" L0,"+this.data.h+"z");var a=createElementID();if(i.setAttribute("id",a),i.appendChild(n),this.globalData.defs.appendChild(i),this.checkMasks()){var s=createNS("g");s.setAttribute("clip-path","url("+getLocationHref()+"#"+a+")"),s.appendChild(this.layerElement),this.transformedElement=s,t?t.appendChild(this.transformedElement):this.baseElement=this.transformedElement}else this.layerElement.setAttribute("clip-path","url("+getLocationHref()+"#"+a+")")}0!==this.data.bm&&this.setBlendMode()},renderElement:function(){this.finalTransform._matMdf&&this.transformedElement.setAttribute("transform",this.finalTransform.mat.to2dCSS()),this.finalTransform._opMdf&&this.transformedElement.setAttribute("opacity",this.finalTransform.mProp.o.v)},destroyBaseElement:function(){this.layerElement=null,this.matteElement=null,this.maskManager.destroy()},getBaseElement:function(){return this.data.hd?null:this.baseElement},createRenderableComponents:function(){this.maskManager=new MaskElement(this.data,this,this.globalData),this.renderableEffectsManager=new SVGEffects(this)},getMatte:function(t){if(!this.matteMasks[t]){var e,r,i,n,a=this.layerId+"_"+t;if(1===t||3===t){var s=createNS("mask");s.setAttribute("id",a),s.setAttribute("mask-type",3===t?"luminance":"alpha"),(i=createNS("use")).setAttributeNS("http://www.w3.org/1999/xlink","href","#"+this.layerId),s.appendChild(i),this.globalData.defs.appendChild(s),featureSupport.maskType||1!==t||(s.setAttribute("mask-type","luminance"),e=createElementID(),r=filtersFactory.createFilter(e),this.globalData.defs.appendChild(r),r.appendChild(filtersFactory.createAlphaToLuminanceFilter()),(n=createNS("g")).appendChild(i),s.appendChild(n),n.setAttribute("filter","url("+getLocationHref()+"#"+e+")"))}else if(2===t){var o=createNS("mask");o.setAttribute("id",a),o.setAttribute("mask-type","alpha");var l=createNS("g");o.appendChild(l),e=createElementID(),r=filtersFactory.createFilter(e);var h=createNS("feComponentTransfer");h.setAttribute("in","SourceGraphic"),r.appendChild(h);var p=createNS("feFuncA");p.setAttribute("type","table"),p.setAttribute("tableValues","1.0 0.0"),h.appendChild(p),this.globalData.defs.appendChild(r);var c=createNS("rect");c.setAttribute("width",this.comp.data.w),c.setAttribute("height",this.comp.data.h),c.setAttribute("x","0"),c.setAttribute("y","0"),c.setAttribute("fill","#ffffff"),c.setAttribute("opacity","0"),l.setAttribute("filter","url("+getLocationHref()+"#"+e+")"),l.appendChild(c),(i=createNS("use")).setAttributeNS("http://www.w3.org/1999/xlink","href","#"+this.layerId),l.appendChild(i),featureSupport.maskType||(o.setAttribute("mask-type","luminance"),r.appendChild(filtersFactory.createAlphaToLuminanceFilter()),n=createNS("g"),l.appendChild(c),n.appendChild(this.layerElement),l.appendChild(n)),this.globalData.defs.appendChild(o)}this.matteMasks[t]=a}return this.matteMasks[t]},setMatte:function(t){this.matteElement&&this.matteElement.setAttribute("mask","url("+getLocationHref()+"#"+t+")")}},HierarchyElement.prototype={initHierarchy:function(){this.hierarchy=[],this._isParent=!1,this.checkParenting()},setHierarchy:function(t){this.hierarchy=t},setAsParent:function(){this._isParent=!0},checkParenting:function(){void 0!==this.data.parent&&this.comp.buildElementParenting(this,this.data.parent,[])}},extendPrototype([RenderableElement,createProxyFunction({initElement:function(t,e,r){this.initFrame(),this.initBaseData(t,e,r),this.initTransform(t,e,r),this.initHierarchy(),this.initRenderable(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),this.createContent(),this.hide()},hide:function(){this.hidden||this.isInRange&&!this.isTransparent||((this.baseElement||this.layerElement).style.display="none",this.hidden=!0)},show:function(){this.isInRange&&!this.isTransparent&&(this.data.hd||((this.baseElement||this.layerElement).style.display="block"),this.hidden=!1,this._isFirstFrame=!0)},renderFrame:function(){this.data.hd||this.hidden||(this.renderTransform(),this.renderRenderable(),this.renderElement(),this.renderInnerContent(),this._isFirstFrame&&(this._isFirstFrame=!1))},renderInnerContent:function(){},prepareFrame:function(t){this._mdf=!1,this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange),this.checkTransparency()},destroy:function(){this.innerElem=null,this.destroyBaseElement()}})],RenderableDOMElement),extendPrototype([BaseElement,TransformElement,SVGBaseElement,HierarchyElement,FrameElement,RenderableDOMElement],IImageElement),IImageElement.prototype.createContent=function(){var t=this.globalData.getAssetsPath(this.assetData);this.innerElem=createNS("image"),this.innerElem.setAttribute("width",this.assetData.w+"px"),this.innerElem.setAttribute("height",this.assetData.h+"px"),this.innerElem.setAttribute("preserveAspectRatio",this.assetData.pr||this.globalData.renderConfig.imagePreserveAspectRatio),this.innerElem.setAttributeNS("http://www.w3.org/1999/xlink","href",t),this.layerElement.appendChild(this.innerElem)},IImageElement.prototype.sourceRectAtTime=function(){return this.sourceRect},IShapeElement.prototype={addShapeToModifiers:function(t){var e,r=this.shapeModifiers.length;for(e=0;e<r;e+=1)this.shapeModifiers[e].addShape(t)},isShapeInAnimatedModifiers:function(t){for(var e=this.shapeModifiers.length;0<e;)if(this.shapeModifiers[0].isAnimatedWithShape(t))return!0;return!1},renderModifiers:function(){if(this.shapeModifiers.length){var t,e=this.shapes.length;for(t=0;t<e;t+=1)this.shapes[t].sh.reset();for(t=(e=this.shapeModifiers.length)-1;t>=0&&!this.shapeModifiers[t].processShapes(this._isFirstFrame);t-=1);}},searchProcessedElement:function(t){for(var e=this.processedElements,r=0,i=e.length;r<i;){if(e[r].elem===t)return e[r].pos;r+=1}return 0},addProcessedElement:function(t,e){for(var r=this.processedElements,i=r.length;i;)if(r[i-=1].elem===t)return void(r[i].pos=e);r.push(new ProcessedElement(t,e))},prepareFrame:function(t){this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange)}};var lineCapEnum={1:"butt",2:"round",3:"square"},lineJoinEnum={1:"miter",2:"round",3:"bevel"};function SVGShapeData(t,e,r){this.caches=[],this.styles=[],this.transformers=t,this.lStr="",this.sh=r,this.lvl=e,this._isAnimated=!!r.k;for(var i=0,n=t.length;i<n;){if(t[i].mProps.dynamicProperties.length){this._isAnimated=!0;break}i+=1}}function SVGStyleData(t,e){this.data=t,this.type=t.ty,this.d="",this.lvl=e,this._mdf=!1,this.closed=!0===t.hd,this.pElem=createNS("path"),this.msElem=null}function DashProperty(t,e,r,i){var n;this.elem=t,this.frameId=-1,this.dataProps=createSizedArray(e.length),this.renderer=r,this.k=!1,this.dashStr="",this.dashArray=createTypedArray("float32",e.length?e.length-1:0),this.dashoffset=createTypedArray("float32",1),this.initDynamicPropertyContainer(i);var a,s=e.length||0;for(n=0;n<s;n+=1)a=PropertyFactory.getProp(t,e[n].v,0,0,this),this.k=a.k||this.k,this.dataProps[n]={n:e[n].n,p:a};this.k||this.getValue(!0),this._isAnimated=this.k}function SVGStrokeStyleData(t,e,r){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.o=PropertyFactory.getProp(t,e.o,0,.01,this),this.w=PropertyFactory.getProp(t,e.w,0,null,this),this.d=new DashProperty(t,e.d||{},"svg",this),this.c=PropertyFactory.getProp(t,e.c,1,255,this),this.style=r,this._isAnimated=!!this._isAnimated}function SVGFillStyleData(t,e,r){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.o=PropertyFactory.getProp(t,e.o,0,.01,this),this.c=PropertyFactory.getProp(t,e.c,1,255,this),this.style=r}function SVGNoStyleData(t,e,r){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.style=r}function GradientProperty(t,e,r){this.data=e,this.c=createTypedArray("uint8c",4*e.p);var i=e.k.k[0].s?e.k.k[0].s.length-4*e.p:e.k.k.length-4*e.p;this.o=createTypedArray("float32",i),this._cmdf=!1,this._omdf=!1,this._collapsable=this.checkCollapsable(),this._hasOpacity=i,this.initDynamicPropertyContainer(r),this.prop=PropertyFactory.getProp(t,e.k,1,null,this),this.k=this.prop.k,this.getValue(!0)}function SVGGradientFillStyleData(t,e,r){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.initGradientData(t,e,r)}function SVGGradientStrokeStyleData(t,e,r){this.initDynamicPropertyContainer(t),this.getValue=this.iterateDynamicProperties,this.w=PropertyFactory.getProp(t,e.w,0,null,this),this.d=new DashProperty(t,e.d||{},"svg",this),this.initGradientData(t,e,r),this._isAnimated=!!this._isAnimated}function ShapeGroupData(){this.it=[],this.prevViewData=[],this.gr=createNS("g")}function SVGTransformData(t,e,r){this.transform={mProps:t,op:e,container:r},this.elements=[],this._isAnimated=this.transform.mProps.dynamicProperties.length||this.transform.op.effectsSequence.length}SVGShapeData.prototype.setAsAnimated=function(){this._isAnimated=!0},SVGStyleData.prototype.reset=function(){this.d="",this._mdf=!1},DashProperty.prototype.getValue=function(t){if((this.elem.globalData.frameId!==this.frameId||t)&&(this.frameId=this.elem.globalData.frameId,this.iterateDynamicProperties(),this._mdf=this._mdf||t,this._mdf)){var e=0,r=this.dataProps.length;for("svg"===this.renderer&&(this.dashStr=""),e=0;e<r;e+=1)"o"!==this.dataProps[e].n?"svg"===this.renderer?this.dashStr+=" "+this.dataProps[e].p.v:this.dashArray[e]=this.dataProps[e].p.v:this.dashoffset[0]=this.dataProps[e].p.v}},extendPrototype([DynamicPropertyContainer],DashProperty),extendPrototype([DynamicPropertyContainer],SVGStrokeStyleData),extendPrototype([DynamicPropertyContainer],SVGFillStyleData),extendPrototype([DynamicPropertyContainer],SVGNoStyleData),GradientProperty.prototype.comparePoints=function(t,e){for(var r=0,i=this.o.length/2;r<i;){if(Math.abs(t[4*r]-t[4*e+2*r])>.01)return!1;r+=1}return!0},GradientProperty.prototype.checkCollapsable=function(){if(this.o.length/2!=this.c.length/4)return!1;if(this.data.k.k[0].s)for(var t=0,e=this.data.k.k.length;t<e;){if(!this.comparePoints(this.data.k.k[t].s,this.data.p))return!1;t+=1}else if(!this.comparePoints(this.data.k.k,this.data.p))return!1;return!0},GradientProperty.prototype.getValue=function(t){if(this.prop.getValue(),this._mdf=!1,this._cmdf=!1,this._omdf=!1,this.prop._mdf||t){var e,r,i,n=4*this.data.p;for(e=0;e<n;e+=1)r=e%4==0?100:255,i=Math.round(this.prop.v[e]*r),this.c[e]!==i&&(this.c[e]=i,this._cmdf=!t);if(this.o.length)for(n=this.prop.v.length,e=4*this.data.p;e<n;e+=1)r=e%2==0?100:1,i=e%2==0?Math.round(100*this.prop.v[e]):this.prop.v[e],this.o[e-4*this.data.p]!==i&&(this.o[e-4*this.data.p]=i,this._omdf=!t);this._mdf=!t}},extendPrototype([DynamicPropertyContainer],GradientProperty),SVGGradientFillStyleData.prototype.initGradientData=function(t,e,r){this.o=PropertyFactory.getProp(t,e.o,0,.01,this),this.s=PropertyFactory.getProp(t,e.s,1,null,this),this.e=PropertyFactory.getProp(t,e.e,1,null,this),this.h=PropertyFactory.getProp(t,e.h||{k:0},0,.01,this),this.a=PropertyFactory.getProp(t,e.a||{k:0},0,degToRads,this),this.g=new GradientProperty(t,e.g,this),this.style=r,this.stops=[],this.setGradientData(r.pElem,e),this.setGradientOpacity(e,r),this._isAnimated=!!this._isAnimated},SVGGradientFillStyleData.prototype.setGradientData=function(t,e){var r=createElementID(),i=createNS(1===e.t?"linearGradient":"radialGradient");i.setAttribute("id",r),i.setAttribute("spreadMethod","pad"),i.setAttribute("gradientUnits","userSpaceOnUse");var n,a,s,o=[];for(s=4*e.g.p,a=0;a<s;a+=4)n=createNS("stop"),i.appendChild(n),o.push(n);t.setAttribute("gf"===e.ty?"fill":"stroke","url("+getLocationHref()+"#"+r+")"),this.gf=i,this.cst=o},SVGGradientFillStyleData.prototype.setGradientOpacity=function(t,e){if(this.g._hasOpacity&&!this.g._collapsable){var r,i,n,a=createNS("mask"),s=createNS("path");a.appendChild(s);var o=createElementID(),l=createElementID();a.setAttribute("id",l);var h=createNS(1===t.t?"linearGradient":"radialGradient");h.setAttribute("id",o),h.setAttribute("spreadMethod","pad"),h.setAttribute("gradientUnits","userSpaceOnUse"),n=t.g.k.k[0].s?t.g.k.k[0].s.length:t.g.k.k.length;var p=this.stops;for(i=4*t.g.p;i<n;i+=2)(r=createNS("stop")).setAttribute("stop-color","rgb(255,255,255)"),h.appendChild(r),p.push(r);s.setAttribute("gf"===t.ty?"fill":"stroke","url("+getLocationHref()+"#"+o+")"),"gs"===t.ty&&(s.setAttribute("stroke-linecap",lineCapEnum[t.lc||2]),s.setAttribute("stroke-linejoin",lineJoinEnum[t.lj||2]),1===t.lj&&s.setAttribute("stroke-miterlimit",t.ml)),this.of=h,this.ms=a,this.ost=p,this.maskId=l,e.msElem=s}},extendPrototype([DynamicPropertyContainer],SVGGradientFillStyleData),extendPrototype([SVGGradientFillStyleData,DynamicPropertyContainer],SVGGradientStrokeStyleData);var buildShapeString=function(t,e,r,i){if(0===e)return"";var n,a=t.o,s=t.i,o=t.v,l=" M"+i.applyToPointStringified(o[0][0],o[0][1]);for(n=1;n<e;n+=1)l+=" C"+i.applyToPointStringified(a[n-1][0],a[n-1][1])+" "+i.applyToPointStringified(s[n][0],s[n][1])+" "+i.applyToPointStringified(o[n][0],o[n][1]);return r&&e&&(l+=" C"+i.applyToPointStringified(a[n-1][0],a[n-1][1])+" "+i.applyToPointStringified(s[0][0],s[0][1])+" "+i.applyToPointStringified(o[0][0],o[0][1]),l+="z"),l},SVGElementsRenderer=function(){var t=new Matrix,e=new Matrix;function r(t,e,r){(r||e.transform.op._mdf)&&e.transform.container.setAttribute("opacity",e.transform.op.v),(r||e.transform.mProps._mdf)&&e.transform.container.setAttribute("transform",e.transform.mProps.v.to2dCSS())}function i(){}function n(r,i,n){var a,s,o,l,h,p,c,f,d,u,m,y=i.styles.length,g=i.lvl;for(p=0;p<y;p+=1){if(l=i.sh._mdf||n,i.styles[p].lvl<g){for(f=e.reset(),u=g-i.styles[p].lvl,m=i.transformers.length-1;!l&&u>0;)l=i.transformers[m].mProps._mdf||l,u-=1,m-=1;if(l)for(u=g-i.styles[p].lvl,m=i.transformers.length-1;u>0;)d=i.transformers[m].mProps.v.props,f.transform(d[0],d[1],d[2],d[3],d[4],d[5],d[6],d[7],d[8],d[9],d[10],d[11],d[12],d[13],d[14],d[15]),u-=1,m-=1}else f=t;if(s=(c=i.sh.paths)._length,l){for(o="",a=0;a<s;a+=1)(h=c.shapes[a])&&h._length&&(o+=buildShapeString(h,h._length,h.c,f));i.caches[p]=o}else o=i.caches[p];i.styles[p].d+=!0===r.hd?"":o,i.styles[p]._mdf=l||i.styles[p]._mdf}}function a(t,e,r){var i=e.style;(e.c._mdf||r)&&i.pElem.setAttribute("fill","rgb("+bmFloor(e.c.v[0])+","+bmFloor(e.c.v[1])+","+bmFloor(e.c.v[2])+")"),(e.o._mdf||r)&&i.pElem.setAttribute("fill-opacity",e.o.v)}function s(t,e,r){o(t,e,r),l(0,e,r)}function o(t,e,r){var i,n,a,s,o,l=e.gf,h=e.g._hasOpacity,p=e.s.v,c=e.e.v;if(e.o._mdf||r){var f="gf"===t.ty?"fill-opacity":"stroke-opacity";e.style.pElem.setAttribute(f,e.o.v)}if(e.s._mdf||r){var d=1===t.t?"x1":"cx",u="x1"===d?"y1":"cy";l.setAttribute(d,p[0]),l.setAttribute(u,p[1]),h&&!e.g._collapsable&&(e.of.setAttribute(d,p[0]),e.of.setAttribute(u,p[1]))}if(e.g._cmdf||r){i=e.cst;var m=e.g.c;for(a=i.length,n=0;n<a;n+=1)(s=i[n]).setAttribute("offset",m[4*n]+"%"),s.setAttribute("stop-color","rgb("+m[4*n+1]+","+m[4*n+2]+","+m[4*n+3]+")")}if(h&&(e.g._omdf||r)){var y=e.g.o;for(a=(i=e.g._collapsable?e.cst:e.ost).length,n=0;n<a;n+=1)s=i[n],e.g._collapsable||s.setAttribute("offset",y[2*n]+"%"),s.setAttribute("stop-opacity",y[2*n+1])}if(1===t.t)(e.e._mdf||r)&&(l.setAttribute("x2",c[0]),l.setAttribute("y2",c[1]),h&&!e.g._collapsable&&(e.of.setAttribute("x2",c[0]),e.of.setAttribute("y2",c[1])));else if((e.s._mdf||e.e._mdf||r)&&(o=Math.sqrt(Math.pow(p[0]-c[0],2)+Math.pow(p[1]-c[1],2)),l.setAttribute("r",o),h&&!e.g._collapsable&&e.of.setAttribute("r",o)),e.e._mdf||e.h._mdf||e.a._mdf||r){o||(o=Math.sqrt(Math.pow(p[0]-c[0],2)+Math.pow(p[1]-c[1],2)));var g=Math.atan2(c[1]-p[1],c[0]-p[0]),v=e.h.v;v>=1?v=.99:v<=-1&&(v=-.99);var b=o*v,_=Math.cos(g+e.a.v)*b+p[0],P=Math.sin(g+e.a.v)*b+p[1];l.setAttribute("fx",_),l.setAttribute("fy",P),h&&!e.g._collapsable&&(e.of.setAttribute("fx",_),e.of.setAttribute("fy",P))}}function l(t,e,r){var i=e.style,n=e.d;n&&(n._mdf||r)&&n.dashStr&&(i.pElem.setAttribute("stroke-dasharray",n.dashStr),i.pElem.setAttribute("stroke-dashoffset",n.dashoffset[0])),e.c&&(e.c._mdf||r)&&i.pElem.setAttribute("stroke","rgb("+bmFloor(e.c.v[0])+","+bmFloor(e.c.v[1])+","+bmFloor(e.c.v[2])+")"),(e.o._mdf||r)&&i.pElem.setAttribute("stroke-opacity",e.o.v),(e.w._mdf||r)&&(i.pElem.setAttribute("stroke-width",e.w.v),i.msElem&&i.msElem.setAttribute("stroke-width",e.w.v))}return{createRenderFunction:function(t){switch(t.ty){case"fl":return a;case"gf":return o;case"gs":return s;case"st":return l;case"sh":case"el":case"rc":case"sr":return n;case"tr":return r;case"no":return i;default:return null}}}}();function SVGShapeElement(t,e,r){this.shapes=[],this.shapesData=t.shapes,this.stylesList=[],this.shapeModifiers=[],this.itemsData=[],this.processedElements=[],this.animatedContents=[],this.initElement(t,e,r),this.prevViewData=[]}function LetterProps(t,e,r,i,n,a){this.o=t,this.sw=e,this.sc=r,this.fc=i,this.m=n,this.p=a,this._mdf={o:!0,sw:!!e,sc:!!r,fc:!!i,m:!0,p:!0}}function TextProperty(t,e){this._frameId=initialDefaultFrame,this.pv="",this.v="",this.kf=!1,this._isFirstFrame=!0,this._mdf=!1,this.data=e,this.elem=t,this.comp=this.elem.comp,this.keysIndex=0,this.canResize=!1,this.minimumFontSize=1,this.effectsSequence=[],this.currentData={ascent:0,boxWidth:this.defaultBoxWidth,f:"",fStyle:"",fWeight:"",fc:"",j:"",justifyOffset:"",l:[],lh:0,lineWidths:[],ls:"",of:"",s:"",sc:"",sw:0,t:0,tr:0,sz:0,ps:null,fillColorAnim:!1,strokeColorAnim:!1,strokeWidthAnim:!1,yOffset:0,finalSize:0,finalText:[],finalLineHeight:0,__complete:!1},this.copyData(this.currentData,this.data.d.k[0].s),this.searchProperty()||this.completeTextData(this.currentData)}extendPrototype([BaseElement,TransformElement,SVGBaseElement,IShapeElement,HierarchyElement,FrameElement,RenderableDOMElement],SVGShapeElement),SVGShapeElement.prototype.initSecondaryElement=function(){},SVGShapeElement.prototype.identityMatrix=new Matrix,SVGShapeElement.prototype.buildExpressionInterface=function(){},SVGShapeElement.prototype.createContent=function(){this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,this.layerElement,0,[],!0),this.filterUniqueShapes()},SVGShapeElement.prototype.filterUniqueShapes=function(){var t,e,r,i,n=this.shapes.length,a=this.stylesList.length,s=[],o=!1;for(r=0;r<a;r+=1){for(i=this.stylesList[r],o=!1,s.length=0,t=0;t<n;t+=1)-1!==(e=this.shapes[t]).styles.indexOf(i)&&(s.push(e),o=e._isAnimated||o);s.length>1&&o&&this.setShapesAsAnimated(s)}},SVGShapeElement.prototype.setShapesAsAnimated=function(t){var e,r=t.length;for(e=0;e<r;e+=1)t[e].setAsAnimated()},SVGShapeElement.prototype.createStyleElement=function(t,e){var r,i=new SVGStyleData(t,e),n=i.pElem;return"st"===t.ty?r=new SVGStrokeStyleData(this,t,i):"fl"===t.ty?r=new SVGFillStyleData(this,t,i):"gf"===t.ty||"gs"===t.ty?(r=new("gf"===t.ty?SVGGradientFillStyleData:SVGGradientStrokeStyleData)(this,t,i),this.globalData.defs.appendChild(r.gf),r.maskId&&(this.globalData.defs.appendChild(r.ms),this.globalData.defs.appendChild(r.of),n.setAttribute("mask","url("+getLocationHref()+"#"+r.maskId+")"))):"no"===t.ty&&(r=new SVGNoStyleData(this,t,i)),"st"!==t.ty&&"gs"!==t.ty||(n.setAttribute("stroke-linecap",lineCapEnum[t.lc||2]),n.setAttribute("stroke-linejoin",lineJoinEnum[t.lj||2]),n.setAttribute("fill-opacity","0"),1===t.lj&&n.setAttribute("stroke-miterlimit",t.ml)),2===t.r&&n.setAttribute("fill-rule","evenodd"),t.ln&&n.setAttribute("id",t.ln),t.cl&&n.setAttribute("class",t.cl),t.bm&&(n.style["mix-blend-mode"]=getBlendMode(t.bm)),this.stylesList.push(i),this.addToAnimatedContents(t,r),r},SVGShapeElement.prototype.createGroupElement=function(t){var e=new ShapeGroupData;return t.ln&&e.gr.setAttribute("id",t.ln),t.cl&&e.gr.setAttribute("class",t.cl),t.bm&&(e.gr.style["mix-blend-mode"]=getBlendMode(t.bm)),e},SVGShapeElement.prototype.createTransformElement=function(t,e){var r=TransformPropertyFactory.getTransformProperty(this,t,this),i=new SVGTransformData(r,r.o,e);return this.addToAnimatedContents(t,i),i},SVGShapeElement.prototype.createShapeElement=function(t,e,r){var i=4;"rc"===t.ty?i=5:"el"===t.ty?i=6:"sr"===t.ty&&(i=7);var n=new SVGShapeData(e,r,ShapePropertyFactory.getShapeProp(this,t,i,this));return this.shapes.push(n),this.addShapeToModifiers(n),this.addToAnimatedContents(t,n),n},SVGShapeElement.prototype.addToAnimatedContents=function(t,e){for(var r=0,i=this.animatedContents.length;r<i;){if(this.animatedContents[r].element===e)return;r+=1}this.animatedContents.push({fn:SVGElementsRenderer.createRenderFunction(t),element:e,data:t})},SVGShapeElement.prototype.setElementStyles=function(t){var e,r=t.styles,i=this.stylesList.length;for(e=0;e<i;e+=1)this.stylesList[e].closed||r.push(this.stylesList[e])},SVGShapeElement.prototype.reloadShapes=function(){var t;this._isFirstFrame=!0;var e=this.itemsData.length;for(t=0;t<e;t+=1)this.prevViewData[t]=this.itemsData[t];for(this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,this.layerElement,0,[],!0),this.filterUniqueShapes(),e=this.dynamicProperties.length,t=0;t<e;t+=1)this.dynamicProperties[t].getValue();this.renderModifiers()},SVGShapeElement.prototype.searchShapes=function(t,e,r,i,n,a,s){var o,l,h,p,c,f,d=[].concat(a),u=t.length-1,m=[],y=[];for(o=u;o>=0;o-=1){if((f=this.searchProcessedElement(t[o]))?e[o]=r[f-1]:t[o]._render=s,"fl"===t[o].ty||"st"===t[o].ty||"gf"===t[o].ty||"gs"===t[o].ty||"no"===t[o].ty)f?e[o].style.closed=!1:e[o]=this.createStyleElement(t[o],n),t[o]._render&&e[o].style.pElem.parentNode!==i&&i.appendChild(e[o].style.pElem),m.push(e[o].style);else if("gr"===t[o].ty){if(f)for(h=e[o].it.length,l=0;l<h;l+=1)e[o].prevViewData[l]=e[o].it[l];else e[o]=this.createGroupElement(t[o]);this.searchShapes(t[o].it,e[o].it,e[o].prevViewData,e[o].gr,n+1,d,s),t[o]._render&&e[o].gr.parentNode!==i&&i.appendChild(e[o].gr)}else"tr"===t[o].ty?(f||(e[o]=this.createTransformElement(t[o],i)),p=e[o].transform,d.push(p)):"sh"===t[o].ty||"rc"===t[o].ty||"el"===t[o].ty||"sr"===t[o].ty?(f||(e[o]=this.createShapeElement(t[o],d,n)),this.setElementStyles(e[o])):"tm"===t[o].ty||"rd"===t[o].ty||"ms"===t[o].ty||"pb"===t[o].ty||"zz"===t[o].ty||"op"===t[o].ty?(f?(c=e[o]).closed=!1:((c=ShapeModifiers.getModifier(t[o].ty)).init(this,t[o]),e[o]=c,this.shapeModifiers.push(c)),y.push(c)):"rp"===t[o].ty&&(f?(c=e[o]).closed=!0:(c=ShapeModifiers.getModifier(t[o].ty),e[o]=c,c.init(this,t,o,e),this.shapeModifiers.push(c),s=!1),y.push(c));this.addProcessedElement(t[o],o+1)}for(u=m.length,o=0;o<u;o+=1)m[o].closed=!0;for(u=y.length,o=0;o<u;o+=1)y[o].closed=!0},SVGShapeElement.prototype.renderInnerContent=function(){var t;this.renderModifiers();var e=this.stylesList.length;for(t=0;t<e;t+=1)this.stylesList[t].reset();for(this.renderShape(),t=0;t<e;t+=1)(this.stylesList[t]._mdf||this._isFirstFrame)&&(this.stylesList[t].msElem&&(this.stylesList[t].msElem.setAttribute("d",this.stylesList[t].d),this.stylesList[t].d="M0 0"+this.stylesList[t].d),this.stylesList[t].pElem.setAttribute("d",this.stylesList[t].d||"M0 0"))},SVGShapeElement.prototype.renderShape=function(){var t,e,r=this.animatedContents.length;for(t=0;t<r;t+=1)e=this.animatedContents[t],(this._isFirstFrame||e.element._isAnimated)&&!0!==e.data&&e.fn(e.data,e.element,this._isFirstFrame)},SVGShapeElement.prototype.destroy=function(){this.destroyBaseElement(),this.shapesData=null,this.itemsData=null},LetterProps.prototype.update=function(t,e,r,i,n,a){this._mdf.o=!1,this._mdf.sw=!1,this._mdf.sc=!1,this._mdf.fc=!1,this._mdf.m=!1,this._mdf.p=!1;var s=!1;return this.o!==t&&(this.o=t,this._mdf.o=!0,s=!0),this.sw!==e&&(this.sw=e,this._mdf.sw=!0,s=!0),this.sc!==r&&(this.sc=r,this._mdf.sc=!0,s=!0),this.fc!==i&&(this.fc=i,this._mdf.fc=!0,s=!0),this.m!==n&&(this.m=n,this._mdf.m=!0,s=!0),!a.length||this.p[0]===a[0]&&this.p[1]===a[1]&&this.p[4]===a[4]&&this.p[5]===a[5]&&this.p[12]===a[12]&&this.p[13]===a[13]||(this.p=a,this._mdf.p=!0,s=!0),s},TextProperty.prototype.defaultBoxWidth=[0,0],TextProperty.prototype.copyData=function(t,e){for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&(t[r]=e[r]);return t},TextProperty.prototype.setCurrentData=function(t){t.__complete||this.completeTextData(t),this.currentData=t,this.currentData.boxWidth=this.currentData.boxWidth||this.defaultBoxWidth,this._mdf=!0},TextProperty.prototype.searchProperty=function(){return this.searchKeyframes()},TextProperty.prototype.searchKeyframes=function(){return this.kf=this.data.d.k.length>1,this.kf&&this.addEffect(this.getKeyframeValue.bind(this)),this.kf},TextProperty.prototype.addEffect=function(t){this.effectsSequence.push(t),this.elem.addDynamicProperty(this)},TextProperty.prototype.getValue=function(t){if(this.elem.globalData.frameId!==this.frameId&&this.effectsSequence.length||t){this.currentData.t=this.data.d.k[this.keysIndex].s.t;var e=this.currentData,r=this.keysIndex;if(this.lock)this.setCurrentData(this.currentData);else{var i;this.lock=!0,this._mdf=!1;var n=this.effectsSequence.length,a=t||this.data.d.k[this.keysIndex].s;for(i=0;i<n;i+=1)a=r!==this.keysIndex?this.effectsSequence[i](a,a.t):this.effectsSequence[i](this.currentData,a.t);e!==a&&this.setCurrentData(a),this.v=this.currentData,this.pv=this.v,this.lock=!1,this.frameId=this.elem.globalData.frameId}}},TextProperty.prototype.getKeyframeValue=function(){for(var t=this.data.d.k,e=this.elem.comp.renderedFrame,r=0,i=t.length;r<=i-1&&!(r===i-1||t[r+1].t>e);)r+=1;return this.keysIndex!==r&&(this.keysIndex=r),this.data.d.k[this.keysIndex].s},TextProperty.prototype.buildFinalText=function(t){for(var e,r,i=[],n=0,a=t.length,s=!1;n<a;)e=t.charCodeAt(n),FontManager.isCombinedCharacter(e)?i[i.length-1]+=t.charAt(n):e>=55296&&e<=56319?(r=t.charCodeAt(n+1))>=56320&&r<=57343?(s||FontManager.isModifier(e,r)?(i[i.length-1]+=t.substr(n,2),s=!1):i.push(t.substr(n,2)),n+=1):i.push(t.charAt(n)):e>56319?(r=t.charCodeAt(n+1),FontManager.isZeroWidthJoiner(e,r)?(s=!0,i[i.length-1]+=t.substr(n,2),n+=1):i.push(t.charAt(n))):FontManager.isZeroWidthJoiner(e)?(i[i.length-1]+=t.charAt(n),s=!0):i.push(t.charAt(n)),n+=1;return i},TextProperty.prototype.completeTextData=function(t){t.__complete=!0;var e,r,i,n,a,s,o,l=this.elem.globalData.fontManager,h=this.data,p=[],c=0,f=h.m.g,d=0,u=0,m=0,y=[],g=0,v=0,b=l.getFontByName(t.f),_=0,P=getFontProperties(b);t.fWeight=P.weight,t.fStyle=P.style,t.finalSize=t.s,t.finalText=this.buildFinalText(t.t),r=t.finalText.length,t.finalLineHeight=t.lh;var E,S=t.tr/1e3*t.finalSize;if(t.sz)for(var x,w,A=!0,C=t.sz[0],k=t.sz[1];A;){x=0,g=0,r=(w=this.buildFinalText(t.t)).length,S=t.tr/1e3*t.finalSize;var T=-1;for(e=0;e<r;e+=1)E=w[e].charCodeAt(0),i=!1," "===w[e]?T=e:13!==E&&3!==E||(g=0,i=!0,x+=t.finalLineHeight||1.2*t.finalSize),l.chars?(o=l.getCharData(w[e],b.fStyle,b.fFamily),_=i?0:o.w*t.finalSize/100):_=l.measureText(w[e],t.f,t.finalSize),g+_>C&&" "!==w[e]?(-1===T?r+=1:e=T,x+=t.finalLineHeight||1.2*t.finalSize,w.splice(e,T===e?1:0,"\r"),T=-1,g=0):(g+=_,g+=S);x+=b.ascent*t.finalSize/100,this.canResize&&t.finalSize>this.minimumFontSize&&k<x?(t.finalSize-=1,t.finalLineHeight=t.finalSize*t.lh/t.s):(t.finalText=w,r=t.finalText.length,A=!1)}g=-S,_=0;var M,D=0;for(e=0;e<r;e+=1)if(i=!1,13===(E=(M=t.finalText[e]).charCodeAt(0))||3===E?(D=0,y.push(g),v=g>v?g:v,g=-2*S,n="",i=!0,m+=1):n=M,l.chars?(o=l.getCharData(M,b.fStyle,l.getFontByName(t.f).fFamily),_=i?0:o.w*t.finalSize/100):_=l.measureText(n,t.f,t.finalSize)," "===M?D+=_+S:(g+=_+S+D,D=0),p.push({l:_,an:_,add:d,n:i,anIndexes:[],val:n,line:m,animatorJustifyOffset:0}),2==f){if(d+=_,""===n||" "===n||e===r-1){for(""!==n&&" "!==n||(d-=_);u<=e;)p[u].an=d,p[u].ind=c,p[u].extra=_,u+=1;c+=1,d=0}}else if(3==f){if(d+=_,""===n||e===r-1){for(""===n&&(d-=_);u<=e;)p[u].an=d,p[u].ind=c,p[u].extra=_,u+=1;d=0,c+=1}}else p[c].ind=c,p[c].extra=0,c+=1;if(t.l=p,v=g>v?g:v,y.push(g),t.sz)t.boxWidth=t.sz[0],t.justifyOffset=0;else switch(t.boxWidth=v,t.j){case 1:t.justifyOffset=-t.boxWidth;break;case 2:t.justifyOffset=-t.boxWidth/2;break;default:t.justifyOffset=0}t.lineWidths=y;var I,F,L,R,B=h.a;s=B.length;var O=[];for(a=0;a<s;a+=1){for((I=B[a]).a.sc&&(t.strokeColorAnim=!0),I.a.sw&&(t.strokeWidthAnim=!0),(I.a.fc||I.a.fh||I.a.fs||I.a.fb)&&(t.fillColorAnim=!0),R=0,L=I.s.b,e=0;e<r;e+=1)(F=p[e]).anIndexes[a]=R,(1==L&&""!==F.val||2==L&&""!==F.val&&" "!==F.val||3==L&&(F.n||" "==F.val||e==r-1)||4==L&&(F.n||e==r-1))&&(1===I.s.rn&&O.push(R),R+=1);h.a[a].s.totalChars=R;var V,$=-1;if(1===I.s.rn)for(e=0;e<r;e+=1)$!=(F=p[e]).anIndexes[a]&&($=F.anIndexes[a],V=O.splice(Math.floor(Math.random()*O.length),1)[0]),F.anIndexes[a]=V}t.yOffset=t.finalLineHeight||1.2*t.finalSize,t.ls=t.ls||0,t.ascent=b.ascent*t.finalSize/100},TextProperty.prototype.updateDocumentData=function(t,e){e=void 0===e?this.keysIndex:e;var r=this.copyData({},this.data.d.k[e].s);r=this.copyData(r,t),this.data.d.k[e].s=r,this.recalculate(e),this.elem.addDynamicProperty(this)},TextProperty.prototype.recalculate=function(t){var e=this.data.d.k[t].s;e.__complete=!1,this.keysIndex=0,this._isFirstFrame=!0,this.getValue(e)},TextProperty.prototype.canResizeFont=function(t){this.canResize=t,this.recalculate(this.keysIndex),this.elem.addDynamicProperty(this)},TextProperty.prototype.setMinimumFontSize=function(t){this.minimumFontSize=Math.floor(t)||1,this.recalculate(this.keysIndex),this.elem.addDynamicProperty(this)};var TextSelectorProp=function(){var t=Math.max,e=Math.min,r=Math.floor;function i(t,e){this._currentTextLength=-1,this.k=!1,this.data=e,this.elem=t,this.comp=t.comp,this.finalS=0,this.finalE=0,this.initDynamicPropertyContainer(t),this.s=PropertyFactory.getProp(t,e.s||{k:0},0,0,this),this.e="e"in e?PropertyFactory.getProp(t,e.e,0,0,this):{v:100},this.o=PropertyFactory.getProp(t,e.o||{k:0},0,0,this),this.xe=PropertyFactory.getProp(t,e.xe||{k:0},0,0,this),this.ne=PropertyFactory.getProp(t,e.ne||{k:0},0,0,this),this.sm=PropertyFactory.getProp(t,e.sm||{k:100},0,0,this),this.a=PropertyFactory.getProp(t,e.a,0,.01,this),this.dynamicProperties.length||this.getValue()}return i.prototype={getMult:function(i){this._currentTextLength!==this.elem.textProperty.currentData.l.length&&this.getValue();var n=0,a=0,s=1,o=1;this.ne.v>0?n=this.ne.v/100:a=-this.ne.v/100,this.xe.v>0?s=1-this.xe.v/100:o=1+this.xe.v/100;var l=BezierFactory.getBezierEasing(n,a,s,o).get,h=0,p=this.finalS,c=this.finalE,f=this.data.sh;if(2===f)h=l(h=c===p?i>=c?1:0:t(0,e(.5/(c-p)+(i-p)/(c-p),1)));else if(3===f)h=l(h=c===p?i>=c?0:1:1-t(0,e(.5/(c-p)+(i-p)/(c-p),1)));else if(4===f)c===p?h=0:(h=t(0,e(.5/(c-p)+(i-p)/(c-p),1)))<.5?h*=2:h=1-2*(h-.5),h=l(h);else if(5===f){if(c===p)h=0;else{var d=c-p,u=-d/2+(i=e(t(0,i+.5-p),c-p)),m=d/2;h=Math.sqrt(1-u*u/(m*m))}h=l(h)}else 6===f?(c===p?h=0:(i=e(t(0,i+.5-p),c-p),h=(1+Math.cos(Math.PI+2*Math.PI*i/(c-p)))/2),h=l(h)):(i>=r(p)&&(h=t(0,e(i-p<0?e(c,1)-(p-i):c-i,1))),h=l(h));if(100!==this.sm.v){var y=.01*this.sm.v;0===y&&(y=1e-8);var g=.5-.5*y;h<g?h=0:(h=(h-g)/y)>1&&(h=1)}return h*this.a.v},getValue:function(t){this.iterateDynamicProperties(),this._mdf=t||this._mdf,this._currentTextLength=this.elem.textProperty.currentData.l.length||0,t&&2===this.data.r&&(this.e.v=this._currentTextLength);var e=2===this.data.r?1:100/this.data.totalChars,r=this.o.v/e,i=this.s.v/e+r,n=this.e.v/e+r;if(i>n){var a=i;i=n,n=a}this.finalS=i,this.finalE=n}},extendPrototype([DynamicPropertyContainer],i),{getTextSelectorProp:function(t,e,r){return new i(t,e,r)}}}();function TextAnimatorDataProperty(t,e,r){var i={propType:!1},n=PropertyFactory.getProp,a=e.a;this.a={r:a.r?n(t,a.r,0,degToRads,r):i,rx:a.rx?n(t,a.rx,0,degToRads,r):i,ry:a.ry?n(t,a.ry,0,degToRads,r):i,sk:a.sk?n(t,a.sk,0,degToRads,r):i,sa:a.sa?n(t,a.sa,0,degToRads,r):i,s:a.s?n(t,a.s,1,.01,r):i,a:a.a?n(t,a.a,1,0,r):i,o:a.o?n(t,a.o,0,.01,r):i,p:a.p?n(t,a.p,1,0,r):i,sw:a.sw?n(t,a.sw,0,0,r):i,sc:a.sc?n(t,a.sc,1,0,r):i,fc:a.fc?n(t,a.fc,1,0,r):i,fh:a.fh?n(t,a.fh,0,0,r):i,fs:a.fs?n(t,a.fs,0,.01,r):i,fb:a.fb?n(t,a.fb,0,.01,r):i,t:a.t?n(t,a.t,0,0,r):i},this.s=TextSelectorProp.getTextSelectorProp(t,e.s,r),this.s.t=e.s.t}function TextAnimatorProperty(t,e,r){this._isFirstFrame=!0,this._hasMaskedPath=!1,this._frameId=-1,this._textData=t,this._renderType=e,this._elem=r,this._animatorsData=createSizedArray(this._textData.a.length),this._pathData={},this._moreOptions={alignment:{}},this.renderedLetters=[],this.lettersChangedFlag=!1,this.initDynamicPropertyContainer(r)}function ITextElement(){}TextAnimatorProperty.prototype.searchProperties=function(){var t,e,r=this._textData.a.length,i=PropertyFactory.getProp;for(t=0;t<r;t+=1)e=this._textData.a[t],this._animatorsData[t]=new TextAnimatorDataProperty(this._elem,e,this);this._textData.p&&"m"in this._textData.p?(this._pathData={a:i(this._elem,this._textData.p.a,0,0,this),f:i(this._elem,this._textData.p.f,0,0,this),l:i(this._elem,this._textData.p.l,0,0,this),r:i(this._elem,this._textData.p.r,0,0,this),p:i(this._elem,this._textData.p.p,0,0,this),m:this._elem.maskManager.getMaskProperty(this._textData.p.m)},this._hasMaskedPath=!0):this._hasMaskedPath=!1,this._moreOptions.alignment=i(this._elem,this._textData.m.a,1,0,this)},TextAnimatorProperty.prototype.getMeasures=function(t,e){if(this.lettersChangedFlag=e,this._mdf||this._isFirstFrame||e||this._hasMaskedPath&&this._pathData.m._mdf){this._isFirstFrame=!1;var r,i,n,a,s,o,l,h,p,c,f,d,u,m,y,g,v,b,_,P=this._moreOptions.alignment.v,E=this._animatorsData,S=this._textData,x=this.mHelper,w=this._renderType,A=this.renderedLetters.length,C=t.l;if(this._hasMaskedPath){if(_=this._pathData.m,!this._pathData.n||this._pathData._mdf){var k,T=_.v;for(this._pathData.r.v&&(T=T.reverse()),s={tLength:0,segments:[]},a=T._length-1,g=0,n=0;n<a;n+=1)k=bez.buildBezierData(T.v[n],T.v[n+1],[T.o[n][0]-T.v[n][0],T.o[n][1]-T.v[n][1]],[T.i[n+1][0]-T.v[n+1][0],T.i[n+1][1]-T.v[n+1][1]]),s.tLength+=k.segmentLength,s.segments.push(k),g+=k.segmentLength;n=a,_.v.c&&(k=bez.buildBezierData(T.v[n],T.v[0],[T.o[n][0]-T.v[n][0],T.o[n][1]-T.v[n][1]],[T.i[0][0]-T.v[0][0],T.i[0][1]-T.v[0][1]]),s.tLength+=k.segmentLength,s.segments.push(k),g+=k.segmentLength),this._pathData.pi=s}if(s=this._pathData.pi,o=this._pathData.f.v,f=0,c=1,h=0,p=!0,m=s.segments,o<0&&_.v.c)for(s.tLength<Math.abs(o)&&(o=-Math.abs(o)%s.tLength),c=(u=m[f=m.length-1].points).length-1;o<0;)o+=u[c].partialLength,(c-=1)<0&&(c=(u=m[f-=1].points).length-1);d=(u=m[f].points)[c-1],y=(l=u[c]).partialLength}a=C.length,r=0,i=0;var M,D,I,F,L,R=1.2*t.finalSize*.714,B=!0;I=E.length;var O,V,$,z,N,G,j,H,q,W,U,Y,X=-1,Z=o,K=f,J=c,Q=-1,tt="",et=this.defaultPropsArray;if(2===t.j||1===t.j){var rt=0,it=0,nt=2===t.j?-.5:-1,at=0,st=!0;for(n=0;n<a;n+=1)if(C[n].n){for(rt&&(rt+=it);at<n;)C[at].animatorJustifyOffset=rt,at+=1;rt=0,st=!0}else{for(D=0;D<I;D+=1)(M=E[D].a).t.propType&&(st&&2===t.j&&(it+=M.t.v*nt),(L=E[D].s.getMult(C[n].anIndexes[D],S.a[D].s.totalChars)).length?rt+=M.t.v*L[0]*nt:rt+=M.t.v*L*nt);st=!1}for(rt&&(rt+=it);at<n;)C[at].animatorJustifyOffset=rt,at+=1}for(n=0;n<a;n+=1){if(x.reset(),z=1,C[n].n)r=0,i+=t.yOffset,i+=B?1:0,o=Z,B=!1,this._hasMaskedPath&&(c=J,d=(u=m[f=K].points)[c-1],y=(l=u[c]).partialLength,h=0),tt="",U="",q="",Y="",et=this.defaultPropsArray;else{if(this._hasMaskedPath){if(Q!==C[n].line){switch(t.j){case 1:o+=g-t.lineWidths[C[n].line];break;case 2:o+=(g-t.lineWidths[C[n].line])/2}Q=C[n].line}X!==C[n].ind&&(C[X]&&(o+=C[X].extra),o+=C[n].an/2,X=C[n].ind),o+=P[0]*C[n].an*.005;var ot=0;for(D=0;D<I;D+=1)(M=E[D].a).p.propType&&((L=E[D].s.getMult(C[n].anIndexes[D],S.a[D].s.totalChars)).length?ot+=M.p.v[0]*L[0]:ot+=M.p.v[0]*L),M.a.propType&&((L=E[D].s.getMult(C[n].anIndexes[D],S.a[D].s.totalChars)).length?ot+=M.a.v[0]*L[0]:ot+=M.a.v[0]*L);for(p=!0,this._pathData.a.v&&(o=.5*C[0].an+(g-this._pathData.f.v-.5*C[0].an-.5*C[C.length-1].an)*X/(a-1),o+=this._pathData.f.v);p;)h+y>=o+ot||!u?(v=(o+ot-h)/l.partialLength,V=d.point[0]+(l.point[0]-d.point[0])*v,$=d.point[1]+(l.point[1]-d.point[1])*v,x.translate(-P[0]*C[n].an*.005,-P[1]*R*.01),p=!1):u&&(h+=l.partialLength,(c+=1)>=u.length&&(c=0,m[f+=1]?u=m[f].points:_.v.c?(c=0,u=m[f=0].points):(h-=l.partialLength,u=null)),u&&(d=l,y=(l=u[c]).partialLength));O=C[n].an/2-C[n].add,x.translate(-O,0,0)}else O=C[n].an/2-C[n].add,x.translate(-O,0,0),x.translate(-P[0]*C[n].an*.005,-P[1]*R*.01,0);for(D=0;D<I;D+=1)(M=E[D].a).t.propType&&(L=E[D].s.getMult(C[n].anIndexes[D],S.a[D].s.totalChars),0===r&&0===t.j||(this._hasMaskedPath?L.length?o+=M.t.v*L[0]:o+=M.t.v*L:L.length?r+=M.t.v*L[0]:r+=M.t.v*L));for(t.strokeWidthAnim&&(G=t.sw||0),t.strokeColorAnim&&(N=t.sc?[t.sc[0],t.sc[1],t.sc[2]]:[0,0,0]),t.fillColorAnim&&t.fc&&(j=[t.fc[0],t.fc[1],t.fc[2]]),D=0;D<I;D+=1)(M=E[D].a).a.propType&&((L=E[D].s.getMult(C[n].anIndexes[D],S.a[D].s.totalChars)).length?x.translate(-M.a.v[0]*L[0],-M.a.v[1]*L[1],M.a.v[2]*L[2]):x.translate(-M.a.v[0]*L,-M.a.v[1]*L,M.a.v[2]*L));for(D=0;D<I;D+=1)(M=E[D].a).s.propType&&((L=E[D].s.getMult(C[n].anIndexes[D],S.a[D].s.totalChars)).length?x.scale(1+(M.s.v[0]-1)*L[0],1+(M.s.v[1]-1)*L[1],1):x.scale(1+(M.s.v[0]-1)*L,1+(M.s.v[1]-1)*L,1));for(D=0;D<I;D+=1){if(M=E[D].a,L=E[D].s.getMult(C[n].anIndexes[D],S.a[D].s.totalChars),M.sk.propType&&(L.length?x.skewFromAxis(-M.sk.v*L[0],M.sa.v*L[1]):x.skewFromAxis(-M.sk.v*L,M.sa.v*L)),M.r.propType&&(L.length?x.rotateZ(-M.r.v*L[2]):x.rotateZ(-M.r.v*L)),M.ry.propType&&(L.length?x.rotateY(M.ry.v*L[1]):x.rotateY(M.ry.v*L)),M.rx.propType&&(L.length?x.rotateX(M.rx.v*L[0]):x.rotateX(M.rx.v*L)),M.o.propType&&(L.length?z+=(M.o.v*L[0]-z)*L[0]:z+=(M.o.v*L-z)*L),t.strokeWidthAnim&&M.sw.propType&&(L.length?G+=M.sw.v*L[0]:G+=M.sw.v*L),t.strokeColorAnim&&M.sc.propType)for(H=0;H<3;H+=1)L.length?N[H]+=(M.sc.v[H]-N[H])*L[0]:N[H]+=(M.sc.v[H]-N[H])*L;if(t.fillColorAnim&&t.fc){if(M.fc.propType)for(H=0;H<3;H+=1)L.length?j[H]+=(M.fc.v[H]-j[H])*L[0]:j[H]+=(M.fc.v[H]-j[H])*L;M.fh.propType&&(j=L.length?addHueToRGB(j,M.fh.v*L[0]):addHueToRGB(j,M.fh.v*L)),M.fs.propType&&(j=L.length?addSaturationToRGB(j,M.fs.v*L[0]):addSaturationToRGB(j,M.fs.v*L)),M.fb.propType&&(j=L.length?addBrightnessToRGB(j,M.fb.v*L[0]):addBrightnessToRGB(j,M.fb.v*L))}}for(D=0;D<I;D+=1)(M=E[D].a).p.propType&&(L=E[D].s.getMult(C[n].anIndexes[D],S.a[D].s.totalChars),this._hasMaskedPath?L.length?x.translate(0,M.p.v[1]*L[0],-M.p.v[2]*L[1]):x.translate(0,M.p.v[1]*L,-M.p.v[2]*L):L.length?x.translate(M.p.v[0]*L[0],M.p.v[1]*L[1],-M.p.v[2]*L[2]):x.translate(M.p.v[0]*L,M.p.v[1]*L,-M.p.v[2]*L));if(t.strokeWidthAnim&&(q=G<0?0:G),t.strokeColorAnim&&(W="rgb("+Math.round(255*N[0])+","+Math.round(255*N[1])+","+Math.round(255*N[2])+")"),t.fillColorAnim&&t.fc&&(U="rgb("+Math.round(255*j[0])+","+Math.round(255*j[1])+","+Math.round(255*j[2])+")"),this._hasMaskedPath){if(x.translate(0,-t.ls),x.translate(0,P[1]*R*.01+i,0),this._pathData.p.v){b=(l.point[1]-d.point[1])/(l.point[0]-d.point[0]);var lt=180*Math.atan(b)/Math.PI;l.point[0]<d.point[0]&&(lt+=180),x.rotate(-lt*Math.PI/180)}x.translate(V,$,0),o-=P[0]*C[n].an*.005,C[n+1]&&X!==C[n+1].ind&&(o+=C[n].an/2,o+=.001*t.tr*t.finalSize)}else{switch(x.translate(r,i,0),t.ps&&x.translate(t.ps[0],t.ps[1]+t.ascent,0),t.j){case 1:x.translate(C[n].animatorJustifyOffset+t.justifyOffset+(t.boxWidth-t.lineWidths[C[n].line]),0,0);break;case 2:x.translate(C[n].animatorJustifyOffset+t.justifyOffset+(t.boxWidth-t.lineWidths[C[n].line])/2,0,0)}x.translate(0,-t.ls),x.translate(O,0,0),x.translate(P[0]*C[n].an*.005,P[1]*R*.01,0),r+=C[n].l+.001*t.tr*t.finalSize}"html"===w?tt=x.toCSS():"svg"===w?tt=x.to2dCSS():et=[x.props[0],x.props[1],x.props[2],x.props[3],x.props[4],x.props[5],x.props[6],x.props[7],x.props[8],x.props[9],x.props[10],x.props[11],x.props[12],x.props[13],x.props[14],x.props[15]],Y=z}A<=n?(F=new LetterProps(Y,q,W,U,tt,et),this.renderedLetters.push(F),A+=1,this.lettersChangedFlag=!0):(F=this.renderedLetters[n],this.lettersChangedFlag=F.update(Y,q,W,U,tt,et)||this.lettersChangedFlag)}}},TextAnimatorProperty.prototype.getValue=function(){this._elem.globalData.frameId!==this._frameId&&(this._frameId=this._elem.globalData.frameId,this.iterateDynamicProperties())},TextAnimatorProperty.prototype.mHelper=new Matrix,TextAnimatorProperty.prototype.defaultPropsArray=[],extendPrototype([DynamicPropertyContainer],TextAnimatorProperty),ITextElement.prototype.initElement=function(t,e,r){this.lettersChangedFlag=!0,this.initFrame(),this.initBaseData(t,e,r),this.textProperty=new TextProperty(this,t.t,this.dynamicProperties),this.textAnimator=new TextAnimatorProperty(t.t,this.renderType,this),this.initTransform(t,e,r),this.initHierarchy(),this.initRenderable(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),this.createContent(),this.hide(),this.textAnimator.searchProperties(this.dynamicProperties)},ITextElement.prototype.prepareFrame=function(t){this._mdf=!1,this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange),(this.textProperty._mdf||this.textProperty._isFirstFrame)&&(this.buildNewText(),this.textProperty._isFirstFrame=!1,this.textProperty._mdf=!1)},ITextElement.prototype.createPathShape=function(t,e){var r,i,n=e.length,a="";for(r=0;r<n;r+=1)"sh"===e[r].ty&&(i=e[r].ks.k,a+=buildShapeString(i,i.i.length,!0,t));return a},ITextElement.prototype.updateDocumentData=function(t,e){this.textProperty.updateDocumentData(t,e)},ITextElement.prototype.canResizeFont=function(t){this.textProperty.canResizeFont(t)},ITextElement.prototype.setMinimumFontSize=function(t){this.textProperty.setMinimumFontSize(t)},ITextElement.prototype.applyTextPropertiesToMatrix=function(t,e,r,i,n){switch(t.ps&&e.translate(t.ps[0],t.ps[1]+t.ascent,0),e.translate(0,-t.ls,0),t.j){case 1:e.translate(t.justifyOffset+(t.boxWidth-t.lineWidths[r]),0,0);break;case 2:e.translate(t.justifyOffset+(t.boxWidth-t.lineWidths[r])/2,0,0)}e.translate(i,n,0)},ITextElement.prototype.buildColor=function(t){return"rgb("+Math.round(255*t[0])+","+Math.round(255*t[1])+","+Math.round(255*t[2])+")"},ITextElement.prototype.emptyProp=new LetterProps,ITextElement.prototype.destroy=function(){};var emptyShapeData={shapes:[]};function SVGTextLottieElement(t,e,r){this.textSpans=[],this.renderType="svg",this.initElement(t,e,r)}function ISolidElement(t,e,r){this.initElement(t,e,r)}function NullElement(t,e,r){this.initFrame(),this.initBaseData(t,e,r),this.initFrame(),this.initTransform(t,e,r),this.initHierarchy()}function SVGRendererBase(){}function ICompElement(){}function SVGCompElement(t,e,r){this.layers=t.layers,this.supports3d=!0,this.completeLayers=!1,this.pendingElements=[],this.elements=this.layers?createSizedArray(this.layers.length):[],this.initElement(t,e,r),this.tm=t.tm?PropertyFactory.getProp(this,t.tm,0,e.frameRate,this):{_placeholder:!0}}function SVGRenderer(t,e){this.animationItem=t,this.layers=null,this.renderedFrame=-1,this.svgElement=createNS("svg");var r="";if(e&&e.title){var i=createNS("title"),n=createElementID();i.setAttribute("id",n),i.textContent=e.title,this.svgElement.appendChild(i),r+=n}if(e&&e.description){var a=createNS("desc"),s=createElementID();a.setAttribute("id",s),a.textContent=e.description,this.svgElement.appendChild(a),r+=" "+s}r&&this.svgElement.setAttribute("aria-labelledby",r);var o=createNS("defs");this.svgElement.appendChild(o);var l=createNS("g");this.svgElement.appendChild(l),this.layerElement=l,this.renderConfig={preserveAspectRatio:e&&e.preserveAspectRatio||"xMidYMid meet",imagePreserveAspectRatio:e&&e.imagePreserveAspectRatio||"xMidYMid slice",contentVisibility:e&&e.contentVisibility||"visible",progressiveLoad:e&&e.progressiveLoad||!1,hideOnTransparent:!(e&&!1===e.hideOnTransparent),viewBoxOnly:e&&e.viewBoxOnly||!1,viewBoxSize:e&&e.viewBoxSize||!1,className:e&&e.className||"",id:e&&e.id||"",focusable:e&&e.focusable,filterSize:{width:e&&e.filterSize&&e.filterSize.width||"100%",height:e&&e.filterSize&&e.filterSize.height||"100%",x:e&&e.filterSize&&e.filterSize.x||"0%",y:e&&e.filterSize&&e.filterSize.y||"0%"},width:e&&e.width,height:e&&e.height,runExpressions:!e||void 0===e.runExpressions||e.runExpressions},this.globalData={_mdf:!1,frameNum:-1,defs:o,renderConfig:this.renderConfig},this.elements=[],this.pendingElements=[],this.destroyed=!1,this.rendererType="svg"}function CVContextData(){var t;for(this.saved=[],this.cArrPos=0,this.cTr=new Matrix,this.cO=1,this.savedOp=createTypedArray("float32",15),t=0;t<15;t+=1)this.saved[t]=createTypedArray("float32",16);this._length=15}function ShapeTransformManager(){this.sequences={},this.sequenceList=[],this.transform_key_count=0}function CVEffects(){}function CVMaskElement(t,e){var r;this.data=t,this.element=e,this.masksProperties=this.data.masksProperties||[],this.viewData=createSizedArray(this.masksProperties.length);var i=this.masksProperties.length,n=!1;for(r=0;r<i;r+=1)"n"!==this.masksProperties[r].mode&&(n=!0),this.viewData[r]=ShapePropertyFactory.getShapeProp(this.element,this.masksProperties[r],3);this.hasMasks=n,n&&this.element.addRenderableComponent(this)}function CVBaseElement(){}function CVShapeData(t,e,r,i){this.styledShapes=[],this.tr=[0,0,0,0,0,0];var n,a=4;"rc"===e.ty?a=5:"el"===e.ty?a=6:"sr"===e.ty&&(a=7),this.sh=ShapePropertyFactory.getShapeProp(t,e,a,t);var s,o=r.length;for(n=0;n<o;n+=1)r[n].closed||(s={transforms:i.addTransformSequence(r[n].transforms),trNodes:[]},this.styledShapes.push(s),r[n].elements.push(s))}function CVShapeElement(t,e,r){this.shapes=[],this.shapesData=t.shapes,this.stylesList=[],this.itemsData=[],this.prevViewData=[],this.shapeModifiers=[],this.processedElements=[],this.transformsManager=new ShapeTransformManager,this.initElement(t,e,r)}function CVTextElement(t,e,r){this.textSpans=[],this.yOffset=0,this.fillColorAnim=!1,this.strokeColorAnim=!1,this.strokeWidthAnim=!1,this.stroke=!1,this.fill=!1,this.justifyOffset=0,this.currentRender=null,this.renderType="canvas",this.values={fill:"rgba(0,0,0,0)",stroke:"rgba(0,0,0,0)",sWidth:0,fValue:""},this.initElement(t,e,r)}function CVImageElement(t,e,r){this.assetData=e.getAssetData(t.refId),this.img=e.imageLoader.getAsset(this.assetData),this.initElement(t,e,r)}function CVSolidElement(t,e,r){this.initElement(t,e,r)}function CanvasRendererBase(t,e){this.animationItem=t,this.renderConfig={clearCanvas:!e||void 0===e.clearCanvas||e.clearCanvas,context:e&&e.context||null,progressiveLoad:e&&e.progressiveLoad||!1,preserveAspectRatio:e&&e.preserveAspectRatio||"xMidYMid meet",imagePreserveAspectRatio:e&&e.imagePreserveAspectRatio||"xMidYMid slice",contentVisibility:e&&e.contentVisibility||"visible",className:e&&e.className||"",id:e&&e.id||""},this.renderConfig.dpr=e&&e.dpr||1,this.animationItem.wrapper&&(this.renderConfig.dpr=e&&e.dpr||window.devicePixelRatio||1),this.renderedFrame=-1,this.globalData={frameNum:-1,_mdf:!1,renderConfig:this.renderConfig,currentGlobalAlpha:-1},this.contextData=new CVContextData,this.elements=[],this.pendingElements=[],this.transformMat=new Matrix,this.completeLayers=!1,this.rendererType="canvas"}function CVCompElement(t,e,r){this.completeLayers=!1,this.layers=t.layers,this.pendingElements=[],this.elements=createSizedArray(this.layers.length),this.initElement(t,e,r),this.tm=t.tm?PropertyFactory.getProp(this,t.tm,0,e.frameRate,this):{_placeholder:!0}}function CanvasRenderer(t,e){this.animationItem=t,this.renderConfig={clearCanvas:!e||void 0===e.clearCanvas||e.clearCanvas,context:e&&e.context||null,progressiveLoad:e&&e.progressiveLoad||!1,preserveAspectRatio:e&&e.preserveAspectRatio||"xMidYMid meet",imagePreserveAspectRatio:e&&e.imagePreserveAspectRatio||"xMidYMid slice",contentVisibility:e&&e.contentVisibility||"visible",className:e&&e.className||"",id:e&&e.id||"",runExpressions:!e||void 0===e.runExpressions||e.runExpressions},this.renderConfig.dpr=e&&e.dpr||1,this.animationItem.wrapper&&(this.renderConfig.dpr=e&&e.dpr||window.devicePixelRatio||1),this.renderedFrame=-1,this.globalData={frameNum:-1,_mdf:!1,renderConfig:this.renderConfig,currentGlobalAlpha:-1},this.contextData=new CVContextData,this.elements=[],this.pendingElements=[],this.transformMat=new Matrix,this.completeLayers=!1,this.rendererType="canvas"}function HBaseElement(){}function HSolidElement(t,e,r){this.initElement(t,e,r)}function HShapeElement(t,e,r){this.shapes=[],this.shapesData=t.shapes,this.stylesList=[],this.shapeModifiers=[],this.itemsData=[],this.processedElements=[],this.animatedContents=[],this.shapesContainer=createNS("g"),this.initElement(t,e,r),this.prevViewData=[],this.currentBBox={x:999999,y:-999999,h:0,w:0}}function HTextElement(t,e,r){this.textSpans=[],this.textPaths=[],this.currentBBox={x:999999,y:-999999,h:0,w:0},this.renderType="svg",this.isMasked=!1,this.initElement(t,e,r)}function HCameraElement(t,e,r){this.initFrame(),this.initBaseData(t,e,r),this.initHierarchy();var i=PropertyFactory.getProp;if(this.pe=i(this,t.pe,0,0,this),t.ks.p.s?(this.px=i(this,t.ks.p.x,1,0,this),this.py=i(this,t.ks.p.y,1,0,this),this.pz=i(this,t.ks.p.z,1,0,this)):this.p=i(this,t.ks.p,1,0,this),t.ks.a&&(this.a=i(this,t.ks.a,1,0,this)),t.ks.or.k.length&&t.ks.or.k[0].to){var n,a=t.ks.or.k.length;for(n=0;n<a;n+=1)t.ks.or.k[n].to=null,t.ks.or.k[n].ti=null}this.or=i(this,t.ks.or,1,degToRads,this),this.or.sh=!0,this.rx=i(this,t.ks.rx,0,degToRads,this),this.ry=i(this,t.ks.ry,0,degToRads,this),this.rz=i(this,t.ks.rz,0,degToRads,this),this.mat=new Matrix,this._prevMat=new Matrix,this._isFirstFrame=!0,this.finalTransform={mProp:this}}function HImageElement(t,e,r){this.assetData=e.getAssetData(t.refId),this.initElement(t,e,r)}function HybridRendererBase(t,e){this.animationItem=t,this.layers=null,this.renderedFrame=-1,this.renderConfig={className:e&&e.className||"",imagePreserveAspectRatio:e&&e.imagePreserveAspectRatio||"xMidYMid slice",hideOnTransparent:!(e&&!1===e.hideOnTransparent),filterSize:{width:e&&e.filterSize&&e.filterSize.width||"400%",height:e&&e.filterSize&&e.filterSize.height||"400%",x:e&&e.filterSize&&e.filterSize.x||"-100%",y:e&&e.filterSize&&e.filterSize.y||"-100%"}},this.globalData={_mdf:!1,frameNum:-1,renderConfig:this.renderConfig},this.pendingElements=[],this.elements=[],this.threeDElements=[],this.destroyed=!1,this.camera=null,this.supports3d=!0,this.rendererType="html"}function HCompElement(t,e,r){this.layers=t.layers,this.supports3d=!t.hasMask,this.completeLayers=!1,this.pendingElements=[],this.elements=this.layers?createSizedArray(this.layers.length):[],this.initElement(t,e,r),this.tm=t.tm?PropertyFactory.getProp(this,t.tm,0,e.frameRate,this):{_placeholder:!0}}function HybridRenderer(t,e){this.animationItem=t,this.layers=null,this.renderedFrame=-1,this.renderConfig={className:e&&e.className||"",imagePreserveAspectRatio:e&&e.imagePreserveAspectRatio||"xMidYMid slice",hideOnTransparent:!(e&&!1===e.hideOnTransparent),filterSize:{width:e&&e.filterSize&&e.filterSize.width||"400%",height:e&&e.filterSize&&e.filterSize.height||"400%",x:e&&e.filterSize&&e.filterSize.x||"-100%",y:e&&e.filterSize&&e.filterSize.y||"-100%"},runExpressions:!e||void 0===e.runExpressions||e.runExpressions},this.globalData={_mdf:!1,frameNum:-1,renderConfig:this.renderConfig},this.pendingElements=[],this.elements=[],this.threeDElements=[],this.destroyed=!1,this.camera=null,this.supports3d=!0,this.rendererType="html"}extendPrototype([BaseElement,TransformElement,SVGBaseElement,HierarchyElement,FrameElement,RenderableDOMElement,ITextElement],SVGTextLottieElement),SVGTextLottieElement.prototype.createContent=function(){this.data.singleShape&&!this.globalData.fontManager.chars&&(this.textContainer=createNS("text"))},SVGTextLottieElement.prototype.buildTextContents=function(t){for(var e=0,r=t.length,i=[],n="";e<r;)t[e]===String.fromCharCode(13)||t[e]===String.fromCharCode(3)?(i.push(n),n=""):n+=t[e],e+=1;return i.push(n),i},SVGTextLottieElement.prototype.buildShapeData=function(t,e){if(t.shapes&&t.shapes.length){var r=t.shapes[0];if(r.it){var i=r.it[r.it.length-1];i.s&&(i.s.k[0]=e,i.s.k[1]=e)}}return t},SVGTextLottieElement.prototype.buildNewText=function(){var t,e;this.addDynamicProperty(this);var r=this.textProperty.currentData;this.renderedLetters=createSizedArray(r?r.l.length:0),r.fc?this.layerElement.setAttribute("fill",this.buildColor(r.fc)):this.layerElement.setAttribute("fill","rgba(0,0,0,0)"),r.sc&&(this.layerElement.setAttribute("stroke",this.buildColor(r.sc)),this.layerElement.setAttribute("stroke-width",r.sw)),this.layerElement.setAttribute("font-size",r.finalSize);var i=this.globalData.fontManager.getFontByName(r.f);if(i.fClass)this.layerElement.setAttribute("class",i.fClass);else{this.layerElement.setAttribute("font-family",i.fFamily);var n=r.fWeight,a=r.fStyle;this.layerElement.setAttribute("font-style",a),this.layerElement.setAttribute("font-weight",n)}this.layerElement.setAttribute("aria-label",r.t);var s,o=r.l||[],l=!!this.globalData.fontManager.chars;e=o.length;var h=this.mHelper,p=this.data.singleShape,c=0,f=0,d=!0,u=.001*r.tr*r.finalSize;if(!p||l||r.sz){var m,y=this.textSpans.length;for(t=0;t<e;t+=1){if(this.textSpans[t]||(this.textSpans[t]={span:null,childSpan:null,glyph:null}),!l||!p||0===t){if(s=y>t?this.textSpans[t].span:createNS(l?"g":"text"),y<=t){if(s.setAttribute("stroke-linecap","butt"),s.setAttribute("stroke-linejoin","round"),s.setAttribute("stroke-miterlimit","4"),this.textSpans[t].span=s,l){var g=createNS("g");s.appendChild(g),this.textSpans[t].childSpan=g}this.textSpans[t].span=s,this.layerElement.appendChild(s)}s.style.display="inherit"}if(h.reset(),p&&(o[t].n&&(c=-u,f+=r.yOffset,f+=d?1:0,d=!1),this.applyTextPropertiesToMatrix(r,h,o[t].line,c,f),c+=o[t].l||0,c+=u),l){var v;if(1===(m=this.globalData.fontManager.getCharData(r.finalText[t],i.fStyle,this.globalData.fontManager.getFontByName(r.f).fFamily)).t)v=new SVGCompElement(m.data,this.globalData,this);else{var b=emptyShapeData;m.data&&m.data.shapes&&(b=this.buildShapeData(m.data,r.finalSize)),v=new SVGShapeElement(b,this.globalData,this)}if(this.textSpans[t].glyph){var _=this.textSpans[t].glyph;this.textSpans[t].childSpan.removeChild(_.layerElement),_.destroy()}this.textSpans[t].glyph=v,v._debug=!0,v.prepareFrame(0),v.renderFrame(),this.textSpans[t].childSpan.appendChild(v.layerElement),1===m.t&&this.textSpans[t].childSpan.setAttribute("transform","scale("+r.finalSize/100+","+r.finalSize/100+")")}else p&&s.setAttribute("transform","translate("+h.props[12]+","+h.props[13]+")"),s.textContent=o[t].val,s.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve")}p&&s&&s.setAttribute("d","")}else{var P=this.textContainer,E="start";switch(r.j){case 1:E="end";break;case 2:E="middle";break;default:E="start"}P.setAttribute("text-anchor",E),P.setAttribute("letter-spacing",u);var S=this.buildTextContents(r.finalText);for(e=S.length,f=r.ps?r.ps[1]+r.ascent:0,t=0;t<e;t+=1)(s=this.textSpans[t].span||createNS("tspan")).textContent=S[t],s.setAttribute("x",0),s.setAttribute("y",f),s.style.display="inherit",P.appendChild(s),this.textSpans[t]||(this.textSpans[t]={span:null,glyph:null}),this.textSpans[t].span=s,f+=r.finalLineHeight;this.layerElement.appendChild(P)}for(;t<this.textSpans.length;)this.textSpans[t].span.style.display="none",t+=1;this._sizeChanged=!0},SVGTextLottieElement.prototype.sourceRectAtTime=function(){if(this.prepareFrame(this.comp.renderedFrame-this.data.st),this.renderInnerContent(),this._sizeChanged){this._sizeChanged=!1;var t=this.layerElement.getBBox();this.bbox={top:t.y,left:t.x,width:t.width,height:t.height}}return this.bbox},SVGTextLottieElement.prototype.getValue=function(){var t,e,r=this.textSpans.length;for(this.renderedFrame=this.comp.renderedFrame,t=0;t<r;t+=1)(e=this.textSpans[t].glyph)&&(e.prepareFrame(this.comp.renderedFrame-this.data.st),e._mdf&&(this._mdf=!0))},SVGTextLottieElement.prototype.renderInnerContent=function(){if((!this.data.singleShape||this._mdf)&&(this.textAnimator.getMeasures(this.textProperty.currentData,this.lettersChangedFlag),this.lettersChangedFlag||this.textAnimator.lettersChangedFlag)){var t,e;this._sizeChanged=!0;var r,i,n,a=this.textAnimator.renderedLetters,s=this.textProperty.currentData.l;for(e=s.length,t=0;t<e;t+=1)s[t].n||(r=a[t],i=this.textSpans[t].span,(n=this.textSpans[t].glyph)&&n.renderFrame(),r._mdf.m&&i.setAttribute("transform",r.m),r._mdf.o&&i.setAttribute("opacity",r.o),r._mdf.sw&&i.setAttribute("stroke-width",r.sw),r._mdf.sc&&i.setAttribute("stroke",r.sc),r._mdf.fc&&i.setAttribute("fill",r.fc))}},extendPrototype([IImageElement],ISolidElement),ISolidElement.prototype.createContent=function(){var t=createNS("rect");t.setAttribute("width",this.data.sw),t.setAttribute("height",this.data.sh),t.setAttribute("fill",this.data.sc),this.layerElement.appendChild(t)},NullElement.prototype.prepareFrame=function(t){this.prepareProperties(t,!0)},NullElement.prototype.renderFrame=function(){},NullElement.prototype.getBaseElement=function(){return null},NullElement.prototype.destroy=function(){},NullElement.prototype.sourceRectAtTime=function(){},NullElement.prototype.hide=function(){},extendPrototype([BaseElement,TransformElement,HierarchyElement,FrameElement],NullElement),extendPrototype([BaseRenderer],SVGRendererBase),SVGRendererBase.prototype.createNull=function(t){return new NullElement(t,this.globalData,this)},SVGRendererBase.prototype.createShape=function(t){return new SVGShapeElement(t,this.globalData,this)},SVGRendererBase.prototype.createText=function(t){return new SVGTextLottieElement(t,this.globalData,this)},SVGRendererBase.prototype.createImage=function(t){return new IImageElement(t,this.globalData,this)},SVGRendererBase.prototype.createSolid=function(t){return new ISolidElement(t,this.globalData,this)},SVGRendererBase.prototype.configAnimation=function(t){this.svgElement.setAttribute("xmlns","http://www.w3.org/2000/svg"),this.svgElement.setAttribute("xmlns:xlink","http://www.w3.org/1999/xlink"),this.renderConfig.viewBoxSize?this.svgElement.setAttribute("viewBox",this.renderConfig.viewBoxSize):this.svgElement.setAttribute("viewBox","0 0 "+t.w+" "+t.h),this.renderConfig.viewBoxOnly||(this.svgElement.setAttribute("width",t.w),this.svgElement.setAttribute("height",t.h),this.svgElement.style.width="100%",this.svgElement.style.height="100%",this.svgElement.style.transform="translate3d(0,0,0)",this.svgElement.style.contentVisibility=this.renderConfig.contentVisibility),this.renderConfig.width&&this.svgElement.setAttribute("width",this.renderConfig.width),this.renderConfig.height&&this.svgElement.setAttribute("height",this.renderConfig.height),this.renderConfig.className&&this.svgElement.setAttribute("class",this.renderConfig.className),this.renderConfig.id&&this.svgElement.setAttribute("id",this.renderConfig.id),void 0!==this.renderConfig.focusable&&this.svgElement.setAttribute("focusable",this.renderConfig.focusable),this.svgElement.setAttribute("preserveAspectRatio",this.renderConfig.preserveAspectRatio),this.animationItem.wrapper.appendChild(this.svgElement);var e=this.globalData.defs;this.setupGlobalData(t,e),this.globalData.progressiveLoad=this.renderConfig.progressiveLoad,this.data=t;var r=createNS("clipPath"),i=createNS("rect");i.setAttribute("width",t.w),i.setAttribute("height",t.h),i.setAttribute("x",0),i.setAttribute("y",0);var n=createElementID();r.setAttribute("id",n),r.appendChild(i),this.layerElement.setAttribute("clip-path","url("+getLocationHref()+"#"+n+")"),e.appendChild(r),this.layers=t.layers,this.elements=createSizedArray(t.layers.length)},SVGRendererBase.prototype.destroy=function(){var t;this.animationItem.wrapper&&(this.animationItem.wrapper.innerText=""),this.layerElement=null,this.globalData.defs=null;var e=this.layers?this.layers.length:0;for(t=0;t<e;t+=1)this.elements[t]&&this.elements[t].destroy();this.elements.length=0,this.destroyed=!0,this.animationItem=null},SVGRendererBase.prototype.updateContainerSize=function(){},SVGRendererBase.prototype.findIndexByInd=function(t){var e=0,r=this.layers.length;for(e=0;e<r;e+=1)if(this.layers[e].ind===t)return e;return-1},SVGRendererBase.prototype.buildItem=function(t){var e=this.elements;if(!e[t]&&99!==this.layers[t].ty){e[t]=!0;var r=this.createItem(this.layers[t]);if(e[t]=r,getExpressionsPlugin()&&(0===this.layers[t].ty&&this.globalData.projectInterface.registerComposition(r),r.initExpressions()),this.appendElementInPos(r,t),this.layers[t].tt){var i="tp"in this.layers[t]?this.findIndexByInd(this.layers[t].tp):t-1;if(-1===i)return;if(this.elements[i]&&!0!==this.elements[i]){var n=e[i].getMatte(this.layers[t].tt);r.setMatte(n)}else this.buildItem(i),this.addPendingElement(r)}}},SVGRendererBase.prototype.checkPendingElements=function(){for(;this.pendingElements.length;){var t=this.pendingElements.pop();if(t.checkParenting(),t.data.tt)for(var e=0,r=this.elements.length;e<r;){if(this.elements[e]===t){var i="tp"in t.data?this.findIndexByInd(t.data.tp):e-1,n=this.elements[i].getMatte(this.layers[e].tt);t.setMatte(n);break}e+=1}}},SVGRendererBase.prototype.renderFrame=function(t){if(this.renderedFrame!==t&&!this.destroyed){var e;null===t?t=this.renderedFrame:this.renderedFrame=t,this.globalData.frameNum=t,this.globalData.frameId+=1,this.globalData.projectInterface.currentFrame=t,this.globalData._mdf=!1;var r=this.layers.length;for(this.completeLayers||this.checkLayers(t),e=r-1;e>=0;e-=1)(this.completeLayers||this.elements[e])&&this.elements[e].prepareFrame(t-this.layers[e].st);if(this.globalData._mdf)for(e=0;e<r;e+=1)(this.completeLayers||this.elements[e])&&this.elements[e].renderFrame()}},SVGRendererBase.prototype.appendElementInPos=function(t,e){var r=t.getBaseElement();if(r){for(var i,n=0;n<e;)this.elements[n]&&!0!==this.elements[n]&&this.elements[n].getBaseElement()&&(i=this.elements[n].getBaseElement()),n+=1;i?this.layerElement.insertBefore(r,i):this.layerElement.appendChild(r)}},SVGRendererBase.prototype.hide=function(){this.layerElement.style.display="none"},SVGRendererBase.prototype.show=function(){this.layerElement.style.display="block"},extendPrototype([BaseElement,TransformElement,HierarchyElement,FrameElement,RenderableDOMElement],ICompElement),ICompElement.prototype.initElement=function(t,e,r){this.initFrame(),this.initBaseData(t,e,r),this.initTransform(t,e,r),this.initRenderable(),this.initHierarchy(),this.initRendererElement(),this.createContainerElements(),this.createRenderableComponents(),!this.data.xt&&e.progressiveLoad||this.buildAllItems(),this.hide()},ICompElement.prototype.prepareFrame=function(t){if(this._mdf=!1,this.prepareRenderableFrame(t),this.prepareProperties(t,this.isInRange),this.isInRange||this.data.xt){if(this.tm._placeholder)this.renderedFrame=t/this.data.sr;else{var e=this.tm.v;e===this.data.op&&(e=this.data.op-1),this.renderedFrame=e}var r,i=this.elements.length;for(this.completeLayers||this.checkLayers(this.renderedFrame),r=i-1;r>=0;r-=1)(this.completeLayers||this.elements[r])&&(this.elements[r].prepareFrame(this.renderedFrame-this.layers[r].st),this.elements[r]._mdf&&(this._mdf=!0))}},ICompElement.prototype.renderInnerContent=function(){var t,e=this.layers.length;for(t=0;t<e;t+=1)(this.completeLayers||this.elements[t])&&this.elements[t].renderFrame()},ICompElement.prototype.setElements=function(t){this.elements=t},ICompElement.prototype.getElements=function(){return this.elements},ICompElement.prototype.destroyElements=function(){var t,e=this.layers.length;for(t=0;t<e;t+=1)this.elements[t]&&this.elements[t].destroy()},ICompElement.prototype.destroy=function(){this.destroyElements(),this.destroyBaseElement()},extendPrototype([SVGRendererBase,ICompElement,SVGBaseElement],SVGCompElement),SVGCompElement.prototype.createComp=function(t){return new SVGCompElement(t,this.globalData,this)},extendPrototype([SVGRendererBase],SVGRenderer),SVGRenderer.prototype.createComp=function(t){return new SVGCompElement(t,this.globalData,this)},CVContextData.prototype.duplicate=function(){var t=2*this._length,e=this.savedOp;this.savedOp=createTypedArray("float32",t),this.savedOp.set(e);var r=0;for(r=this._length;r<t;r+=1)this.saved[r]=createTypedArray("float32",16);this._length=t},CVContextData.prototype.reset=function(){this.cArrPos=0,this.cTr.reset(),this.cO=1},ShapeTransformManager.prototype={addTransformSequence:function(t){var e,r=t.length,i="_";for(e=0;e<r;e+=1)i+=t[e].transform.key+"_";var n=this.sequences[i];return n||(n={transforms:[].concat(t),finalTransform:new Matrix,_mdf:!1},this.sequences[i]=n,this.sequenceList.push(n)),n},processSequence:function(t,e){for(var r,i=0,n=t.transforms.length,a=e;i<n&&!e;){if(t.transforms[i].transform.mProps._mdf){a=!0;break}i+=1}if(a)for(t.finalTransform.reset(),i=n-1;i>=0;i-=1)r=t.transforms[i].transform.mProps.v.props,t.finalTransform.transform(r[0],r[1],r[2],r[3],r[4],r[5],r[6],r[7],r[8],r[9],r[10],r[11],r[12],r[13],r[14],r[15]);t._mdf=a},processSequences:function(t){var e,r=this.sequenceList.length;for(e=0;e<r;e+=1)this.processSequence(this.sequenceList[e],t)},getNewKey:function(){return this.transform_key_count+=1,"_"+this.transform_key_count}},CVEffects.prototype.renderFrame=function(){},CVMaskElement.prototype.renderFrame=function(){if(this.hasMasks){var t,e,r,i,n=this.element.finalTransform.mat,a=this.element.canvasContext,s=this.masksProperties.length;for(a.beginPath(),t=0;t<s;t+=1)if("n"!==this.masksProperties[t].mode){var o;this.masksProperties[t].inv&&(a.moveTo(0,0),a.lineTo(this.element.globalData.compSize.w,0),a.lineTo(this.element.globalData.compSize.w,this.element.globalData.compSize.h),a.lineTo(0,this.element.globalData.compSize.h),a.lineTo(0,0)),i=this.viewData[t].v,e=n.applyToPointArray(i.v[0][0],i.v[0][1],0),a.moveTo(e[0],e[1]);var l=i._length;for(o=1;o<l;o+=1)r=n.applyToTriplePoints(i.o[o-1],i.i[o],i.v[o]),a.bezierCurveTo(r[0],r[1],r[2],r[3],r[4],r[5]);r=n.applyToTriplePoints(i.o[o-1],i.i[0],i.v[0]),a.bezierCurveTo(r[0],r[1],r[2],r[3],r[4],r[5])}this.element.globalData.renderer.save(!0),a.clip()}},CVMaskElement.prototype.getMaskProperty=MaskElement.prototype.getMaskProperty,CVMaskElement.prototype.destroy=function(){this.element=null},CVBaseElement.prototype={createElements:function(){},initRendererElement:function(){},createContainerElements:function(){this.canvasContext=this.globalData.canvasContext,this.renderableEffectsManager=new CVEffects(this)},createContent:function(){},setBlendMode:function(){var t=this.globalData;if(t.blendMode!==this.data.bm){t.blendMode=this.data.bm;var e=getBlendMode(this.data.bm);t.canvasContext.globalCompositeOperation=e}},createRenderableComponents:function(){this.maskManager=new CVMaskElement(this.data,this)},hideElement:function(){this.hidden||this.isInRange&&!this.isTransparent||(this.hidden=!0)},showElement:function(){this.isInRange&&!this.isTransparent&&(this.hidden=!1,this._isFirstFrame=!0,this.maskManager._isFirstFrame=!0)},renderFrame:function(){if(!this.hidden&&!this.data.hd){this.renderTransform(),this.renderRenderable(),this.setBlendMode();var t=0===this.data.ty;this.globalData.renderer.save(t),this.globalData.renderer.ctxTransform(this.finalTransform.mat.props),this.globalData.renderer.ctxOpacity(this.finalTransform.mProp.o.v),this.renderInnerContent(),this.globalData.renderer.restore(t),this.maskManager.hasMasks&&this.globalData.renderer.restore(!0),this._isFirstFrame&&(this._isFirstFrame=!1)}},destroy:function(){this.canvasContext=null,this.data=null,this.globalData=null,this.maskManager.destroy()},mHelper:new Matrix},CVBaseElement.prototype.hide=CVBaseElement.prototype.hideElement,CVBaseElement.prototype.show=CVBaseElement.prototype.showElement,CVShapeData.prototype.setAsAnimated=SVGShapeData.prototype.setAsAnimated,extendPrototype([BaseElement,TransformElement,CVBaseElement,IShapeElement,HierarchyElement,FrameElement,RenderableElement],CVShapeElement),CVShapeElement.prototype.initElement=RenderableDOMElement.prototype.initElement,CVShapeElement.prototype.transformHelper={opacity:1,_opMdf:!1},CVShapeElement.prototype.dashResetter=[],CVShapeElement.prototype.createContent=function(){this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,!0,[])},CVShapeElement.prototype.createStyleElement=function(t,e){var r={data:t,type:t.ty,preTransforms:this.transformsManager.addTransformSequence(e),transforms:[],elements:[],closed:!0===t.hd},i={};if("fl"===t.ty||"st"===t.ty?(i.c=PropertyFactory.getProp(this,t.c,1,255,this),i.c.k||(r.co="rgb("+bmFloor(i.c.v[0])+","+bmFloor(i.c.v[1])+","+bmFloor(i.c.v[2])+")")):"gf"!==t.ty&&"gs"!==t.ty||(i.s=PropertyFactory.getProp(this,t.s,1,null,this),i.e=PropertyFactory.getProp(this,t.e,1,null,this),i.h=PropertyFactory.getProp(this,t.h||{k:0},0,.01,this),i.a=PropertyFactory.getProp(this,t.a||{k:0},0,degToRads,this),i.g=new GradientProperty(this,t.g,this)),i.o=PropertyFactory.getProp(this,t.o,0,.01,this),"st"===t.ty||"gs"===t.ty){if(r.lc=lineCapEnum[t.lc||2],r.lj=lineJoinEnum[t.lj||2],1==t.lj&&(r.ml=t.ml),i.w=PropertyFactory.getProp(this,t.w,0,null,this),i.w.k||(r.wi=i.w.v),t.d){var n=new DashProperty(this,t.d,"canvas",this);i.d=n,i.d.k||(r.da=i.d.dashArray,r.do=i.d.dashoffset[0])}}else r.r=2===t.r?"evenodd":"nonzero";return this.stylesList.push(r),i.style=r,i},CVShapeElement.prototype.createGroupElement=function(){return{it:[],prevViewData:[]}},CVShapeElement.prototype.createTransformElement=function(t){return{transform:{opacity:1,_opMdf:!1,key:this.transformsManager.getNewKey(),op:PropertyFactory.getProp(this,t.o,0,.01,this),mProps:TransformPropertyFactory.getTransformProperty(this,t,this)}}},CVShapeElement.prototype.createShapeElement=function(t){var e=new CVShapeData(this,t,this.stylesList,this.transformsManager);return this.shapes.push(e),this.addShapeToModifiers(e),e},CVShapeElement.prototype.reloadShapes=function(){var t;this._isFirstFrame=!0;var e=this.itemsData.length;for(t=0;t<e;t+=1)this.prevViewData[t]=this.itemsData[t];for(this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,!0,[]),e=this.dynamicProperties.length,t=0;t<e;t+=1)this.dynamicProperties[t].getValue();this.renderModifiers(),this.transformsManager.processSequences(this._isFirstFrame)},CVShapeElement.prototype.addTransformToStyleList=function(t){var e,r=this.stylesList.length;for(e=0;e<r;e+=1)this.stylesList[e].closed||this.stylesList[e].transforms.push(t)},CVShapeElement.prototype.removeTransformFromStyleList=function(){var t,e=this.stylesList.length;for(t=0;t<e;t+=1)this.stylesList[t].closed||this.stylesList[t].transforms.pop()},CVShapeElement.prototype.closeStyles=function(t){var e,r=t.length;for(e=0;e<r;e+=1)t[e].closed=!0},CVShapeElement.prototype.searchShapes=function(t,e,r,i,n){var a,s,o,l,h,p,c=t.length-1,f=[],d=[],u=[].concat(n);for(a=c;a>=0;a-=1){if((l=this.searchProcessedElement(t[a]))?e[a]=r[l-1]:t[a]._shouldRender=i,"fl"===t[a].ty||"st"===t[a].ty||"gf"===t[a].ty||"gs"===t[a].ty)l?e[a].style.closed=!1:e[a]=this.createStyleElement(t[a],u),f.push(e[a].style);else if("gr"===t[a].ty){if(l)for(o=e[a].it.length,s=0;s<o;s+=1)e[a].prevViewData[s]=e[a].it[s];else e[a]=this.createGroupElement(t[a]);this.searchShapes(t[a].it,e[a].it,e[a].prevViewData,i,u)}else"tr"===t[a].ty?(l||(p=this.createTransformElement(t[a]),e[a]=p),u.push(e[a]),this.addTransformToStyleList(e[a])):"sh"===t[a].ty||"rc"===t[a].ty||"el"===t[a].ty||"sr"===t[a].ty?l||(e[a]=this.createShapeElement(t[a])):"tm"===t[a].ty||"rd"===t[a].ty||"pb"===t[a].ty||"zz"===t[a].ty||"op"===t[a].ty?(l?(h=e[a]).closed=!1:((h=ShapeModifiers.getModifier(t[a].ty)).init(this,t[a]),e[a]=h,this.shapeModifiers.push(h)),d.push(h)):"rp"===t[a].ty&&(l?(h=e[a]).closed=!0:(h=ShapeModifiers.getModifier(t[a].ty),e[a]=h,h.init(this,t,a,e),this.shapeModifiers.push(h),i=!1),d.push(h));this.addProcessedElement(t[a],a+1)}for(this.removeTransformFromStyleList(),this.closeStyles(f),c=d.length,a=0;a<c;a+=1)d[a].closed=!0},CVShapeElement.prototype.renderInnerContent=function(){this.transformHelper.opacity=1,this.transformHelper._opMdf=!1,this.renderModifiers(),this.transformsManager.processSequences(this._isFirstFrame),this.renderShape(this.transformHelper,this.shapesData,this.itemsData,!0)},CVShapeElement.prototype.renderShapeTransform=function(t,e){(t._opMdf||e.op._mdf||this._isFirstFrame)&&(e.opacity=t.opacity,e.opacity*=e.op.v,e._opMdf=!0)},CVShapeElement.prototype.drawLayer=function(){var t,e,r,i,n,a,s,o,l,h=this.stylesList.length,p=this.globalData.renderer,c=this.globalData.canvasContext;for(t=0;t<h;t+=1)if(("st"!==(o=(l=this.stylesList[t]).type)&&"gs"!==o||0!==l.wi)&&l.data._shouldRender&&0!==l.coOp&&0!==this.globalData.currentGlobalAlpha){for(p.save(),a=l.elements,"st"===o||"gs"===o?(c.strokeStyle="st"===o?l.co:l.grd,c.lineWidth=l.wi,c.lineCap=l.lc,c.lineJoin=l.lj,c.miterLimit=l.ml||0):c.fillStyle="fl"===o?l.co:l.grd,p.ctxOpacity(l.coOp),"st"!==o&&"gs"!==o&&c.beginPath(),p.ctxTransform(l.preTransforms.finalTransform.props),r=a.length,e=0;e<r;e+=1){for("st"!==o&&"gs"!==o||(c.beginPath(),l.da&&(c.setLineDash(l.da),c.lineDashOffset=l.do)),n=(s=a[e].trNodes).length,i=0;i<n;i+=1)"m"===s[i].t?c.moveTo(s[i].p[0],s[i].p[1]):"c"===s[i].t?c.bezierCurveTo(s[i].pts[0],s[i].pts[1],s[i].pts[2],s[i].pts[3],s[i].pts[4],s[i].pts[5]):c.closePath();"st"!==o&&"gs"!==o||(c.stroke(),l.da&&c.setLineDash(this.dashResetter))}"st"!==o&&"gs"!==o&&c.fill(l.r),p.restore()}},CVShapeElement.prototype.renderShape=function(t,e,r,i){var n,a;for(a=t,n=e.length-1;n>=0;n-=1)"tr"===e[n].ty?(a=r[n].transform,this.renderShapeTransform(t,a)):"sh"===e[n].ty||"el"===e[n].ty||"rc"===e[n].ty||"sr"===e[n].ty?this.renderPath(e[n],r[n]):"fl"===e[n].ty?this.renderFill(e[n],r[n],a):"st"===e[n].ty?this.renderStroke(e[n],r[n],a):"gf"===e[n].ty||"gs"===e[n].ty?this.renderGradientFill(e[n],r[n],a):"gr"===e[n].ty?this.renderShape(a,e[n].it,r[n].it):e[n].ty;i&&this.drawLayer()},CVShapeElement.prototype.renderStyledShape=function(t,e){if(this._isFirstFrame||e._mdf||t.transforms._mdf){var r,i,n,a=t.trNodes,s=e.paths,o=s._length;a.length=0;var l=t.transforms.finalTransform;for(n=0;n<o;n+=1){var h=s.shapes[n];if(h&&h.v){for(i=h._length,r=1;r<i;r+=1)1===r&&a.push({t:"m",p:l.applyToPointArray(h.v[0][0],h.v[0][1],0)}),a.push({t:"c",pts:l.applyToTriplePoints(h.o[r-1],h.i[r],h.v[r])});1===i&&a.push({t:"m",p:l.applyToPointArray(h.v[0][0],h.v[0][1],0)}),h.c&&i&&(a.push({t:"c",pts:l.applyToTriplePoints(h.o[r-1],h.i[0],h.v[0])}),a.push({t:"z"}))}}t.trNodes=a}},CVShapeElement.prototype.renderPath=function(t,e){if(!0!==t.hd&&t._shouldRender){var r,i=e.styledShapes.length;for(r=0;r<i;r+=1)this.renderStyledShape(e.styledShapes[r],e.sh)}},CVShapeElement.prototype.renderFill=function(t,e,r){var i=e.style;(e.c._mdf||this._isFirstFrame)&&(i.co="rgb("+bmFloor(e.c.v[0])+","+bmFloor(e.c.v[1])+","+bmFloor(e.c.v[2])+")"),(e.o._mdf||r._opMdf||this._isFirstFrame)&&(i.coOp=e.o.v*r.opacity)},CVShapeElement.prototype.renderGradientFill=function(t,e,r){var i,n=e.style;if(!n.grd||e.g._mdf||e.s._mdf||e.e._mdf||1!==t.t&&(e.h._mdf||e.a._mdf)){var a,s=this.globalData.canvasContext,o=e.s.v,l=e.e.v;if(1===t.t)i=s.createLinearGradient(o[0],o[1],l[0],l[1]);else{var h=Math.sqrt(Math.pow(o[0]-l[0],2)+Math.pow(o[1]-l[1],2)),p=Math.atan2(l[1]-o[1],l[0]-o[0]),c=e.h.v;c>=1?c=.99:c<=-1&&(c=-.99);var f=h*c,d=Math.cos(p+e.a.v)*f+o[0],u=Math.sin(p+e.a.v)*f+o[1];i=s.createRadialGradient(d,u,0,o[0],o[1],h)}var m=t.g.p,y=e.g.c,g=1;for(a=0;a<m;a+=1)e.g._hasOpacity&&e.g._collapsable&&(g=e.g.o[2*a+1]),i.addColorStop(y[4*a]/100,"rgba("+y[4*a+1]+","+y[4*a+2]+","+y[4*a+3]+","+g+")");n.grd=i}n.coOp=e.o.v*r.opacity},CVShapeElement.prototype.renderStroke=function(t,e,r){var i=e.style,n=e.d;n&&(n._mdf||this._isFirstFrame)&&(i.da=n.dashArray,i.do=n.dashoffset[0]),(e.c._mdf||this._isFirstFrame)&&(i.co="rgb("+bmFloor(e.c.v[0])+","+bmFloor(e.c.v[1])+","+bmFloor(e.c.v[2])+")"),(e.o._mdf||r._opMdf||this._isFirstFrame)&&(i.coOp=e.o.v*r.opacity),(e.w._mdf||this._isFirstFrame)&&(i.wi=e.w.v)},CVShapeElement.prototype.destroy=function(){this.shapesData=null,this.globalData=null,this.canvasContext=null,this.stylesList.length=0,this.itemsData.length=0},extendPrototype([BaseElement,TransformElement,CVBaseElement,HierarchyElement,FrameElement,RenderableElement,ITextElement],CVTextElement),CVTextElement.prototype.tHelper=createTag("canvas").getContext("2d"),CVTextElement.prototype.buildNewText=function(){var t=this.textProperty.currentData;this.renderedLetters=createSizedArray(t.l?t.l.length:0);var e=!1;t.fc?(e=!0,this.values.fill=this.buildColor(t.fc)):this.values.fill="rgba(0,0,0,0)",this.fill=e;var r=!1;t.sc&&(r=!0,this.values.stroke=this.buildColor(t.sc),this.values.sWidth=t.sw);var i,n,a,s,o,l,h,p,c,f,d,u,m=this.globalData.fontManager.getFontByName(t.f),y=t.l,g=this.mHelper;this.stroke=r,this.values.fValue=t.finalSize+"px "+this.globalData.fontManager.getFontByName(t.f).fFamily,n=t.finalText.length;var v=this.data.singleShape,b=.001*t.tr*t.finalSize,_=0,P=0,E=!0,S=0;for(i=0;i<n;i+=1){s=(a=this.globalData.fontManager.getCharData(t.finalText[i],m.fStyle,this.globalData.fontManager.getFontByName(t.f).fFamily))&&a.data||{},g.reset(),v&&y[i].n&&(_=-b,P+=t.yOffset,P+=E?1:0,E=!1),c=(h=s.shapes?s.shapes[0].it:[]).length,g.scale(t.finalSize/100,t.finalSize/100),v&&this.applyTextPropertiesToMatrix(t,g,y[i].line,_,P),d=createSizedArray(c-1);var x=0;for(p=0;p<c;p+=1)if("sh"===h[p].ty){for(l=h[p].ks.k.i.length,f=h[p].ks.k,u=[],o=1;o<l;o+=1)1===o&&u.push(g.applyToX(f.v[0][0],f.v[0][1],0),g.applyToY(f.v[0][0],f.v[0][1],0)),u.push(g.applyToX(f.o[o-1][0],f.o[o-1][1],0),g.applyToY(f.o[o-1][0],f.o[o-1][1],0),g.applyToX(f.i[o][0],f.i[o][1],0),g.applyToY(f.i[o][0],f.i[o][1],0),g.applyToX(f.v[o][0],f.v[o][1],0),g.applyToY(f.v[o][0],f.v[o][1],0));u.push(g.applyToX(f.o[o-1][0],f.o[o-1][1],0),g.applyToY(f.o[o-1][0],f.o[o-1][1],0),g.applyToX(f.i[0][0],f.i[0][1],0),g.applyToY(f.i[0][0],f.i[0][1],0),g.applyToX(f.v[0][0],f.v[0][1],0),g.applyToY(f.v[0][0],f.v[0][1],0)),d[x]=u,x+=1}v&&(_+=y[i].l,_+=b),this.textSpans[S]?this.textSpans[S].elem=d:this.textSpans[S]={elem:d},S+=1}},CVTextElement.prototype.renderInnerContent=function(){var t,e,r,i,n,a,s=this.canvasContext;s.font=this.values.fValue,s.lineCap="butt",s.lineJoin="miter",s.miterLimit=4,this.data.singleShape||this.textAnimator.getMeasures(this.textProperty.currentData,this.lettersChangedFlag);var o,l=this.textAnimator.renderedLetters,h=this.textProperty.currentData.l;e=h.length;var p,c,f=null,d=null,u=null;for(t=0;t<e;t+=1)if(!h[t].n){if((o=l[t])&&(this.globalData.renderer.save(),this.globalData.renderer.ctxTransform(o.p),this.globalData.renderer.ctxOpacity(o.o)),this.fill){for(o&&o.fc?f!==o.fc&&(f=o.fc,s.fillStyle=o.fc):f!==this.values.fill&&(f=this.values.fill,s.fillStyle=this.values.fill),i=(p=this.textSpans[t].elem).length,this.globalData.canvasContext.beginPath(),r=0;r<i;r+=1)for(a=(c=p[r]).length,this.globalData.canvasContext.moveTo(c[0],c[1]),n=2;n<a;n+=6)this.globalData.canvasContext.bezierCurveTo(c[n],c[n+1],c[n+2],c[n+3],c[n+4],c[n+5]);this.globalData.canvasContext.closePath(),this.globalData.canvasContext.fill()}if(this.stroke){for(o&&o.sw?u!==o.sw&&(u=o.sw,s.lineWidth=o.sw):u!==this.values.sWidth&&(u=this.values.sWidth,s.lineWidth=this.values.sWidth),o&&o.sc?d!==o.sc&&(d=o.sc,s.strokeStyle=o.sc):d!==this.values.stroke&&(d=this.values.stroke,s.strokeStyle=this.values.stroke),i=(p=this.textSpans[t].elem).length,this.globalData.canvasContext.beginPath(),r=0;r<i;r+=1)for(a=(c=p[r]).length,this.globalData.canvasContext.moveTo(c[0],c[1]),n=2;n<a;n+=6)this.globalData.canvasContext.bezierCurveTo(c[n],c[n+1],c[n+2],c[n+3],c[n+4],c[n+5]);this.globalData.canvasContext.closePath(),this.globalData.canvasContext.stroke()}o&&this.globalData.renderer.restore()}},extendPrototype([BaseElement,TransformElement,CVBaseElement,HierarchyElement,FrameElement,RenderableElement],CVImageElement),CVImageElement.prototype.initElement=SVGShapeElement.prototype.initElement,CVImageElement.prototype.prepareFrame=IImageElement.prototype.prepareFrame,CVImageElement.prototype.createContent=function(){if(this.img.width&&(this.assetData.w!==this.img.width||this.assetData.h!==this.img.height)){var t=createTag("canvas");t.width=this.assetData.w,t.height=this.assetData.h;var e,r,i=t.getContext("2d"),n=this.img.width,a=this.img.height,s=n/a,o=this.assetData.w/this.assetData.h,l=this.assetData.pr||this.globalData.renderConfig.imagePreserveAspectRatio;s>o&&"xMidYMid slice"===l||s<o&&"xMidYMid slice"!==l?e=(r=a)*o:r=(e=n)/o,i.drawImage(this.img,(n-e)/2,(a-r)/2,e,r,0,0,this.assetData.w,this.assetData.h),this.img=t}},CVImageElement.prototype.renderInnerContent=function(){this.canvasContext.drawImage(this.img,0,0)},CVImageElement.prototype.destroy=function(){this.img=null},extendPrototype([BaseElement,TransformElement,CVBaseElement,HierarchyElement,FrameElement,RenderableElement],CVSolidElement),CVSolidElement.prototype.initElement=SVGShapeElement.prototype.initElement,CVSolidElement.prototype.prepareFrame=IImageElement.prototype.prepareFrame,CVSolidElement.prototype.renderInnerContent=function(){var t=this.canvasContext;t.fillStyle=this.data.sc,t.fillRect(0,0,this.data.sw,this.data.sh)},extendPrototype([BaseRenderer],CanvasRendererBase),CanvasRendererBase.prototype.createShape=function(t){return new CVShapeElement(t,this.globalData,this)},CanvasRendererBase.prototype.createText=function(t){return new CVTextElement(t,this.globalData,this)},CanvasRendererBase.prototype.createImage=function(t){return new CVImageElement(t,this.globalData,this)},CanvasRendererBase.prototype.createSolid=function(t){return new CVSolidElement(t,this.globalData,this)},CanvasRendererBase.prototype.createNull=SVGRenderer.prototype.createNull,CanvasRendererBase.prototype.ctxTransform=function(t){if(1!==t[0]||0!==t[1]||0!==t[4]||1!==t[5]||0!==t[12]||0!==t[13])if(this.renderConfig.clearCanvas){this.transformMat.cloneFromProps(t);var e=this.contextData.cTr.props;this.transformMat.transform(e[0],e[1],e[2],e[3],e[4],e[5],e[6],e[7],e[8],e[9],e[10],e[11],e[12],e[13],e[14],e[15]),this.contextData.cTr.cloneFromProps(this.transformMat.props);var r=this.contextData.cTr.props;this.canvasContext.setTransform(r[0],r[1],r[4],r[5],r[12],r[13])}else this.canvasContext.transform(t[0],t[1],t[4],t[5],t[12],t[13])},CanvasRendererBase.prototype.ctxOpacity=function(t){if(!this.renderConfig.clearCanvas)return this.canvasContext.globalAlpha*=t<0?0:t,void(this.globalData.currentGlobalAlpha=this.contextData.cO);this.contextData.cO*=t<0?0:t,this.globalData.currentGlobalAlpha!==this.contextData.cO&&(this.canvasContext.globalAlpha=this.contextData.cO,this.globalData.currentGlobalAlpha=this.contextData.cO)},CanvasRendererBase.prototype.reset=function(){this.renderConfig.clearCanvas?this.contextData.reset():this.canvasContext.restore()},CanvasRendererBase.prototype.save=function(t){if(this.renderConfig.clearCanvas){t&&this.canvasContext.save();var e,r=this.contextData.cTr.props;this.contextData._length<=this.contextData.cArrPos&&this.contextData.duplicate();var i=this.contextData.saved[this.contextData.cArrPos];for(e=0;e<16;e+=1)i[e]=r[e];this.contextData.savedOp[this.contextData.cArrPos]=this.contextData.cO,this.contextData.cArrPos+=1}else this.canvasContext.save()},CanvasRendererBase.prototype.restore=function(t){if(this.renderConfig.clearCanvas){t&&(this.canvasContext.restore(),this.globalData.blendMode="source-over"),this.contextData.cArrPos-=1;var e,r=this.contextData.saved[this.contextData.cArrPos],i=this.contextData.cTr.props;for(e=0;e<16;e+=1)i[e]=r[e];this.canvasContext.setTransform(r[0],r[1],r[4],r[5],r[12],r[13]),r=this.contextData.savedOp[this.contextData.cArrPos],this.contextData.cO=r,this.globalData.currentGlobalAlpha!==r&&(this.canvasContext.globalAlpha=r,this.globalData.currentGlobalAlpha=r)}else this.canvasContext.restore()},CanvasRendererBase.prototype.configAnimation=function(t){if(this.animationItem.wrapper){this.animationItem.container=createTag("canvas");var e=this.animationItem.container.style;e.width="100%",e.height="100%";var r="0px 0px 0px";e.transformOrigin=r,e.mozTransformOrigin=r,e.webkitTransformOrigin=r,e["-webkit-transform"]=r,e.contentVisibility=this.renderConfig.contentVisibility,this.animationItem.wrapper.appendChild(this.animationItem.container),this.canvasContext=this.animationItem.container.getContext("2d"),this.renderConfig.className&&this.animationItem.container.setAttribute("class",this.renderConfig.className),this.renderConfig.id&&this.animationItem.container.setAttribute("id",this.renderConfig.id)}else this.canvasContext=this.renderConfig.context;this.data=t,this.layers=t.layers,this.transformCanvas={w:t.w,h:t.h,sx:0,sy:0,tx:0,ty:0},this.setupGlobalData(t,document.body),this.globalData.canvasContext=this.canvasContext,this.globalData.renderer=this,this.globalData.isDashed=!1,this.globalData.progressiveLoad=this.renderConfig.progressiveLoad,this.globalData.transformCanvas=this.transformCanvas,this.elements=createSizedArray(t.layers.length),this.updateContainerSize()},CanvasRendererBase.prototype.updateContainerSize=function(t,e){var r,i,n,a;if(this.reset(),t?(r=t,i=e,this.canvasContext.canvas.width=r,this.canvasContext.canvas.height=i):(this.animationItem.wrapper&&this.animationItem.container?(r=this.animationItem.wrapper.offsetWidth,i=this.animationItem.wrapper.offsetHeight):(r=this.canvasContext.canvas.width,i=this.canvasContext.canvas.height),this.canvasContext.canvas.width=r*this.renderConfig.dpr,this.canvasContext.canvas.height=i*this.renderConfig.dpr),-1!==this.renderConfig.preserveAspectRatio.indexOf("meet")||-1!==this.renderConfig.preserveAspectRatio.indexOf("slice")){var s=this.renderConfig.preserveAspectRatio.split(" "),o=s[1]||"meet",l=s[0]||"xMidYMid",h=l.substr(0,4),p=l.substr(4);n=r/i,(a=this.transformCanvas.w/this.transformCanvas.h)>n&&"meet"===o||a<n&&"slice"===o?(this.transformCanvas.sx=r/(this.transformCanvas.w/this.renderConfig.dpr),this.transformCanvas.sy=r/(this.transformCanvas.w/this.renderConfig.dpr)):(this.transformCanvas.sx=i/(this.transformCanvas.h/this.renderConfig.dpr),this.transformCanvas.sy=i/(this.transformCanvas.h/this.renderConfig.dpr)),this.transformCanvas.tx="xMid"===h&&(a<n&&"meet"===o||a>n&&"slice"===o)?(r-this.transformCanvas.w*(i/this.transformCanvas.h))/2*this.renderConfig.dpr:"xMax"===h&&(a<n&&"meet"===o||a>n&&"slice"===o)?(r-this.transformCanvas.w*(i/this.transformCanvas.h))*this.renderConfig.dpr:0,this.transformCanvas.ty="YMid"===p&&(a>n&&"meet"===o||a<n&&"slice"===o)?(i-this.transformCanvas.h*(r/this.transformCanvas.w))/2*this.renderConfig.dpr:"YMax"===p&&(a>n&&"meet"===o||a<n&&"slice"===o)?(i-this.transformCanvas.h*(r/this.transformCanvas.w))*this.renderConfig.dpr:0}else"none"===this.renderConfig.preserveAspectRatio?(this.transformCanvas.sx=r/(this.transformCanvas.w/this.renderConfig.dpr),this.transformCanvas.sy=i/(this.transformCanvas.h/this.renderConfig.dpr),this.transformCanvas.tx=0,this.transformCanvas.ty=0):(this.transformCanvas.sx=this.renderConfig.dpr,this.transformCanvas.sy=this.renderConfig.dpr,this.transformCanvas.tx=0,this.transformCanvas.ty=0);this.transformCanvas.props=[this.transformCanvas.sx,0,0,0,0,this.transformCanvas.sy,0,0,0,0,1,0,this.transformCanvas.tx,this.transformCanvas.ty,0,1],this.ctxTransform(this.transformCanvas.props),this.canvasContext.beginPath(),this.canvasContext.rect(0,0,this.transformCanvas.w,this.transformCanvas.h),this.canvasContext.closePath(),this.canvasContext.clip(),this.renderFrame(this.renderedFrame,!0)},CanvasRendererBase.prototype.destroy=function(){var t;for(this.renderConfig.clearCanvas&&this.animationItem.wrapper&&(this.animationItem.wrapper.innerText=""),t=(this.layers?this.layers.length:0)-1;t>=0;t-=1)this.elements[t]&&this.elements[t].destroy();this.elements.length=0,this.globalData.canvasContext=null,this.animationItem.container=null,this.destroyed=!0},CanvasRendererBase.prototype.renderFrame=function(t,e){if((this.renderedFrame!==t||!0!==this.renderConfig.clearCanvas||e)&&!this.destroyed&&-1!==t){var r;this.renderedFrame=t,this.globalData.frameNum=t-this.animationItem._isFirstFrame,this.globalData.frameId+=1,this.globalData._mdf=!this.renderConfig.clearCanvas||e,this.globalData.projectInterface.currentFrame=t;var i=this.layers.length;for(this.completeLayers||this.checkLayers(t),r=0;r<i;r+=1)(this.completeLayers||this.elements[r])&&this.elements[r].prepareFrame(t-this.layers[r].st);if(this.globalData._mdf){for(!0===this.renderConfig.clearCanvas?this.canvasContext.clearRect(0,0,this.transformCanvas.w,this.transformCanvas.h):this.save(),r=i-1;r>=0;r-=1)(this.completeLayers||this.elements[r])&&this.elements[r].renderFrame();!0!==this.renderConfig.clearCanvas&&this.restore()}}},CanvasRendererBase.prototype.buildItem=function(t){var e=this.elements;if(!e[t]&&99!==this.layers[t].ty){var r=this.createItem(this.layers[t],this,this.globalData);e[t]=r,r.initExpressions()}},CanvasRendererBase.prototype.checkPendingElements=function(){for(;this.pendingElements.length;)this.pendingElements.pop().checkParenting()},CanvasRendererBase.prototype.hide=function(){this.animationItem.container.style.display="none"},CanvasRendererBase.prototype.show=function(){this.animationItem.container.style.display="block"},extendPrototype([CanvasRendererBase,ICompElement,CVBaseElement],CVCompElement),CVCompElement.prototype.renderInnerContent=function(){var t,e=this.canvasContext;for(e.beginPath(),e.moveTo(0,0),e.lineTo(this.data.w,0),e.lineTo(this.data.w,this.data.h),e.lineTo(0,this.data.h),e.lineTo(0,0),e.clip(),t=this.layers.length-1;t>=0;t-=1)(this.completeLayers||this.elements[t])&&this.elements[t].renderFrame()},CVCompElement.prototype.destroy=function(){var t;for(t=this.layers.length-1;t>=0;t-=1)this.elements[t]&&this.elements[t].destroy();this.layers=null,this.elements=null},CVCompElement.prototype.createComp=function(t){return new CVCompElement(t,this.globalData,this)},extendPrototype([CanvasRendererBase],CanvasRenderer),CanvasRenderer.prototype.createComp=function(t){return new CVCompElement(t,this.globalData,this)},HBaseElement.prototype={checkBlendMode:function(){},initRendererElement:function(){this.baseElement=createTag(this.data.tg||"div"),this.data.hasMask?(this.svgElement=createNS("svg"),this.layerElement=createNS("g"),this.maskedElement=this.layerElement,this.svgElement.appendChild(this.layerElement),this.baseElement.appendChild(this.svgElement)):this.layerElement=this.baseElement,styleDiv(this.baseElement)},createContainerElements:function(){this.renderableEffectsManager=new CVEffects(this),this.transformedElement=this.baseElement,this.maskedElement=this.layerElement,this.data.ln&&this.layerElement.setAttribute("id",this.data.ln),this.data.cl&&this.layerElement.setAttribute("class",this.data.cl),0!==this.data.bm&&this.setBlendMode()},renderElement:function(){var t=this.transformedElement?this.transformedElement.style:{};if(this.finalTransform._matMdf){var e=this.finalTransform.mat.toCSS();t.transform=e,t.webkitTransform=e}this.finalTransform._opMdf&&(t.opacity=this.finalTransform.mProp.o.v)},renderFrame:function(){this.data.hd||this.hidden||(this.renderTransform(),this.renderRenderable(),this.renderElement(),this.renderInnerContent(),this._isFirstFrame&&(this._isFirstFrame=!1))},destroy:function(){this.layerElement=null,this.transformedElement=null,this.matteElement&&(this.matteElement=null),this.maskManager&&(this.maskManager.destroy(),this.maskManager=null)},createRenderableComponents:function(){this.maskManager=new MaskElement(this.data,this,this.globalData)},addEffects:function(){},setMatte:function(){}},HBaseElement.prototype.getBaseElement=SVGBaseElement.prototype.getBaseElement,HBaseElement.prototype.destroyBaseElement=HBaseElement.prototype.destroy,HBaseElement.prototype.buildElementParenting=BaseRenderer.prototype.buildElementParenting,extendPrototype([BaseElement,TransformElement,HBaseElement,HierarchyElement,FrameElement,RenderableDOMElement],HSolidElement),HSolidElement.prototype.createContent=function(){var t;this.data.hasMask?((t=createNS("rect")).setAttribute("width",this.data.sw),t.setAttribute("height",this.data.sh),t.setAttribute("fill",this.data.sc),this.svgElement.setAttribute("width",this.data.sw),this.svgElement.setAttribute("height",this.data.sh)):((t=createTag("div")).style.width=this.data.sw+"px",t.style.height=this.data.sh+"px",t.style.backgroundColor=this.data.sc),this.layerElement.appendChild(t)},extendPrototype([BaseElement,TransformElement,HSolidElement,SVGShapeElement,HBaseElement,HierarchyElement,FrameElement,RenderableElement],HShapeElement),HShapeElement.prototype._renderShapeFrame=HShapeElement.prototype.renderInnerContent,HShapeElement.prototype.createContent=function(){var t;if(this.baseElement.style.fontSize=0,this.data.hasMask)this.layerElement.appendChild(this.shapesContainer),t=this.svgElement;else{t=createNS("svg");var e=this.comp.data?this.comp.data:this.globalData.compSize;t.setAttribute("width",e.w),t.setAttribute("height",e.h),t.appendChild(this.shapesContainer),this.layerElement.appendChild(t)}this.searchShapes(this.shapesData,this.itemsData,this.prevViewData,this.shapesContainer,0,[],!0),this.filterUniqueShapes(),this.shapeCont=t},HShapeElement.prototype.getTransformedPoint=function(t,e){var r,i=t.length;for(r=0;r<i;r+=1)e=t[r].mProps.v.applyToPointArray(e[0],e[1],0);return e},HShapeElement.prototype.calculateShapeBoundingBox=function(t,e){var r,i,n,a,s,o=t.sh.v,l=t.transformers,h=o._length;if(!(h<=1)){for(r=0;r<h-1;r+=1)i=this.getTransformedPoint(l,o.v[r]),n=this.getTransformedPoint(l,o.o[r]),a=this.getTransformedPoint(l,o.i[r+1]),s=this.getTransformedPoint(l,o.v[r+1]),this.checkBounds(i,n,a,s,e);o.c&&(i=this.getTransformedPoint(l,o.v[r]),n=this.getTransformedPoint(l,o.o[r]),a=this.getTransformedPoint(l,o.i[0]),s=this.getTransformedPoint(l,o.v[0]),this.checkBounds(i,n,a,s,e))}},HShapeElement.prototype.checkBounds=function(t,e,r,i,n){this.getBoundsOfCurve(t,e,r,i);var a=this.shapeBoundingBox;n.x=bmMin(a.left,n.x),n.xMax=bmMax(a.right,n.xMax),n.y=bmMin(a.top,n.y),n.yMax=bmMax(a.bottom,n.yMax)},HShapeElement.prototype.shapeBoundingBox={left:0,right:0,top:0,bottom:0},HShapeElement.prototype.tempBoundingBox={x:0,xMax:0,y:0,yMax:0,width:0,height:0},HShapeElement.prototype.getBoundsOfCurve=function(t,e,r,i){for(var n,a,s,o,l,h,p,c=[[t[0],i[0]],[t[1],i[1]]],f=0;f<2;++f)a=6*t[f]-12*e[f]+6*r[f],n=-3*t[f]+9*e[f]-9*r[f]+3*i[f],s=3*e[f]-3*t[f],a|=0,s|=0,0==(n|=0)&&0===a||(0===n?(o=-s/a)>0&&o<1&&c[f].push(this.calculateF(o,t,e,r,i,f)):(l=a*a-4*s*n)>=0&&((h=(-a+bmSqrt(l))/(2*n))>0&&h<1&&c[f].push(this.calculateF(h,t,e,r,i,f)),(p=(-a-bmSqrt(l))/(2*n))>0&&p<1&&c[f].push(this.calculateF(p,t,e,r,i,f))));this.shapeBoundingBox.left=bmMin.apply(null,c[0]),this.shapeBoundingBox.top=bmMin.apply(null,c[1]),this.shapeBoundingBox.right=bmMax.apply(null,c[0]),this.shapeBoundingBox.bottom=bmMax.apply(null,c[1])},HShapeElement.prototype.calculateF=function(t,e,r,i,n,a){return bmPow(1-t,3)*e[a]+3*bmPow(1-t,2)*t*r[a]+3*(1-t)*bmPow(t,2)*i[a]+bmPow(t,3)*n[a]},HShapeElement.prototype.calculateBoundingBox=function(t,e){var r,i=t.length;for(r=0;r<i;r+=1)t[r]&&t[r].sh?this.calculateShapeBoundingBox(t[r],e):t[r]&&t[r].it?this.calculateBoundingBox(t[r].it,e):t[r]&&t[r].style&&t[r].w&&this.expandStrokeBoundingBox(t[r].w,e)},HShapeElement.prototype.expandStrokeBoundingBox=function(t,e){var r=0;if(t.keyframes){for(var i=0;i<t.keyframes.length;i+=1){var n=t.keyframes[i].s;n>r&&(r=n)}r*=t.mult}else r=t.v*t.mult;e.x-=r,e.xMax+=r,e.y-=r,e.yMax+=r},HShapeElement.prototype.currentBoxContains=function(t){return this.currentBBox.x<=t.x&&this.currentBBox.y<=t.y&&this.currentBBox.width+this.currentBBox.x>=t.x+t.width&&this.currentBBox.height+this.currentBBox.y>=t.y+t.height},HShapeElement.prototype.renderInnerContent=function(){if(this._renderShapeFrame(),!this.hidden&&(this._isFirstFrame||this._mdf)){var t=this.tempBoundingBox,e=999999;if(t.x=e,t.xMax=-e,t.y=e,t.yMax=-e,this.calculateBoundingBox(this.itemsData,t),t.width=t.xMax<t.x?0:t.xMax-t.x,t.height=t.yMax<t.y?0:t.yMax-t.y,this.currentBoxContains(t))return;var r=!1;if(this.currentBBox.w!==t.width&&(this.currentBBox.w=t.width,this.shapeCont.setAttribute("width",t.width),r=!0),this.currentBBox.h!==t.height&&(this.currentBBox.h=t.height,this.shapeCont.setAttribute("height",t.height),r=!0),r||this.currentBBox.x!==t.x||this.currentBBox.y!==t.y){this.currentBBox.w=t.width,this.currentBBox.h=t.height,this.currentBBox.x=t.x,this.currentBBox.y=t.y,this.shapeCont.setAttribute("viewBox",this.currentBBox.x+" "+this.currentBBox.y+" "+this.currentBBox.w+" "+this.currentBBox.h);var i=this.shapeCont.style,n="translate("+this.currentBBox.x+"px,"+this.currentBBox.y+"px)";i.transform=n,i.webkitTransform=n}}},extendPrototype([BaseElement,TransformElement,HBaseElement,HierarchyElement,FrameElement,RenderableDOMElement,ITextElement],HTextElement),HTextElement.prototype.createContent=function(){if(this.isMasked=this.checkMasks(),this.isMasked){this.renderType="svg",this.compW=this.comp.data.w,this.compH=this.comp.data.h,this.svgElement.setAttribute("width",this.compW),this.svgElement.setAttribute("height",this.compH);var t=createNS("g");this.maskedElement.appendChild(t),this.innerElem=t}else this.renderType="html",this.innerElem=this.layerElement;this.checkParenting()},HTextElement.prototype.buildNewText=function(){var t=this.textProperty.currentData;this.renderedLetters=createSizedArray(t.l?t.l.length:0);var e=this.innerElem.style,r=t.fc?this.buildColor(t.fc):"rgba(0,0,0,0)";e.fill=r,e.color=r,t.sc&&(e.stroke=this.buildColor(t.sc),e.strokeWidth=t.sw+"px");var i,n,a=this.globalData.fontManager.getFontByName(t.f);if(!this.globalData.fontManager.chars)if(e.fontSize=t.finalSize+"px",e.lineHeight=t.finalSize+"px",a.fClass)this.innerElem.className=a.fClass;else{e.fontFamily=a.fFamily;var s=t.fWeight,o=t.fStyle;e.fontStyle=o,e.fontWeight=s}var l,h,p,c=t.l;n=c.length;var f,d=this.mHelper,u="",m=0;for(i=0;i<n;i+=1){if(this.globalData.fontManager.chars?(this.textPaths[m]?l=this.textPaths[m]:((l=createNS("path")).setAttribute("stroke-linecap",lineCapEnum[1]),l.setAttribute("stroke-linejoin",lineJoinEnum[2]),l.setAttribute("stroke-miterlimit","4")),this.isMasked||(this.textSpans[m]?p=(h=this.textSpans[m]).children[0]:((h=createTag("div")).style.lineHeight=0,(p=createNS("svg")).appendChild(l),styleDiv(h)))):this.isMasked?l=this.textPaths[m]?this.textPaths[m]:createNS("text"):this.textSpans[m]?(h=this.textSpans[m],l=this.textPaths[m]):(styleDiv(h=createTag("span")),styleDiv(l=createTag("span")),h.appendChild(l)),this.globalData.fontManager.chars){var y,g=this.globalData.fontManager.getCharData(t.finalText[i],a.fStyle,this.globalData.fontManager.getFontByName(t.f).fFamily);if(y=g?g.data:null,d.reset(),y&&y.shapes&&y.shapes.length&&(f=y.shapes[0].it,d.scale(t.finalSize/100,t.finalSize/100),u=this.createPathShape(d,f),l.setAttribute("d",u)),this.isMasked)this.innerElem.appendChild(l);else{if(this.innerElem.appendChild(h),y&&y.shapes){document.body.appendChild(p);var v=p.getBBox();p.setAttribute("width",v.width+2),p.setAttribute("height",v.height+2),p.setAttribute("viewBox",v.x-1+" "+(v.y-1)+" "+(v.width+2)+" "+(v.height+2));var b=p.style,_="translate("+(v.x-1)+"px,"+(v.y-1)+"px)";b.transform=_,b.webkitTransform=_,c[i].yOffset=v.y-1}else p.setAttribute("width",1),p.setAttribute("height",1);h.appendChild(p)}}else if(l.textContent=c[i].val,l.setAttributeNS("http://www.w3.org/XML/1998/namespace","xml:space","preserve"),this.isMasked)this.innerElem.appendChild(l);else{this.innerElem.appendChild(h);var P=l.style,E="translate3d(0,"+-t.finalSize/1.2+"px,0)";P.transform=E,P.webkitTransform=E}this.isMasked?this.textSpans[m]=l:this.textSpans[m]=h,this.textSpans[m].style.display="block",this.textPaths[m]=l,m+=1}for(;m<this.textSpans.length;)this.textSpans[m].style.display="none",m+=1},HTextElement.prototype.renderInnerContent=function(){var t;if(this.data.singleShape){if(!this._isFirstFrame&&!this.lettersChangedFlag)return;if(this.isMasked&&this.finalTransform._matMdf){this.svgElement.setAttribute("viewBox",-this.finalTransform.mProp.p.v[0]+" "+-this.finalTransform.mProp.p.v[1]+" "+this.compW+" "+this.compH),t=this.svgElement.style;var e="translate("+-this.finalTransform.mProp.p.v[0]+"px,"+-this.finalTransform.mProp.p.v[1]+"px)";t.transform=e,t.webkitTransform=e}}if(this.textAnimator.getMeasures(this.textProperty.currentData,this.lettersChangedFlag),this.lettersChangedFlag||this.textAnimator.lettersChangedFlag){var r,i,n,a,s,o=0,l=this.textAnimator.renderedLetters,h=this.textProperty.currentData.l;for(i=h.length,r=0;r<i;r+=1)h[r].n?o+=1:(a=this.textSpans[r],s=this.textPaths[r],n=l[o],o+=1,n._mdf.m&&(this.isMasked?a.setAttribute("transform",n.m):(a.style.webkitTransform=n.m,a.style.transform=n.m)),a.style.opacity=n.o,n.sw&&n._mdf.sw&&s.setAttribute("stroke-width",n.sw),n.sc&&n._mdf.sc&&s.setAttribute("stroke",n.sc),n.fc&&n._mdf.fc&&(s.setAttribute("fill",n.fc),s.style.color=n.fc));if(this.innerElem.getBBox&&!this.hidden&&(this._isFirstFrame||this._mdf)){var p=this.innerElem.getBBox();if(this.currentBBox.w!==p.width&&(this.currentBBox.w=p.width,this.svgElement.setAttribute("width",p.width)),this.currentBBox.h!==p.height&&(this.currentBBox.h=p.height,this.svgElement.setAttribute("height",p.height)),this.currentBBox.w!==p.width+2||this.currentBBox.h!==p.height+2||this.currentBBox.x!==p.x-1||this.currentBBox.y!==p.y-1){this.currentBBox.w=p.width+2,this.currentBBox.h=p.height+2,this.currentBBox.x=p.x-1,this.currentBBox.y=p.y-1,this.svgElement.setAttribute("viewBox",this.currentBBox.x+" "+this.currentBBox.y+" "+this.currentBBox.w+" "+this.currentBBox.h),t=this.svgElement.style;var c="translate("+this.currentBBox.x+"px,"+this.currentBBox.y+"px)";t.transform=c,t.webkitTransform=c}}}},extendPrototype([BaseElement,FrameElement,HierarchyElement],HCameraElement),HCameraElement.prototype.setup=function(){var t,e,r,i,n=this.comp.threeDElements.length;for(t=0;t<n;t+=1)if("3d"===(e=this.comp.threeDElements[t]).type){r=e.perspectiveElem.style,i=e.container.style;var a=this.pe.v+"px",s="0px 0px 0px",o="matrix3d(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1)";r.perspective=a,r.webkitPerspective=a,i.transformOrigin=s,i.mozTransformOrigin=s,i.webkitTransformOrigin=s,r.transform=o,r.webkitTransform=o}},HCameraElement.prototype.createElements=function(){},HCameraElement.prototype.hide=function(){},HCameraElement.prototype.renderFrame=function(){var t,e,r=this._isFirstFrame;if(this.hierarchy)for(e=this.hierarchy.length,t=0;t<e;t+=1)r=this.hierarchy[t].finalTransform.mProp._mdf||r;if(r||this.pe._mdf||this.p&&this.p._mdf||this.px&&(this.px._mdf||this.py._mdf||this.pz._mdf)||this.rx._mdf||this.ry._mdf||this.rz._mdf||this.or._mdf||this.a&&this.a._mdf){if(this.mat.reset(),this.hierarchy)for(t=e=this.hierarchy.length-1;t>=0;t-=1){var i=this.hierarchy[t].finalTransform.mProp;this.mat.translate(-i.p.v[0],-i.p.v[1],i.p.v[2]),this.mat.rotateX(-i.or.v[0]).rotateY(-i.or.v[1]).rotateZ(i.or.v[2]),this.mat.rotateX(-i.rx.v).rotateY(-i.ry.v).rotateZ(i.rz.v),this.mat.scale(1/i.s.v[0],1/i.s.v[1],1/i.s.v[2]),this.mat.translate(i.a.v[0],i.a.v[1],i.a.v[2])}if(this.p?this.mat.translate(-this.p.v[0],-this.p.v[1],this.p.v[2]):this.mat.translate(-this.px.v,-this.py.v,this.pz.v),this.a){var n;n=this.p?[this.p.v[0]-this.a.v[0],this.p.v[1]-this.a.v[1],this.p.v[2]-this.a.v[2]]:[this.px.v-this.a.v[0],this.py.v-this.a.v[1],this.pz.v-this.a.v[2]];var a=Math.sqrt(Math.pow(n[0],2)+Math.pow(n[1],2)+Math.pow(n[2],2)),s=[n[0]/a,n[1]/a,n[2]/a],o=Math.sqrt(s[2]*s[2]+s[0]*s[0]),l=Math.atan2(s[1],o),h=Math.atan2(s[0],-s[2]);this.mat.rotateY(h).rotateX(-l)}this.mat.rotateX(-this.rx.v).rotateY(-this.ry.v).rotateZ(this.rz.v),this.mat.rotateX(-this.or.v[0]).rotateY(-this.or.v[1]).rotateZ(this.or.v[2]),this.mat.translate(this.globalData.compSize.w/2,this.globalData.compSize.h/2,0),this.mat.translate(0,0,this.pe.v);var p=!this._prevMat.equals(this.mat);if((p||this.pe._mdf)&&this.comp.threeDElements){var c,f,d;for(e=this.comp.threeDElements.length,t=0;t<e;t+=1)if("3d"===(c=this.comp.threeDElements[t]).type){if(p){var u=this.mat.toCSS();(d=c.container.style).transform=u,d.webkitTransform=u}this.pe._mdf&&((f=c.perspectiveElem.style).perspective=this.pe.v+"px",f.webkitPerspective=this.pe.v+"px")}this.mat.clone(this._prevMat)}}this._isFirstFrame=!1},HCameraElement.prototype.prepareFrame=function(t){this.prepareProperties(t,!0)},HCameraElement.prototype.destroy=function(){},HCameraElement.prototype.getBaseElement=function(){return null},extendPrototype([BaseElement,TransformElement,HBaseElement,HSolidElement,HierarchyElement,FrameElement,RenderableElement],HImageElement),HImageElement.prototype.createContent=function(){var t=this.globalData.getAssetsPath(this.assetData),e=new Image;this.data.hasMask?(this.imageElem=createNS("image"),this.imageElem.setAttribute("width",this.assetData.w+"px"),this.imageElem.setAttribute("height",this.assetData.h+"px"),this.imageElem.setAttributeNS("http://www.w3.org/1999/xlink","href",t),this.layerElement.appendChild(this.imageElem),this.baseElement.setAttribute("width",this.assetData.w),this.baseElement.setAttribute("height",this.assetData.h)):this.layerElement.appendChild(e),e.crossOrigin="anonymous",e.src=t,this.data.ln&&this.baseElement.setAttribute("id",this.data.ln)},extendPrototype([BaseRenderer],HybridRendererBase),HybridRendererBase.prototype.buildItem=SVGRenderer.prototype.buildItem,HybridRendererBase.prototype.checkPendingElements=function(){for(;this.pendingElements.length;)this.pendingElements.pop().checkParenting()},HybridRendererBase.prototype.appendElementInPos=function(t,e){var r=t.getBaseElement();if(r){var i=this.layers[e];if(i.ddd&&this.supports3d)this.addTo3dContainer(r,e);else if(this.threeDElements)this.addTo3dContainer(r,e);else{for(var n,a,s=0;s<e;)this.elements[s]&&!0!==this.elements[s]&&this.elements[s].getBaseElement&&(a=this.elements[s],n=(this.layers[s].ddd?this.getThreeDContainerByPos(s):a.getBaseElement())||n),s+=1;n?i.ddd&&this.supports3d||this.layerElement.insertBefore(r,n):i.ddd&&this.supports3d||this.layerElement.appendChild(r)}}},HybridRendererBase.prototype.createShape=function(t){return this.supports3d?new HShapeElement(t,this.globalData,this):new SVGShapeElement(t,this.globalData,this)},HybridRendererBase.prototype.createText=function(t){return this.supports3d?new HTextElement(t,this.globalData,this):new SVGTextLottieElement(t,this.globalData,this)},HybridRendererBase.prototype.createCamera=function(t){return this.camera=new HCameraElement(t,this.globalData,this),this.camera},HybridRendererBase.prototype.createImage=function(t){return this.supports3d?new HImageElement(t,this.globalData,this):new IImageElement(t,this.globalData,this)},HybridRendererBase.prototype.createSolid=function(t){return this.supports3d?new HSolidElement(t,this.globalData,this):new ISolidElement(t,this.globalData,this)},HybridRendererBase.prototype.createNull=SVGRenderer.prototype.createNull,HybridRendererBase.prototype.getThreeDContainerByPos=function(t){for(var e=0,r=this.threeDElements.length;e<r;){if(this.threeDElements[e].startPos<=t&&this.threeDElements[e].endPos>=t)return this.threeDElements[e].perspectiveElem;e+=1}return null},HybridRendererBase.prototype.createThreeDContainer=function(t,e){var r,i,n=createTag("div");styleDiv(n);var a=createTag("div");if(styleDiv(a),"3d"===e){(r=n.style).width=this.globalData.compSize.w+"px",r.height=this.globalData.compSize.h+"px";var s="50% 50%";r.webkitTransformOrigin=s,r.mozTransformOrigin=s,r.transformOrigin=s;var o="matrix3d(1,0,0,0,0,1,0,0,0,0,1,0,0,0,0,1)";(i=a.style).transform=o,i.webkitTransform=o}n.appendChild(a);var l={container:a,perspectiveElem:n,startPos:t,endPos:t,type:e};return this.threeDElements.push(l),l},HybridRendererBase.prototype.build3dContainers=function(){var t,e,r=this.layers.length,i="";for(t=0;t<r;t+=1)this.layers[t].ddd&&3!==this.layers[t].ty?("3d"!==i&&(i="3d",e=this.createThreeDContainer(t,"3d")),e.endPos=Math.max(e.endPos,t)):("2d"!==i&&(i="2d",e=this.createThreeDContainer(t,"2d")),e.endPos=Math.max(e.endPos,t));for(t=(r=this.threeDElements.length)-1;t>=0;t-=1)this.resizerElem.appendChild(this.threeDElements[t].perspectiveElem)},HybridRendererBase.prototype.addTo3dContainer=function(t,e){for(var r=0,i=this.threeDElements.length;r<i;){if(e<=this.threeDElements[r].endPos){for(var n,a=this.threeDElements[r].startPos;a<e;)this.elements[a]&&this.elements[a].getBaseElement&&(n=this.elements[a].getBaseElement()),a+=1;n?this.threeDElements[r].container.insertBefore(t,n):this.threeDElements[r].container.appendChild(t);break}r+=1}},HybridRendererBase.prototype.configAnimation=function(t){var e=createTag("div"),r=this.animationItem.wrapper,i=e.style;i.width=t.w+"px",i.height=t.h+"px",this.resizerElem=e,styleDiv(e),i.transformStyle="flat",i.mozTransformStyle="flat",i.webkitTransformStyle="flat",this.renderConfig.className&&e.setAttribute("class",this.renderConfig.className),r.appendChild(e),i.overflow="hidden";var n=createNS("svg");n.setAttribute("width","1"),n.setAttribute("height","1"),styleDiv(n),this.resizerElem.appendChild(n);var a=createNS("defs");n.appendChild(a),this.data=t,this.setupGlobalData(t,n),this.globalData.defs=a,this.layers=t.layers,this.layerElement=this.resizerElem,this.build3dContainers(),this.updateContainerSize()},HybridRendererBase.prototype.destroy=function(){var t;this.animationItem.wrapper&&(this.animationItem.wrapper.innerText=""),this.animationItem.container=null,this.globalData.defs=null;var e=this.layers?this.layers.length:0;for(t=0;t<e;t+=1)this.elements[t].destroy();this.elements.length=0,this.destroyed=!0,this.animationItem=null},HybridRendererBase.prototype.updateContainerSize=function(){var t,e,r,i,n=this.animationItem.wrapper.offsetWidth,a=this.animationItem.wrapper.offsetHeight,s=n/a;this.globalData.compSize.w/this.globalData.compSize.h>s?(t=n/this.globalData.compSize.w,e=n/this.globalData.compSize.w,r=0,i=(a-this.globalData.compSize.h*(n/this.globalData.compSize.w))/2):(t=a/this.globalData.compSize.h,e=a/this.globalData.compSize.h,r=(n-this.globalData.compSize.w*(a/this.globalData.compSize.h))/2,i=0);var o=this.resizerElem.style;o.webkitTransform="matrix3d("+t+",0,0,0,0,"+e+",0,0,0,0,1,0,"+r+","+i+",0,1)",o.transform=o.webkitTransform},HybridRendererBase.prototype.renderFrame=SVGRenderer.prototype.renderFrame,HybridRendererBase.prototype.hide=function(){this.resizerElem.style.display="none"},HybridRendererBase.prototype.show=function(){this.resizerElem.style.display="block"},HybridRendererBase.prototype.initItems=function(){if(this.buildAllItems(),this.camera)this.camera.setup();else{var t,e=this.globalData.compSize.w,r=this.globalData.compSize.h,i=this.threeDElements.length;for(t=0;t<i;t+=1){var n=this.threeDElements[t].perspectiveElem.style;n.webkitPerspective=Math.sqrt(Math.pow(e,2)+Math.pow(r,2))+"px",n.perspective=n.webkitPerspective}}},HybridRendererBase.prototype.searchExtraCompositions=function(t){var e,r=t.length,i=createTag("div");for(e=0;e<r;e+=1)if(t[e].xt){var n=this.createComp(t[e],i,this.globalData.comp,null);n.initExpressions(),this.globalData.projectInterface.registerComposition(n)}},extendPrototype([HybridRendererBase,ICompElement,HBaseElement],HCompElement),HCompElement.prototype._createBaseContainerElements=HCompElement.prototype.createContainerElements,HCompElement.prototype.createContainerElements=function(){this._createBaseContainerElements(),this.data.hasMask?(this.svgElement.setAttribute("width",this.data.w),this.svgElement.setAttribute("height",this.data.h),this.transformedElement=this.baseElement):this.transformedElement=this.layerElement},HCompElement.prototype.addTo3dContainer=function(t,e){for(var r,i=0;i<e;)this.elements[i]&&this.elements[i].getBaseElement&&(r=this.elements[i].getBaseElement()),i+=1;r?this.layerElement.insertBefore(t,r):this.layerElement.appendChild(t)},HCompElement.prototype.createComp=function(t){return this.supports3d?new HCompElement(t,this.globalData,this):new SVGCompElement(t,this.globalData,this)},extendPrototype([HybridRendererBase],HybridRenderer),HybridRenderer.prototype.createComp=function(t){return this.supports3d?new HCompElement(t,this.globalData,this):new SVGCompElement(t,this.globalData,this)};var CompExpressionInterface=function(t){function e(e){for(var r=0,i=t.layers.length;r<i;){if(t.layers[r].nm===e||t.layers[r].ind===e)return t.elements[r].layerInterface;r+=1}return null}return Object.defineProperty(e,"_name",{value:t.data.nm}),e.layer=e,e.pixelAspect=1,e.height=t.data.h||t.globalData.compSize.h,e.width=t.data.w||t.globalData.compSize.w,e.pixelAspect=1,e.frameDuration=1/t.globalData.frameRate,e.displayStartTime=0,e.numLayers=t.layers.length,e},Expressions=function(){var t={initExpressions:function(t){var e=0,r=[];t.renderer.compInterface=CompExpressionInterface(t.renderer),t.renderer.globalData.projectInterface.registerComposition(t.renderer),t.renderer.globalData.pushExpression=function(){e+=1},t.renderer.globalData.popExpression=function(){0==(e-=1)&&function(){var t,e=r.length;for(t=0;t<e;t+=1)r[t].release();r.length=0}()},t.renderer.globalData.registerExpressionProperty=function(t){-1===r.indexOf(t)&&r.push(t)}}};return t}(),MaskManagerInterface=function(){function t(t,e){this._mask=t,this._data=e}return Object.defineProperty(t.prototype,"maskPath",{get:function(){return this._mask.prop.k&&this._mask.prop.getValue(),this._mask.prop}}),Object.defineProperty(t.prototype,"maskOpacity",{get:function(){return this._mask.op.k&&this._mask.op.getValue(),100*this._mask.op.v}}),function(e){var r,i=createSizedArray(e.viewData.length),n=e.viewData.length;for(r=0;r<n;r+=1)i[r]=new t(e.viewData[r],e.masksProperties[r]);return function(t){for(r=0;r<n;){if(e.masksProperties[r].nm===t)return i[r];r+=1}return null}}}(),ExpressionPropertyInterface=function(){var t={pv:0,v:0,mult:1},e={pv:[0,0,0],v:[0,0,0],mult:1};function r(t,e,r){Object.defineProperty(t,"velocity",{get:function(){return e.getVelocityAtTime(e.comp.currentFrame)}}),t.numKeys=e.keyframes?e.keyframes.length:0,t.key=function(i){if(!t.numKeys)return 0;var n;n="s"in e.keyframes[i-1]?e.keyframes[i-1].s:"e"in e.keyframes[i-2]?e.keyframes[i-2].e:e.keyframes[i-2].s;var a="unidimensional"===r?new Number(n):Object.assign({},n);return a.time=e.keyframes[i-1].t/e.elem.comp.globalData.frameRate,a.value="unidimensional"===r?n[0]:n,a},t.valueAtTime=e.getValueAtTime,t.speedAtTime=e.getSpeedAtTime,t.velocityAtTime=e.getVelocityAtTime,t.propertyGroup=e.propertyGroup}function i(){return t}return function(n){return n?"unidimensional"===n.propType?function(e){e&&"pv"in e||(e=t);var i=1/e.mult,n=e.pv*i,a=new Number(n);return a.value=n,r(a,e,"unidimensional"),function(){return e.k&&e.getValue(),n=e.v*i,a.value!==n&&((a=new Number(n)).value=n,r(a,e,"unidimensional")),a}}(n):function(t){t&&"pv"in t||(t=e);var i=1/t.mult,n=t.data&&t.data.l||t.pv.length,a=createTypedArray("float32",n),s=createTypedArray("float32",n);return a.value=s,r(a,t,"multidimensional"),function(){t.k&&t.getValue();for(var e=0;e<n;e+=1)s[e]=t.v[e]*i,a[e]=s[e];return a}}(n):i}}(),TransformExpressionInterface=function(t){function e(t){switch(t){case"scale":case"Scale":case"ADBE Scale":case 6:return e.scale;case"rotation":case"Rotation":case"ADBE Rotation":case"ADBE Rotate Z":case 10:return e.rotation;case"ADBE Rotate X":return e.xRotation;case"ADBE Rotate Y":return e.yRotation;case"position":case"Position":case"ADBE Position":case 2:return e.position;case"ADBE Position_0":return e.xPosition;case"ADBE Position_1":return e.yPosition;case"ADBE Position_2":return e.zPosition;case"anchorPoint":case"AnchorPoint":case"Anchor Point":case"ADBE AnchorPoint":case 1:return e.anchorPoint;case"opacity":case"Opacity":case 11:return e.opacity;default:return null}}var r,i,n,a;return Object.defineProperty(e,"rotation",{get:ExpressionPropertyInterface(t.r||t.rz)}),Object.defineProperty(e,"zRotation",{get:ExpressionPropertyInterface(t.rz||t.r)}),Object.defineProperty(e,"xRotation",{get:ExpressionPropertyInterface(t.rx)}),Object.defineProperty(e,"yRotation",{get:ExpressionPropertyInterface(t.ry)}),Object.defineProperty(e,"scale",{get:ExpressionPropertyInterface(t.s)}),t.p?a=ExpressionPropertyInterface(t.p):(r=ExpressionPropertyInterface(t.px),i=ExpressionPropertyInterface(t.py),t.pz&&(n=ExpressionPropertyInterface(t.pz))),Object.defineProperty(e,"position",{get:function(){return t.p?a():[r(),i(),n?n():0]}}),Object.defineProperty(e,"xPosition",{get:ExpressionPropertyInterface(t.px)}),Object.defineProperty(e,"yPosition",{get:ExpressionPropertyInterface(t.py)}),Object.defineProperty(e,"zPosition",{get:ExpressionPropertyInterface(t.pz)}),Object.defineProperty(e,"anchorPoint",{get:ExpressionPropertyInterface(t.a)}),Object.defineProperty(e,"opacity",{get:ExpressionPropertyInterface(t.o)}),Object.defineProperty(e,"skew",{get:ExpressionPropertyInterface(t.sk)}),Object.defineProperty(e,"skewAxis",{get:ExpressionPropertyInterface(t.sa)}),Object.defineProperty(e,"orientation",{get:ExpressionPropertyInterface(t.or)}),e},LayerExpressionInterface=function(){function t(t){var e=new Matrix;return void 0!==t?this._elem.finalTransform.mProp.getValueAtTime(t).clone(e):this._elem.finalTransform.mProp.applyToMatrix(e),e}function e(t,e){var r=this.getMatrix(e);return r.props[12]=0,r.props[13]=0,r.props[14]=0,this.applyPoint(r,t)}function r(t,e){var r=this.getMatrix(e);return this.applyPoint(r,t)}function i(t,e){var r=this.getMatrix(e);return r.props[12]=0,r.props[13]=0,r.props[14]=0,this.invertPoint(r,t)}function n(t,e){var r=this.getMatrix(e);return this.invertPoint(r,t)}function a(t,e){if(this._elem.hierarchy&&this._elem.hierarchy.length){var r,i=this._elem.hierarchy.length;for(r=0;r<i;r+=1)this._elem.hierarchy[r].finalTransform.mProp.applyToMatrix(t)}return t.applyToPointArray(e[0],e[1],e[2]||0)}function s(t,e){if(this._elem.hierarchy&&this._elem.hierarchy.length){var r,i=this._elem.hierarchy.length;for(r=0;r<i;r+=1)this._elem.hierarchy[r].finalTransform.mProp.applyToMatrix(t)}return t.inversePoint(e)}function o(t){var e=new Matrix;if(e.reset(),this._elem.finalTransform.mProp.applyToMatrix(e),this._elem.hierarchy&&this._elem.hierarchy.length){var r,i=this._elem.hierarchy.length;for(r=0;r<i;r+=1)this._elem.hierarchy[r].finalTransform.mProp.applyToMatrix(e);return e.inversePoint(t)}return e.inversePoint(t)}function l(){return[1,1,1,1]}return function(h){var p;function c(t){switch(t){case"ADBE Root Vectors Group":case"Contents":case 2:return c.shapeInterface;case 1:case 6:case"Transform":case"transform":case"ADBE Transform Group":return p;case 4:case"ADBE Effect Parade":case"effects":case"Effects":return c.effect;case"ADBE Text Properties":return c.textInterface;default:return null}}c.getMatrix=t,c.invertPoint=s,c.applyPoint=a,c.toWorld=r,c.toWorldVec=e,c.fromWorld=n,c.fromWorldVec=i,c.toComp=r,c.fromComp=o,c.sampleImage=l,c.sourceRectAtTime=h.sourceRectAtTime.bind(h),c._elem=h;var f=getDescriptor(p=TransformExpressionInterface(h.finalTransform.mProp),"anchorPoint");return Object.defineProperties(c,{hasParent:{get:function(){return h.hierarchy.length}},parent:{get:function(){return h.hierarchy[0].layerInterface}},rotation:getDescriptor(p,"rotation"),scale:getDescriptor(p,"scale"),position:getDescriptor(p,"position"),opacity:getDescriptor(p,"opacity"),anchorPoint:f,anchor_point:f,transform:{get:function(){return p}},active:{get:function(){return h.isInRange}}}),c.startTime=h.data.st,c.index=h.data.ind,c.source=h.data.refId,c.height=0===h.data.ty?h.data.h:100,c.width=0===h.data.ty?h.data.w:100,c.inPoint=h.data.ip/h.comp.globalData.frameRate,c.outPoint=h.data.op/h.comp.globalData.frameRate,c._name=h.data.nm,c.registerMaskInterface=function(t){c.mask=new MaskManagerInterface(t,h)},c.registerEffectsInterface=function(t){c.effect=t},c}}(),propertyGroupFactory=function(t,e){return function(r){return(r=void 0===r?1:r)<=0?t:e(r-1)}},PropertyInterface=function(t,e){var r={_name:t};return function(t){return(t=void 0===t?1:t)<=0?r:e(t-1)}},EffectsExpressionInterface=function(){var t={createEffectsInterface:function(t,r){if(t.effectsManager){var i,n=[],a=t.data.ef,s=t.effectsManager.effectElements.length;for(i=0;i<s;i+=1)n.push(e(a[i],t.effectsManager.effectElements[i],r,t));var o=t.data.ef||[],l=function(t){for(i=0,s=o.length;i<s;){if(t===o[i].nm||t===o[i].mn||t===o[i].ix)return n[i];i+=1}return null};return Object.defineProperty(l,"numProperties",{get:function(){return o.length}}),l}return null}};function e(t,i,n,a){function s(e){for(var r=t.ef,i=0,n=r.length;i<n;){if(e===r[i].nm||e===r[i].mn||e===r[i].ix)return 5===r[i].ty?h[i]:h[i]();i+=1}throw new Error}var o,l=propertyGroupFactory(s,n),h=[],p=t.ef.length;for(o=0;o<p;o+=1)5===t.ef[o].ty?h.push(e(t.ef[o],i.effectElements[o],i.effectElements[o].propertyGroup,a)):h.push(r(i.effectElements[o],t.ef[o].ty,a,l));return"ADBE Color Control"===t.mn&&Object.defineProperty(s,"color",{get:function(){return h[0]()}}),Object.defineProperties(s,{numProperties:{get:function(){return t.np}},_name:{value:t.nm},propertyGroup:{value:l}}),s.enabled=0!==t.en,s.active=s.enabled,s}function r(t,e,r,i){var n=ExpressionPropertyInterface(t.p);return t.p.setGroupProperty&&t.p.setGroupProperty(PropertyInterface("",i)),function(){return 10===e?r.comp.compInterface(t.p.v):n()}}return t}(),ShapePathInterface=function(t,e,r){var i=e.sh;function n(t){return"Shape"===t||"shape"===t||"Path"===t||"path"===t||"ADBE Vector Shape"===t||2===t?n.path:null}var a=propertyGroupFactory(n,r);return i.setGroupProperty(PropertyInterface("Path",a)),Object.defineProperties(n,{path:{get:function(){return i.k&&i.getValue(),i}},shape:{get:function(){return i.k&&i.getValue(),i}},_name:{value:t.nm},ix:{value:t.ix},propertyIndex:{value:t.ix},mn:{value:t.mn},propertyGroup:{value:r}}),n},ShapeExpressionInterface=function(){function t(t,s,f){var d,u=[],m=t?t.length:0;for(d=0;d<m;d+=1)"gr"===t[d].ty?u.push(e(t[d],s[d],f)):"fl"===t[d].ty?u.push(r(t[d],s[d],f)):"st"===t[d].ty?u.push(n(t[d],s[d],f)):"tm"===t[d].ty?u.push(a(t[d],s[d],f)):"tr"===t[d].ty||("el"===t[d].ty?u.push(o(t[d],s[d],f)):"sr"===t[d].ty?u.push(l(t[d],s[d],f)):"sh"===t[d].ty?u.push(ShapePathInterface(t[d],s[d],f)):"rc"===t[d].ty?u.push(h(t[d],s[d],f)):"rd"===t[d].ty?u.push(p(t[d],s[d],f)):"rp"===t[d].ty?u.push(c(t[d],s[d],f)):"gf"===t[d].ty?u.push(i(t[d],s[d],f)):u.push((t[d],s[d],function(){return null})));return u}function e(e,r,i){var n=function(t){switch(t){case"ADBE Vectors Group":case"Contents":case 2:return n.content;default:return n.transform}};n.propertyGroup=propertyGroupFactory(n,i);var a=function(e,r,i){var n,a=function(t){for(var e=0,r=n.length;e<r;){if(n[e]._name===t||n[e].mn===t||n[e].propertyIndex===t||n[e].ix===t||n[e].ind===t)return n[e];e+=1}return"number"==typeof t?n[t-1]:null};a.propertyGroup=propertyGroupFactory(a,i),n=t(e.it,r.it,a.propertyGroup),a.numProperties=n.length;var o=s(e.it[e.it.length-1],r.it[r.it.length-1],a.propertyGroup);return a.transform=o,a.propertyIndex=e.cix,a._name=e.nm,a}(e,r,n.propertyGroup),o=s(e.it[e.it.length-1],r.it[r.it.length-1],n.propertyGroup);return n.content=a,n.transform=o,Object.defineProperty(n,"_name",{get:function(){return e.nm}}),n.numProperties=e.np,n.propertyIndex=e.ix,n.nm=e.nm,n.mn=e.mn,n}function r(t,e,r){function i(t){return"Color"===t||"color"===t?i.color:"Opacity"===t||"opacity"===t?i.opacity:null}return Object.defineProperties(i,{color:{get:ExpressionPropertyInterface(e.c)},opacity:{get:ExpressionPropertyInterface(e.o)},_name:{value:t.nm},mn:{value:t.mn}}),e.c.setGroupProperty(PropertyInterface("Color",r)),e.o.setGroupProperty(PropertyInterface("Opacity",r)),i}function i(t,e,r){function i(t){return"Start Point"===t||"start point"===t?i.startPoint:"End Point"===t||"end point"===t?i.endPoint:"Opacity"===t||"opacity"===t?i.opacity:null}return Object.defineProperties(i,{startPoint:{get:ExpressionPropertyInterface(e.s)},endPoint:{get:ExpressionPropertyInterface(e.e)},opacity:{get:ExpressionPropertyInterface(e.o)},type:{get:function(){return"a"}},_name:{value:t.nm},mn:{value:t.mn}}),e.s.setGroupProperty(PropertyInterface("Start Point",r)),e.e.setGroupProperty(PropertyInterface("End Point",r)),e.o.setGroupProperty(PropertyInterface("Opacity",r)),i}function n(t,e,r){var i,n=propertyGroupFactory(h,r),a=propertyGroupFactory(l,n);function s(r){Object.defineProperty(l,t.d[r].nm,{get:ExpressionPropertyInterface(e.d.dataProps[r].p)})}var o=t.d?t.d.length:0,l={};for(i=0;i<o;i+=1)s(i),e.d.dataProps[i].p.setGroupProperty(a);function h(t){return"Color"===t||"color"===t?h.color:"Opacity"===t||"opacity"===t?h.opacity:"Stroke Width"===t||"stroke width"===t?h.strokeWidth:null}return Object.defineProperties(h,{color:{get:ExpressionPropertyInterface(e.c)},opacity:{get:ExpressionPropertyInterface(e.o)},strokeWidth:{get:ExpressionPropertyInterface(e.w)},dash:{get:function(){return l}},_name:{value:t.nm},mn:{value:t.mn}}),e.c.setGroupProperty(PropertyInterface("Color",n)),e.o.setGroupProperty(PropertyInterface("Opacity",n)),e.w.setGroupProperty(PropertyInterface("Stroke Width",n)),h}function a(t,e,r){function i(e){return e===t.e.ix||"End"===e||"end"===e?i.end:e===t.s.ix?i.start:e===t.o.ix?i.offset:null}var n=propertyGroupFactory(i,r);return i.propertyIndex=t.ix,e.s.setGroupProperty(PropertyInterface("Start",n)),e.e.setGroupProperty(PropertyInterface("End",n)),e.o.setGroupProperty(PropertyInterface("Offset",n)),i.propertyIndex=t.ix,i.propertyGroup=r,Object.defineProperties(i,{start:{get:ExpressionPropertyInterface(e.s)},end:{get:ExpressionPropertyInterface(e.e)},offset:{get:ExpressionPropertyInterface(e.o)},_name:{value:t.nm}}),i.mn=t.mn,i}function s(t,e,r){function i(e){return t.a.ix===e||"Anchor Point"===e?i.anchorPoint:t.o.ix===e||"Opacity"===e?i.opacity:t.p.ix===e||"Position"===e?i.position:t.r.ix===e||"Rotation"===e||"ADBE Vector Rotation"===e?i.rotation:t.s.ix===e||"Scale"===e?i.scale:t.sk&&t.sk.ix===e||"Skew"===e?i.skew:t.sa&&t.sa.ix===e||"Skew Axis"===e?i.skewAxis:null}var n=propertyGroupFactory(i,r);return e.transform.mProps.o.setGroupProperty(PropertyInterface("Opacity",n)),e.transform.mProps.p.setGroupProperty(PropertyInterface("Position",n)),e.transform.mProps.a.setGroupProperty(PropertyInterface("Anchor Point",n)),e.transform.mProps.s.setGroupProperty(PropertyInterface("Scale",n)),e.transform.mProps.r.setGroupProperty(PropertyInterface("Rotation",n)),e.transform.mProps.sk&&(e.transform.mProps.sk.setGroupProperty(PropertyInterface("Skew",n)),e.transform.mProps.sa.setGroupProperty(PropertyInterface("Skew Angle",n))),e.transform.op.setGroupProperty(PropertyInterface("Opacity",n)),Object.defineProperties(i,{opacity:{get:ExpressionPropertyInterface(e.transform.mProps.o)},position:{get:ExpressionPropertyInterface(e.transform.mProps.p)},anchorPoint:{get:ExpressionPropertyInterface(e.transform.mProps.a)},scale:{get:ExpressionPropertyInterface(e.transform.mProps.s)},rotation:{get:ExpressionPropertyInterface(e.transform.mProps.r)},skew:{get:ExpressionPropertyInterface(e.transform.mProps.sk)},skewAxis:{get:ExpressionPropertyInterface(e.transform.mProps.sa)},_name:{value:t.nm}}),i.ty="tr",i.mn=t.mn,i.propertyGroup=r,i}function o(t,e,r){function i(e){return t.p.ix===e?i.position:t.s.ix===e?i.size:null}var n=propertyGroupFactory(i,r);i.propertyIndex=t.ix;var a="tm"===e.sh.ty?e.sh.prop:e.sh;return a.s.setGroupProperty(PropertyInterface("Size",n)),a.p.setGroupProperty(PropertyInterface("Position",n)),Object.defineProperties(i,{size:{get:ExpressionPropertyInterface(a.s)},position:{get:ExpressionPropertyInterface(a.p)},_name:{value:t.nm}}),i.mn=t.mn,i}function l(t,e,r){function i(e){return t.p.ix===e?i.position:t.r.ix===e?i.rotation:t.pt.ix===e?i.points:t.or.ix===e||"ADBE Vector Star Outer Radius"===e?i.outerRadius:t.os.ix===e?i.outerRoundness:!t.ir||t.ir.ix!==e&&"ADBE Vector Star Inner Radius"!==e?t.is&&t.is.ix===e?i.innerRoundness:null:i.innerRadius}var n=propertyGroupFactory(i,r),a="tm"===e.sh.ty?e.sh.prop:e.sh;return i.propertyIndex=t.ix,a.or.setGroupProperty(PropertyInterface("Outer Radius",n)),a.os.setGroupProperty(PropertyInterface("Outer Roundness",n)),a.pt.setGroupProperty(PropertyInterface("Points",n)),a.p.setGroupProperty(PropertyInterface("Position",n)),a.r.setGroupProperty(PropertyInterface("Rotation",n)),t.ir&&(a.ir.setGroupProperty(PropertyInterface("Inner Radius",n)),a.is.setGroupProperty(PropertyInterface("Inner Roundness",n))),Object.defineProperties(i,{position:{get:ExpressionPropertyInterface(a.p)},rotation:{get:ExpressionPropertyInterface(a.r)},points:{get:ExpressionPropertyInterface(a.pt)},outerRadius:{get:ExpressionPropertyInterface(a.or)},outerRoundness:{get:ExpressionPropertyInterface(a.os)},innerRadius:{get:ExpressionPropertyInterface(a.ir)},innerRoundness:{get:ExpressionPropertyInterface(a.is)},_name:{value:t.nm}}),i.mn=t.mn,i}function h(t,e,r){function i(e){return t.p.ix===e?i.position:t.r.ix===e?i.roundness:t.s.ix===e||"Size"===e||"ADBE Vector Rect Size"===e?i.size:null}var n=propertyGroupFactory(i,r),a="tm"===e.sh.ty?e.sh.prop:e.sh;return i.propertyIndex=t.ix,a.p.setGroupProperty(PropertyInterface("Position",n)),a.s.setGroupProperty(PropertyInterface("Size",n)),a.r.setGroupProperty(PropertyInterface("Rotation",n)),Object.defineProperties(i,{position:{get:ExpressionPropertyInterface(a.p)},roundness:{get:ExpressionPropertyInterface(a.r)},size:{get:ExpressionPropertyInterface(a.s)},_name:{value:t.nm}}),i.mn=t.mn,i}function p(t,e,r){function i(e){return t.r.ix===e||"Round Corners 1"===e?i.radius:null}var n=propertyGroupFactory(i,r),a=e;return i.propertyIndex=t.ix,a.rd.setGroupProperty(PropertyInterface("Radius",n)),Object.defineProperties(i,{radius:{get:ExpressionPropertyInterface(a.rd)},_name:{value:t.nm}}),i.mn=t.mn,i}function c(t,e,r){function i(e){return t.c.ix===e||"Copies"===e?i.copies:t.o.ix===e||"Offset"===e?i.offset:null}var n=propertyGroupFactory(i,r),a=e;return i.propertyIndex=t.ix,a.c.setGroupProperty(PropertyInterface("Copies",n)),a.o.setGroupProperty(PropertyInterface("Offset",n)),Object.defineProperties(i,{copies:{get:ExpressionPropertyInterface(a.c)},offset:{get:ExpressionPropertyInterface(a.o)},_name:{value:t.nm}}),i.mn=t.mn,i}return function(e,r,i){var n;function a(t){if("number"==typeof t)return 0===(t=void 0===t?1:t)?i:n[t-1];for(var e=0,r=n.length;e<r;){if(n[e]._name===t)return n[e];e+=1}return null}return a.propertyGroup=propertyGroupFactory(a,(function(){return i})),n=t(e,r,a.propertyGroup),a.numProperties=n.length,a._name="Contents",a}}(),TextExpressionInterface=function(t){var e,r;function i(t){return"ADBE Text Document"===t?i.sourceText:null}return Object.defineProperty(i,"sourceText",{get:function(){t.textProperty.getValue();var i=t.textProperty.currentData.t;return i!==e&&(t.textProperty.currentData.t=e,(r=new String(i)).value=i||new String(i)),r}}),i};function _typeof$2(t){return _typeof$2="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_typeof$2(t)}var FootageInterface=(dataInterfaceFactory=function(t){function e(t){return"Outline"===t?e.outlineInterface():null}return e._name="Outline",e.outlineInterface=function(t){var e="",r=t.getFootageData();function i(t){if(r[t])return e=t,"object"===_typeof$2(r=r[t])?i:r;var n=t.indexOf(e);if(-1!==n){var a=parseInt(t.substr(n+e.length),10);return"object"===_typeof$2(r=r[a])?i:r}return""}return function(){return e="",r=t.getFootageData(),i}}(t),e},function(t){function e(t){return"Data"===t?e.dataInterface:null}return e._name="Data",e.dataInterface=dataInterfaceFactory(t),e}),dataInterfaceFactory,interfaces={layer:LayerExpressionInterface,effects:EffectsExpressionInterface,comp:CompExpressionInterface,shape:ShapeExpressionInterface,text:TextExpressionInterface,footage:FootageInterface};function getInterface(t){return interfaces[t]||null}function _typeof$1(t){return _typeof$1="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_typeof$1(t)}function seedRandom(t,e){var r=this,i=256,n=e.pow(i,6),a=e.pow(2,52),s=2*a,o=255;function l(t){var e,r=t.length,n=this,a=0,s=n.i=n.j=0,l=n.S=[];for(r||(t=[r++]);a<i;)l[a]=a++;for(a=0;a<i;a++)l[a]=l[s=o&s+t[a%r]+(e=l[a])],l[s]=e;n.g=function(t){for(var e,r=0,a=n.i,s=n.j,l=n.S;t--;)e=l[a=o&a+1],r=r*i+l[o&(l[a]=l[s=o&s+e])+(l[s]=e)];return n.i=a,n.j=s,r}}function h(t,e){return e.i=t.i,e.j=t.j,e.S=t.S.slice(),e}function p(t,e){var r,i=[],n=_typeof$1(t);if(e&&"object"==n)for(r in t)try{i.push(p(t[r],e-1))}catch(t){}return i.length?i:"string"==n?t:t+"\0"}function c(t,e){for(var r,i=t+"",n=0;n<i.length;)e[o&n]=o&(r^=19*e[o&n])+i.charCodeAt(n++);return f(e)}function f(t){return String.fromCharCode.apply(0,t)}e.seedrandom=function(o,d,u){var m=[],y=c(p((d=!0===d?{entropy:!0}:d||{}).entropy?[o,f(t)]:null===o?function(){try{var e=new Uint8Array(i);return(r.crypto||r.msCrypto).getRandomValues(e),f(e)}catch(e){var n=r.navigator,a=n&&n.plugins;return[+new Date,r,a,r.screen,f(t)]}}():o,3),m),g=new l(m),v=function(){for(var t=g.g(6),e=n,r=0;t<a;)t=(t+r)*i,e*=i,r=g.g(1);for(;t>=s;)t/=2,e/=2,r>>>=1;return(t+r)/e};return v.int32=function(){return 0|g.g(4)},v.quick=function(){return g.g(4)/4294967296},v.double=v,c(f(g.S),t),(d.pass||u||function(t,r,i,n){return n&&(n.S&&h(n,g),t.state=function(){return h(g,{})}),i?(e.random=t,r):t})(v,y,"global"in d?d.global:this==e,d.state)},c(e.random(),t)}function initialize$2(t){seedRandom([],t)}var propTypes={SHAPE:"shape"};function _typeof(t){return _typeof="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"==typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},_typeof(t)}var ExpressionManager=function(){var ob={},Math=BMMath,window=null,document=null,XMLHttpRequest=null,fetch=null,frames=null;function $bm_isInstanceOfArray(t){return t.constructor===Array||t.constructor===Float32Array}function isNumerable(t,e){return"number"===t||"boolean"===t||"string"===t||e instanceof Number}function $bm_neg(t){var e=_typeof(t);if("number"===e||"boolean"===e||t instanceof Number)return-t;if($bm_isInstanceOfArray(t)){var r,i=t.length,n=[];for(r=0;r<i;r+=1)n[r]=-t[r];return n}return t.propType?t.v:-t}initialize$2(BMMath);var easeInBez=BezierFactory.getBezierEasing(.333,0,.833,.833,"easeIn").get,easeOutBez=BezierFactory.getBezierEasing(.167,.167,.667,1,"easeOut").get,easeInOutBez=BezierFactory.getBezierEasing(.33,0,.667,1,"easeInOut").get;function sum(t,e){var r=_typeof(t),i=_typeof(e);if("string"===r||"string"===i)return t+e;if(isNumerable(r,t)&&isNumerable(i,e))return t+e;if($bm_isInstanceOfArray(t)&&isNumerable(i,e))return(t=t.slice(0))[0]+=e,t;if(isNumerable(r,t)&&$bm_isInstanceOfArray(e))return(e=e.slice(0))[0]=t+e[0],e;if($bm_isInstanceOfArray(t)&&$bm_isInstanceOfArray(e)){for(var n=0,a=t.length,s=e.length,o=[];n<a||n<s;)("number"==typeof t[n]||t[n]instanceof Number)&&("number"==typeof e[n]||e[n]instanceof Number)?o[n]=t[n]+e[n]:o[n]=void 0===e[n]?t[n]:t[n]||e[n],n+=1;return o}return 0}var add=sum;function sub(t,e){var r=_typeof(t),i=_typeof(e);if(isNumerable(r,t)&&isNumerable(i,e))return"string"===r&&(t=parseInt(t,10)),"string"===i&&(e=parseInt(e,10)),t-e;if($bm_isInstanceOfArray(t)&&isNumerable(i,e))return(t=t.slice(0))[0]-=e,t;if(isNumerable(r,t)&&$bm_isInstanceOfArray(e))return(e=e.slice(0))[0]=t-e[0],e;if($bm_isInstanceOfArray(t)&&$bm_isInstanceOfArray(e)){for(var n=0,a=t.length,s=e.length,o=[];n<a||n<s;)("number"==typeof t[n]||t[n]instanceof Number)&&("number"==typeof e[n]||e[n]instanceof Number)?o[n]=t[n]-e[n]:o[n]=void 0===e[n]?t[n]:t[n]||e[n],n+=1;return o}return 0}function mul(t,e){var r,i,n,a=_typeof(t),s=_typeof(e);if(isNumerable(a,t)&&isNumerable(s,e))return t*e;if($bm_isInstanceOfArray(t)&&isNumerable(s,e)){for(n=t.length,r=createTypedArray("float32",n),i=0;i<n;i+=1)r[i]=t[i]*e;return r}if(isNumerable(a,t)&&$bm_isInstanceOfArray(e)){for(n=e.length,r=createTypedArray("float32",n),i=0;i<n;i+=1)r[i]=t*e[i];return r}return 0}function div(t,e){var r,i,n,a=_typeof(t),s=_typeof(e);if(isNumerable(a,t)&&isNumerable(s,e))return t/e;if($bm_isInstanceOfArray(t)&&isNumerable(s,e)){for(n=t.length,r=createTypedArray("float32",n),i=0;i<n;i+=1)r[i]=t[i]/e;return r}if(isNumerable(a,t)&&$bm_isInstanceOfArray(e)){for(n=e.length,r=createTypedArray("float32",n),i=0;i<n;i+=1)r[i]=t/e[i];return r}return 0}function mod(t,e){return"string"==typeof t&&(t=parseInt(t,10)),"string"==typeof e&&(e=parseInt(e,10)),t%e}var $bm_sum=sum,$bm_sub=sub,$bm_mul=mul,$bm_div=div,$bm_mod=mod;function clamp(t,e,r){if(e>r){var i=r;r=e,e=i}return Math.min(Math.max(t,e),r)}function radiansToDegrees(t){return t/degToRads}var radians_to_degrees=radiansToDegrees;function degreesToRadians(t){return t*degToRads}var degrees_to_radians=radiansToDegrees,helperLengthArray=[0,0,0,0,0,0];function length(t,e){if("number"==typeof t||t instanceof Number)return e=e||0,Math.abs(t-e);var r;e||(e=helperLengthArray);var i=Math.min(t.length,e.length),n=0;for(r=0;r<i;r+=1)n+=Math.pow(e[r]-t[r],2);return Math.sqrt(n)}function normalize(t){return div(t,length(t))}function rgbToHsl(t){var e,r,i=t[0],n=t[1],a=t[2],s=Math.max(i,n,a),o=Math.min(i,n,a),l=(s+o)/2;if(s===o)e=0,r=0;else{var h=s-o;switch(r=l>.5?h/(2-s-o):h/(s+o),s){case i:e=(n-a)/h+(n<a?6:0);break;case n:e=(a-i)/h+2;break;case a:e=(i-n)/h+4}e/=6}return[e,r,l,t[3]]}function hue2rgb(t,e,r){return r<0&&(r+=1),r>1&&(r-=1),r<1/6?t+6*(e-t)*r:r<.5?e:r<2/3?t+(e-t)*(2/3-r)*6:t}function hslToRgb(t){var e,r,i,n=t[0],a=t[1],s=t[2];if(0===a)e=s,i=s,r=s;else{var o=s<.5?s*(1+a):s+a-s*a,l=2*s-o;e=hue2rgb(l,o,n+1/3),r=hue2rgb(l,o,n),i=hue2rgb(l,o,n-1/3)}return[e,r,i,t[3]]}function linear(t,e,r,i,n){if(void 0!==i&&void 0!==n||(i=e,n=r,e=0,r=1),r<e){var a=r;r=e,e=a}if(t<=e)return i;if(t>=r)return n;var s,o=r===e?0:(t-e)/(r-e);if(!i.length)return i+(n-i)*o;var l=i.length,h=createTypedArray("float32",l);for(s=0;s<l;s+=1)h[s]=i[s]+(n[s]-i[s])*o;return h}function random(t,e){if(void 0===e&&(void 0===t?(t=0,e=1):(e=t,t=void 0)),e.length){var r,i=e.length;t||(t=createTypedArray("float32",i));var n=createTypedArray("float32",i),a=BMMath.random();for(r=0;r<i;r+=1)n[r]=t[r]+a*(e[r]-t[r]);return n}return void 0===t&&(t=0),t+BMMath.random()*(e-t)}function createPath(t,e,r,i){var n,a=t.length,s=shapePool.newElement();s.setPathData(!!i,a);var o,l,h=[0,0];for(n=0;n<a;n+=1)o=e&&e[n]?e[n]:h,l=r&&r[n]?r[n]:h,s.setTripleAt(t[n][0],t[n][1],l[0]+t[n][0],l[1]+t[n][1],o[0]+t[n][0],o[1]+t[n][1],n,!0);return s}function initiateExpression(elem,data,property){function noOp(t){return t}if(!elem.globalData.renderConfig.runExpressions)return noOp;var val=data.x,needsVelocity=/velocity(?![\w\d])/.test(val),_needsRandom=-1!==val.indexOf("random"),elemType=elem.data.ty,transform,$bm_transform,content,effect,thisProperty=property;thisProperty.valueAtTime=thisProperty.getValueAtTime,Object.defineProperty(thisProperty,"value",{get:function(){return thisProperty.v}}),elem.comp.frameDuration=1/elem.comp.globalData.frameRate,elem.comp.displayStartTime=0;var inPoint=elem.data.ip/elem.comp.globalData.frameRate,outPoint=elem.data.op/elem.comp.globalData.frameRate,width=elem.data.sw?elem.data.sw:0,height=elem.data.sh?elem.data.sh:0,name=elem.data.nm,loopIn,loop_in,loopOut,loop_out,smooth,toWorld,fromWorld,fromComp,toComp,fromCompToSurface,position,rotation,anchorPoint,scale,thisLayer,thisComp,mask,valueAtTime,velocityAtTime,scoped_bm_rt,expression_function=eval("[function _expression_function(){"+val+";scoped_bm_rt=$bm_rt}]")[0],numKeys=property.kf?data.k.length:0,active=!this.data||!0!==this.data.hd,wiggle=function(t,e){var r,i,n=this.pv.length?this.pv.length:1,a=createTypedArray("float32",n),s=Math.floor(5*time);for(r=0,i=0;r<s;){for(i=0;i<n;i+=1)a[i]+=-e+2*e*BMMath.random();r+=1}var o=5*time,l=o-Math.floor(o),h=createTypedArray("float32",n);if(n>1){for(i=0;i<n;i+=1)h[i]=this.pv[i]+a[i]+(-e+2*e*BMMath.random())*l;return h}return this.pv+a[0]+(-e+2*e*BMMath.random())*l}.bind(this);function loopInDuration(t,e){return loopIn(t,e,!0)}function loopOutDuration(t,e){return loopOut(t,e,!0)}thisProperty.loopIn&&(loopIn=thisProperty.loopIn.bind(thisProperty),loop_in=loopIn),thisProperty.loopOut&&(loopOut=thisProperty.loopOut.bind(thisProperty),loop_out=loopOut),thisProperty.smooth&&(smooth=thisProperty.smooth.bind(thisProperty)),this.getValueAtTime&&(valueAtTime=this.getValueAtTime.bind(this)),this.getVelocityAtTime&&(velocityAtTime=this.getVelocityAtTime.bind(this));var comp=elem.comp.globalData.projectInterface.bind(elem.comp.globalData.projectInterface),time,velocity,value,text,textIndex,textTotal,selectorValue;function lookAt(t,e){var r=[e[0]-t[0],e[1]-t[1],e[2]-t[2]],i=Math.atan2(r[0],Math.sqrt(r[1]*r[1]+r[2]*r[2]))/degToRads;return[-Math.atan2(r[1],r[2])/degToRads,i,0]}function easeOut(t,e,r,i,n){return applyEase(easeOutBez,t,e,r,i,n)}function easeIn(t,e,r,i,n){return applyEase(easeInBez,t,e,r,i,n)}function ease(t,e,r,i,n){return applyEase(easeInOutBez,t,e,r,i,n)}function applyEase(t,e,r,i,n,a){void 0===n?(n=r,a=i):e=(e-r)/(i-r),e>1?e=1:e<0&&(e=0);var s=t(e);if($bm_isInstanceOfArray(n)){var o,l=n.length,h=createTypedArray("float32",l);for(o=0;o<l;o+=1)h[o]=(a[o]-n[o])*s+n[o];return h}return(a-n)*s+n}function nearestKey(t){var e,r,i,n=data.k.length;if(data.k.length&&"number"!=typeof data.k[0])if(r=-1,(t*=elem.comp.globalData.frameRate)<data.k[0].t)r=1,i=data.k[0].t;else{for(e=0;e<n-1;e+=1){if(t===data.k[e].t){r=e+1,i=data.k[e].t;break}if(t>data.k[e].t&&t<data.k[e+1].t){t-data.k[e].t>data.k[e+1].t-t?(r=e+2,i=data.k[e+1].t):(r=e+1,i=data.k[e].t);break}}-1===r&&(r=e+1,i=data.k[e].t)}else r=0,i=0;var a={};return a.index=r,a.time=i/elem.comp.globalData.frameRate,a}function key(t){var e,r,i;if(!data.k.length||"number"==typeof data.k[0])throw new Error("The property has no keyframe at index "+t);t-=1,e={time:data.k[t].t/elem.comp.globalData.frameRate,value:[]};var n=Object.prototype.hasOwnProperty.call(data.k[t],"s")?data.k[t].s:data.k[t-1].e;for(i=n.length,r=0;r<i;r+=1)e[r]=n[r],e.value[r]=n[r];return e}function framesToTime(t,e){return e||(e=elem.comp.globalData.frameRate),t/e}function timeToFrames(t,e){return t||0===t||(t=time),e||(e=elem.comp.globalData.frameRate),t*e}function seedRandom(t){BMMath.seedrandom(randSeed+t)}function sourceRectAtTime(){return elem.sourceRectAtTime()}function substring(t,e){return"string"==typeof value?void 0===e?value.substring(t):value.substring(t,e):""}function substr(t,e){return"string"==typeof value?void 0===e?value.substr(t):value.substr(t,e):""}function posterizeTime(t){time=0===t?0:Math.floor(time*t)/t,value=valueAtTime(time)}var index=elem.data.ind,hasParent=!(!elem.hierarchy||!elem.hierarchy.length),parent,randSeed=Math.floor(1e6*Math.random()),globalData=elem.globalData;function executeExpression(t){return value=t,this.frameExpressionId===elem.globalData.frameId&&"textSelector"!==this.propType?value:("textSelector"===this.propType&&(textIndex=this.textIndex,textTotal=this.textTotal,selectorValue=this.selectorValue),thisLayer||(text=elem.layerInterface.text,thisLayer=elem.layerInterface,thisComp=elem.comp.compInterface,toWorld=thisLayer.toWorld.bind(thisLayer),fromWorld=thisLayer.fromWorld.bind(thisLayer),fromComp=thisLayer.fromComp.bind(thisLayer),toComp=thisLayer.toComp.bind(thisLayer),mask=thisLayer.mask?thisLayer.mask.bind(thisLayer):null,fromCompToSurface=fromComp),transform||(transform=elem.layerInterface("ADBE Transform Group"),$bm_transform=transform,transform&&(anchorPoint=transform.anchorPoint)),4!==elemType||content||(content=thisLayer("ADBE Root Vectors Group")),effect||(effect=thisLayer(4)),(hasParent=!(!elem.hierarchy||!elem.hierarchy.length))&&!parent&&(parent=elem.hierarchy[0].layerInterface),time=this.comp.renderedFrame/this.comp.globalData.frameRate,_needsRandom&&seedRandom(randSeed+time),needsVelocity&&(velocity=velocityAtTime(time)),expression_function(),this.frameExpressionId=elem.globalData.frameId,scoped_bm_rt=scoped_bm_rt.propType===propTypes.SHAPE?scoped_bm_rt.v:scoped_bm_rt)}return executeExpression.__preventDeadCodeRemoval=[$bm_transform,anchorPoint,time,velocity,inPoint,outPoint,width,height,name,loop_in,loop_out,smooth,toComp,fromCompToSurface,toWorld,fromWorld,mask,position,rotation,scale,thisComp,numKeys,active,wiggle,loopInDuration,loopOutDuration,comp,lookAt,easeOut,easeIn,ease,nearestKey,key,text,textIndex,textTotal,selectorValue,framesToTime,timeToFrames,sourceRectAtTime,substring,substr,posterizeTime,index,globalData],executeExpression}return ob.initiateExpression=initiateExpression,ob.__preventDeadCodeRemoval=[window,document,XMLHttpRequest,fetch,frames,$bm_neg,add,$bm_sum,$bm_sub,$bm_mul,$bm_div,$bm_mod,clamp,radians_to_degrees,degreesToRadians,degrees_to_radians,normalize,rgbToHsl,hslToRgb,linear,random,createPath],ob}(),expressionHelpers={searchExpressions:function(t,e,r){e.x&&(r.k=!0,r.x=!0,r.initiateExpression=ExpressionManager.initiateExpression,r.effectsSequence.push(r.initiateExpression(t,e,r).bind(r)))},getSpeedAtTime:function(t){var e=this.getValueAtTime(t),r=this.getValueAtTime(t+-.01),i=0;if(e.length){var n;for(n=0;n<e.length;n+=1)i+=Math.pow(r[n]-e[n],2);i=100*Math.sqrt(i)}else i=0;return i},getVelocityAtTime:function(t){if(void 0!==this.vel)return this.vel;var e,r,i=-.001,n=this.getValueAtTime(t),a=this.getValueAtTime(t+i);if(n.length)for(e=createTypedArray("float32",n.length),r=0;r<n.length;r+=1)e[r]=(a[r]-n[r])/i;else e=(a-n)/i;return e},getValueAtTime:function(t){return t*=this.elem.globalData.frameRate,(t-=this.offsetTime)!==this._cachingAtTime.lastFrame&&(this._cachingAtTime.lastIndex=this._cachingAtTime.lastFrame<t?this._cachingAtTime.lastIndex:0,this._cachingAtTime.value=this.interpolateValue(t,this._cachingAtTime),this._cachingAtTime.lastFrame=t),this._cachingAtTime.value},getStaticValueAtTime:function(){return this.pv},setGroupProperty:function(t){this.propertyGroup=t}};function addPropertyDecorator(){function t(t,e,r){if(!this.k||!this.keyframes)return this.pv;t=t?t.toLowerCase():"";var i,n,a,s,o,l=this.comp.renderedFrame,h=this.keyframes,p=h[h.length-1].t;if(l<=p)return this.pv;if(r?n=p-(i=e?Math.abs(p-this.elem.comp.globalData.frameRate*e):Math.max(0,p-this.elem.data.ip)):((!e||e>h.length-1)&&(e=h.length-1),i=p-(n=h[h.length-1-e].t)),"pingpong"===t){if(Math.floor((l-n)/i)%2!=0)return this.getValueAtTime((i-(l-n)%i+n)/this.comp.globalData.frameRate,0)}else{if("offset"===t){var c=this.getValueAtTime(n/this.comp.globalData.frameRate,0),f=this.getValueAtTime(p/this.comp.globalData.frameRate,0),d=this.getValueAtTime(((l-n)%i+n)/this.comp.globalData.frameRate,0),u=Math.floor((l-n)/i);if(this.pv.length){for(s=(o=new Array(c.length)).length,a=0;a<s;a+=1)o[a]=(f[a]-c[a])*u+d[a];return o}return(f-c)*u+d}if("continue"===t){var m=this.getValueAtTime(p/this.comp.globalData.frameRate,0),y=this.getValueAtTime((p-.001)/this.comp.globalData.frameRate,0);if(this.pv.length){for(s=(o=new Array(m.length)).length,a=0;a<s;a+=1)o[a]=m[a]+(m[a]-y[a])*((l-p)/this.comp.globalData.frameRate)/5e-4;return o}return m+(l-p)/.001*(m-y)}}return this.getValueAtTime(((l-n)%i+n)/this.comp.globalData.frameRate,0)}function e(t,e,r){if(!this.k)return this.pv;t=t?t.toLowerCase():"";var i,n,a,s,o,l=this.comp.renderedFrame,h=this.keyframes,p=h[0].t;if(l>=p)return this.pv;if(r?n=p+(i=e?Math.abs(this.elem.comp.globalData.frameRate*e):Math.max(0,this.elem.data.op-p)):((!e||e>h.length-1)&&(e=h.length-1),i=(n=h[e].t)-p),"pingpong"===t){if(Math.floor((p-l)/i)%2==0)return this.getValueAtTime(((p-l)%i+p)/this.comp.globalData.frameRate,0)}else{if("offset"===t){var c=this.getValueAtTime(p/this.comp.globalData.frameRate,0),f=this.getValueAtTime(n/this.comp.globalData.frameRate,0),d=this.getValueAtTime((i-(p-l)%i+p)/this.comp.globalData.frameRate,0),u=Math.floor((p-l)/i)+1;if(this.pv.length){for(s=(o=new Array(c.length)).length,a=0;a<s;a+=1)o[a]=d[a]-(f[a]-c[a])*u;return o}return d-(f-c)*u}if("continue"===t){var m=this.getValueAtTime(p/this.comp.globalData.frameRate,0),y=this.getValueAtTime((p+.001)/this.comp.globalData.frameRate,0);if(this.pv.length){for(s=(o=new Array(m.length)).length,a=0;a<s;a+=1)o[a]=m[a]+(m[a]-y[a])*(p-l)/.001;return o}return m+(m-y)*(p-l)/.001}}return this.getValueAtTime((i-((p-l)%i+p))/this.comp.globalData.frameRate,0)}function r(t,e){if(!this.k)return this.pv;if(t=.5*(t||.4),(e=Math.floor(e||5))<=1)return this.pv;var r,i,n=this.comp.renderedFrame/this.comp.globalData.frameRate,a=n-t,s=e>1?(n+t-a)/(e-1):1,o=0,l=0;for(r=this.pv.length?createTypedArray("float32",this.pv.length):0;o<e;){if(i=this.getValueAtTime(a+o*s),this.pv.length)for(l=0;l<this.pv.length;l+=1)r[l]+=i[l];else r+=i;o+=1}if(this.pv.length)for(l=0;l<this.pv.length;l+=1)r[l]/=e;else r/=e;return r}function i(t){this._transformCachingAtTime||(this._transformCachingAtTime={v:new Matrix});var e=this._transformCachingAtTime.v;if(e.cloneFromProps(this.pre.props),this.appliedTransformations<1){var r=this.a.getValueAtTime(t);e.translate(-r[0]*this.a.mult,-r[1]*this.a.mult,r[2]*this.a.mult)}if(this.appliedTransformations<2){var i=this.s.getValueAtTime(t);e.scale(i[0]*this.s.mult,i[1]*this.s.mult,i[2]*this.s.mult)}if(this.sk&&this.appliedTransformations<3){var n=this.sk.getValueAtTime(t),a=this.sa.getValueAtTime(t);e.skewFromAxis(-n*this.sk.mult,a*this.sa.mult)}if(this.r&&this.appliedTransformations<4){var s=this.r.getValueAtTime(t);e.rotate(-s*this.r.mult)}else if(!this.r&&this.appliedTransformations<4){var o=this.rz.getValueAtTime(t),l=this.ry.getValueAtTime(t),h=this.rx.getValueAtTime(t),p=this.or.getValueAtTime(t);e.rotateZ(-o*this.rz.mult).rotateY(l*this.ry.mult).rotateX(h*this.rx.mult).rotateZ(-p[2]*this.or.mult).rotateY(p[1]*this.or.mult).rotateX(p[0]*this.or.mult)}if(this.data.p&&this.data.p.s){var c=this.px.getValueAtTime(t),f=this.py.getValueAtTime(t);if(this.data.p.z){var d=this.pz.getValueAtTime(t);e.translate(c*this.px.mult,f*this.py.mult,-d*this.pz.mult)}else e.translate(c*this.px.mult,f*this.py.mult,0)}else{var u=this.p.getValueAtTime(t);e.translate(u[0]*this.p.mult,u[1]*this.p.mult,-u[2]*this.p.mult)}return e}function n(){return this.v.clone(new Matrix)}var a=TransformPropertyFactory.getTransformProperty;TransformPropertyFactory.getTransformProperty=function(t,e,r){var s=a(t,e,r);return s.dynamicProperties.length?s.getValueAtTime=i.bind(s):s.getValueAtTime=n.bind(s),s.setGroupProperty=expressionHelpers.setGroupProperty,s};var s=PropertyFactory.getProp;PropertyFactory.getProp=function(i,n,a,o,l){var h=s(i,n,a,o,l);h.kf?h.getValueAtTime=expressionHelpers.getValueAtTime.bind(h):h.getValueAtTime=expressionHelpers.getStaticValueAtTime.bind(h),h.setGroupProperty=expressionHelpers.setGroupProperty,h.loopOut=t,h.loopIn=e,h.smooth=r,h.getVelocityAtTime=expressionHelpers.getVelocityAtTime.bind(h),h.getSpeedAtTime=expressionHelpers.getSpeedAtTime.bind(h),h.numKeys=1===n.a?n.k.length:0,h.propertyIndex=n.ix;var p=0;return 0!==a&&(p=createTypedArray("float32",1===n.a?n.k[0].s.length:n.k.length)),h._cachingAtTime={lastFrame:initialDefaultFrame,lastIndex:0,value:p},expressionHelpers.searchExpressions(i,n,h),h.k&&l.addDynamicProperty(h),h};var o=ShapePropertyFactory.getConstructorFunction(),l=ShapePropertyFactory.getKeyframedConstructorFunction();function h(){}h.prototype={vertices:function(t,e){this.k&&this.getValue();var r,i=this.v;void 0!==e&&(i=this.getValueAtTime(e,0));var n=i._length,a=i[t],s=i.v,o=createSizedArray(n);for(r=0;r<n;r+=1)o[r]="i"===t||"o"===t?[a[r][0]-s[r][0],a[r][1]-s[r][1]]:[a[r][0],a[r][1]];return o},points:function(t){return this.vertices("v",t)},inTangents:function(t){return this.vertices("i",t)},outTangents:function(t){return this.vertices("o",t)},isClosed:function(){return this.v.c},pointOnPath:function(t,e){var r=this.v;void 0!==e&&(r=this.getValueAtTime(e,0)),this._segmentsLength||(this._segmentsLength=bez.getSegmentsLength(r));for(var i,n=this._segmentsLength,a=n.lengths,s=n.totalLength*t,o=0,l=a.length,h=0;o<l;){if(h+a[o].addedLength>s){var p=o,c=r.c&&o===l-1?0:o+1,f=(s-h)/a[o].addedLength;i=bez.getPointInSegment(r.v[p],r.v[c],r.o[p],r.i[c],f,a[o]);break}h+=a[o].addedLength,o+=1}return i||(i=r.c?[r.v[0][0],r.v[0][1]]:[r.v[r._length-1][0],r.v[r._length-1][1]]),i},vectorOnPath:function(t,e,r){1==t?t=this.v.c:0==t&&(t=.999);var i=this.pointOnPath(t,e),n=this.pointOnPath(t+.001,e),a=n[0]-i[0],s=n[1]-i[1],o=Math.sqrt(Math.pow(a,2)+Math.pow(s,2));return 0===o?[0,0]:"tangent"===r?[a/o,s/o]:[-s/o,a/o]},tangentOnPath:function(t,e){return this.vectorOnPath(t,e,"tangent")},normalOnPath:function(t,e){return this.vectorOnPath(t,e,"normal")},setGroupProperty:expressionHelpers.setGroupProperty,getValueAtTime:expressionHelpers.getStaticValueAtTime},extendPrototype([h],o),extendPrototype([h],l),l.prototype.getValueAtTime=function(t){return this._cachingAtTime||(this._cachingAtTime={shapeValue:shapePool.clone(this.pv),lastIndex:0,lastTime:initialDefaultFrame}),t*=this.elem.globalData.frameRate,(t-=this.offsetTime)!==this._cachingAtTime.lastTime&&(this._cachingAtTime.lastIndex=this._cachingAtTime.lastTime<t?this._caching.lastIndex:0,this._cachingAtTime.lastTime=t,this.interpolateShape(t,this._cachingAtTime.shapeValue,this._cachingAtTime)),this._cachingAtTime.shapeValue},l.prototype.initiateExpression=ExpressionManager.initiateExpression;var p=ShapePropertyFactory.getShapeProp;ShapePropertyFactory.getShapeProp=function(t,e,r,i,n){var a=p(t,e,r,i,n);return a.propertyIndex=e.ix,a.lock=!1,3===r?expressionHelpers.searchExpressions(t,e.pt,a):4===r&&expressionHelpers.searchExpressions(t,e.ks,a),a.k&&t.addDynamicProperty(a),a}}function initialize$1(){addPropertyDecorator()}function addDecorator(){TextProperty.prototype.getExpressionValue=function(t,e){var r=this.calculateExpression(e);if(t.t!==r){var i={};return this.copyData(i,t),i.t=r.toString(),i.__complete=!1,i}return t},TextProperty.prototype.searchProperty=function(){var t=this.searchKeyframes(),e=this.searchExpressions();return this.kf=t||e,this.kf},TextProperty.prototype.searchExpressions=function(){return this.data.d.x?(this.calculateExpression=ExpressionManager.initiateExpression.bind(this)(this.elem,this.data.d,this),this.addEffect(this.getExpressionValue.bind(this)),!0):null}}function initialize(){addDecorator()}function SVGComposableEffect(){}function SVGTintFilter(t,e,r,i,n){this.filterManager=e;var a=createNS("feColorMatrix");a.setAttribute("type","matrix"),a.setAttribute("color-interpolation-filters","linearRGB"),a.setAttribute("values","0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0"),a.setAttribute("result",i+"_tint_1"),t.appendChild(a),(a=createNS("feColorMatrix")).setAttribute("type","matrix"),a.setAttribute("color-interpolation-filters","sRGB"),a.setAttribute("values","1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"),a.setAttribute("result",i+"_tint_2"),t.appendChild(a),this.matrixFilter=a;var s=this.createMergeNode(i,[n,i+"_tint_1",i+"_tint_2"]);t.appendChild(s)}function SVGFillFilter(t,e,r,i){this.filterManager=e;var n=createNS("feColorMatrix");n.setAttribute("type","matrix"),n.setAttribute("color-interpolation-filters","sRGB"),n.setAttribute("values","1 0 0 0 0 0 1 0 0 0 0 0 1 0 0 0 0 0 1 0"),n.setAttribute("result",i),t.appendChild(n),this.matrixFilter=n}function SVGStrokeEffect(t,e,r){this.initialized=!1,this.filterManager=e,this.elem=r,this.paths=[]}function SVGTritoneFilter(t,e,r,i){this.filterManager=e;var n=createNS("feColorMatrix");n.setAttribute("type","matrix"),n.setAttribute("color-interpolation-filters","linearRGB"),n.setAttribute("values","0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0.3333 0.3333 0.3333 0 0 0 0 0 1 0"),t.appendChild(n);var a=createNS("feComponentTransfer");a.setAttribute("color-interpolation-filters","sRGB"),a.setAttribute("result",i),this.matrixFilter=a;var s=createNS("feFuncR");s.setAttribute("type","table"),a.appendChild(s),this.feFuncR=s;var o=createNS("feFuncG");o.setAttribute("type","table"),a.appendChild(o),this.feFuncG=o;var l=createNS("feFuncB");l.setAttribute("type","table"),a.appendChild(l),this.feFuncB=l,t.appendChild(a)}function SVGProLevelsFilter(t,e,r,i){this.filterManager=e;var n=this.filterManager.effectElements,a=createNS("feComponentTransfer");(n[10].p.k||0!==n[10].p.v||n[11].p.k||1!==n[11].p.v||n[12].p.k||1!==n[12].p.v||n[13].p.k||0!==n[13].p.v||n[14].p.k||1!==n[14].p.v)&&(this.feFuncR=this.createFeFunc("feFuncR",a)),(n[17].p.k||0!==n[17].p.v||n[18].p.k||1!==n[18].p.v||n[19].p.k||1!==n[19].p.v||n[20].p.k||0!==n[20].p.v||n[21].p.k||1!==n[21].p.v)&&(this.feFuncG=this.createFeFunc("feFuncG",a)),(n[24].p.k||0!==n[24].p.v||n[25].p.k||1!==n[25].p.v||n[26].p.k||1!==n[26].p.v||n[27].p.k||0!==n[27].p.v||n[28].p.k||1!==n[28].p.v)&&(this.feFuncB=this.createFeFunc("feFuncB",a)),(n[31].p.k||0!==n[31].p.v||n[32].p.k||1!==n[32].p.v||n[33].p.k||1!==n[33].p.v||n[34].p.k||0!==n[34].p.v||n[35].p.k||1!==n[35].p.v)&&(this.feFuncA=this.createFeFunc("feFuncA",a)),(this.feFuncR||this.feFuncG||this.feFuncB||this.feFuncA)&&(a.setAttribute("color-interpolation-filters","sRGB"),t.appendChild(a)),(n[3].p.k||0!==n[3].p.v||n[4].p.k||1!==n[4].p.v||n[5].p.k||1!==n[5].p.v||n[6].p.k||0!==n[6].p.v||n[7].p.k||1!==n[7].p.v)&&((a=createNS("feComponentTransfer")).setAttribute("color-interpolation-filters","sRGB"),a.setAttribute("result",i),t.appendChild(a),this.feFuncRComposed=this.createFeFunc("feFuncR",a),this.feFuncGComposed=this.createFeFunc("feFuncG",a),this.feFuncBComposed=this.createFeFunc("feFuncB",a))}function SVGDropShadowEffect(t,e,r,i,n){var a=e.container.globalData.renderConfig.filterSize,s=e.data.fs||a;t.setAttribute("x",s.x||a.x),t.setAttribute("y",s.y||a.y),t.setAttribute("width",s.width||a.width),t.setAttribute("height",s.height||a.height),this.filterManager=e;var o=createNS("feGaussianBlur");o.setAttribute("in","SourceAlpha"),o.setAttribute("result",i+"_drop_shadow_1"),o.setAttribute("stdDeviation","0"),this.feGaussianBlur=o,t.appendChild(o);var l=createNS("feOffset");l.setAttribute("dx","25"),l.setAttribute("dy","0"),l.setAttribute("in",i+"_drop_shadow_1"),l.setAttribute("result",i+"_drop_shadow_2"),this.feOffset=l,t.appendChild(l);var h=createNS("feFlood");h.setAttribute("flood-color","#00ff00"),h.setAttribute("flood-opacity","1"),h.setAttribute("result",i+"_drop_shadow_3"),this.feFlood=h,t.appendChild(h);var p=createNS("feComposite");p.setAttribute("in",i+"_drop_shadow_3"),p.setAttribute("in2",i+"_drop_shadow_2"),p.setAttribute("operator","in"),p.setAttribute("result",i+"_drop_shadow_4"),t.appendChild(p);var c=this.createMergeNode(i,[i+"_drop_shadow_4",n]);t.appendChild(c)}SVGComposableEffect.prototype={createMergeNode:function(t,e){var r,i,n=createNS("feMerge");for(n.setAttribute("result",t),i=0;i<e.length;i+=1)(r=createNS("feMergeNode")).setAttribute("in",e[i]),n.appendChild(r),n.appendChild(r);return n}},extendPrototype([SVGComposableEffect],SVGTintFilter),SVGTintFilter.prototype.renderFrame=function(t){if(t||this.filterManager._mdf){var e=this.filterManager.effectElements[0].p.v,r=this.filterManager.effectElements[1].p.v,i=this.filterManager.effectElements[2].p.v/100;this.matrixFilter.setAttribute("values",r[0]-e[0]+" 0 0 0 "+e[0]+" "+(r[1]-e[1])+" 0 0 0 "+e[1]+" "+(r[2]-e[2])+" 0 0 0 "+e[2]+" 0 0 0 "+i+" 0")}},SVGFillFilter.prototype.renderFrame=function(t){if(t||this.filterManager._mdf){var e=this.filterManager.effectElements[2].p.v,r=this.filterManager.effectElements[6].p.v;this.matrixFilter.setAttribute("values","0 0 0 0 "+e[0]+" 0 0 0 0 "+e[1]+" 0 0 0 0 "+e[2]+" 0 0 0 "+r+" 0")}},SVGStrokeEffect.prototype.initialize=function(){var t,e,r,i,n=this.elem.layerElement.children||this.elem.layerElement.childNodes;for(1===this.filterManager.effectElements[1].p.v?(i=this.elem.maskManager.masksProperties.length,r=0):i=1+(r=this.filterManager.effectElements[0].p.v-1),(e=createNS("g")).setAttribute("fill","none"),e.setAttribute("stroke-linecap","round"),e.setAttribute("stroke-dashoffset",1);r<i;r+=1)t=createNS("path"),e.appendChild(t),this.paths.push({p:t,m:r});if(3===this.filterManager.effectElements[10].p.v){var a=createNS("mask"),s=createElementID();a.setAttribute("id",s),a.setAttribute("mask-type","alpha"),a.appendChild(e),this.elem.globalData.defs.appendChild(a);var o=createNS("g");for(o.setAttribute("mask","url("+getLocationHref()+"#"+s+")");n[0];)o.appendChild(n[0]);this.elem.layerElement.appendChild(o),this.masker=a,e.setAttribute("stroke","#fff")}else if(1===this.filterManager.effectElements[10].p.v||2===this.filterManager.effectElements[10].p.v){if(2===this.filterManager.effectElements[10].p.v)for(n=this.elem.layerElement.children||this.elem.layerElement.childNodes;n.length;)this.elem.layerElement.removeChild(n[0]);this.elem.layerElement.appendChild(e),this.elem.layerElement.removeAttribute("mask"),e.setAttribute("stroke","#fff")}this.initialized=!0,this.pathMasker=e},SVGStrokeEffect.prototype.renderFrame=function(t){var e;this.initialized||this.initialize();var r,i,n=this.paths.length;for(e=0;e<n;e+=1)if(-1!==this.paths[e].m&&(r=this.elem.maskManager.viewData[this.paths[e].m],i=this.paths[e].p,(t||this.filterManager._mdf||r.prop._mdf)&&i.setAttribute("d",r.lastPath),t||this.filterManager.effectElements[9].p._mdf||this.filterManager.effectElements[4].p._mdf||this.filterManager.effectElements[7].p._mdf||this.filterManager.effectElements[8].p._mdf||r.prop._mdf)){var a;if(0!==this.filterManager.effectElements[7].p.v||100!==this.filterManager.effectElements[8].p.v){var s=.01*Math.min(this.filterManager.effectElements[7].p.v,this.filterManager.effectElements[8].p.v),o=.01*Math.max(this.filterManager.effectElements[7].p.v,this.filterManager.effectElements[8].p.v),l=i.getTotalLength();a="0 0 0 "+l*s+" ";var h,p=l*(o-s),c=1+2*this.filterManager.effectElements[4].p.v*this.filterManager.effectElements[9].p.v*.01,f=Math.floor(p/c);for(h=0;h<f;h+=1)a+="1 "+2*this.filterManager.effectElements[4].p.v*this.filterManager.effectElements[9].p.v*.01+" ";a+="0 "+10*l+" 0 0"}else a="1 "+2*this.filterManager.effectElements[4].p.v*this.filterManager.effectElements[9].p.v*.01;i.setAttribute("stroke-dasharray",a)}if((t||this.filterManager.effectElements[4].p._mdf)&&this.pathMasker.setAttribute("stroke-width",2*this.filterManager.effectElements[4].p.v),(t||this.filterManager.effectElements[6].p._mdf)&&this.pathMasker.setAttribute("opacity",this.filterManager.effectElements[6].p.v),(1===this.filterManager.effectElements[10].p.v||2===this.filterManager.effectElements[10].p.v)&&(t||this.filterManager.effectElements[3].p._mdf)){var d=this.filterManager.effectElements[3].p.v;this.pathMasker.setAttribute("stroke","rgb("+bmFloor(255*d[0])+","+bmFloor(255*d[1])+","+bmFloor(255*d[2])+")")}},SVGTritoneFilter.prototype.renderFrame=function(t){if(t||this.filterManager._mdf){var e=this.filterManager.effectElements[0].p.v,r=this.filterManager.effectElements[1].p.v,i=this.filterManager.effectElements[2].p.v,n=i[0]+" "+r[0]+" "+e[0],a=i[1]+" "+r[1]+" "+e[1],s=i[2]+" "+r[2]+" "+e[2];this.feFuncR.setAttribute("tableValues",n),this.feFuncG.setAttribute("tableValues",a),this.feFuncB.setAttribute("tableValues",s)}},SVGProLevelsFilter.prototype.createFeFunc=function(t,e){var r=createNS(t);return r.setAttribute("type","table"),e.appendChild(r),r},SVGProLevelsFilter.prototype.getTableValue=function(t,e,r,i,n){for(var a,s,o=0,l=Math.min(t,e),h=Math.max(t,e),p=Array.call(null,{length:256}),c=0,f=n-i,d=e-t;o<=256;)s=(a=o/256)<=l?d<0?n:i:a>=h?d<0?i:n:i+f*Math.pow((a-t)/d,1/r),p[c]=s,c+=1,o+=256/255;return p.join(" ")},SVGProLevelsFilter.prototype.renderFrame=function(t){if(t||this.filterManager._mdf){var e,r=this.filterManager.effectElements;this.feFuncRComposed&&(t||r[3].p._mdf||r[4].p._mdf||r[5].p._mdf||r[6].p._mdf||r[7].p._mdf)&&(e=this.getTableValue(r[3].p.v,r[4].p.v,r[5].p.v,r[6].p.v,r[7].p.v),this.feFuncRComposed.setAttribute("tableValues",e),this.feFuncGComposed.setAttribute("tableValues",e),this.feFuncBComposed.setAttribute("tableValues",e)),this.feFuncR&&(t||r[10].p._mdf||r[11].p._mdf||r[12].p._mdf||r[13].p._mdf||r[14].p._mdf)&&(e=this.getTableValue(r[10].p.v,r[11].p.v,r[12].p.v,r[13].p.v,r[14].p.v),this.feFuncR.setAttribute("tableValues",e)),this.feFuncG&&(t||r[17].p._mdf||r[18].p._mdf||r[19].p._mdf||r[20].p._mdf||r[21].p._mdf)&&(e=this.getTableValue(r[17].p.v,r[18].p.v,r[19].p.v,r[20].p.v,r[21].p.v),this.feFuncG.setAttribute("tableValues",e)),this.feFuncB&&(t||r[24].p._mdf||r[25].p._mdf||r[26].p._mdf||r[27].p._mdf||r[28].p._mdf)&&(e=this.getTableValue(r[24].p.v,r[25].p.v,r[26].p.v,r[27].p.v,r[28].p.v),this.feFuncB.setAttribute("tableValues",e)),this.feFuncA&&(t||r[31].p._mdf||r[32].p._mdf||r[33].p._mdf||r[34].p._mdf||r[35].p._mdf)&&(e=this.getTableValue(r[31].p.v,r[32].p.v,r[33].p.v,r[34].p.v,r[35].p.v),this.feFuncA.setAttribute("tableValues",e))}},extendPrototype([SVGComposableEffect],SVGDropShadowEffect),SVGDropShadowEffect.prototype.renderFrame=function(t){if(t||this.filterManager._mdf){if((t||this.filterManager.effectElements[4].p._mdf)&&this.feGaussianBlur.setAttribute("stdDeviation",this.filterManager.effectElements[4].p.v/4),t||this.filterManager.effectElements[0].p._mdf){var e=this.filterManager.effectElements[0].p.v;this.feFlood.setAttribute("flood-color",rgbToHex(Math.round(255*e[0]),Math.round(255*e[1]),Math.round(255*e[2])))}if((t||this.filterManager.effectElements[1].p._mdf)&&this.feFlood.setAttribute("flood-opacity",this.filterManager.effectElements[1].p.v/255),t||this.filterManager.effectElements[2].p._mdf||this.filterManager.effectElements[3].p._mdf){var r=this.filterManager.effectElements[3].p.v,i=(this.filterManager.effectElements[2].p.v-90)*degToRads,n=r*Math.cos(i),a=r*Math.sin(i);this.feOffset.setAttribute("dx",n),this.feOffset.setAttribute("dy",a)}}};var _svgMatteSymbols=[];function SVGMatte3Effect(t,e,r){this.initialized=!1,this.filterManager=e,this.filterElem=t,this.elem=r,r.matteElement=createNS("g"),r.matteElement.appendChild(r.layerElement),r.matteElement.appendChild(r.transformedElement),r.baseElement=r.matteElement}function SVGGaussianBlurEffect(t,e,r,i){t.setAttribute("x","-100%"),t.setAttribute("y","-100%"),t.setAttribute("width","300%"),t.setAttribute("height","300%"),this.filterManager=e;var n=createNS("feGaussianBlur");n.setAttribute("result",i),t.appendChild(n),this.feGaussianBlur=n}return SVGMatte3Effect.prototype.findSymbol=function(t){for(var e=0,r=_svgMatteSymbols.length;e<r;){if(_svgMatteSymbols[e]===t)return _svgMatteSymbols[e];e+=1}return null},SVGMatte3Effect.prototype.replaceInParent=function(t,e){var r=t.layerElement.parentNode;if(r){for(var i,n=r.children,a=0,s=n.length;a<s&&n[a]!==t.layerElement;)a+=1;a<=s-2&&(i=n[a+1]);var o=createNS("use");o.setAttribute("href","#"+e),i?r.insertBefore(o,i):r.appendChild(o)}},SVGMatte3Effect.prototype.setElementAsMask=function(t,e){if(!this.findSymbol(e)){var r=createElementID(),i=createNS("mask");i.setAttribute("id",e.layerId),i.setAttribute("mask-type","alpha"),_svgMatteSymbols.push(e);var n=t.globalData.defs;n.appendChild(i);var a=createNS("symbol");a.setAttribute("id",r),this.replaceInParent(e,r),a.appendChild(e.layerElement),n.appendChild(a);var s=createNS("use");s.setAttribute("href","#"+r),i.appendChild(s),e.data.hd=!1,e.show()}t.setMatte(e.layerId)},SVGMatte3Effect.prototype.initialize=function(){for(var t=this.filterManager.effectElements[0].p.v,e=this.elem.comp.elements,r=0,i=e.length;r<i;)e[r]&&e[r].data.ind===t&&this.setElementAsMask(this.elem,e[r]),r+=1;this.initialized=!0},SVGMatte3Effect.prototype.renderFrame=function(){this.initialized||this.initialize()},SVGGaussianBlurEffect.prototype.renderFrame=function(t){if(t||this.filterManager._mdf){var e=.3*this.filterManager.effectElements[0].p.v,r=this.filterManager.effectElements[1].p.v,i=3==r?0:e,n=2==r?0:e;this.feGaussianBlur.setAttribute("stdDeviation",i+" "+n);var a=1==this.filterManager.effectElements[2].p.v?"wrap":"duplicate";this.feGaussianBlur.setAttribute("edgeMode",a)}},registerRenderer("canvas",CanvasRenderer),registerRenderer("html",HybridRenderer),registerRenderer("svg",SVGRenderer),ShapeModifiers.registerModifier("tm",TrimModifier),ShapeModifiers.registerModifier("pb",PuckerAndBloatModifier),ShapeModifiers.registerModifier("rp",RepeaterModifier),ShapeModifiers.registerModifier("rd",RoundCornersModifier),ShapeModifiers.registerModifier("zz",ZigZagModifier),ShapeModifiers.registerModifier("op",OffsetPathModifier),setExpressionsPlugin(Expressions),setExpressionInterfaces(getInterface),initialize$1(),initialize(),registerEffect(20,SVGTintFilter,!0),registerEffect(21,SVGFillFilter,!0),registerEffect(22,SVGStrokeEffect,!1),registerEffect(23,SVGTritoneFilter,!0),registerEffect(24,SVGProLevelsFilter,!0),registerEffect(25,SVGDropShadowEffect,!0),registerEffect(28,SVGMatte3Effect,!1),registerEffect(29,SVGGaussianBlurEffect,!0),lottie},module.exports=factory())})(lottie$1,lottie$1.exports);var lottie=lottie$1.exports,_templateObject$1,styles=r$3(_templateObject$1||(_templateObject$1=_taggedTemplateLiteral(["\n  * {\n    box-sizing: border-box;\n  }\n\n  :host {\n    --lottie-player-toolbar-height: 35px;\n    --lottie-player-toolbar-background-color: transparent;\n    --lottie-player-toolbar-icon-color: #999;\n    --lottie-player-toolbar-icon-hover-color: #222;\n    --lottie-player-toolbar-icon-active-color: #555;\n    --lottie-player-seeker-track-color: #ccc;\n    --lottie-player-seeker-thumb-color: rgba(0, 107, 120, 0.8);\n    --lottie-player-seeker-display: block;\n\n    display: block;\n    width: 100%;\n    height: 100%;\n  }\n\n  .main {\n    display: flex;\n    flex-direction: column;\n    height: 100%;\n    width: 100%;\n  }\n\n  .animation {\n    width: 100%;\n    height: 100%;\n    display: flex;\n  }\n  .animation.controls {\n    height: calc(100% - 35px);\n  }\n\n  .toolbar {\n    display: flex;\n    align-items: center;\n    justify-items: center;\n    background-color: var(--lottie-player-toolbar-background-color);\n    margin: 0 5px;\n    height: 35px;\n  }\n\n  .toolbar button {\n    cursor: pointer;\n    fill: var(--lottie-player-toolbar-icon-color);\n    display: flex;\n    background: none;\n    border: 0;\n    padding: 0;\n    outline: none;\n    height: 100%;\n  }\n\n  .toolbar button:hover {\n    fill: var(--lottie-player-toolbar-icon-hover-color);\n  }\n\n  .toolbar button.active {\n    fill: var(--lottie-player-toolbar-icon-active-color);\n  }\n\n  .toolbar button.active:hover {\n    fill: var(--lottie-player-toolbar-icon-hover-color);\n  }\n\n  .toolbar button:focus {\n    outline: 1px dotted var(--lottie-player-toolbar-icon-active-color);\n  }\n\n  .toolbar button svg {\n  }\n\n  .toolbar button.disabled svg {\n    display: none;\n  }\n\n  .seeker {\n    -webkit-appearance: none;\n    width: 95%;\n    outline: none;\n    background-color: var(--lottie-player-toolbar-background-color);\n    display: var(--lottie-player-seeker-display);\n  }\n\n  .seeker::-webkit-slider-runnable-track {\n    width: 100%;\n    height: 5px;\n    cursor: pointer;\n    background: var(--lottie-player-seeker-track-color);\n    border-radius: 3px;\n  }\n  .seeker::-webkit-slider-thumb {\n    height: 15px;\n    width: 15px;\n    border-radius: 50%;\n    background: var(--lottie-player-seeker-thumb-color);\n    cursor: pointer;\n    -webkit-appearance: none;\n    margin-top: -5px;\n  }\n  .seeker:focus::-webkit-slider-runnable-track {\n    background: #999;\n  }\n  .seeker::-moz-range-track {\n    width: 100%;\n    height: 5px;\n    cursor: pointer;\n    background: var(--lottie-player-seeker-track-color);\n    border-radius: 3px;\n  }\n  .seeker::-moz-range-thumb {\n    height: 15px;\n    width: 15px;\n    border-radius: 50%;\n    background: var(--lottie-player-seeker-thumb-color);\n    cursor: pointer;\n  }\n  .seeker::-ms-track {\n    width: 100%;\n    height: 5px;\n    cursor: pointer;\n    background: transparent;\n    border-color: transparent;\n    color: transparent;\n  }\n  .seeker::-ms-fill-lower {\n    background: var(--lottie-player-seeker-track-color);\n    border-radius: 3px;\n  }\n  .seeker::-ms-fill-upper {\n    background: var(--lottie-player-seeker-track-color);\n    border-radius: 3px;\n  }\n  .seeker::-ms-thumb {\n    border: 0;\n    height: 15px;\n    width: 15px;\n    border-radius: 50%;\n    background: var(--lottie-player-seeker-thumb-color);\n    cursor: pointer;\n  }\n  .seeker:focus::-ms-fill-lower {\n    background: var(--lottie-player-seeker-track-color);\n  }\n  .seeker:focus::-ms-fill-upper {\n    background: var(--lottie-player-seeker-track-color);\n  }\n\n  .error {\n    display: flex;\n    justify-content: center;\n    height: 100%;\n    align-items: center;\n  }\n"]))),LOTTIE_PLAYER_VERSION="1.7.1",LOTTIE_WEB_VERSION="^5.10.0",_templateObject,_templateObject2,_templateObject3,_templateObject4,_templateObject5,PlayerState,PlayMode,PlayerEvents;function parseSrc(t){if("object"==typeof t)return t;try{return JSON.parse(t)}catch(e){return new URL(t,window.location.href).toString()}}function isLottie(t){return["v","ip","op","layers","fr","w","h"].every((e=>Object.prototype.hasOwnProperty.call(t,e)))}function fromURL(t){return _fromURL.apply(this,arguments)}function _fromURL(){return(_fromURL=_asyncToGenerator((function*(t){if("string"!=typeof t)throw new Error("The url value must be a string");var e;try{var r=new URL(t),i=yield fetch(r.toString());e=yield i.json()}catch(t){throw new Error("An error occurred while trying to load the Lottie file from URL")}return e}))).apply(this,arguments)}!function(t){t.Destroyed="destroyed",t.Error="error",t.Frozen="frozen",t.Loading="loading",t.Paused="paused",t.Playing="playing",t.Stopped="stopped"}(PlayerState||(PlayerState={})),function(t){t.Bounce="bounce",t.Normal="normal"}(PlayMode||(PlayMode={})),function(t){t.Complete="complete",t.Destroyed="destroyed",t.Error="error",t.Frame="frame",t.Freeze="freeze",t.Load="load",t.Loop="loop",t.Pause="pause",t.Play="play",t.Ready="ready",t.Rendered="rendered",t.Stop="stop"}(PlayerEvents||(PlayerEvents={}));var LottiePlayer=class extends s{constructor(){super(...arguments),this.autoplay=!1,this.background="transparent",this.controls=!1,this.currentState=PlayerState.Loading,this.description="Lottie animation",this.direction=1,this.disableCheck=!1,this.disableShadowDOM=!1,this.hover=!1,this.intermission=1,this.loop=!1,this.mode=PlayMode.Normal,this.preserveAspectRatio="xMidYMid meet",this.renderer="svg",this.speed=1,this._io=void 0,this._counter=1}load(t){var e=this;return _asyncToGenerator((function*(){var r={container:e.container,loop:!1,autoplay:!1,renderer:e.renderer,rendererSettings:Object.assign({preserveAspectRatio:e.preserveAspectRatio,clearCanvas:!1,progressiveLoad:!0,hideOnTransparent:!0},e.viewBoxSize&&{viewBoxSize:e.viewBoxSize})};try{var i=parseSrc(t),n={},a="string"==typeof i?"path":"animationData";e._lottie&&e._lottie.destroy(),e.webworkers&&lottie$1.exports.useWebWorker(!0),e._lottie=lottie$1.exports.loadAnimation(Object.assign(Object.assign({},r),{[a]:i})),e._attachEventListeners(),e.disableCheck||("path"===a?(n=yield fromURL(i),a="animationData"):n=i,isLottie(n)||(e.currentState=PlayerState.Error,e.dispatchEvent(new CustomEvent(PlayerEvents.Error))))}catch(t){e.currentState=PlayerState.Error,e.dispatchEvent(new CustomEvent(PlayerEvents.Error))}}))()}getLottie(){return this._lottie}getVersions(){return{lottieWebVersion:LOTTIE_WEB_VERSION,lottiePlayerVersion:LOTTIE_PLAYER_VERSION}}play(){this._lottie&&(this._lottie.play(),this.currentState=PlayerState.Playing,this.dispatchEvent(new CustomEvent(PlayerEvents.Play)))}pause(){this._lottie&&(this._lottie.pause(),this.currentState=PlayerState.Paused,this.dispatchEvent(new CustomEvent(PlayerEvents.Pause)))}stop(){this._lottie&&(this._counter=1,this._lottie.stop(),this.currentState=PlayerState.Stopped,this.dispatchEvent(new CustomEvent(PlayerEvents.Stop)))}destroy(){this._lottie&&(this._lottie.destroy(),this._lottie=null,this.currentState=PlayerState.Destroyed,this.dispatchEvent(new CustomEvent(PlayerEvents.Destroyed)),this.remove())}seek(t){if(this._lottie){var e=/^(\d+)(%?)$/.exec(t.toString());if(e){var r="%"===e[2]?this._lottie.totalFrames*Number(e[1])/100:Number(e[1]);this.seeker=r,this.currentState===PlayerState.Playing?this._lottie.goToAndPlay(r,!0):(this._lottie.goToAndStop(r,!0),this._lottie.pause())}}}snapshot(){var t=!(arguments.length>0&&void 0!==arguments[0])||arguments[0];if(this.shadowRoot){var e=this.shadowRoot.querySelector(".animation svg"),r=(new XMLSerializer).serializeToString(e);if(t){var i=document.createElement("a");i.href="data:image/svg+xml;charset=utf-8,".concat(encodeURIComponent(r)),i.download="download_".concat(this.seeker,".svg"),document.body.appendChild(i),i.click(),document.body.removeChild(i)}return r}}setSpeed(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:1;this._lottie&&this._lottie.setSpeed(t)}setDirection(t){this._lottie&&this._lottie.setDirection(t)}setLooping(t){this._lottie&&(this.loop=t,this._lottie.loop=t)}togglePlay(){return this.currentState===PlayerState.Playing?this.pause():this.play()}toggleLooping(){this.setLooping(!this.loop)}resize(){this._lottie&&this._lottie.resize()}static get styles(){return styles}disconnectedCallback(){this.isConnected||(this._io&&(this._io.disconnect(),this._io=void 0),document.removeEventListener("visibilitychange",(()=>this._onVisibilityChange())),this.destroy())}render(){var t=this.controls?"main controls":"main",e=this.controls?"animation controls":"animation";return $(_templateObject||(_templateObject=_taggedTemplateLiteral([' <div\n      id="animation-container"\n      class=','\n      lang="en"\n      aria-label=','\n      role="img"\n    >\n      <div\n        id="animation"\n        class=','\n        style="background:',';"\n      >\n        ',"\n      </div>\n      ","\n    </div>"])),t,this.description,e,this.background,this.currentState===PlayerState.Error?$(_templateObject2||(_templateObject2=_taggedTemplateLiteral(['<div class="error">⚠️</div>']))):void 0,this.controls&&!this.disableShadowDOM?this.renderControls():void 0)}createRenderRoot(){return this.disableShadowDOM&&(this.style.display="block"),this.disableShadowDOM?this:super.createRenderRoot()}firstUpdated(){"IntersectionObserver"in window&&(this._io=new IntersectionObserver((t=>{t[0].isIntersecting?this.currentState===PlayerState.Frozen&&this.play():this.currentState===PlayerState.Playing&&this.freeze()})),this._io.observe(this.container)),void 0!==document.hidden&&document.addEventListener("visibilitychange",(()=>this._onVisibilityChange())),this.src&&this.load(this.src),this.dispatchEvent(new CustomEvent(PlayerEvents.Rendered))}renderControls(){var t=this.currentState===PlayerState.Playing,e=this.currentState===PlayerState.Paused,r=this.currentState===PlayerState.Stopped;return $(_templateObject3||(_templateObject3=_taggedTemplateLiteral(['\n      <div\n        id="lottie-controls"\n        aria-label="lottie-animation-controls"\n        class="toolbar"\n      >\n        <button\n          id="lottie-play-button"\n          @click=',"\n          class=",'\n          style="align-items:center;"\n          tabindex="0"\n          aria-label="play-pause"\n        >\n          ','\n        </button>\n        <button\n          id="lottie-stop-button"\n          @click=',"\n          class=",'\n          style="align-items:center;"\n          tabindex="0"\n          aria-label="stop"\n        >\n          <svg width="24" height="24" aria-hidden="true" focusable="false">\n            <path d="M6 6h12v12H6V6z" />\n          </svg>\n        </button>\n        <input\n          id="lottie-seeker-input"\n          class="seeker"\n          type="range"\n          min="0"\n          step="1"\n          max="100"\n          .value=',"\n          @input=","\n          @mousedown=","\n          @mouseup=",'\n          aria-valuemin="1"\n          aria-valuemax="100"\n          role="slider"\n          aria-valuenow=','\n          tabindex="0"\n          aria-label="lottie-seek-input"\n        />\n        <button\n          id="lottie-loop-toggle"\n          @click=',"\n          class=",'\n          style="align-items:center;"\n          tabindex="0"\n          aria-label="loop-toggle"\n        >\n          <svg width="24" height="24" aria-hidden="true" focusable="false">\n            <path\n              d="M17.016 17.016v-4.031h1.969v6h-12v3l-3.984-3.984 3.984-3.984v3h10.031zM6.984 6.984v4.031H5.015v-6h12v-3l3.984 3.984-3.984 3.984v-3H6.984z"\n            />\n          </svg>\n        </button>\n      </div>\n    '])),this.togglePlay,t||e?"active":"",$(t?_templateObject4||(_templateObject4=_taggedTemplateLiteral(['<svg\n                width="24"\n                height="24"\n                aria-hidden="true"\n                focusable="false"\n              >\n                <path\n                  d="M14.016 5.016H18v13.969h-3.984V5.016zM6 18.984V5.015h3.984v13.969H6z"\n                />\n              </svg>'])):_templateObject5||(_templateObject5=_taggedTemplateLiteral(['<svg\n                width="24"\n                height="24"\n                aria-hidden="true"\n                focusable="false"\n              >\n                <path d="M8.016 5.016L18.985 12 8.016 18.984V5.015z" />\n              </svg>']))),this.stop,r?"active":"",this.seeker,this._handleSeekChange,(()=>{this._prevState=this.currentState,this.freeze()}),(()=>{this._prevState===PlayerState.Playing&&this.play()}),this.seeker,this.toggleLooping,this.loop?"active":"")}_onVisibilityChange(){!0===document.hidden&&this.currentState===PlayerState.Playing?this.freeze():this.currentState===PlayerState.Frozen&&this.play()}_handleSeekChange(t){if(this._lottie&&!isNaN(t.target.value)){var e=t.target.value/100*this._lottie.totalFrames;this.seek(e)}}_attachEventListeners(){this._lottie.addEventListener("enterFrame",(()=>{this.seeker=this._lottie.currentFrame/this._lottie.totalFrames*100,this.dispatchEvent(new CustomEvent(PlayerEvents.Frame,{detail:{frame:this._lottie.currentFrame,seeker:this.seeker}}))})),this._lottie.addEventListener("complete",(()=>{if(this.currentState===PlayerState.Playing){if(!this.loop||this.count&&this._counter>=this.count){if(this.dispatchEvent(new CustomEvent(PlayerEvents.Complete)),this.mode!==PlayMode.Bounce)return;if(0===this._lottie.currentFrame)return}this.mode===PlayMode.Bounce?(this.count&&(this._counter+=.5),setTimeout((()=>{this.dispatchEvent(new CustomEvent(PlayerEvents.Loop)),this.currentState===PlayerState.Playing&&(this._lottie.setDirection(-1*this._lottie.playDirection),this._lottie.play())}),this.intermission)):(this.count&&(this._counter+=1),window.setTimeout((()=>{this.dispatchEvent(new CustomEvent(PlayerEvents.Loop)),this.currentState===PlayerState.Playing&&(-1===this.direction?(this.seek("99%"),this.play()):(this._lottie.stop(),this._lottie.play()))}),this.intermission))}else this.dispatchEvent(new CustomEvent(PlayerEvents.Complete))})),this._lottie.addEventListener("DOMLoaded",(()=>{this.setSpeed(this.speed),this.setDirection(this.direction),this.autoplay&&(-1===this.direction&&this.seek("100%"),this.play()),this.dispatchEvent(new CustomEvent(PlayerEvents.Ready))})),this._lottie.addEventListener("data_ready",(()=>{this.dispatchEvent(new CustomEvent(PlayerEvents.Load))})),this._lottie.addEventListener("data_failed",(()=>{this.currentState=PlayerState.Error,this.dispatchEvent(new CustomEvent(PlayerEvents.Error))})),this.container.addEventListener("mouseenter",(()=>{this.hover&&this.currentState!==PlayerState.Playing&&this.play()})),this.container.addEventListener("mouseleave",(()=>{this.hover&&this.currentState===PlayerState.Playing&&this.stop()}))}freeze(){this._lottie&&(this._lottie.pause(),this.currentState=PlayerState.Frozen,this.dispatchEvent(new CustomEvent(PlayerEvents.Freeze)))}};__decorate([e$5({type:Boolean})],LottiePlayer.prototype,"autoplay",void 0),__decorate([e$5({type:String,reflect:!0})],LottiePlayer.prototype,"background",void 0),__decorate([e$5({type:Boolean})],LottiePlayer.prototype,"controls",void 0),__decorate([e$5({type:Number})],LottiePlayer.prototype,"count",void 0),__decorate([e$5({type:String})],LottiePlayer.prototype,"currentState",void 0),__decorate([e$5({type:String})],LottiePlayer.prototype,"description",void 0),__decorate([e$5({type:Number})],LottiePlayer.prototype,"direction",void 0),__decorate([e$5({type:Boolean})],LottiePlayer.prototype,"disableCheck",void 0),__decorate([e$5({type:Boolean})],LottiePlayer.prototype,"disableShadowDOM",void 0),__decorate([e$5({type:Boolean})],LottiePlayer.prototype,"hover",void 0),__decorate([e$5()],LottiePlayer.prototype,"intermission",void 0),__decorate([e$5({type:Boolean,reflect:!0})],LottiePlayer.prototype,"loop",void 0),__decorate([e$5()],LottiePlayer.prototype,"mode",void 0),__decorate([e$5({type:String})],LottiePlayer.prototype,"preserveAspectRatio",void 0),__decorate([e$5({type:String})],LottiePlayer.prototype,"renderer",void 0),__decorate([e$5({type:String})],LottiePlayer.prototype,"viewBoxSize",void 0),__decorate([e$5()],LottiePlayer.prototype,"seeker",void 0),__decorate([e$5({type:Number})],LottiePlayer.prototype,"speed",void 0),__decorate([e$5({type:String})],LottiePlayer.prototype,"src",void 0),__decorate([e$5({type:Boolean})],LottiePlayer.prototype,"webworkers",void 0),__decorate([i(".animation")],LottiePlayer.prototype,"container",void 0),LottiePlayer=__decorate([n$1("lottie-player")],LottiePlayer)}}]);