<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="72px" height="52px" viewBox="0 0 72 52" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 3.7.1 (28215) - http://www.bohemiancoding.com/sketch -->
    <title>slider-focus</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0" y="0" width="70" height="50" rx="4"></rect>
        <mask id="mask-3" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="-1" y="-1" width="72" height="52">
            <rect x="-1" y="-1" width="72" height="52" fill="white"></rect>
            <use xlink:href="#path-1" fill="black"></use>
        </mask>
        <rect id="path-4" x="18" y="5" width="35" height="41"></rect>
        <mask id="mask-5" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="-1" y="-1" width="37" height="43">
            <rect x="17" y="4" width="37" height="43" fill="white"></rect>
            <use xlink:href="#path-4" fill="black"></use>
        </mask>
        <rect id="path-6" x="0" y="7" width="17" height="36"></rect>
        <mask id="mask-7" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="-1" y="-1" width="19" height="38">
            <rect x="-1" y="6" width="19" height="38" fill="white"></rect>
            <use xlink:href="#path-6" fill="black"></use>
        </mask>
        <rect id="path-8" x="54" y="7" width="16" height="36"></rect>
        <mask id="mask-9" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="-1" y="-1" width="18" height="38">
            <rect x="53" y="6" width="18" height="38" fill="white"></rect>
            <use xlink:href="#path-8" fill="black"></use>
        </mask>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="slider-focus" transform="translate(1.000000, 1.000000)">
            <mask id="mask-2" fill="white">
                <use xlink:href="#path-1"></use>
            </mask>
            <g id="Rectangle-1-Copy-4">
                <use fill-opacity="0.01" fill="#00A0D2" fill-rule="evenodd" xlink:href="#path-1"></use>
                <use stroke="#3498DB" mask="url(#mask-3)" stroke-width="2" xlink:href="#path-1"></use>
            </g>
            <g id="Rectangle-5" mask="url(#mask-2)">
                <use fill-opacity="0.127858922" fill="#3498DB" fill-rule="evenodd" xlink:href="#path-4"></use>
                <use stroke="#3498DB" mask="url(#mask-5)" stroke-width="2" xlink:href="#path-4"></use>
            </g>
            <g id="Rectangle-5-Copy-5" mask="url(#mask-2)">
                <use fill-opacity="0.127858922" fill="#3498DB" fill-rule="evenodd" xlink:href="#path-6"></use>
                <use stroke="#3498DB" mask="url(#mask-7)" stroke-width="2" xlink:href="#path-6"></use>
            </g>
            <g id="Rectangle-5-Copy-6" mask="url(#mask-2)">
                <use fill-opacity="0.127858922" fill="#3498DB" fill-rule="evenodd" xlink:href="#path-8"></use>
                <use stroke="#3498DB" mask="url(#mask-9)" stroke-width="2" xlink:href="#path-8"></use>
            </g>
            <polygon id="Triangle-1-Copy-3" fill="#3498DB" mask="url(#mask-2)" points="11 23 11 28 6 25.5"></polygon>
            <polygon id="Triangle-1-Copy-2" fill="#3498DB" mask="url(#mask-2)" points="65 23 65 28 60 25.5"></polygon>
        </g>
    </g>
</svg>