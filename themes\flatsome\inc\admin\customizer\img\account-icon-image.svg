<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="100px" height="72px" viewBox="0 0 100 72" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 3.8.1 (29687) - http://www.bohemiancoding.com/sketch -->
    <title>account-icon-image</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0" y="0" width="100" height="71.4285714" rx="4"></rect>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="100" height="71.4285714" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
        <mask id="mask-4" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="100" height="71.4285714" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
        <linearGradient x1="274.066542%" y1="-34.4059336%" x2="27.6117208%" y2="100%" id="linearGradient-5">
            <stop stop-color="#FFFFFF" offset="0%"></stop>
            <stop stop-color="#8064E0" offset="42.9781115%"></stop>
            <stop stop-color="#00A0D2" offset="100%"></stop>
        </linearGradient>
        <rect id="path-6" x="24.3075435" y="8.28125" width="52.9293346" height="53.4462226" rx="26.4646673"></rect>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="account-icon-image">
            <mask id="mask-3" fill="white">
                <use xlink:href="#path-1"></use>
            </mask>
            <g id="Mask" stroke="#3498DB" mask="url(#mask-2)" stroke-width="2" fill-opacity="0.01" fill="#00A0D2">
                <use mask="url(#mask-4)" xlink:href="#path-1"></use>
            </g>
            <mask id="mask-7" fill="white">
                <use xlink:href="#path-6"></use>
            </mask>
            <use id="Rectangle-166-Copy-4" fill="url(#linearGradient-5)" xlink:href="#path-6"></use>
            <path d="M55.1344535,45.2167237 C55.1344535,45.2167237 58.5427908,41.8710684 59.2851815,37.3716714 C61.2831724,37.3716714 62.5172255,32.5882462 60.5192347,30.9056245 C60.6034637,29.1348562 63.0872406,17 50.5057747,17 C37.9243088,17 40.4080856,29.1348562 40.4923147,30.9056245 C38.4943238,32.5882462 39.728377,37.3716714 41.7263678,37.3716714 C42.4687586,41.8710684 45.8790547,45.2167237 45.8790547,45.2167237 C45.8790547,45.2167237 45.8516313,48.3802092 44.6920131,48.5623789 C40.9545949,49.1519821 27,55.2536895 27,61.945 L74.0115494,61.945 C74.0115494,55.2536895 60.0569545,49.1519821 56.3214951,48.5623789 C55.1618769,48.3802092 55.1344535,45.2167237 55.1344535,45.2167237 Z" id="Path" fill-opacity="0.127858922" fill="#FFFFFF" mask="url(#mask-7)"></path>
        </g>
    </g>
</svg>