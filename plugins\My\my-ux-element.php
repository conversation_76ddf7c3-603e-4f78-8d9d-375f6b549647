    <?php
    /**
     * Plugin Name:  My UX Element
     * Description:  A custom element for UX Builder.
     * Version:      1.0.0
     * Author:       Your Name
     */

    add_action('ux_builder_enqueue_scripts', 'my_ux_element_enqueue_scripts');
    function my_ux_element_enqueue_scripts() {
        wp_enqueue_script( 'my-ux-element-script', plugins_url( '/js/my-ux-element.js', __FILE__ ), array( 'jquery' ), '1.0.0', true );
        wp_enqueue_style( 'my-ux-element-style', plugins_url( '/css/my-ux-element.css', __FILE__ ), array(), '1.0.0' );
    }

    add_action('ux_builder_register_elements', 'my_ux_element_register_elements');
    function my_ux_element_register_elements() {
        return array(
            'my_element' => array(
                'name'       => 'my_element',
                'title'      => 'My Custom Element',
                'category'   => 'content',
                'icon'       => 'icon-placeholder',
                'params'     => array(
                    array(
                        'type'  => 'textfield',
                        'heading' => 'Text',
                        'param_name' => 'text',
                        'value' => 'Default text'
                    )
                ),
                'scripts' => array(
                    plugins_url( '/js/my-ux-element-frontend.js', __FILE__ ),
                ),
                'styles' => array(
                    plugins_url( '/css/my-ux-element-frontend.css', __FILE__ )
                ),
            )
        );
    }