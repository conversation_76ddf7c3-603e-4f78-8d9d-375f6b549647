<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<svg width="70px" height="67px" viewBox="0 0 70 67" version="1.1" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink">
    <!-- Generator: Sketch 3.7.2 (28276) - http://www.bohemiancoding.com/sketch -->
    <title>category-off-canvas</title>
    <desc>Created with Sketch.</desc>
    <defs>
        <rect id="path-1" x="0" y="0" width="70" height="50" rx="4"></rect>
        <mask id="mask-2" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="70" height="50" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
        <mask id="mask-4" maskContentUnits="userSpaceOnUse" maskUnits="objectBoundingBox" x="0" y="0" width="70" height="50" fill="white">
            <use xlink:href="#path-1"></use>
        </mask>
    </defs>
    <g id="Page-1" stroke="none" stroke-width="1" fill="none" fill-rule="evenodd">
        <g id="category-off-canvas">
            <text id="Off-Canvas" font-family="Lato-Regular, Lato" font-size="13" font-weight="normal" fill="#9A8F9A">
                <tspan x="0" y="66">Off Canvas</tspan>
            </text>
            <mask id="mask-3" fill="white">
                <use xlink:href="#path-1"></use>
            </mask>
            <g id="Mask" stroke="#3498DB" mask="url(#mask-2)" stroke-width="2" fill-opacity="0.01" fill="#00A0D2">
                <use mask="url(#mask-4)" xlink:href="#path-1"></use>
            </g>
            <path d="M25.9735,8.18 L25.9735,8.891 L22.8775,8.891 L22.8775,11.1455 L25.5235,11.1455 L25.5235,11.8565 L22.8775,11.8565 L22.8775,14.6285 L22,14.6285 L22,8.18 L25.9735,8.18 Z M27.859,10.07 L27.859,14.6285 L27.058,14.6285 L27.058,10.07 L27.859,10.07 Z M28.039,8.639 C28.039,8.71700039 28.0232502,8.78974966 27.99175,8.85725 C27.9602498,8.92475034 27.9182503,8.98474974 27.86575,9.03725 C27.8132497,9.08975026 27.7525003,9.13099985 27.6835,9.161 C27.6144997,9.19100015 27.5410004,9.206 27.463,9.206 C27.3849996,9.206 27.3122503,9.19100015 27.24475,9.161 C27.1772497,9.13099985 27.1172503,9.08975026 27.06475,9.03725 C27.0122497,8.98474974 26.9710002,8.92475034 26.941,8.85725 C26.9109999,8.78974966 26.896,8.71700039 26.896,8.639 C26.896,8.56099961 26.9109999,8.48675035 26.941,8.41625 C26.9710002,8.34574965 27.0122497,8.28425026 27.06475,8.23175 C27.1172503,8.17924974 27.1772497,8.13800015 27.24475,8.108 C27.3122503,8.07799985 27.3849996,8.063 27.463,8.063 C27.5410004,8.063 27.6144997,8.07799985 27.6835,8.108 C27.7525003,8.13800015 27.8132497,8.17924974 27.86575,8.23175 C27.9182503,8.28425026 27.9602498,8.34574965 27.99175,8.41625 C28.0232502,8.48675035 28.039,8.56099961 28.039,8.639 L28.039,8.639 Z M30.163,8 L30.163,14.6285 L29.362,14.6285 L29.362,8 L30.163,8 Z M32.9575,14.7005 C32.5974982,14.7005 32.320751,14.600001 32.12725,14.399 C31.933749,14.197999 31.837,13.9085019 31.837,13.5305 L31.837,10.7405 L31.288,10.7405 C31.2399998,10.7405 31.1995002,10.7262501 31.1665,10.69775 C31.1334998,10.6692499 31.117,10.6250003 31.117,10.565 L31.117,10.2455 L31.864,10.151 L32.0485,8.7425 C32.0545,8.69749978 32.0739998,8.66075014 32.107,8.63225 C32.1400002,8.60374986 32.1819997,8.5895 32.233,8.5895 L32.638,8.5895 L32.638,10.16 L33.943,10.16 L33.943,10.7405 L32.638,10.7405 L32.638,13.4765 C32.638,13.668501 32.6844995,13.8109995 32.7775,13.904 C32.8705005,13.9970005 32.9904993,14.0435 33.1375,14.0435 C33.2215004,14.0435 33.2942497,14.0322501 33.35575,14.00975 C33.4172503,13.9872499 33.4704998,13.9625001 33.5155,13.9355 C33.5605002,13.9084999 33.5987498,13.8837501 33.63025,13.86125 C33.6617502,13.8387499 33.6894999,13.8275 33.7135,13.8275 C33.7555002,13.8275 33.7929998,13.8529997 33.826,13.904 L34.06,14.2865 C33.9219993,14.4155006 33.755501,14.5167496 33.5605,14.59025 C33.365499,14.6637504 33.164501,14.7005 32.9575,14.7005 L32.9575,14.7005 Z M36.7375,9.998 C37.0105014,9.998 37.2624988,10.0437495 37.4935,10.13525 C37.7245012,10.2267505 37.9239992,10.3587491 38.092,10.53125 C38.2600008,10.7037509 38.3912495,10.9167487 38.48575,11.17025 C38.5802505,11.4237513 38.6275,11.7124984 38.6275,12.0365 C38.6275,12.1625006 38.6140001,12.2464998 38.587,12.2885 C38.5599999,12.3305002 38.5090004,12.3515 38.434,12.3515 L35.401,12.3515 C35.407,12.6395014 35.4459996,12.8899989 35.518,13.103 C35.5900004,13.3160011 35.6889994,13.4937493 35.815,13.63625 C35.9410006,13.7787507 36.0909991,13.8852496 36.265,13.95575 C36.4390009,14.0262504 36.6339989,14.0615 36.85,14.0615 C37.051001,14.0615 37.2242493,14.0382502 37.36975,13.99175 C37.5152507,13.9452498 37.6404995,13.8950003 37.7455,13.841 C37.8505005,13.7869997 37.9382496,13.7367502 38.00875,13.69025 C38.0792504,13.6437498 38.1399997,13.6205 38.191,13.6205 C38.2570003,13.6205 38.3079998,13.6459997 38.344,13.697 L38.569,13.9895 C38.4699995,14.1095006 38.3515007,14.2137496 38.2135,14.30225 C38.0754993,14.3907504 37.9277508,14.4634997 37.77025,14.5205 C37.6127492,14.5775003 37.4500008,14.6202499 37.282,14.64875 C37.1139992,14.6772501 36.9475008,14.6915 36.7825,14.6915 C36.4674984,14.6915 36.1772513,14.6382505 35.91175,14.53175 C35.6462487,14.4252495 35.416751,14.269251 35.22325,14.06375 C35.029749,13.858249 34.8790005,13.6040015 34.771,13.301 C34.6629995,12.9979985 34.609,12.650002 34.609,12.257 C34.609,11.9389984 34.6577495,11.6420014 34.75525,11.366 C34.8527505,11.0899986 34.9929991,10.850751 35.176,10.64825 C35.3590009,10.445749 35.5824987,10.2867506 35.8465,10.17125 C36.1105013,10.0557494 36.4074984,9.998 36.7375,9.998 L36.7375,9.998 Z M36.7555,10.5875 C36.3684981,10.5875 36.0640011,10.6992489 35.842,10.92275 C35.6199989,11.1462511 35.4820003,11.455998 35.428,11.852 L37.9075,11.852 C37.9075,11.6659991 37.8820003,11.4957508 37.831,11.34125 C37.7799997,11.1867492 37.7050005,11.0532506 37.606,10.94075 C37.5069995,10.8282494 37.3862507,10.7412503 37.24375,10.67975 C37.1012493,10.6182497 36.9385009,10.5875 36.7555,10.5875 L36.7555,10.5875 Z M39.649,14.6285 L39.649,10.07 L40.108,10.07 C40.1950004,10.07 40.2549998,10.0864998 40.288,10.1195 C40.3210002,10.1525002 40.3434999,10.2094996 40.3555,10.2905 L40.4095,11.0015 C40.5655008,10.6834984 40.7582489,10.4352509 40.98775,10.25675 C41.2172511,10.0782491 41.4864985,9.989 41.7955,9.989 C41.9215006,9.989 42.0354995,10.0032499 42.1375,10.03175 C42.2395005,10.0602501 42.3339996,10.0999997 42.421,10.151 L42.3175,10.7495 C42.2964999,10.8245004 42.2500004,10.862 42.178,10.862 C42.1359998,10.862 42.0715004,10.8477501 41.9845,10.81925 C41.8974996,10.7907499 41.7760008,10.7765 41.62,10.7765 C41.3409986,10.7765 41.1077509,10.8574992 40.92025,11.0195 C40.7327491,11.1815008 40.5760006,11.4169985 40.45,11.726 L40.45,14.6285 L39.649,14.6285 Z" id="Filter" fill="#3498DB" mask="url(#mask-3)"></path>
            <rect id="Rectangle-5-Copy-7" fill="#3498DB" mask="url(#mask-3)" x="12" y="18" width="47" height="32"></rect>
            <rect id="Rectangle-5-Copy-7" fill="#3498DB" mask="url(#mask-3)" x="12" y="8" width="8" height="3"></rect>
            <rect id="Rectangle-5-Copy-7" fill="#3498DB" mask="url(#mask-3)" x="12" y="12" width="8" height="3"></rect>
        </g>
    </g>
</svg>