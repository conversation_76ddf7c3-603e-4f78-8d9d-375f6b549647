li[data-slug="woocommerce"] > span,
tr[data-content="attachment"]{
    display: none!important;
}

.wp-core-ui .woocommerce-button{
    background-color: #bb77ae!important;
    border-color: #A36597!important;;
    -webkit-box-shadow: inset 0 1px 0 rgba(255,255,255,.25),0 1px 0 #A36597!important;
    box-shadow: inset 0 1px 0 rgba(255,255,255,.25),0 1px 0 #A36597!important;
    text-shadow: 0 -1px 1px #A36597,1px 0 1px #A36597,0 1px 1px #A36597,-1px 0 1px #A36597!important;
    opacity: 1;
}

.envato-setup-content ul{
    list-style: disc
}

.envato-setup-content p.lead{
    font-size: 1.2em;
    color:#000;
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
}
.envato-setup-content p.success{
    color: #7eb62e!important;
}
.envato-setup-content p.error{
    color: red!important;
}


/*
tr[data-content="product_variation"]{
  display: none!important;
} */

.envato-setup-content p, .envato-setup-content table {
    font-size: 1em;
    color: #666
}

body {
    margin: 30px auto 24px;
    box-shadow: none;
    background: #f1f1f1;
    padding: 0;
    border: 0;
}

#wc-logo {
    border: 0;
    margin: 0 0 24px;
    padding: 0;
    text-align: center
}

#wc-logo img {
    max-width: 50%
}

.envato-setup-content {
    box-shadow: 0 1px 3px rgba(0, 0, 0, .13);
    padding: 24px 24px 0;
    background: #fff;
    overflow: hidden;
}

.envato-setup-content h1, .envato-setup-content h2, .envato-setup-content h3,  .envato-setup-content table {
    margin: 0 0 24px;
    border: 0;
    padding: 0;
    color: #666;
    clear: none
}
.envato-setup-content table{
    margin:0;
}

.envato-setup-content p {
    margin: 0 0 24px
}

.envato-setup-content a {
    color: #0091cd
}

.envato-setup-content a:focus, .envato-setup-content a:hover {
    color: #111
}

.envato-setup-content .notice {
    margin-left: 0;
    margin-right: 0;
    margin-bottom: 15px;
}

.envato-setup-content .notice p:last-child {
    margin-bottom: 0;
}

.envato-setup-content .form-table th {
    width: 35%;
    vertical-align: top;
    font-weight: 400
}

.envato-setup-content .form-table td {
    vertical-align: top
}

.envato-setup-content .form-table td input, .envato-setup-content .form-table td select {
    width: 100%;
    box-sizing: border-box
}

.envato-setup-content .form-table td input[size] {
    width: auto
}

.envato-setup-content .form-table td .description {
    line-height: 1.5em;
    display: block;
    margin-top: .25em;
    color: #999;
    font-style: italic
}

.envato-setup-content .form-table td .input-checkbox, .envato-setup-content .form-table td .input-radio {
    width: auto;
    box-sizing: inherit;
    padding: inherit;
    margin: 0 .5em 0 0;
    box-shadow: none
}

.envato-setup-content .form-table .section_title td {
    padding: 0
}

.envato-setup-content .form-table .section_title td h2, .envato-setup-content .form-table .section_title td p {
    margin: 12px 0 0
}

.envato-setup-content .form-table td, .envato-setup-content .form-table th {
    padding: 12px 0;
    margin: 0;
    border: 0
}

.envato-setup-content .form-table td:first-child, .envato-setup-content .form-table th:first-child {
    padding-right: 1em
}

.envato-setup-content .form-table table.tax-rates {
    width: 100%;
    font-size: .92em
}

.envato-setup-content .form-table table.tax-rates th {
    padding: 0;
    text-align: center;
    width: auto;
    vertical-align: middle
}

.envato-setup-content .form-table table.tax-rates td {
    border: 1px solid #eee;
    padding: 6px;
    text-align: center;
    vertical-align: middle
}

.envato-setup-content .form-table table.tax-rates td input {
    outline: 0;
    border: 0;
    padding: 0;
    box-shadow: none;
    text-align: center
}

.envato-setup-content .form-table table.tax-rates td.sort {
    cursor: move;
    color: #ccc
}

.envato-setup-content .form-table table.tax-rates td.sort:before {
    content: "\f333";
    font-family: dashicons
}

.envato-setup-content .form-table table.tax-rates .add {
    padding: 1em 0 0 1em;
    line-height: 1em;
    font-size: 1em;
    width: 0;
    margin: 6px 0 0;
    height: 0;
    overflow: hidden;
    position: relative;
    display: inline-block
}

.envato-setup-content .form-table table.tax-rates .add:before {
    content: "\f502";
    font-family: dashicons;
    position: absolute;
    left: 0;
    top: 0
}

.envato-setup-content .form-table table.tax-rates .remove {
    padding: 1em 0 0 1em;
    line-height: 1em;
    font-size: 1em;
    width: 0;
    margin: 0;
    height: 0;
    overflow: hidden;
    position: relative;
    display: inline-block
}

.envato-setup-content .form-table table.tax-rates .remove:before {
    content: "\f182";
    font-family: dashicons;
    position: absolute;
    left: 0;
    top: 0
}

.envato-setup-content .envato-setup-plugins {
    width: 100%;
    border-top: 1px solid #eee
}

.envato-setup-content .envato-setup-plugins thead th {
    display: none
}

.envato-setup-content .envato-setup-plugins .plugin-name {
    width: 30%;
    font-weight: 700
}

.envato-setup-content .envato-setup-plugins td, .envato-setup-content .envato-setup-plugins th {
    padding: 14px 0;
    border-bottom: 1px solid #eee
}

.envato-setup-content .envato-setup-plugins td:first-child, .envato-setup-content .envato-setup-plugins th:first-child {
    padding-right: 9px
}

.envato-setup-content .envato-setup-plugins th {
    padding-top: 0
}

.envato-setup-content .envato-setup-plugins .page-options p {
    color: #777;
    margin: 6px 0 0 24px;
    line-height: 1.75em
}

.envato-setup-content .envato-setup-plugins .page-options p input {
    vertical-align: middle;
    margin: 1px 0 0;
    height: 1.75em;
    width: 1.75em;
    line-height: 1.75em
}

.envato-setup-content .envato-setup-plugins .page-options p label {
    line-height: 1
}

@media screen and (max-width: 782px) {
    .envato-setup-content .form-table tbody th {
        width: auto
    }
}

.envato-setup-content .twitter-share-button {
    float: right
}

.envato-setup-content .envato-setup-next-steps {
    overflow: hidden;
    margin: 0 0 24px
}

.envato-setup-content .envato-setup-next-steps h2 {
    margin-bottom: 12px
}

.envato-setup-content .envato-setup-next-steps .envato-setup-next-steps-first {
    float: left;
    width: 50%;
    box-sizing: border-box
}

.envato-setup-content .envato-setup-next-steps .envato-setup-next-steps-last {
    float: right;
    width: 50%;
    box-sizing: border-box
}

.envato-setup-content .envato-setup-next-steps ul {
    padding: 0 2em 0 0;
    list-style: none;
    margin: 0 0 -.75em
}

.envato-setup-content .envato-setup-next-steps ul li a {
    display: block;
    padding: 0 0 .75em
}

.envato-setup-content .envato-setup-next-steps ul .setup-product a {
    text-align: center;
    font-size: 1em;
    padding: 1em;
    line-height: 1.75em;
    height: auto;
    margin: 0 0 .75em;
    opacity: 1;
}
.envato-setup-content .envato-setup-next-steps ul .setup-product a.button-primary {
    background-color: #0091cd;
    border-color: #0091cd;
    -webkit-box-shadow: inset 0 1px 0 rgba(255, 255, 255, .2), 0 1px 0 rgba(0, 0, 0, .15);
    box-shadow: inset 0 1px 0 rgba(255, 255, 255, .2), 0 1px 0 rgba(0, 0, 0, .15)
}

.envato-setup-content .envato-setup-next-steps ul li a:before {
    color: #82878c;
    font: 400 20px/1 dashicons;
    speak: none;
    display: inline-block;
    padding: 0 10px 0 0;
    top: 1px;
    position: relative;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    text-decoration: none !important;
    vertical-align: top
}

.envato-setup-content .envato-setup-next-steps ul .documentation a:before {
    content: "\f331"
}

.envato-setup-content .envato-setup-next-steps ul .howto a:before {
    content: "\f223"
}

.envato-setup-content .envato-setup-next-steps ul .rating a:before {
    content: "\f155"
}

.envato-setup-content .envato-setup-next-steps ul .support a:before {
    content: "\f307"
}

.envato-setup-content .updated, .envato-setup-content .woocommerce-language-pack, .envato-setup-content .woocommerce-tracker {
    padding: 24px 24px 0;
    margin: 0 0 24px;
    overflow: hidden;
    background: #f5f5f5
}

.envato-setup-content .updated p, .envato-setup-content .woocommerce-language-pack p, .envato-setup-content .woocommerce-tracker p {
    padding: 0;
    margin: 0 0 12px
}

.envato-setup-content .updated p:last-child, .envato-setup-content .woocommerce-language-pack p:last-child, .envato-setup-content .woocommerce-tracker p:last-child {
    margin: 0 0 24px
}

.envato-setup-content .registration-form {
    margin-bottom: 24px;
    font-size: 16px;
}

.envato-setup-steps {
    padding: 0 0 24px;
    margin: 0;
    list-style: none;
    overflow: hidden;
    color: #ccc;
    width: 100%;
    display: -webkit-inline-flex;
    display: -ms-inline-flexbox;
    display: inline-flex
}

.envato-setup-steps li {
    width: 20%;
    float: left;
    padding: 0 0 .8em;
    margin: 0;
    text-align: center;
    position: relative;
    border-bottom: 4px solid #ccc;
    line-height: 1.4em
}

.envato-setup-steps li:before {
    content: "";
    border: 4px solid #ccc;
    border-radius: 100%;
    width: 4px;
    height: 4px;
    position: absolute;
    bottom: 0;
    left: 50%;
    margin-left: -6px;
    margin-bottom: -8px;
    background: #fff
}

.envato-setup-steps li a{
    text-decoration: none;
}
.envato-setup-steps li.active {
    border-color: #0091cd;
    color: #0091cd
}
.envato-setup-steps li.active a{
    color: #0091cd
}

.envato-setup-steps li.active:before {
    border-color: #0091cd
}

.envato-setup-steps li.done {
    border-color: #0091cd;
    color: #0091cd
}
.envato-setup-steps li.done a {
    color: #0091cd
}

.envato-setup-steps li.done:before {
    border-color: #0091cd;
    background: #0091cd
}

.envato-setup .envato-setup-actions {
    overflow: hidden
}

.envato-setup .envato-setup-actions .button {
    float: right;
    font-size: 1.25em;
    padding: .5em 1em;
    line-height: 1em;
    margin-right: .5em;
    height: auto;
    box-shadow: none;
}

.envato-setup .envato-setup-actions .button.disabled {
    pointer-events: none;
    opacity: .5;
}

.envato-setup .envato-setup-actions .button-primary {
    margin: 0;
    float: right;
    opacity: 1;
}

.wc-return-to-dashboard {
    font-size: .85em;
    color: #b5b5b5;
    margin: 1.18em 0;
    display: block;
    text-align: center
}


.dtbaker_loading_button_current{
    color:#CCC !important;
    text-align: center;

}
.envato-wizard-plugins li{
    position: relative;
}
.envato-wizard-plugins li span{
    padding: 0 0 0 10px;
    font-size: 0.9em;
    color: #0091cd;
    display: inline-block;
    position: relative;

}
.envato-wizard-plugins.installing li .spinner{
    visibility: visible;
}
.envato-wizard-plugins li .spinner{
    display: inline-block;
    position: absolute;

}
.envato-setup-pages{
    width:100%;
}
.envato-setup-pages .check{
    width:35px;
}
.envato-setup-pages .item{
    width:90px;
}
.envato-setup-pages td,
.envato-setup-pages th{
    padding:5px;
}
.envato-setup-pages .status{
    display: none;
}
.envato-setup-pages.installing .status{
    display: table-cell;
}
.envato-setup-pages.installing .status span{
    display: inline-block;
    position: relative;
}
.envato-setup-pages.installing .description{
    display: none;
}

.envato-setup-pages.installing .spinner{
    visibility: visible;
}
.envato-setup-pages  .spinner{
    display: inline-block;
    position: absolute;

}

.theme-presets{
    background-color: rgba(0,0,0,.03);
    padding: 10px 20px;
    margin-left: -25px;
    margin-right: -25px;
    margin-bottom: 20px;
}

.theme-presets ul{
    list-style:none;
    margin:0px 0 15px 0;
    padding: 0;
    overflow-x:auto;
    display: block;
    white-space: nowrap;
}
.theme-presets ul li{
    list-style:none;
    display: inline-block;
    padding: 6px;
    margin:0;
    vertical-align: bottom;
}
.theme-presets ul li.current{
    background: #000;
    border-radius: 5px;
}
.theme-presets ul li a{
    float:left;
    line-height:0;
}
.theme-presets ul li a img{
    width: 160px;
    height:auto;
}
