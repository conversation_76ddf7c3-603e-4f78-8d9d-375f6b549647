# UX Date Element

A custom WordPress plugin that adds a "Current Date" element to UX Builder (Flatsome theme) for displaying the current date with various formatting options.

## Features

- **Easy Integration**: Seamlessly integrates with UX Builder
- **Multiple Date Formats**: Choose from predefined date formats or use custom PHP date format strings
- **Responsive Design**: Works on all device sizes
- **Clean Output**: Generates semantic HTML with proper CSS classes
- **Secure**: All inputs are properly sanitized

## Requirements

- WordPress 5.0 or higher
- PHP 7.4 or higher
- Flatsome theme with UX Builder

## Installation

1. Upload the `ux-date-element` folder to your `/wp-content/plugins/` directory
2. Activate the plugin through the 'Plugins' menu in WordPress
3. The "Current Date" element will now be available in UX Builder under the "Content" category

## Usage

### In UX Builder
1. Edit a page with UX Builder
2. Look for "Current Date" in the Content elements panel
3. Drag the element to your desired location
4. Configure the date format in the element options
5. Save and preview your page

### Available Date Formats
- **December 27, 2025** (F j, Y)
- **12/27/2025** (m/d/Y)
- **27/12/2025** (d/m/Y)
- **2025-12-27** (Y-m-d)
- **Friday, December 27, 2025** (l, F j, Y)

### Using Shortcode Directly
You can also use the shortcode directly in posts, pages, or widgets:

```
[ux_current_date]
[ux_current_date format="Y-m-d"]
[ux_current_date format="l, F j, Y"]
```

## Testing

The plugin includes comprehensive tests to ensure reliability:

### Running Tests

1. **Activation Tests**: 
   - Navigate to: `your-site.com/wp-content/plugins/ux-date-element/tests/test-plugin-activation.php?run_activation_tests=1`

2. **Shortcode Tests**:
   - Navigate to: `your-site.com/wp-content/plugins/ux-date-element/tests/test-shortcode-output.php?run_shortcode_tests=1`

### Test Coverage
- Plugin activation and deactivation
- UX Builder element registration
- Shortcode functionality
- Date format processing
- HTML output structure
- Input sanitization
- Security validation

## Development

### File Structure
```
ux-date-element/
├── ux-date-element.php          # Main plugin file
├── includes/                    # Additional PHP classes (future use)
├── assets/
│   ├── css/
│   │   └── ux-date-element.css  # Frontend styles
│   └── js/                      # Frontend scripts (future use)
├── tests/                       # Test files
│   ├── test-plugin-activation.php
│   └── test-shortcode-output.php
└── README.md                    # This file
```

### Hooks and Filters
The plugin uses standard WordPress hooks:
- `init` - Initialize plugin functionality
- `ux_builder_setup` - Register UX Builder element
- `wp_enqueue_scripts` - Load frontend assets

### Custom CSS
The plugin generates HTML with the class `ux-date-element` for easy styling:

```css
.ux-date-element {
    /* Your custom styles here */
    font-weight: bold;
    color: #333;
}
```

## Troubleshooting

### Plugin Won't Activate
- Ensure Flatsome theme is active
- Check PHP version (7.4+ required)
- Verify WordPress version (5.0+ required)

### Element Not Appearing in UX Builder
- Confirm plugin is activated
- Check that UX Builder is available
- Look for any PHP errors in error logs

### Date Not Displaying
- Verify shortcode syntax: `[ux_current_date]`
- Check date format parameter
- Ensure no caching issues

## Support

For support and bug reports, please check:
1. Plugin error logs
2. WordPress debug logs
3. Browser console for JavaScript errors

## Changelog

### Version 1.0.0
- Initial release
- Basic date display functionality
- UX Builder integration
- Multiple date format options
- Comprehensive testing suite

## License

This plugin is licensed under the GPL v2 or later.

## Credits

Developed for WordPress UX Builder (Flatsome theme) integration.
