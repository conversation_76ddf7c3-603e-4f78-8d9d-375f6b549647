var kirkiSetSettingValue;_.isUndefined(window.kirkiSetSettingValue)&&(kirkiSetSettingValue={set:function(i,n){var e,a=this,t=wp.customize.settings.controls[i];if(_.isUndefined(t))return!0;switch(a.setValue(i,n),t.type){case"kirki-background":_.isUndefined(n["background-color"])||a.setColorPicker(a.findElement(i,".kirki-color-control"),n["background-color"]),a.findElement(i,".placeholder, .thumbnail").removeClass().addClass("placeholder").html("No file selected"),_.each(["background-repeat","background-position"],function(e){_.isUndefined(n[e])||a.setSelectWoo(a.findElement(i,"."+e+" select"),n[e])}),_.each(["background-size","background-attachment"],function(e){jQuery(a.findElement(i,"."+e+' input[value="'+n+'"]')).prop("checked",!0)}),e=JSON.stringify(n).replace(/'/g,"&#39"),jQuery(a.findElement(i,".background-hidden-value").attr("value",e)).trigger("change");break;case"kirki-code":jQuery(a.findElement(i,".CodeMirror"))[0].CodeMirror.setValue(n);break;case"checkbox":case"kirki-switch":case"kirki-toggle":n=1===n||"1"===n||!0===n,jQuery(a.findElement(i,"input")).prop("checked",n),wp.customize.instance(i).set(n);break;case"kirki-select":a.setSelectWoo(a.findElement(i,"select"),n);break;case"kirki-slider":jQuery(a.findElement(i,"input")).prop("value",n),jQuery(a.findElement(i,".kirki_range_value .value")).html(n);break;case"kirki-generic":(_.isUndefined(t.choices)||_.isUndefined(t.choices.element))&&(t.choices.element="input"),jQuery(a.findElement(i,t.choices.element)).prop("value",n);break;case"kirki-color":a.setColorPicker(a.findElement(i,".kirki-color-control"),n);break;case"kirki-multicheck":a.findElement(i,"input").each(function(){jQuery(this).prop("checked",!1)}),_.each(n,function(e,t){jQuery(a.findElement(i,'input[value="'+n[t]+'"]')).prop("checked",!0)});break;case"kirki-multicolor":_.each(n,function(e,t){a.setColorPicker(a.findElement(i,".multicolor-index-"+t),e)});break;case"kirki-radio-buttonset":case"kirki-radio-image":case"kirki-radio":case"kirki-dashicons":case"kirki-color-palette":case"kirki-palette":jQuery(a.findElement(i,'input[value="'+n+'"]')).prop("checked",!0);break;case"kirki-typography":_.each(["font-family","variant"],function(e){_.isUndefined(n[e])||a.setSelectWoo(a.findElement(i,"."+e+" select"),n[e])}),_.each(["font-size","line-height","letter-spacing","word-spacing"],function(e){_.isUndefined(n[e])||jQuery(a.findElement(i,"."+e+" input")).prop("value",n[e])}),_.isUndefined(n.color)||a.setColorPicker(a.findElement(i,".kirki-color-control"),n.color),e=JSON.stringify(n).replace(/'/g,"&#39"),jQuery(a.findElement(i,".typography-hidden-value").attr("value",e)).trigger("change");break;case"kirki-dimensions":_.each(n,function(e,t){jQuery(a.findElement(i,"."+t+" input")).prop("value",e)});break;case"kirki-repeater":case"kirki-custom":break;default:jQuery(a.findElement(i,"input")).prop("value",n)}},setColorPicker:function(e,t){e.attr("data-default-color",t).data("default-color",t).wpColorPicker("color",t)},setSelectWoo:function(e,t){jQuery(e).selectWoo().val(t).trigger("change")},setTextarea:function(e,t){jQuery(e).prop("value",t)},findElement:function(e,t){return wp.customize.control(e).container.find(t)},setValue:function(e,t,i){i=_.isUndefined(i)?100:parseInt(i,10),wp.customize.instance(e).set({}),setTimeout(function(){wp.customize.instance(e).set(t)},i)}}),(kirki={initialized:!1,initialize:function(){this.initialized||(setTimeout(function(){kirki.util.webfonts.standard.initialize(),kirki.util.webfonts.google.initialize()},150),this.initialized=!0)}}).initialize();var kirki=kirki||{},kirki=(kirki=jQuery.extend(kirki,{control:{"kirki-radio":{init:function(e){this.template(e),kirki.input.radio.init(e)},template:function(e){var t=wp.template("kirki-input-radio");e.container.html(t({label:e.params.label,description:e.params.description,"data-id":e.id,inputAttrs:e.params.inputAttrs,default:e.params.default,value:kirki.setting.get(e.id),choices:e.params.choices}))}},"kirki-color":{init:function(e){this.template(e),kirki.input.color.init(e)},template:function(e){var t=wp.template("kirki-input-color");e.container.html(t({label:e.params.label,description:e.params.description,"data-id":e.id,mode:e.params.mode,inputAttrs:e.params.inputAttrs,"data-palette":e.params.palette,"data-default-color":e.params.default,"data-alpha":e.params.choices.alpha,value:kirki.setting.get(e.id)}))}},"kirki-generic":{init:function(e){this.template(e),_.isUndefined(e.params)||_.isUndefined(e.params.choices)||_.isUndefined(e.params.choices.element)||"textarea"!==e.params.choices.element?kirki.input.genericInput.init(e):kirki.input.textarea.init(e)},template:function(e){var t,i={label:e.params.label,description:e.params.description,"data-id":e.id,inputAttrs:e.params.inputAttrs,choices:e.params.choices,value:kirki.setting.get(e.id)};if(!_.isUndefined(e.params)&&!_.isUndefined(e.params.choices)&&!_.isUndefined(e.params.choices.element)&&"textarea"===e.params.choices.element)return t=wp.template("kirki-input-textarea"),void e.container.html(t(i));t=wp.template("kirki-input-generic"),e.container.html(t(i))}},"kirki-number":{init:function(e){this.template(e),kirki.input.number.init(e)},template:function(e){var t=wp.template("kirki-input-number");e.container.html(t(args={label:e.params.label,description:e.params.description,"data-id":e.id,inputAttrs:e.params.inputAttrs,choices:e.params.choices,value:kirki.setting.get(e.id)}))}},"kirki-image":{init:function(e){this.template(e),kirki.input.image.init(e)},template:function(e){var t=wp.template("kirki-input-image");e.container.html(t(args={label:e.params.label,description:e.params.description,"data-id":e.id,inputAttrs:e.params.inputAttrs,choices:e.params.choices,value:kirki.setting.get(e.id)}))}},"kirki-select":{init:function(e){this.template(e),kirki.input.select.init(e)},template:function(e){var t=wp.template("kirki-input-select");e.container.html(t({label:e.params.label,description:e.params.description,"data-id":e.id,inputAttrs:e.params.inputAttrs,choices:e.params.choices,value:kirki.setting.get(e.id),multiple:e.params.multiple||1,placeholder:e.params.placeholder}))}}}}))||{},kirki=(kirki=jQuery.extend(kirki,{input:{radio:{init:function(e){jQuery('input[data-id="'+e.id+'"]').on("change keyup paste click",function(){kirki.setting.set(e.id,jQuery(this).val())})}},color:{init:function(e){var t,i=jQuery('.kirki-color-control[data-id="'+e.id+'"]');e.choices=e.choices||{},_.isEmpty(e.choices)&&e.params.choices&&(e.choices=e.params.choices),_.isEmpty(e.choices)||i.wpColorPicker(e.choices),setTimeout(function(){(t=jQuery('.kirki-input-container[data-id="'+e.id+'"] .wp-picker-clear')).length&&t.click(function(){kirki.setting.set(e.id,"")})},200),i.wpColorPicker({change:function(){setTimeout(function(){kirki.setting.set(e.id,i.val())},20)}})}},genericInput:{init:function(e){jQuery('input[data-id="'+e.id+'"]').on("change keyup paste click",function(){kirki.setting.set(e.id,jQuery(this).val())})}},textarea:{init:function(e){jQuery('textarea[data-id="'+e.id+'"]').on("change keyup paste click",function(){kirki.setting.set(e.id,jQuery(this).val())})}},select:{init:function(e){var t,i=jQuery('select[data-id="'+e.id+'"]'),n=parseInt(i.data("multiple"),10),a={escapeMarkup:function(e){return e}};e.params.placeholder&&(a.placeholder=e.params.placeholder,a.allowClear=!0),1<n&&(a.maximumSelectionLength=n),jQuery(i).selectWoo(a).on("change",function(){t=null===(t=jQuery(this).val())&&1<n?[]:t,kirki.setting.set(e.id,t)})}},number:{init:function(i){var e,t,n=jQuery('input[data-id="'+i.id+'"]'),a=i.setting._value;i.params.choices=_.defaults(i.params.choices,{min:0,max:100,step:1}),!isNaN(a)&&""!==a||(a=i.params.choices.min<0&&0<i.params.choices.max?0:i.params.choices.min),a=parseFloat(a),i.params.choices.step="any"===i.params.choices.step?.001:i.params.choices.step,i.params.choices.min=parseFloat(i.params.choices.min),i.params.choices.max=parseFloat(i.params.choices.max),i.params.choices.step=parseFloat(i.params.choices.step),e=jQuery('.kirki-input-container[data-id="'+i.id+'"] .plus'),t=jQuery('.kirki-input-container[data-id="'+i.id+'"] .minus'),e.click(function(){var e=parseFloat(n.val()),t=e>=i.params.choices.max?e:e+i.params.choices.step;n.val(t),n.trigger("change")}),t.click(function(){var e=parseFloat(n.val()),t=e<=i.params.choices.min?e:e-i.params.choices.step;n.val(t),n.trigger("change")}),n.on("change keyup paste click",function(){var e=jQuery(this).val();isNaN(e)&&(e=parseFloat(e,10),e=isNaN(e)?0:e,jQuery(this).attr("value",e)),kirki.setting.set(i.id,e)})}},image:{init:function(a){var t=kirki.setting.get(a.id),n=_.isUndefined(a.params.choices)||_.isUndefined(a.params.choices.save_as)?"url":a.params.choices.save_as,r=a.container.find(".placeholder, .thumbnail"),e="array"===n?t.url:t,s=a.container.find(".image-upload-remove-button"),o=a.container.find(".image-default-button"),t="array"===n&&_.isString(t)?{url:t}:t;"id"!==n&&"ID"!==n||""===t||wp.media.attachment(t).fetch().then(function(){setTimeout(function(){var e=wp.media.attachment(t).get("url");r.removeClass().addClass("thumbnail thumbnail-image").html('<img src="'+e+'" alt="" />')},700)}),("url"===n&&""!==t||"array"===n&&!_.isUndefined(t.url)&&""!==t.url)&&a.container.find("image-default-button").hide(),("url"===n&&""===t||"array"===n&&(_.isUndefined(t.url)||""===t.url))&&s.hide(),t===a.params.default&&a.container.find("image-default-button").hide(),""!==e&&r.removeClass().addClass("thumbnail thumbnail-image").html('<img src="'+e+'" alt="" />'),a.container.on("click",".image-upload-button",function(e){var i=wp.media({multiple:!1}).open().on("select",function(){var e=i.state().get("selection").first().toJSON(),t=e.url;_.isUndefined(e.sizes)||(t=e.sizes.full.url,_.isUndefined(e.sizes.medium)?_.isUndefined(e.sizes.thumbnail)||(t=e.sizes.thumbnail.url):t=e.sizes.medium.url),"array"===n?kirki.setting.set(a.id,{id:e.id,url:e.sizes.full.url,width:e.width,height:e.height}):"id"===n?kirki.setting.set(a.id,e.id):kirki.setting.set(a.id,_.isUndefined(e.sizes)?e.url:e.sizes.full.url),r.length&&r.removeClass().addClass("thumbnail thumbnail-image").html('<img src="'+t+'" alt="" />'),s.length&&(s.show(),o.hide())});e.preventDefault()}),a.container.on("click",".image-upload-remove-button",function(e){var t,i,n;e.preventDefault(),kirki.setting.set(a.id,""),t=a.container.find(".placeholder, .thumbnail"),i=a.container.find(".image-upload-remove-button"),n=a.container.find(".image-default-button"),t.length&&t.removeClass().addClass("placeholder").html(kirkiL10n.noFileSelected),i.length&&(i.hide(),jQuery(n).hasClass("button")&&n.show())}),a.container.on("click",".image-default-button",function(e){var t,i,n;e.preventDefault(),kirki.setting.set(a.id,a.params.default),t=a.container.find(".placeholder, .thumbnail"),i=a.container.find(".image-upload-remove-button"),n=a.container.find(".image-default-button"),t.length&&t.removeClass().addClass("thumbnail thumbnail-image").html('<img src="'+a.params.default+'" alt="" />'),i.length&&(i.show(),n.hide())})}}}}))||{},kirki=(kirki=jQuery.extend(kirki,{setting:{get:function(e){var t=e.split("["),i="",n=0,a="";return _.each(t,function(e,t){e=e.replace("]",""),0===t?i=e:i+="["+e+"]",_.isUndefined(wp.customize.instance(i))||(a=wp.customize.instance(i).get(),n=t),n<t&&_.isObject(a)&&!_.isUndefined(a[e])&&(a=a[e])}),a},set:function(e,t,i){var n,a,r,s,o="",c="",l={},u=e;_.isObject(e)&&(u=jQuery(e).attr("data-id")?e.attr("data-id"):e.parents("[data-id]").attr("data-id")),void 0===wp.customize.control(u)?(n=u.split("["),_.each(n,function(e,t){e=e.replace("]",""),o=0===t?e:"["+e+"]",_.isUndefined(wp.customize.instance(o))||(c=o,a=wp.customize.instance(c).get())}),""!==(r=u.replace(c,""))?(_.isObject(a)||(a={}),"["===r.charAt(0)&&(r=r.replace("[","")),s=r.split("["),_.each(s,function(e,t){s[t]=e.replace("]","")}),i&&s.push(i),l='{"'+s.join('":{"')+'":"'+t+'"'+"}".repeat(s.length),l=JSON.parse(l),jQuery.extend(!0,a,l),t=a):i&&((a=_.isObject(a)?a:{})[i]=t,t=a),wp.customize.control(c).setting.set(t)):wp.customize.control(u).setting.set(t)}}}))||{};kirki=jQuery.extend(kirki,{util:{webfonts:{google:{fonts:{},initialize:function(){this.setFonts()},setFonts:function(){var t=this;_.isEmpty(t.fonts)&&jQuery.post(ajaxurl,{action:"kirki_fonts_google_all_get"},function(e){t.fonts=JSON.parse(e)})},getFont:function(e){var t=this.getFonts();return void 0!==t[e]&&t[e]},getFonts:function(e,i,t){var n=this,a={},r={},s={};return e="alpha"!==(e=e||"alpha")&&"popularity"!==e&&"trending"!==e?"alpha":e,t=t||0,t=parseInt(t,10),"alpha"===e?a=jQuery.extend({},n.fonts.items):_.each(n.fonts.order[e],function(e){a[e]=n.fonts.items[e]}),""!==i&&i?_.each(a,function(e,t){i===e.category&&(r[t]=e)}):r=a,0<t?(_.each(_.first(_.keys(r),t),function(e){s[e]=r[e]}),s):r},getVariants:function(e){var t=this.getFont(e);return!!t&&(!_.isUndefined(t.variants)&&t.variants)}},standard:{fonts:{},initialize:function(){this.setFonts()},setFonts:function(){var t=this;_.isEmpty(t.fonts)&&jQuery.post(ajaxurl,{action:"kirki_fonts_standard_all_get"},function(e){t.fonts=JSON.parse(e)})},getVariants:function(){return["regular","italic","700","700italic"]}},getFontType:function(e){return void 0!==this.standard.fonts[e]||void 0!==this.standard.fonts.stack&&void 0!==this.standard.fonts.stack[e]?"standard":void 0!==this.google.fonts.items[e]&&"google"}},validate:{cssValue:function(e){var t,i;return"string"!=typeof e||"number"!=typeof e||(0===e||"0"===e||"auto"===e||"inherit"===e||"initial"===e||(0<=e.indexOf("calc(")&&0<=e.indexOf(")")||(t=parseFloat(e),i=e.replace(t,""),e?!isNaN(t)&&-1<jQuery.inArray(i,["fr","rem","em","ex","%","px","cm","mm","in","pt","pc","ch","vh","vw","vmin","vmax"]):void 0)))}},parseHtmlEntities:function(e){return(new DOMParser).parseFromString("<!doctype html><body>"+e,"text/html").body.textContent}}}),function(){"use strict";wp.customize.kirkiDynamicControl=wp.customize.Control.extend({initialize:function(e,t){var i=t||{};i.params=i.params||{},i.params.type||(i.params.type="kirki-generic"),i.params.content||(i.params.content=jQuery("<li></li>"),i.params.content.attr("id","customize-control-"+e.replace(/]/g,"").replace(/\[/g,"-")),i.params.content.attr("class","customize-control customize-control-"+i.params.type)),this.propertyElements=[],wp.customize.Control.prototype.initialize.call(this,e,i)},_setUpSettingRootLinks:function(){var n=this;n.container.find("[data-customize-setting-link]").each(function(){var i=jQuery(this);wp.customize(i.data("customizeSettingLink"),function(e){var t=new wp.customize.Element(i);n.elements.push(t),t.sync(e),t.set(e())})})},_setUpSettingPropertyLinks:function(){var n=this;n.setting&&n.container.find("[data-customize-setting-property-link]").each(function(){var e=jQuery(this),i=e.data("customizeSettingPropertyLink"),t=new wp.customize.Element(e);n.propertyElements.push(t),t.set(n.setting()[i]),t.bind(function(e){var t=n.setting();e!==t[i]&&((t=_.clone(t))[i]=e,n.setting.set(t))}),n.setting.bind(function(e){e[i]!==t.get()&&t.set(e[i])})})},ready:function(){var e=this;e._setUpSettingRootLinks(),e._setUpSettingPropertyLinks(),wp.customize.Control.prototype.ready.call(e),e.deferred.embedded.done(function(){e.initKirkiControl(e)})},embed:function(){var t=this,e=t.section();e&&wp.customize.section(e,function(e){"kirki-expanded"===e.params.type||e.expanded()||wp.customize.settings.autofocus.control===t.id?t.actuallyEmbed():e.expanded.bind(function(e){e&&t.actuallyEmbed()})})},actuallyEmbed:function(){"resolved"!==this.deferred.embedded.state()&&(this.renderContent(),this.deferred.embedded.resolve())},focus:function(e){this.actuallyEmbed(),wp.customize.Control.prototype.focus.call(this,e)},initKirkiControl:function(e){void 0===kirki.control[e.params.type]?this.container.on("change keyup paste click","input",function(){e.setting.set(jQuery(this).val())}):kirki.control[e.params.type].init(e)}})}(),_.each(kirki.control,function(e,t){wp.customize.controlConstructor[t]=wp.customize.kirkiDynamicControl.extend({})}),wp.customize.controlConstructor["kirki-background"]=wp.customize.Control.extend({ready:function(){"use strict";!_.isUndefined(window.kirkiControlLoader)&&_.isFunction(kirkiControlLoader)?kirkiControlLoader(this):this.initKirkiControl()},initKirkiControl:function(){var s=this,e=s.setting._value,t=s.container.find(".kirki-color-control");!_.isUndefined(e["background-image"])&&""!==e["background-image"]||(s.container.find(".background-wrapper > .background-repeat").hide(),s.container.find(".background-wrapper > .background-position").hide(),s.container.find(".background-wrapper > .background-size").hide(),s.container.find(".background-wrapper > .background-attachment").hide()),t.wpColorPicker({change:function(){setTimeout(function(){s.saveValue("background-color",t.val())},100)}}),s.container.on("change",".background-repeat select",function(){s.saveValue("background-repeat",jQuery(this).val())}),s.container.on("change click",".background-size input",function(){s.saveValue("background-size",jQuery(this).val())}),s.container.on("change",".background-position select",function(){s.saveValue("background-position",jQuery(this).val())}),s.container.on("change click",".background-attachment input",function(){s.saveValue("background-attachment",jQuery(this).val())}),s.container.on("click",".background-image-upload-button",function(e){var r=wp.media({multiple:!1}).open().on("select",function(){var e,t,i,n=r.state().get("selection").first(),a=n.toJSON().sizes.full.url;_.isUndefined(n.toJSON().sizes.medium)?_.isUndefined(n.toJSON().sizes.thumbnail)||(a=n.toJSON().sizes.thumbnail.url):a=n.toJSON().sizes.medium.url,e=n.toJSON().sizes.full.url,n.toJSON().id,n.toJSON().width,n.toJSON().height,""!==e&&s.container.find(".background-wrapper > .background-repeat, .background-wrapper > .background-position, .background-wrapper > .background-size, .background-wrapper > .background-attachment").show(),s.saveValue("background-image",e),t=s.container.find(".placeholder, .thumbnail"),i=s.container.find(".background-image-upload-remove-button"),t.length&&t.removeClass().addClass("thumbnail thumbnail-image").html('<img src="'+a+'" alt="" />'),i.length&&i.show()});e.preventDefault()}),s.container.on("click",".background-image-upload-remove-button",function(e){var t,i;e.preventDefault(),s.saveValue("background-image",""),t=s.container.find(".placeholder, .thumbnail"),i=s.container.find(".background-image-upload-remove-button"),s.container.find(".background-wrapper > .background-repeat").hide(),s.container.find(".background-wrapper > .background-position").hide(),s.container.find(".background-wrapper > .background-size").hide(),s.container.find(".background-wrapper > .background-attachment").hide(),t.length&&t.removeClass().addClass("placeholder").html("No file selected"),i.length&&i.hide()})},saveValue:function(e,t){var i=jQuery("#customize-control-"+this.id.replace("[","-").replace("]","")+" .background-hidden-value"),n=this.setting._value;n[e]=t,jQuery(i).attr("value",JSON.stringify(n)).trigger("change"),this.setting.set(n)}}),wp.customize.controlConstructor["kirki-color-palette"]=wp.customize.kirkiDynamicControl.extend({}),wp.customize.controlConstructor["kirki-dashicons"]=wp.customize.kirkiDynamicControl.extend({}),wp.customize.controlConstructor["kirki-date"]=wp.customize.kirkiDynamicControl.extend({initKirkiControl:function(){var e=this,t=e.selector+" input.datepicker";jQuery(t).datepicker({dateFormat:"yy-mm-dd"}),this.container.on("change keyup paste","input.datepicker",function(){e.setting.set(jQuery(this).val())})}}),wp.customize.controlConstructor["kirki-dimension"]=wp.customize.kirkiDynamicControl.extend({initKirkiControl:function(){var e,t=this;t.kirkiNotifications(),this.container.on("change keyup paste","input",function(){e=jQuery(this).val(),t.setting.set(e)})},kirkiNotifications:function(){var n=void 0!==this.params.choices&&void 0!==this.params.choices.accept_unitless&&!0===this.params.choices.accept_unitless;wp.customize(this.id,function(i){i.bind(function(e){var t="long_title";!1!==kirki.util.validate.cssValue(e)||n&&!isNaN(e)?i.notifications.remove(t):i.notifications.add(t,new wp.customize.Notification(t,{type:"warning",message:dimensionkirkiL10n["invalid-value"]}))})})}}),wp.customize.controlConstructor["kirki-dimensions"]=wp.customize.kirkiDynamicControl.extend({initKirkiControl:function(){var e,t=this.params.choices.controls,i={},n=[];for(_.each(t,function(e,t){!0===e&&n.push(t)}),e=0;e<n.length;e++)i[n[e]]=this.setting._value[n[e]],this.updateDimensionsValue(n[e],i)},updateDimensionsValue:function(e,t){var i=this;i.container.on("change keyup paste","."+e+" input",function(){t[e]=jQuery(this).val(),i.kirkiNotifications(),i.saveValue(t)})},saveValue:function(e){var i={};_.each(e,function(e,t){i[t]=e}),this.setting.set(i)},kirkiNotifications:function(){wp.customize(this.id,function(a){a.bind(function(e){var t,i="long_title",n={};if(a.notifications.remove(i),_.each(e,function(e,t){!1===kirki.util.validate.cssValue(e)?n[t]=e:delete n[t]}),!_.isEmpty(n))return t=dimensionskirkiL10n["invalid-value"]+" ("+_.values(n).toString()+") ",void a.notifications.add(i,new wp.customize.Notification(i,{type:"warning",message:t}));a.notifications.remove(i)})})}}),wp.customize.controlConstructor["kirki-editor"]=wp.customize.kirkiDynamicControl.extend({initKirkiControl:function(){var i,n=this,a=n.container.find("textarea"),e="kirki-editor-"+n.id.replace("[","").replace("]","");wp.editor&&wp.editor.initialize&&wp.editor.initialize(e,{tinymce:{wpautop:!0},quicktags:!0,mediaButtons:!0}),(i=tinyMCE.get(e))&&i.onChange.add(function(e){var t;e.save(),t=i.getContent(),a.val(t).trigger("change"),wp.customize.instance(n.id).set(t)})}}),wp.customize.controlConstructor["kirki-multicheck"]=wp.customize.kirkiDynamicControl.extend({initKirkiControl:function(){var n=this;n.container.on("change","input",function(){var t=[],i=0;jQuery.each(n.params.choices,function(e){n.container.find('input[value="'+e+'"]').is(":checked")?(n.container.find('input[value="'+e+'"]').parent().addClass("checked"),t[i]=e,i++):n.container.find('input[value="'+e+'"]').parent().removeClass("checked")}),n.setting.set(t)})}}),wp.customize.controlConstructor["kirki-multicolor"]=wp.customize.Control.extend({ready:function(){"use strict";!_.isUndefined(window.kirkiControlLoader)&&_.isFunction(kirkiControlLoader)?kirkiControlLoader(this):this.initKirkiControl()},initKirkiControl:function(){"use strict";var r=this.params.choices,e=Object.keys(r),t=(this.params.value,0);function i(e,t,i){var n=e.container.find(".multicolor-index-"+i),a={change:function(){setTimeout(function(){e.saveValue(i,n.val()),e.container.find(".multicolor-index-"+i).trigger("change")},100)}};_.isObject(r.irisArgs)&&_.each(r.irisArgs,function(e,t){a[t]=e}),n.wpColorPicker(a)}for(;t<Object.keys(r).length;)i(this,0,e[t]),t++},saveValue:function(e,t){var i=this.container.find(".multicolor-hidden-value"),n=this.setting._value;n[e]=t,jQuery(i).attr("value",JSON.stringify(n)).trigger("change"),this.setting.set(n)}}),wp.customize.controlConstructor["kirki-palette"]=wp.customize.kirkiDynamicControl.extend({}),wp.customize.controlConstructor["kirki-radio-buttonset"]=wp.customize.kirkiDynamicControl.extend({}),wp.customize.controlConstructor["kirki-radio-image"]=wp.customize.kirkiDynamicControl.extend({});var RepeaterRow=function(e,t,i,n){"use strict";var a=this;this.rowIndex=e,this.container=t,this.label=i,this.header=this.container.find(".repeater-row-header"),this.header.on("click",function(){a.toggleMinimize()}),this.container.on("click",".repeater-row-remove",function(){a.remove()}),this.header.on("mousedown",function(){a.container.trigger("row:start-dragging")}),this.container.on("keyup change","input, select, textarea",function(e){a.container.trigger("row:update",[a.rowIndex,jQuery(e.target).data("field"),e.target])}),this.setRowIndex=function(e){this.rowIndex=e,this.container.attr("data-row",e),this.container.data("row",e),this.updateLabel()},this.toggleMinimize=function(){this.container.toggleClass("minimized"),this.header.find(".dashicons").toggleClass("dashicons-arrow-up").toggleClass("dashicons-arrow-down")},this.remove=function(){this.container.slideUp(300,function(){jQuery(this).detach()}),this.container.trigger("row:remove",[this.rowIndex])},this.updateLabel=function(){var e,t,i;if("field"===this.label.type&&(e=this.container.find('.repeater-field [data-field="'+this.label.field+'"]'),_.isFunction(e.val)&&""!==(t=e.val())))return _.isUndefined(n.params.fields[this.label.field])||_.isUndefined(n.params.fields[this.label.field].type)||("select"===n.params.fields[this.label.field].type?_.isUndefined(n.params.fields[this.label.field].choices)||_.isUndefined(n.params.fields[this.label.field].choices[e.val()])||(t=n.params.fields[this.label.field].choices[e.val()]):"radio"!==n.params.fields[this.label.field].type&&"radio-image"!==n.params.fields[this.label.field].type||(i=n.selector+' [data-row="'+this.rowIndex+'"] .repeater-field [data-field="'+this.label.field+'"]:checked',t=jQuery(i).val())),void this.header.find(".repeater-row-label").text(t);this.header.find(".repeater-row-label").text(this.label.value+" "+(this.rowIndex+1))},this.updateLabel()};wp.customize.controlConstructor.repeater=wp.customize.Control.extend({ready:function(){"use strict";!_.isUndefined(window.kirkiControlLoader)&&_.isFunction(kirkiControlLoader)?kirkiControlLoader(this):this.initKirkiControl()},initKirkiControl:function(){"use strict";var t,i,n=this,e=this.params.value;this.settingField=this.container.find("[data-customize-setting-link]").first(),this.setValue([],!1),this.repeaterFieldsContainer=this.container.find(".repeater-fields").first(),this.currentIndex=0,this.rows=[],t=!1,_.isUndefined(this.params.choices.limit)||(t=!(this.params.choices.limit<=0)&&parseInt(this.params.choices.limit,10)),this.container.on("click","button.repeater-add",function(e){e.preventDefault(),!t||n.currentIndex<t?((i=n.addRow()).toggleMinimize(),n.initColorPicker(),n.initSelect(i)):jQuery(n.selector+" .limit").addClass("highlight")}),this.container.on("click",".repeater-row-remove",function(){n.currentIndex--,(!t||n.currentIndex<t)&&jQuery(n.selector+" .limit").removeClass("highlight")}),this.container.on("click keypress",".repeater-field-image .upload-button,.repeater-field-cropped_image .upload-button,.repeater-field-upload .upload-button",function(e){e.preventDefault(),n.$thisButton=jQuery(this),n.openFrame(e)}),this.container.on("click keypress",".repeater-field-image .remove-button,.repeater-field-cropped_image .remove-button",function(e){e.preventDefault(),n.$thisButton=jQuery(this),n.removeImage(e)}),this.container.on("click keypress",".repeater-field-upload .remove-button",function(e){e.preventDefault(),n.$thisButton=jQuery(this),n.removeFile(e)}),this.repeaterTemplate=_.memoize(function(){var t={evaluate:/<#([\s\S]+?)#>/g,interpolate:/\{\{\{([\s\S]+?)\}\}\}/g,escape:/\{\{([^\}]+?)\}\}(?!\})/g,variable:"data"};return function(e){return _.template(n.container.find(".customize-control-repeater-content").first().html(),null,t)(e)}}),e.length&&_.each(e,function(e){i=n.addRow(e),n.initColorPicker(),n.initSelect(i,e)}),this.setValue(e,!0,!0),this.repeaterFieldsContainer.sortable({handle:".repeater-row-header",update:function(){n.sort()}})},openFrame:function(e){"use strict";wp.customize.utils.isKeydownButNotEnterEvent(e)||(this.$thisButton.closest(".repeater-field").hasClass("repeater-field-cropped_image")?this.initCropperFrame():this.initFrame(),this.frame.open())},initFrame:function(){"use strict";var e=this.getMimeType();this.frame=wp.media({states:[new wp.media.controller.Library({library:wp.media.query({type:e}),multiple:!1,date:!1})]}),this.frame.on("select",this.onSelect,this)},initCropperFrame:function(){"use strict";var t=this.$thisButton.siblings("input.hidden-field").attr("data-field"),e=this.getMimeType();_.isString(t)&&""!==t&&_.isObject(this.params.fields[t])&&"cropped_image"===this.params.fields[t].type&&["width","height","flex_width","flex_height"].forEach(function(e){_.isUndefined(this.params.fields[t][e])||(this.params[e]=this.params.fields[t][e])}.bind(this)),this.frame=wp.media({button:{text:"Select and Crop",close:!1},states:[new wp.media.controller.Library({library:wp.media.query({type:e}),multiple:!1,date:!1,suggestedWidth:this.params.width,suggestedHeight:this.params.height}),new wp.media.controller.CustomizeImageCropper({imgSelectOptions:this.calculateImageSelectOptions,control:this})]}),this.frame.on("select",this.onSelectForCrop,this),this.frame.on("cropped",this.onCropped,this),this.frame.on("skippedcrop",this.onSkippedCrop,this)},onSelect:function(){"use strict";var e=this.frame.state().get("selection").first().toJSON();this.$thisButton.closest(".repeater-field").hasClass("repeater-field-upload")?this.setFileInRepeaterField(e):this.setImageInRepeaterField(e)},onSelectForCrop:function(){"use strict";var e=this.frame.state().get("selection").first().toJSON();this.params.width!==e.width||this.params.height!==e.height||this.params.flex_width||this.params.flex_height?this.frame.setState("cropper"):this.setImageInRepeaterField(e)},onCropped:function(e){"use strict";this.setImageInRepeaterField(e)},calculateImageSelectOptions:function(e,t){"use strict";var i,n,a,r=t.get("control"),s=!!parseInt(r.params.flex_width,10),o=!!parseInt(r.params.flex_height,10),c=e.get("width"),l=e.get("height"),u=parseInt(r.params.width,10),d=parseInt(r.params.height,10),p=u/d,h=c,f=l;return t.set("canSkipCrop",!r.mustBeCropped(s,o,u,d,c,l)),p<h/f?u=(d=f)*p:d=(u=h)/p,!(a={handles:!0,keys:!0,instance:!0,persistent:!0,imageWidth:c,imageHeight:l,x1:i=(h-u)/2,y1:n=(f-d)/2,x2:u+i,y2:d+n})==o&&!1==s&&(a.aspectRatio=u+":"+d),!1==o&&(a.maxHeight=d),!1==s&&(a.maxWidth=u),a},mustBeCropped:function(e,t,i,n,a,r){"use strict";return!(!0===e&&!0===t||!0===e&&n===r||!0===t&&i===a||i===a&&n===r||a<=i)},onSkippedCrop:function(){"use strict";var e=this.frame.state().get("selection").first().toJSON();this.setImageInRepeaterField(e)},setImageInRepeaterField:function(e){"use strict";var t=this.$thisButton.closest(".repeater-field-image,.repeater-field-cropped_image");t.find(".kirki-image-attachment").html('<img src="'+e.url+'">').hide().slideDown("slow"),t.find(".hidden-field").val(e.id),this.$thisButton.text(this.$thisButton.data("alt-label")),t.find(".remove-button").show(),t.find("input, textarea, select").trigger("change"),this.frame.close()},setFileInRepeaterField:function(e){"use strict";var t=this.$thisButton.closest(".repeater-field-upload");t.find(".kirki-file-attachment").html('<span class="file"><span class="dashicons dashicons-media-default"></span> '+e.filename+"</span>").hide().slideDown("slow"),t.find(".hidden-field").val(e.id),this.$thisButton.text(this.$thisButton.data("alt-label")),t.find(".upload-button").show(),t.find(".remove-button").show(),t.find("input, textarea, select").trigger("change"),this.frame.close()},getMimeType:function(){"use strict";var e=this.$thisButton.siblings("input.hidden-field").attr("data-field");return _.isString(e)&&""!==e&&_.isObject(this.params.fields[e])&&"upload"===this.params.fields[e].type&&!_.isUndefined(this.params.fields[e].mime_type)?this.params.fields[e].mime_type:"image"},removeImage:function(e){"use strict";var t,i;wp.customize.utils.isKeydownButNotEnterEvent(e)||(i=(t=this.$thisButton.closest(".repeater-field-image,.repeater-field-cropped_image,.repeater-field-upload")).find(".upload-button"),t.find(".kirki-image-attachment").slideUp("fast",function(){jQuery(this).show().html(jQuery(this).data("placeholder"))}),t.find(".hidden-field").val(""),i.text(i.data("label")),this.$thisButton.hide(),t.find("input, textarea, select").trigger("change"))},removeFile:function(e){"use strict";var t,i;wp.customize.utils.isKeydownButNotEnterEvent(e)||(i=(t=this.$thisButton.closest(".repeater-field-upload")).find(".upload-button"),t.find(".kirki-file-attachment").slideUp("fast",function(){jQuery(this).show().html(jQuery(this).data("placeholder"))}),t.find(".hidden-field").val(""),i.text(i.data("label")),this.$thisButton.hide(),t.find("input, textarea, select").trigger("change"))},getValue:function(){"use strict";return JSON.parse(decodeURI(this.setting.get()))},setValue:function(e,t,i){"use strict";var a=e,r=[];i&&(jQuery.each(this.params.fields,function(e,t){"image"!==t.type&&"cropped_image"!==t.type&&"upload"!==t.type||r.push(e)}),jQuery.each(e,function(i,n){jQuery.each(r,function(e,t){_.isUndefined(n[t])||_.isUndefined(n[t].id)||(a[i][t]=n[t].id)})})),this.setting.set(encodeURI(JSON.stringify(a))),t&&this.settingField.trigger("change")},addRow:function(e){"use strict";var t,a,i,r=this,n=r.repeaterTemplate(),s=this.getValue(),o={};if(n){if(t=jQuery.extend(!0,{},r.params.fields),e)for(i in e)e.hasOwnProperty(i)&&t.hasOwnProperty(i)&&(t[i].default=e[i]);for(i in t.index=this.currentIndex,n=n(t),(a=new RepeaterRow(r.currentIndex,jQuery(n).appendTo(r.repeaterFieldsContainer),r.params.row_label,r)).container.on("row:remove",function(e,t){r.deleteRow(t)}),a.container.on("row:update",function(e,t,i,n){r.updateField.call(r,e,t,i,n),a.updateLabel()}),this.rows[this.currentIndex]=a,t)t.hasOwnProperty(i)&&(o[i]=t[i].default);return s[this.currentIndex]=o,this.setValue(s,!0),this.currentIndex++,a}},sort:function(){"use strict";var i=this,e=this.repeaterFieldsContainer.find(".repeater-row"),n=[],a=i.getValue(),r=[],s=[];e.each(function(e,t){n.push(jQuery(t).data("row"))}),jQuery.each(n,function(e,t){r[e]=i.rows[t],r[e].setRowIndex(e),s[e]=a[t]}),i.rows=r,i.setValue(s)},deleteRow:function(e){"use strict";var t,i=this.getValue();for(t in i[e]&&this.rows[e]&&(delete i[e],delete this.rows[e],this.setValue(i,!0)),this.rows)this.rows.hasOwnProperty(t)&&this.rows[t]&&(this.rows[t].updateLabel(),0)},updateField:function(e,t,i,n){"use strict";var a,r,s;this.rows[t]&&this.params.fields[i]&&(a=this.params.fields[i].type,r=this.rows[t],s=this.getValue(),n=jQuery(n),_.isUndefined(s[r.rowIndex][i])||(s[r.rowIndex][i]="checkbox"===a?n.is(":checked"):n.val(),this.setValue(s,!0)))},initColorPicker:function(){"use strict";var r=this,e=r.container.find(".color-picker-hex"),t={},i=e.data("field");_.isUndefined(i)||_.isUndefined(r.params.fields[i])||_.isUndefined(r.params.fields[i].palettes)||!_.isObject(r.params.fields[i].palettes)||(t.palettes=r.params.fields[i].palettes),t.change=function(e,t){var i=jQuery(e.target),n=i.closest(".repeater-row").data("row"),a=r.getValue();a[n][i.data("field")]=t.color.toString(),r.setValue(a,!0)},0!==e.length&&e.wpColorPicker(t)},initSelect:function(e,t){"use strict";var i,n,a=this,r=e.container.find(".repeater-field select"),s={};0!==r.length&&(i=r.data("field"),"undefed"!==(n=jQuery(r).data("multiple"))&&jQuery.isNumeric(n)&&1<(n=parseInt(n,10))&&(s.maximumSelectionLength=n),(t=t||{})[i]=t[i]||"",jQuery(r).selectWoo(s).val(t[i]||jQuery(r).val()),this.container.on("change",".repeater-field select",function(e){var t=jQuery(e.target),i=t.closest(".repeater-row").data("row"),n=a.getValue();n[i][t.data("field")]=jQuery(this).val(),a.setValue(n)}))}}),wp.customize.controlConstructor["kirki-slider"]=wp.customize.kirkiDynamicControl.extend({initKirkiControl:function(){var e=this,t="postMessage"===e.setting.transport?"mousemove change":"change",i=e.container.find('input[type="range"]'),n=e.container.find('input[type="text"]'),a=e.setting._value;n.attr("value",a),i.on("mousemove change",function(){n.attr("value",i.val())}),i.on(t,function(){e.setting.set(i.val())}),n.on("input paste change",function(){i.attr("value",n.val()),e.setting.set(n.val())}),e.container.find(".slider-reset").on("click",function(){n.attr("value",e.params.default),i.attr("value",e.params.default),e.setting.set(n.val())})}}),wp.customize.controlConstructor["kirki-sortable"]=wp.customize.Control.extend({ready:function(){"use strict";var e=this;jQuery(e.container.find("ul.sortable").first()).sortable({update:function(){e.setting.set(e.getNewVal())}}).disableSelection().find("li").each(function(){jQuery(this).find("i.visibility").click(function(){jQuery(this).toggleClass("dashicons-visibility-faint").parents("li:eq(0)").toggleClass("invisible")})}).click(function(){e.setting.set(e.getNewVal())})},getNewVal:function(){var e=jQuery(this.container.find("li")),t=[];return _.each(e,function(e){jQuery(e).hasClass("invisible")||t.push(jQuery(e).data("value"))}),t}}),wp.customize.controlConstructor["kirki-switch"]=wp.customize.kirkiDynamicControl.extend({initKirkiControl:function(){"use strict";var e=this,t=e.setting._value;this.container.on("change","input",function(){t=!!jQuery(this).is(":checked"),e.setting.set(t)})}}),wp.customize.controlConstructor["kirki-toggle"]=wp.customize.kirkiDynamicControl.extend({initKirkiControl:function(){var e=this,t=e.setting._value;this.container.on("change","input",function(){t=!!jQuery(this).is(":checked"),e.setting.set(t)})}}),wp.customize.controlConstructor["kirki-typography"]=wp.customize.kirkiDynamicControl.extend({initKirkiControl:function(){"use strict";var e,t=this,i=t.setting._value;t.renderFontSelector(),t.renderBackupFontSelector(),t.renderVariantSelector(),void 0!==t.params.default["font-size"]&&this.container.on("change keyup paste",".font-size input",function(){t.saveValue("font-size",jQuery(this).val())}),void 0!==t.params.default["line-height"]&&this.container.on("change keyup paste",".line-height input",function(){t.saveValue("line-height",jQuery(this).val())}),void 0!==t.params.default["margin-top"]&&this.container.on("change keyup paste",".margin-top input",function(){t.saveValue("margin-top",jQuery(this).val())}),void 0!==t.params.default["margin-bottom"]&&this.container.on("change keyup paste",".margin-bottom input",function(){t.saveValue("margin-bottom",jQuery(this).val())}),void 0!==t.params.default["letter-spacing"]&&(i["letter-spacing"]=jQuery.isNumeric(i["letter-spacing"])?i["letter-spacing"]+"px":i["letter-spacing"],this.container.on("change keyup paste",".letter-spacing input",function(){i["letter-spacing"]=jQuery.isNumeric(jQuery(this).val())?jQuery(this).val()+"px":jQuery(this).val(),t.saveValue("letter-spacing",i["letter-spacing"])})),void 0!==t.params.default["word-spacing"]&&this.container.on("change keyup paste",".word-spacing input",function(){t.saveValue("word-spacing",jQuery(this).val())}),void 0!==t.params.default["text-align"]&&this.container.on("change",".text-align input",function(){t.saveValue("text-align",jQuery(this).val())}),void 0!==t.params.default["text-transform"]&&jQuery(t.selector+" .text-transform select").selectWoo().on("change",function(){t.saveValue("text-transform",jQuery(this).val())}),void 0!==t.params.default["text-decoration"]&&jQuery(t.selector+" .text-decoration select").selectWoo().on("change",function(){t.saveValue("text-decoration",jQuery(this).val())}),void 0!==t.params.default.color&&(e=this.container.find(".kirki-color-control")).wpColorPicker({change:function(){setTimeout(function(){t.saveValue("color",e.val())},100)}})},renderFontSelector:function(){var e,t,i=this,n=i.selector+" .font-family select",a=[],r=[],s=[],o=i.setting._value,c=i.getFonts();_.isUndefined(c.standard)||_.each(c.standard,function(e){r.push({id:e.family.replace(/&quot;/g,"&#39"),text:e.label})}),_.isUndefined(c.google)||_.each(c.google,function(e){s.push({id:e.family,text:e.family})}),t={},_.isUndefined(i.params)||_.isUndefined(i.params.choices)||_.isUndefined(i.params.choices.fonts)||_.isUndefined(i.params.choices.fonts.families)||(t=i.params.choices.fonts.families),a=jQuery.extend({},t,{default:{text:kirkiL10n.defaultCSSValues,children:[{id:"",text:kirkiL10n.defaultBrowserFamily},{id:"initial",text:"initial"},{id:"inherit",text:"inherit"}]},standard:{text:kirkiL10n.standardFonts,children:r},google:{text:kirkiL10n.googleFonts,children:s}}),kirkiL10n.isScriptDebug&&(console.info('Kirki Debug: Font families for control "'+i.id+'":'),console.info(a)),a=_.values(a),e=jQuery(n).selectWoo({data:a}),!o["font-family"]&&""!==o["font-family"]||(o["font-family"]=kirki.util.parseHtmlEntities(o["font-family"].replace(/'/g,'"')),e.val(o["font-family"]).trigger("change")),e.on("change",function(){i.saveValue("font-family",jQuery(this).val()),i.renderBackupFontSelector(),i.renderVariantSelector()})},renderBackupFontSelector:function(){var e,t=this,i=t.selector+" .font-backup select",n=[],a=t.setting._value,r=a["font-family"],s=t.getFonts();!_.isUndefined(a["font-backup"])&&null!==a["font-backup"]||(a["font-backup"]=""),"inherit"!==r&&"initial"!==r&&"google"===kirki.util.webfonts.getFontType(r)?(jQuery(t.selector+" .font-backup").show(),_.isUndefined(s.standard)||_.each(s.standard,function(e){n.push({id:e.family.replace(/&quot;/g,"&#39"),text:e.label})}),e=jQuery(i).selectWoo({data:n}),void 0!==a["font-backup"]&&e.val(a["font-backup"].replace(/'/g,'"')).trigger("change"),e.on("change",function(){t.saveValue("font-backup",jQuery(this).val())})):jQuery(t.selector+" .font-backup").hide()},renderVariantSelector:function(){var e,t,i,n=this,a=n.setting._value,r=a["font-family"],s=n.selector+" .variant select",o=[],c=!1,l=kirki.util.webfonts.getFontType(r),u=["","regular","italic","700","700italic"];if("google"===l&&(u=kirki.util.webfonts.google.getVariants(r)),_.isUndefined(n.params)||_.isUndefined(n.params.choices)||_.isUndefined(n.params.choices.fonts)||_.isUndefined(n.params.choices.fonts.variants)||_.isUndefined(n.params.choices.fonts.variants[r])||(u=n.params.choices.fonts.variants[r]),kirkiL10n.isScriptDebug&&(console.info('Kirki Debug: Font variants for font-family "'+r+'":'),console.info(u)),"inherit"!==r&&"initial"!==r&&""!==r||(a.variant="inherit",u=[""],jQuery(n.selector+" .variant").hide()),u.length<=1)return jQuery(n.selector+" .variant").hide(),a.variant=u[0],n.saveValue("variant",a.variant),i=""!==a.variant&&a.variant?(e=_.isString(a.variant)?a.variant.match(/\d/g):"400",e=_.isObject(e)?e.join(""):"400",a.variant&&-1!==a.variant.indexOf("italic")?"italic":"normal"):e="",n.saveValue("font-weight",e),void n.saveValue("font-style",i);jQuery(n.selector+" .font-backup").show(),jQuery(n.selector+" .variant").show(),_.each(u,function(e){a.variant===e&&(c=!0),o.push({id:e,text:e})}),c||(a.variant="regular"),jQuery(s).hasClass("select2-hidden-accessible")&&(jQuery(s).selectWoo("destroy"),jQuery(s).empty()),(t=jQuery(s).selectWoo({data:o})).val(a.variant).trigger("change"),t.on("change",function(){n.saveValue("variant",jQuery(this).val()),"string"!=typeof a.variant&&(a.variant=u[0]),e=_.isString(a.variant)?a.variant.match(/\d/g):"400",e=_.isObject(e)?e.join(""):"400",i=-1!==a.variant.indexOf("italic")?"italic":"normal",n.saveValue("font-weight",e),n.saveValue("font-style",i)})},getFonts:function(){var e=this,t=kirki.util.webfonts.google.getFonts(),i={},n="alpha",a=0,r={};return _.isEmpty(e.params.choices.fonts.google)?i=kirki.util.webfonts.google.getFonts(n,"",a):"alpha"===e.params.choices.fonts.google[0]||"popularity"===e.params.choices.fonts.google[0]||"trending"===e.params.choices.fonts.google[0]?(n=e.params.choices.fonts.google[0],isNaN(e.params.choices.fonts.google[1])||(a=parseInt(e.params.choices.fonts.google[1],10)),i=kirki.util.webfonts.google.getFonts(n,"",a)):_.each(e.params.choices.fonts.google,function(e){void 0===t[e]||_.isEmpty(t[e])||(i[e]=t[e])}),_.isEmpty(e.params.choices.fonts.standard)?_.each(kirki.util.webfonts.standard.fonts,function(e,t){r[t]={family:e.stack,label:e.label}}):_.each(e.params.choices.fonts.standard,function(e){void 0===kirki.util.webfonts.standard.fonts[e]||_.isEmpty(kirki.util.webfonts.standard.fonts[e])?r[e]={family:e,label:e}:(r[e]={},"undefined"===kirki.util.webfonts.standard.fonts[e].stack||_.isEmpty(kirki.util.webfonts.standard.fonts[e].stack)?r[e].family=i[e]:r[e].family=kirki.util.webfonts.standard.fonts[e].stack,"undefined"===kirki.util.webfonts.standard.fonts[e].label||_.isEmpty(kirki.util.webfonts.standard.fonts[e].label)?_.isEmpty(r[e])||(r[e].label=r[e]):r[e].label=kirki.util.webfonts.standard.fonts[e].label)}),{google:i,standard:r}},saveValue:function(e,t){var i=this.container.find(".typography-hidden-value"),n=this.setting._value;n[e]=t,jQuery(i).attr("value",JSON.stringify(n)).trigger("change"),this.setting.set(n)}});